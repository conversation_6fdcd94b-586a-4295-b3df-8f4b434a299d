# Tóm tắt: <PERSON><PERSON> sung Active UOM cho Create Workflow

## 🎯 Mục tiê<PERSON>
<PERSON><PERSON> sung hỗ trợ `active_uom` và `active_conversion_factor` trong component Create Workflow để gửi thông tin đơn vị đo lường hiện tại đến API `createTaskandResourceArray`.

## ✅ Các thay đổi đã thực hiện

### 1. Cập nhật Stores

#### TaskItemUsedCreateStore.tsx
```typescript
export type TaskItemUsed = {
  // ... existing fields
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
};
```

#### TaskProductionCreateStore.tsx
```typescript
export type TaskProduction = {
  // ... existing fields
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
};
```

### 2. Cập nhật Create Components

#### CreateItemCreateView.tsx
- Khởi tạo `active_uom` và `active_conversion_factor` với giá trị mặc định khi tạo TaskItemUsed
```typescript
return {
  // ... existing fields
  active_uom: item.fullObject.uom, // Khởi tạo với UOM mặc định
  active_conversion_factor: item.fullObject.conversion_factor, // Khởi tạo với conversion_factor mặc định
};
```

#### CreateProductionCreateView.tsx
- Khởi tạo `active_uom` và `active_conversion_factor` với giá trị mặc định khi tạo TaskProduction
```typescript
return {
  // ... existing fields
  active_uom: item.fullObject.uom, // Khởi tạo với UOM mặc định
  active_conversion_factor: item.fullObject.conversion_factor, // Khởi tạo với conversion_factor mặc định
};
```

### 3. Cập nhật Table Components

#### ItemUsedTableCreateView.tsx
- Thêm import `Select` từ antd
- Cập nhật cột UOM để cho phép chọn UOM khác nhau
- Thêm logic cập nhật `active_uom` và `active_conversion_factor` khi người dùng thay đổi UOM

```typescript
{
  title: <FormattedMessage id="common.unit" />,
  dataIndex: 'uom_label',
  editable: false,
  renderFormItem: (_, { record }) => {
    const uoms = record?.uoms || [];
    return (
      <Select
        value={record?.active_uom || record?.uom}
        onChange={(value) => {
          const selectedUom = uoms.find((uom: any) => uom.uom === value);
          if (selectedUom) {
            // Cập nhật active_uom và active_conversion_factor
            const updatedRecord = {
              ...record,
              active_uom: selectedUom.uom,
              active_conversion_factor: selectedUom.conversion_factor,
              uom_label: selectedUom.uom_label,
            };
            
            // Cập nhật trong store
            const updatedTaskItems = taskItemUsed.map((item) =>
              item.iot_category_id === record?.iot_category_id ? updatedRecord : item
            );
            setTaskItemUsed(updatedTaskItems);
          }
        }}
        options={uoms.map((uom: any) => ({
          label: uom.uom_label,
          value: uom.uom,
        }))}
      />
    );
  },
  render: (_, record) => {
    return record?.uom_label || record?.active_uom;
  },
}
```

#### ProductionTableCreateView.tsx
- Tương tự như ItemUsedTableCreateView, thêm khả năng chọn UOM cho production

### 4. Cập nhật Main Create Component

#### Create/index.tsx
- Cập nhật logic mapping để gửi `active_uom` và `active_conversion_factor` trong request

**Cho item_list:**
```typescript
item_list: taskItems.map((d: TaskItemUsed) => {
  const {
    quantity = 0,
    description = null,
    iot_category_id = null,
    exp_quantity,
    conversion_factor = 1,
    loss_quantity = 0,
    active_uom,
    active_conversion_factor,
  } = d;

  // Tính toán lại exp_quantity nếu cần thiết
  const calculatedExpQuantity =
    exp_quantity !== undefined && conversion_factor !== undefined
      ? exp_quantity * conversion_factor
      : exp_quantity;

  return {
    quantity,
    description,
    iot_category_id,
    exp_quantity: calculatedExpQuantity,
    loss_quantity,
    active_uom,
    active_conversion_factor,
  };
}),
```

**Cho prod_quantity_list:**
```typescript
prod_quantity_list: productions.map((d: TaskProduction) => {
  const {
    quantity = 0,
    description = null,
    product_id = null,
    exp_quantity,
    conversion_factor = 1,
    lost_quantity = 0,
    active_uom,
    active_conversion_factor,
  } = d;

  // Tính toán lại exp_quantity nếu cần thiết
  const calculatedExpQuantity =
    exp_quantity !== undefined && conversion_factor !== undefined
      ? exp_quantity * conversion_factor
      : exp_quantity;

  return {
    quantity,
    description,
    product_id,
    exp_quantity: calculatedExpQuantity,
    lost_quantity,
    active_uom,
    active_conversion_factor,
  };
}),
```

## 🔧 Cách hoạt động

1. **Khởi tạo**: Khi người dùng thêm item/production, `active_uom` và `active_conversion_factor` được khởi tạo với giá trị mặc định
2. **Chọn UOM**: Người dùng có thể chọn UOM khác nhau từ dropdown trong table
3. **Cập nhật**: Khi chọn UOM mới, `active_uom` và `active_conversion_factor` được cập nhật tự động
4. **Gửi API**: Khi submit form, các giá trị này được gửi đến API `createTaskandResourceArray`

## 📋 Validation Rules (từ API)
- `active_uom`: Optional, string, max 140 characters
- `active_conversion_factor`: Optional, number, min 0.000000001, max 999999999

## 🎉 Lợi ích
- ✅ **Linh hoạt**: Người dùng có thể chọn đơn vị đo lường phù hợp
- ✅ **Chính xác**: API biết được đơn vị hiện tại để xử lý đúng
- ✅ **Tương thích**: Tương thích ngược với dữ liệu cũ
- ✅ **Nhất quán**: Cùng cách thức với các component update khác

## 📁 Files đã thay đổi
1. `user-web/src/stores/TaskItemUsedCreateStore.tsx`
2. `user-web/src/stores/TaskProductionCreateStore.tsx`
3. `user-web/src/components/Task/TaskItemUsed/CreateItemCreateView.tsx`
4. `user-web/src/components/Task/TaskProductionNew/CreateProductionCreateView.tsx`
5. `user-web/src/components/Task/TaskItemUsed/ItemUsedTableCreateView.tsx`
6. `user-web/src/components/Task/TaskProductionNew/ProductionTableCreateView.tsx`
7. `user-web/src/pages/FarmingManagement/WorkflowManagement/Create/index.tsx`

## 🧪 Demo Component
Đã tạo demo component tại: `user-web/src/components/Task/TaskItemUsed/demo/CreateWorkflowActiveUOMDemo.tsx`

## 🚀 Kết luận
API `createTaskandResourceArray` đã hoàn toàn sẵn sàng để xử lý `active_uom` và `active_conversion_factor`. Frontend đã được cập nhật để gửi các field này trong `item_list` và `prod_quantity_list` khi tạo task mới.
