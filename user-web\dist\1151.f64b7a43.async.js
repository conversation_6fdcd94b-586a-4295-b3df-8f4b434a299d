"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1151],{1151:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(38925);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(38513);
/* harmony import */ var _components_CollapsibleInfoCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(68177);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(85893);




var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_0__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    wrapper: {
      display: 'flex',
      flexDirection: 'column',
      gap: token.margin
    }
  };
});
var GeneralInfo = function GeneralInfo(_ref2) {
  var children = _ref2.children,
    infoTabs = _ref2.infoTabs;
  var styles = useStyles();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
    className: styles.wrapper,
    children: infoTabs && infoTabs.length ? infoTabs.map(function (infoTab) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_components_CollapsibleInfoCard__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, {
        cardInfo: infoTab
      }, infoTab.name);
    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
      message: "C\\xE2y n\\xE0y ch\\u01B0a c\\xF3 th\\xF4ng tin g\\xEC",
      type: "info"
    })
  });
};
/* harmony default export */ __webpack_exports__["default"] = (GeneralInfo);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///1151
`)}}]);
