"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1403],{78164:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   S: function() { return /* binding */ ErrorBoundary; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(15671);
/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(43144);
/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(97326);
/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(32531);
/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29388);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(4942);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(29905);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);









// eslint-disable-next-line @typescript-eslint/ban-types

var ErrorBoundary = /*#__PURE__*/function (_React$Component) {
  (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(ErrorBoundary, _React$Component);
  var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)(ErrorBoundary);
  function ErrorBoundary() {
    var _this;
    (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)(this, ErrorBoundary);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _super.call.apply(_super, [this].concat(args));
    (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(_this), "state", {
      hasError: false,
      errorInfo: ''
    });
    return _this;
  }
  (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)(ErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      // You can also log the error to an error reporting service
      // eslint-disable-next-line no-console
      console.log(error, errorInfo);
    }
  }, {
    key: "render",
    value: function render() {
      if (this.state.hasError) {
        // You can render any custom fallback UI
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .ZP, {
          status: "error",
          title: "Something went wrong.",
          extra: this.state.errorInfo
        });
      }
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      return {
        hasError: true,
        errorInfo: error.message
      };
    }
  }]);
  return ErrorBoundary;
}(react__WEBPACK_IMPORTED_MODULE_0__.Component);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///78164
`)},85265:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ drawer; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@rc-component/portal/es/index.js + 6 modules
var es = __webpack_require__(2788);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(8410);
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/context.js

var DrawerContext = /*#__PURE__*/react.createContext(null);
var RefContext = /*#__PURE__*/react.createContext({});
/* harmony default export */ var context = (DrawerContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(82225);
// EXTERNAL MODULE: ./node_modules/rc-util/es/KeyCode.js
var KeyCode = __webpack_require__(15105);
// EXTERNAL MODULE: ./node_modules/rc-util/es/pickAttrs.js
var pickAttrs = __webpack_require__(64217);
// EXTERNAL MODULE: ./node_modules/rc-util/es/index.js
var rc_util_es = __webpack_require__(56790);
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/DrawerPanel.js






var DrawerPanel = function DrawerPanel(props) {
  var prefixCls = props.prefixCls,
    className = props.className,
    style = props.style,
    children = props.children,
    containerRef = props.containerRef,
    id = props.id,
    onMouseEnter = props.onMouseEnter,
    onMouseOver = props.onMouseOver,
    onMouseLeave = props.onMouseLeave,
    onClick = props.onClick,
    onKeyDown = props.onKeyDown,
    onKeyUp = props.onKeyUp;
  var eventHandlers = {
    onMouseEnter: onMouseEnter,
    onMouseOver: onMouseOver,
    onMouseLeave: onMouseLeave,
    onClick: onClick,
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp
  };
  var _React$useContext = react.useContext(RefContext),
    panelRef = _React$useContext.panel;
  var mergedRef = (0,rc_util_es/* useComposeRef */.x1)(panelRef, containerRef);

  // =============================== Render ===============================

  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
    id: id,
    className: classnames_default()("".concat(prefixCls, "-content"), className),
    style: (0,objectSpread2/* default */.Z)({}, style),
    "aria-modal": "true",
    role: "dialog",
    ref: mergedRef
  }, eventHandlers), children));
};
if (false) {}
/* harmony default export */ var es_DrawerPanel = (DrawerPanel);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var es_warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/util.js


function parseWidthHeight(value) {
  if (typeof value === 'string' && String(Number(value)) === value) {
    (0,es_warning/* default */.ZP)(false, 'Invalid value type of \`width\` or \`height\` which should be number type instead.');
    return Number(value);
  }
  return value;
}
function warnCheck(props) {
  warning(!('wrapperClassName' in props), "'wrapperClassName' is removed. Please use 'rootClassName' instead.");
  warning(canUseDom() || !props.open, "Drawer with 'open' in SSR is not work since no place to createPortal. Please move to 'useEffect' instead.");
}
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/DrawerPopup.js












var sentinelStyle = {
  width: 0,
  height: 0,
  overflow: 'hidden',
  outline: 'none',
  position: 'absolute'
};
function DrawerPopup(props, ref) {
  var _ref, _pushConfig$distance, _pushConfig;
  var prefixCls = props.prefixCls,
    open = props.open,
    placement = props.placement,
    inline = props.inline,
    push = props.push,
    forceRender = props.forceRender,
    autoFocus = props.autoFocus,
    keyboard = props.keyboard,
    drawerClassNames = props.classNames,
    rootClassName = props.rootClassName,
    rootStyle = props.rootStyle,
    zIndex = props.zIndex,
    className = props.className,
    id = props.id,
    style = props.style,
    motion = props.motion,
    width = props.width,
    height = props.height,
    children = props.children,
    mask = props.mask,
    maskClosable = props.maskClosable,
    maskMotion = props.maskMotion,
    maskClassName = props.maskClassName,
    maskStyle = props.maskStyle,
    afterOpenChange = props.afterOpenChange,
    onClose = props.onClose,
    onMouseEnter = props.onMouseEnter,
    onMouseOver = props.onMouseOver,
    onMouseLeave = props.onMouseLeave,
    onClick = props.onClick,
    onKeyDown = props.onKeyDown,
    onKeyUp = props.onKeyUp,
    styles = props.styles;

  // ================================ Refs ================================
  var panelRef = react.useRef();
  var sentinelStartRef = react.useRef();
  var sentinelEndRef = react.useRef();
  react.useImperativeHandle(ref, function () {
    return panelRef.current;
  });
  var onPanelKeyDown = function onPanelKeyDown(event) {
    var keyCode = event.keyCode,
      shiftKey = event.shiftKey;
    switch (keyCode) {
      // Tab active
      case KeyCode/* default */.Z.TAB:
        {
          if (keyCode === KeyCode/* default */.Z.TAB) {
            if (!shiftKey && document.activeElement === sentinelEndRef.current) {
              var _sentinelStartRef$cur;
              (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 || _sentinelStartRef$cur.focus({
                preventScroll: true
              });
            } else if (shiftKey && document.activeElement === sentinelStartRef.current) {
              var _sentinelEndRef$curre;
              (_sentinelEndRef$curre = sentinelEndRef.current) === null || _sentinelEndRef$curre === void 0 || _sentinelEndRef$curre.focus({
                preventScroll: true
              });
            }
          }
          break;
        }

      // Close
      case KeyCode/* default */.Z.ESC:
        {
          if (onClose && keyboard) {
            event.stopPropagation();
            onClose(event);
          }
          break;
        }
    }
  };

  // ========================== Control ===========================
  // Auto Focus
  react.useEffect(function () {
    if (open && autoFocus) {
      var _panelRef$current;
      (_panelRef$current = panelRef.current) === null || _panelRef$current === void 0 || _panelRef$current.focus({
        preventScroll: true
      });
    }
  }, [open]);

  // ============================ Push ============================
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    pushed = _React$useState2[0],
    setPushed = _React$useState2[1];
  var parentContext = react.useContext(context);

  // Merge push distance
  var pushConfig;
  if (push === false) {
    pushConfig = {
      distance: 0
    };
  } else if (push === true) {
    pushConfig = {};
  } else {
    pushConfig = push || {};
  }
  var pushDistance = (_ref = (_pushConfig$distance = (_pushConfig = pushConfig) === null || _pushConfig === void 0 ? void 0 : _pushConfig.distance) !== null && _pushConfig$distance !== void 0 ? _pushConfig$distance : parentContext === null || parentContext === void 0 ? void 0 : parentContext.pushDistance) !== null && _ref !== void 0 ? _ref : 180;
  var mergedContext = react.useMemo(function () {
    return {
      pushDistance: pushDistance,
      push: function push() {
        setPushed(true);
      },
      pull: function pull() {
        setPushed(false);
      }
    };
  }, [pushDistance]);

  // ========================= ScrollLock =========================
  // Tell parent to push
  react.useEffect(function () {
    if (open) {
      var _parentContext$push;
      parentContext === null || parentContext === void 0 || (_parentContext$push = parentContext.push) === null || _parentContext$push === void 0 || _parentContext$push.call(parentContext);
    } else {
      var _parentContext$pull;
      parentContext === null || parentContext === void 0 || (_parentContext$pull = parentContext.pull) === null || _parentContext$pull === void 0 || _parentContext$pull.call(parentContext);
    }
  }, [open]);

  // Clean up
  react.useEffect(function () {
    return function () {
      var _parentContext$pull2;
      parentContext === null || parentContext === void 0 || (_parentContext$pull2 = parentContext.pull) === null || _parentContext$pull2 === void 0 || _parentContext$pull2.call(parentContext);
    };
  }, []);

  // ============================ Mask ============================
  var maskNode = mask && /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, (0,esm_extends/* default */.Z)({
    key: "mask"
  }, maskMotion, {
    visible: open
  }), function (_ref2, maskRef) {
    var motionMaskClassName = _ref2.className,
      motionMaskStyle = _ref2.style;
    return /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-mask"), motionMaskClassName, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.mask, maskClassName),
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, motionMaskStyle), maskStyle), styles === null || styles === void 0 ? void 0 : styles.mask),
      onClick: maskClosable && open ? onClose : undefined,
      ref: maskRef
    });
  });

  // =========================== Panel ============================
  var motionProps = typeof motion === 'function' ? motion(placement) : motion;
  var wrapperStyle = {};
  if (pushed && pushDistance) {
    switch (placement) {
      case 'top':
        wrapperStyle.transform = "translateY(".concat(pushDistance, "px)");
        break;
      case 'bottom':
        wrapperStyle.transform = "translateY(".concat(-pushDistance, "px)");
        break;
      case 'left':
        wrapperStyle.transform = "translateX(".concat(pushDistance, "px)");
        break;
      default:
        wrapperStyle.transform = "translateX(".concat(-pushDistance, "px)");
        break;
    }
  }
  if (placement === 'left' || placement === 'right') {
    wrapperStyle.width = parseWidthHeight(width);
  } else {
    wrapperStyle.height = parseWidthHeight(height);
  }
  var eventHandlers = {
    onMouseEnter: onMouseEnter,
    onMouseOver: onMouseOver,
    onMouseLeave: onMouseLeave,
    onClick: onClick,
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp
  };
  var panelNode = /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, (0,esm_extends/* default */.Z)({
    key: "panel"
  }, motionProps, {
    visible: open,
    forceRender: forceRender,
    onVisibleChanged: function onVisibleChanged(nextVisible) {
      afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);
    },
    removeOnLeave: false,
    leavedClassName: "".concat(prefixCls, "-content-wrapper-hidden")
  }), function (_ref3, motionRef) {
    var motionClassName = _ref3.className,
      motionStyle = _ref3.style;
    return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
      className: classnames_default()("".concat(prefixCls, "-content-wrapper"), drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.wrapper, motionClassName),
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, wrapperStyle), motionStyle), styles === null || styles === void 0 ? void 0 : styles.wrapper)
    }, (0,pickAttrs/* default */.Z)(props, {
      data: true
    })), /*#__PURE__*/react.createElement(es_DrawerPanel, (0,esm_extends/* default */.Z)({
      id: id,
      containerRef: motionRef,
      prefixCls: prefixCls,
      className: classnames_default()(className, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.content),
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, style), styles === null || styles === void 0 ? void 0 : styles.content)
    }, eventHandlers), children));
  });

  // =========================== Render ===========================
  var containerStyle = (0,objectSpread2/* default */.Z)({}, rootStyle);
  if (zIndex) {
    containerStyle.zIndex = zIndex;
  }
  return /*#__PURE__*/react.createElement(context.Provider, {
    value: mergedContext
  }, /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(prefixCls, "".concat(prefixCls, "-").concat(placement), rootClassName, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-open"), open), "".concat(prefixCls, "-inline"), inline)),
    style: containerStyle,
    tabIndex: -1,
    ref: panelRef,
    onKeyDown: onPanelKeyDown
  }, maskNode, /*#__PURE__*/react.createElement("div", {
    tabIndex: 0,
    ref: sentinelStartRef,
    style: sentinelStyle,
    "aria-hidden": "true",
    "data-sentinel": "start"
  }), panelNode, /*#__PURE__*/react.createElement("div", {
    tabIndex: 0,
    ref: sentinelEndRef,
    style: sentinelStyle,
    "aria-hidden": "true",
    "data-sentinel": "end"
  })));
}
var RefDrawerPopup = /*#__PURE__*/react.forwardRef(DrawerPopup);
if (false) {}
/* harmony default export */ var es_DrawerPopup = (RefDrawerPopup);
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/Drawer.js








var Drawer = function Drawer(props) {
  var _props$open = props.open,
    open = _props$open === void 0 ? false : _props$open,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-drawer' : _props$prefixCls,
    _props$placement = props.placement,
    placement = _props$placement === void 0 ? 'right' : _props$placement,
    _props$autoFocus = props.autoFocus,
    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,
    _props$keyboard = props.keyboard,
    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,
    _props$width = props.width,
    width = _props$width === void 0 ? 378 : _props$width,
    _props$mask = props.mask,
    mask = _props$mask === void 0 ? true : _props$mask,
    _props$maskClosable = props.maskClosable,
    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,
    getContainer = props.getContainer,
    forceRender = props.forceRender,
    afterOpenChange = props.afterOpenChange,
    destroyOnClose = props.destroyOnClose,
    onMouseEnter = props.onMouseEnter,
    onMouseOver = props.onMouseOver,
    onMouseLeave = props.onMouseLeave,
    onClick = props.onClick,
    onKeyDown = props.onKeyDown,
    onKeyUp = props.onKeyUp,
    panelRef = props.panelRef;
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    animatedVisible = _React$useState2[0],
    setAnimatedVisible = _React$useState2[1];

  // ============================= Warn =============================
  if (false) {}

  // ============================= Open =============================
  var _React$useState3 = react.useState(false),
    _React$useState4 = (0,slicedToArray/* default */.Z)(_React$useState3, 2),
    mounted = _React$useState4[0],
    setMounted = _React$useState4[1];
  (0,useLayoutEffect/* default */.Z)(function () {
    setMounted(true);
  }, []);
  var mergedOpen = mounted ? open : false;

  // ============================ Focus =============================
  var popupRef = react.useRef();
  var lastActiveRef = react.useRef();
  (0,useLayoutEffect/* default */.Z)(function () {
    if (mergedOpen) {
      lastActiveRef.current = document.activeElement;
    }
  }, [mergedOpen]);

  // ============================= Open =============================
  var internalAfterOpenChange = function internalAfterOpenChange(nextVisible) {
    var _popupRef$current;
    setAnimatedVisible(nextVisible);
    afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(nextVisible);
    if (!nextVisible && lastActiveRef.current && !((_popupRef$current = popupRef.current) !== null && _popupRef$current !== void 0 && _popupRef$current.contains(lastActiveRef.current))) {
      var _lastActiveRef$curren;
      (_lastActiveRef$curren = lastActiveRef.current) === null || _lastActiveRef$curren === void 0 || _lastActiveRef$curren.focus({
        preventScroll: true
      });
    }
  };

  // =========================== Context ============================
  var refContext = react.useMemo(function () {
    return {
      panel: panelRef
    };
  }, [panelRef]);

  // ============================ Render ============================
  if (!forceRender && !animatedVisible && !mergedOpen && destroyOnClose) {
    return null;
  }
  var eventHandlers = {
    onMouseEnter: onMouseEnter,
    onMouseOver: onMouseOver,
    onMouseLeave: onMouseLeave,
    onClick: onClick,
    onKeyDown: onKeyDown,
    onKeyUp: onKeyUp
  };
  var drawerPopupProps = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
    open: mergedOpen,
    prefixCls: prefixCls,
    placement: placement,
    autoFocus: autoFocus,
    keyboard: keyboard,
    width: width,
    mask: mask,
    maskClosable: maskClosable,
    inline: getContainer === false,
    afterOpenChange: internalAfterOpenChange,
    ref: popupRef
  }, eventHandlers);
  return /*#__PURE__*/react.createElement(RefContext.Provider, {
    value: refContext
  }, /*#__PURE__*/react.createElement(es/* default */.Z, {
    open: mergedOpen || forceRender || animatedVisible,
    autoDestroy: false,
    getContainer: getContainer,
    autoLock: mask && (mergedOpen || animatedVisible)
  }, /*#__PURE__*/react.createElement(es_DrawerPopup, drawerPopupProps)));
};
if (false) {}
/* harmony default export */ var es_Drawer = (Drawer);
;// CONCATENATED MODULE: ./node_modules/rc-drawer/es/index.js
// export this package's api

/* harmony default export */ var rc_drawer_es = (es_Drawer);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/hooks/useZIndex.js
var useZIndex = __webpack_require__(87263);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var motion = __webpack_require__(33603);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/zindexContext.js
var zindexContext = __webpack_require__(43945);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var config_provider_context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/form/context.js
var form_context = __webpack_require__(65223);
// EXTERNAL MODULE: ./node_modules/antd/es/space/Compact.js
var Compact = __webpack_require__(4173);
// EXTERNAL MODULE: ./node_modules/antd/es/watermark/context.js
var watermark_context = __webpack_require__(16569);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/hooks/useClosable.js
var useClosable = __webpack_require__(69760);
;// CONCATENATED MODULE: ./node_modules/antd/es/drawer/DrawerPanel.js
"use client";





const DrawerPanel_DrawerPanel = props => {
  var _a, _b;
  const {
    prefixCls,
    title,
    footer,
    extra,
    closeIcon,
    closable,
    onClose,
    headerStyle,
    bodyStyle,
    footerStyle,
    children,
    classNames: drawerClassNames,
    styles: drawerStyles
  } = props;
  const {
    drawer: drawerContext
  } = react.useContext(config_provider_context/* ConfigContext */.E_);
  const customCloseIconRender = react.useCallback(icon => ( /*#__PURE__*/react.createElement("button", {
    type: "button",
    onClick: onClose,
    "aria-label": "Close",
    className: \`\${prefixCls}-close\`
  }, icon)), [onClose]);
  const [mergedClosable, mergedCloseIcon] = (0,useClosable/* default */.Z)(closable, typeof closeIcon !== 'undefined' ? closeIcon : drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.closeIcon, customCloseIconRender, undefined, true);
  const headerNode = react.useMemo(() => {
    var _a, _b;
    if (!title && !mergedClosable) {
      return null;
    }
    return /*#__PURE__*/react.createElement("div", {
      style: Object.assign(Object.assign(Object.assign({}, (_a = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.styles) === null || _a === void 0 ? void 0 : _a.header), headerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.header),
      className: classnames_default()(\`\${prefixCls}-header\`, {
        [\`\${prefixCls}-header-close-only\`]: mergedClosable && !title && !extra
      }, (_b = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.classNames) === null || _b === void 0 ? void 0 : _b.header, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.header)
    }, /*#__PURE__*/react.createElement("div", {
      className: \`\${prefixCls}-header-title\`
    }, mergedCloseIcon, title && /*#__PURE__*/react.createElement("div", {
      className: \`\${prefixCls}-title\`
    }, title)), extra && /*#__PURE__*/react.createElement("div", {
      className: \`\${prefixCls}-extra\`
    }, extra));
  }, [mergedClosable, mergedCloseIcon, extra, headerStyle, prefixCls, title]);
  const footerNode = react.useMemo(() => {
    var _a, _b;
    if (!footer) {
      return null;
    }
    const footerClassName = \`\${prefixCls}-footer\`;
    return /*#__PURE__*/react.createElement("div", {
      className: classnames_default()(footerClassName, (_a = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.footer, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.footer),
      style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.styles) === null || _b === void 0 ? void 0 : _b.footer), footerStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.footer)
    }, footer);
  }, [footer, footerStyle, prefixCls]);
  return /*#__PURE__*/react.createElement(react.Fragment, null, headerNode, /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(\`\${prefixCls}-body\`, drawerClassNames === null || drawerClassNames === void 0 ? void 0 : drawerClassNames.body, (_a = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.classNames) === null || _a === void 0 ? void 0 : _a.body),
    style: Object.assign(Object.assign(Object.assign({}, (_b = drawerContext === null || drawerContext === void 0 ? void 0 : drawerContext.styles) === null || _b === void 0 ? void 0 : _b.body), bodyStyle), drawerStyles === null || drawerStyles === void 0 ? void 0 : drawerStyles.body)
  }, children), footerNode);
};
/* harmony default export */ var drawer_DrawerPanel = (DrawerPanel_DrawerPanel);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/statistic.js
var statistic = __webpack_require__(45503);
;// CONCATENATED MODULE: ./node_modules/antd/es/drawer/style/motion.js
const getMoveTranslate = direction => {
  const value = '100%';
  return {
    left: \`translateX(-\${value})\`,
    right: \`translateX(\${value})\`,
    top: \`translateY(-\${value})\`,
    bottom: \`translateY(\${value})\`
  }[direction];
};
const getEnterLeaveStyle = (startStyle, endStyle) => ({
  '&-enter, &-appear': Object.assign(Object.assign({}, startStyle), {
    '&-active': endStyle
  }),
  '&-leave': Object.assign(Object.assign({}, endStyle), {
    '&-active': startStyle
  })
});
const getFadeStyle = (from, duration) => Object.assign({
  '&-enter, &-appear, &-leave': {
    '&-start': {
      transition: 'none'
    },
    '&-active': {
      transition: \`all \${duration}\`
    }
  }
}, getEnterLeaveStyle({
  opacity: from
}, {
  opacity: 1
}));
const getPanelMotionStyles = (direction, duration) => [getFadeStyle(0.7, duration), getEnterLeaveStyle({
  transform: getMoveTranslate(direction)
}, {
  transform: 'none'
})];
const genMotionStyle = token => {
  const {
    componentCls,
    motionDurationSlow
  } = token;
  return {
    [componentCls]: {
      // ======================== Mask ========================
      [\`\${componentCls}-mask-motion\`]: getFadeStyle(0, motionDurationSlow),
      // ======================= Panel ========================
      [\`\${componentCls}-panel-motion\`]: ['left', 'right', 'top', 'bottom'].reduce((obj, direction) => Object.assign(Object.assign({}, obj), {
        [\`&-\${direction}\`]: getPanelMotionStyles(direction, motionDurationSlow)
      }), {})
    }
  };
};
/* harmony default export */ var style_motion = (genMotionStyle);
;// CONCATENATED MODULE: ./node_modules/antd/es/drawer/style/index.js



// =============================== Base ===============================
const genDrawerStyle = token => {
  const {
    componentCls,
    zIndexPopup,
    colorBgMask,
    colorBgElevated,
    motionDurationSlow,
    motionDurationMid,
    padding,
    paddingLG,
    fontSizeLG,
    lineHeightLG,
    lineWidth,
    lineType,
    colorSplit,
    marginSM,
    colorIcon,
    colorIconHover,
    colorText,
    fontWeightStrong,
    footerPaddingBlock,
    footerPaddingInline
  } = token;
  const wrapperCls = \`\${componentCls}-content-wrapper\`;
  return {
    [componentCls]: {
      position: 'fixed',
      inset: 0,
      zIndex: zIndexPopup,
      pointerEvents: 'none',
      '&-pure': {
        position: 'relative',
        background: colorBgElevated,
        display: 'flex',
        flexDirection: 'column',
        [\`&\${componentCls}-left\`]: {
          boxShadow: token.boxShadowDrawerLeft
        },
        [\`&\${componentCls}-right\`]: {
          boxShadow: token.boxShadowDrawerRight
        },
        [\`&\${componentCls}-top\`]: {
          boxShadow: token.boxShadowDrawerUp
        },
        [\`&\${componentCls}-bottom\`]: {
          boxShadow: token.boxShadowDrawerDown
        }
      },
      '&-inline': {
        position: 'absolute'
      },
      // ====================== Mask ======================
      [\`\${componentCls}-mask\`]: {
        position: 'absolute',
        inset: 0,
        zIndex: zIndexPopup,
        background: colorBgMask,
        pointerEvents: 'auto'
      },
      // ==================== Content =====================
      [wrapperCls]: {
        position: 'absolute',
        zIndex: zIndexPopup,
        maxWidth: '100vw',
        transition: \`all \${motionDurationSlow}\`,
        '&-hidden': {
          display: 'none'
        }
      },
      // Placement
      [\`&-left > \${wrapperCls}\`]: {
        top: 0,
        bottom: 0,
        left: {
          _skip_check_: true,
          value: 0
        },
        boxShadow: token.boxShadowDrawerLeft
      },
      [\`&-right > \${wrapperCls}\`]: {
        top: 0,
        right: {
          _skip_check_: true,
          value: 0
        },
        bottom: 0,
        boxShadow: token.boxShadowDrawerRight
      },
      [\`&-top > \${wrapperCls}\`]: {
        top: 0,
        insetInline: 0,
        boxShadow: token.boxShadowDrawerUp
      },
      [\`&-bottom > \${wrapperCls}\`]: {
        bottom: 0,
        insetInline: 0,
        boxShadow: token.boxShadowDrawerDown
      },
      [\`\${componentCls}-content\`]: {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        overflow: 'auto',
        background: colorBgElevated,
        pointerEvents: 'auto'
      },
      // Header
      [\`\${componentCls}-header\`]: {
        display: 'flex',
        flex: 0,
        alignItems: 'center',
        padding: \`\${(0,cssinjs_es/* unit */.bf)(padding)} \${(0,cssinjs_es/* unit */.bf)(paddingLG)}\`,
        fontSize: fontSizeLG,
        lineHeight: lineHeightLG,
        borderBottom: \`\${(0,cssinjs_es/* unit */.bf)(lineWidth)} \${lineType} \${colorSplit}\`,
        '&-title': {
          display: 'flex',
          flex: 1,
          alignItems: 'center',
          minWidth: 0,
          minHeight: 0
        }
      },
      [\`\${componentCls}-extra\`]: {
        flex: 'none'
      },
      [\`\${componentCls}-close\`]: {
        display: 'inline-block',
        marginInlineEnd: marginSM,
        color: colorIcon,
        fontWeight: fontWeightStrong,
        fontSize: fontSizeLG,
        fontStyle: 'normal',
        lineHeight: 1,
        textAlign: 'center',
        textTransform: 'none',
        textDecoration: 'none',
        background: 'transparent',
        border: 0,
        outline: 0,
        cursor: 'pointer',
        transition: \`color \${motionDurationMid}\`,
        textRendering: 'auto',
        '&:focus, &:hover': {
          color: colorIconHover,
          textDecoration: 'none'
        }
      },
      [\`\${componentCls}-title\`]: {
        flex: 1,
        margin: 0,
        color: colorText,
        fontWeight: token.fontWeightStrong,
        fontSize: fontSizeLG,
        lineHeight: lineHeightLG
      },
      // Body
      [\`\${componentCls}-body\`]: {
        flex: 1,
        minWidth: 0,
        minHeight: 0,
        padding: paddingLG,
        overflow: 'auto'
      },
      // Footer
      [\`\${componentCls}-footer\`]: {
        flexShrink: 0,
        padding: \`\${(0,cssinjs_es/* unit */.bf)(footerPaddingBlock)} \${(0,cssinjs_es/* unit */.bf)(footerPaddingInline)}\`,
        borderTop: \`\${(0,cssinjs_es/* unit */.bf)(lineWidth)} \${lineType} \${colorSplit}\`
      },
      // ====================== RTL =======================
      '&-rtl': {
        direction: 'rtl'
      }
    }
  };
};
const prepareComponentToken = token => ({
  zIndexPopup: token.zIndexPopupBase,
  footerPaddingBlock: token.paddingXS,
  footerPaddingInline: token.padding
});
// ============================== Export ==============================
/* harmony default export */ var drawer_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('Drawer', token => {
  const drawerToken = (0,statistic/* merge */.TS)(token, {});
  return [genDrawerStyle(drawerToken), style_motion(drawerToken)];
}, prepareComponentToken));
;// CONCATENATED MODULE: ./node_modules/antd/es/drawer/index.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};













const SizeTypes = (/* unused pure expression or super */ null && (['default', 'large']));
const defaultPushState = {
  distance: 180
};
const drawer_Drawer = props => {
  var _a;
  const {
      rootClassName,
      width,
      height,
      size = 'default',
      mask = true,
      push = defaultPushState,
      open,
      afterOpenChange,
      onClose,
      prefixCls: customizePrefixCls,
      getContainer: customizeGetContainer,
      style,
      className,
      // Deprecated
      visible,
      afterVisibleChange,
      maskStyle,
      drawerStyle,
      contentWrapperStyle
    } = props,
    rest = __rest(props, ["rootClassName", "width", "height", "size", "mask", "push", "open", "afterOpenChange", "onClose", "prefixCls", "getContainer", "style", "className", "visible", "afterVisibleChange", "maskStyle", "drawerStyle", "contentWrapperStyle"]);
  const {
    getPopupContainer,
    getPrefixCls,
    direction,
    drawer
  } = react.useContext(config_provider_context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('drawer', customizePrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = drawer_style(prefixCls);
  const getContainer =
  // \u6709\u53EF\u80FD\u4E3A false\uFF0C\u6240\u4EE5\u4E0D\u80FD\u76F4\u63A5\u5224\u65AD
  customizeGetContainer === undefined && getPopupContainer ? () => getPopupContainer(document.body) : customizeGetContainer;
  const drawerClassName = classnames_default()({
    'no-mask': !mask,
    [\`\${prefixCls}-rtl\`]: direction === 'rtl'
  }, rootClassName, hashId, cssVarCls);
  // ========================== Warning ===========================
  if (false) {}
  // ============================ Size ============================
  const mergedWidth = react.useMemo(() => width !== null && width !== void 0 ? width : size === 'large' ? 736 : 378, [width, size]);
  const mergedHeight = react.useMemo(() => height !== null && height !== void 0 ? height : size === 'large' ? 736 : 378, [height, size]);
  // =========================== Motion ===========================
  const maskMotion = {
    motionName: (0,motion/* getTransitionName */.m)(prefixCls, 'mask-motion'),
    motionAppear: true,
    motionEnter: true,
    motionLeave: true,
    motionDeadline: 500
  };
  const panelMotion = motionPlacement => ({
    motionName: (0,motion/* getTransitionName */.m)(prefixCls, \`panel-motion-\${motionPlacement}\`),
    motionAppear: true,
    motionEnter: true,
    motionLeave: true,
    motionDeadline: 500
  });
  // ============================ Refs ============================
  // Select \`ant-modal-content\` by \`panelRef\`
  const panelRef = (0,watermark_context/* usePanelRef */.H)();
  // ============================ zIndex ============================
  const [zIndex, contextZIndex] = (0,useZIndex/* useZIndex */.Cn)('Drawer', rest.zIndex);
  // =========================== Render ===========================
  const {
    classNames: propClassNames = {},
    styles: propStyles = {}
  } = rest;
  const {
    classNames: contextClassNames = {},
    styles: contextStyles = {}
  } = drawer || {};
  return wrapCSSVar( /*#__PURE__*/react.createElement(Compact/* NoCompactStyle */.BR, null, /*#__PURE__*/react.createElement(form_context/* NoFormStyle */.Ux, {
    status: true,
    override: true
  }, /*#__PURE__*/react.createElement(zindexContext/* default */.Z.Provider, {
    value: contextZIndex
  }, /*#__PURE__*/react.createElement(rc_drawer_es, Object.assign({
    prefixCls: prefixCls,
    onClose: onClose,
    maskMotion: maskMotion,
    motion: panelMotion
  }, rest, {
    classNames: {
      mask: classnames_default()(propClassNames.mask, contextClassNames.mask),
      content: classnames_default()(propClassNames.content, contextClassNames.content)
    },
    styles: {
      mask: Object.assign(Object.assign(Object.assign({}, propStyles.mask), maskStyle), contextStyles.mask),
      content: Object.assign(Object.assign(Object.assign({}, propStyles.content), drawerStyle), contextStyles.content),
      wrapper: Object.assign(Object.assign(Object.assign({}, propStyles.wrapper), contentWrapperStyle), contextStyles.wrapper)
    },
    open: open !== null && open !== void 0 ? open : visible,
    mask: mask,
    push: push,
    width: mergedWidth,
    height: mergedHeight,
    style: Object.assign(Object.assign({}, drawer === null || drawer === void 0 ? void 0 : drawer.style), style),
    className: classnames_default()(drawer === null || drawer === void 0 ? void 0 : drawer.className, className),
    rootClassName: drawerClassName,
    getContainer: getContainer,
    afterOpenChange: afterOpenChange !== null && afterOpenChange !== void 0 ? afterOpenChange : afterVisibleChange,
    panelRef: panelRef,
    zIndex: zIndex
  }), /*#__PURE__*/react.createElement(drawer_DrawerPanel, Object.assign({
    prefixCls: prefixCls
  }, rest, {
    onClose: onClose
  })))))));
};
/** @private Internal Component. Do not use in your production. */
const PurePanel = props => {
  const {
      prefixCls: customizePrefixCls,
      style,
      className,
      placement = 'right'
    } = props,
    restProps = __rest(props, ["prefixCls", "style", "className", "placement"]);
  const {
    getPrefixCls
  } = react.useContext(config_provider_context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('drawer', customizePrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = drawer_style(prefixCls);
  const cls = classnames_default()(prefixCls, \`\${prefixCls}-pure\`, \`\${prefixCls}-\${placement}\`, hashId, cssVarCls, className);
  return wrapCSSVar( /*#__PURE__*/react.createElement("div", {
    className: cls,
    style: style
  }, /*#__PURE__*/react.createElement(drawer_DrawerPanel, Object.assign({
    prefixCls: prefixCls
  }, restProps))));
};
drawer_Drawer._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;
if (false) {}
/* harmony default export */ var drawer = (drawer_Drawer);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85265
`)}}]);
