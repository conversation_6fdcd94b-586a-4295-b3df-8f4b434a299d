"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1454],{31454:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ AllZone; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js
var ReloadOutlined = __webpack_require__(43471);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-list/es/index.js + 20 modules
var es = __webpack_require__(64176);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/SeasonalManagement/components/GeneralSeasonalCard.tsx
var GeneralSeasonalCard = __webpack_require__(80570);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/Zone/AllZone/ListCropInZone.tsx









var ListCropInZone = function ListCropInZone(_ref) {
  var zoneId = _ref.zoneId,
    projectId = _ref.projectId,
    title = _ref.title;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var actionRef = (0,react.useRef)(null);
  var queryParams = (0,react.useMemo)(function () {
    return {
      zone_id: zoneId
    };
  }, [zoneId]);
  var _useRequest = (0,_umi_production_exports.useRequest)(function () {
      return (0,cropManager/* getCropManagementInfoList */.Gz)(queryParams);
    }, {
      onSuccess: function onSuccess(responseData) {
        // if (responseData) {
        //   setCropList(responseData);
        //   setFirstLoad(false);
        // }
      },
      onError: function onError() {
        // setFirstLoad(false);
      },
      manual: true
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    run = _useRequest.run;
  var handleReload = (0,react.useCallback)(function () {
    // actionRef.current?.reload();
    run();
  }, []);
  (0,react.useEffect)(function () {
    handleReload();
  }, [queryParams]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: title,
    bodyStyle: {
      paddingInline: 0
    },
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ReloadOutlined/* default */.Z, {}),
      onClick: handleReload
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es/* ProList */.Rs, {
      loading: loading,
      actionRef: actionRef,
      rowKey: 'name',
      renderItem: function renderItem(item) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(GeneralSeasonalCard/* default */.Z, objectSpread2_default()({}, item))
        });
      },
      grid: {
        gutter: 20,
        xs: 2,
        md: 2,
        lg: 2,
        xl: 3,
        xxl: 3,
        column: 2
      },
      dataSource: data
      // request={async (params) => {
      //   try {
      //     const filters: Array<any[]> = [];
      //     if (zoneId) {
      //       filters.push([DOCTYPE_ERP.iotDevice, 'zone_id', '=', zoneId]);
      //     }
      //     const res = await getCrop({
      //       filters: filters,
      //       page: params.current,
      //       size: params.pageSize,
      //       fields: ['*'],
      //       order_by: 'creation',
      //       project_id: projectId,
      //     });

      //     // const data = res.data.map(formatRender);
      //     return {
      //       data: res,
      //       total: res.length,
      //     };
      //   } catch (error) {
      //     message.error(
      //       formatMessage({
      //         id: 'common.error',
      //       }),
      //     );
      //     return {
      //       data: [],
      //     };
      //   }
      // }}
      ,
      pagination: {
        pageSize: 20
      }
    })
  });
};
/* harmony default export */ var AllZone_ListCropInZone = (ListCropInZone);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js
var objectWithoutProperties = __webpack_require__(13769);
var objectWithoutProperties_default = /*#__PURE__*/__webpack_require__.n(objectWithoutProperties);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/common/contanst/img.ts
var img = __webpack_require__(13490);
// EXTERNAL MODULE: ./src/pages/Zone/AddNewZone.tsx
var AddNewZone = __webpack_require__(98622);
// EXTERNAL MODULE: ./src/pages/Zone/UpdateZone.tsx + 1 modules
var UpdateZone = __webpack_require__(28113);
// EXTERNAL MODULE: ./src/services/zones.ts
var zones = __webpack_require__(95728);
// EXTERNAL MODULE: ./src/utils/file.ts
var file = __webpack_require__(80320);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-card/es/components/CheckCard/index.js + 2 modules
var CheckCard = __webpack_require__(97321);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useDebounceFn/index.js
var useDebounceFn = __webpack_require__(85980);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var es_avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/antd/es/skeleton/index.js + 12 modules
var skeleton = __webpack_require__(99559);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/nanoid/index.browser.js
var index_browser = __webpack_require__(53416);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/Zone/AllZone/ListZone.tsx






var _excluded = ["avatar", "name", "title", "actions", "onClick"];

















var isAllKey = Symbol('all');
var allValue = (0,index_browser/* nanoid */.x0)();
var ZoneCard = function ZoneCard(_ref) {
  var avatar = _ref.avatar,
    name = _ref.name,
    title = _ref.title,
    actions = _ref.actions,
    _onClick = _ref.onClick,
    item = objectWithoutProperties_default()(_ref, _excluded);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useRequest = (0,_umi_production_exports.useRequest)( /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var res;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,cropManager/* getCropManagementInfoList */.Gz)({
              zone_id: name,
              page: 1,
              size: 1,
              fields: ['name']
            });
          case 2:
            res = _context.sent;
            return _context.abrupt("return", {
              data: res.pagination.totalElements
            });
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))),
    data = _useRequest.data,
    loading = _useRequest.loading,
    error = _useRequest.error;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(CheckCard/* default */.Z, {
    onClick: function onClick() {
      return _onClick(name);
    },
    style: {
      width: '100%',
      // height: '100%',
      minHeight: 160,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between'
    },
    title: title,
    avatar: /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
      src: avatar,
      size: "large"
    }),
    description: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        paddingTop: 10
      },
      children: loading ? /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z.Input, {}) : error ? /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: "error",
        children: formatMessage({
          id: 'common.error'
        })
      }) : /*#__PURE__*/(0,jsx_runtime.jsxs)(tag/* default */.Z, {
        color: "success",
        children: [formatMessage({
          id: 'common.number_of_crops_in_the_zone'
        }), ": ", data]
      })
    }),
    value: name,
    actions: actions
  });
};
var ListZone = function ListZone(_ref3) {
  var onItemClick = _ref3.onItemClick,
    projectId = _ref3.projectId;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    deleteConfirmVisible = _useState2[0],
    setDeleteConfirmVisible = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    deletingZone = _useState4[0],
    setDeletingZone = _useState4[1];
  var actionRef = (0,react.useRef)();
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
  };
  var showDeleteConfirm = function showDeleteConfirm(zone) {
    setDeletingZone(zone);
    setDeleteConfirmVisible(true);
  };
  var handleDeleteConfirm = function handleDeleteConfirm() {
    // Perform the delete action here (you might want to call an API or update state)
    // For now, let's just log a message
    console.log('Deleting zone:', deletingZone);

    // Close the delete confirmation modal
    setDeleteConfirmVisible(false);
  };
  var formatRender = (0,react.useCallback)(function (item
  // | {
  //     label: string;
  //     name: string;
  //     [isAllKey]: boolean;
  //   },
  ) {
    return {
      // ...item,
      name: item.name,
      avatar: item.image ? (0,file/* genDownloadUrl */.h)(item.image) : img/* DEFAULT_FALLBACK_IMG */.W,
      data: item,
      title: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        style: {
          whiteSpace: 'normal'
        },
        children: item.label
      }),
      // content: (
      //   <span
      //     style={{
      //       wordBreak: 'normal',
      //     }}
      //   >
      //     {(item as any)[isAllKey] ? 'L\u1ECDc theo t\u1EA5t c\u1EA3 khu v\u1EF1c' : \`L\u1ECDc theo khu v\u1EF1c \${item.label}\`}
      //   </span>
      // ),
      actions: item[isAllKey] ? [] : [/*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
        style: {
          display: 'flex',
          gap: 6,
          alignItems: 'center',
          justifyContent: 'space-around'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateZone/* default */.Z, {
          onSuccess: handleReload,
          modalProps: {
            zoneId: item.name
          }
        }, "edit")
      }, "action")
      // <>
      //   {canUpdateCategory && (
      //     <UpdateCategory
      //       key={'edit' + item.name}
      //       refreshFnc={handleReload}
      //       value={item}
      //     ></UpdateCategory>
      //   )}
      // </>,
      // <>
      //   {canDeleteCategory && (
      //     <DeleteCategory
      //       key={'remove' + item.name}
      //       refreshFnc={handleReload}
      //       value={item}
      //     />
      //   )}
      // </>,
      ]
    };
  }, []);
  (0,react.useEffect)(function () {
    handleReload();
  }, [projectId]);
  var keyWordsRef = (0,react.useRef)('');
  var _useDebounceFn = (0,useDebounceFn/* default */.Z)(function (e) {
      var value = e.target.value;
      keyWordsRef.current = value;
      handleReload();
    }, {
      wait: 400
    }),
    onSearch = _useDebounceFn.run;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bordered: true,
      style: {
        marginBottom: 20
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        justify: "space-between",
        gutter: 16,
        align: "middle",
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 8,
          flex: "1 0 25%",
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
            addonBefore: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: 'common.zone_name'
            }),
            onChange: onSearch
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 8,
          style: {
            textAlign: 'right'
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(AddNewZone/* default */.Z, {
              onSuccess: handleReload,
              modalProps: {
                defaultValue: {
                  project_id: projectId
                }
              }
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
              icon: /*#__PURE__*/(0,jsx_runtime.jsx)(ReloadOutlined/* default */.Z, {}),
              onClick: handleReload
            })]
          })
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
      bodyStyle: {
        paddingInline: 0
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(CheckCard/* default */.Z.Group, {
        style: {
          width: '100%'
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es/* ProList */.Rs, {
          actionRef: actionRef,
          tableAlertRender: function tableAlertRender(_ref4) {
            var _selectedRows$;
            var selectedRows = _ref4.selectedRows,
              onCleanSelected = _ref4.onCleanSelected;
            return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
              children: ["\\u0110\\xE3 ch\\u1ECDn ", (selectedRows === null || selectedRows === void 0 || (_selectedRows$ = selectedRows[0]) === null || _selectedRows$ === void 0 ? void 0 : _selectedRows$.title) || 'T\u1EA5t c\u1EA3 khu v\u1EF1c']
            });
          },
          itemCardProps: {
            ghost: true
          },
          showActions: "hover",
          tableAlertOptionRender: function tableAlertOptionRender() {
            return null;
          },
          pagination: {
            pageSize: 20
          },
          renderItem: function renderItem(item) {
            return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, {
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(ZoneCard, objectSpread2_default()({
                onClick: onItemClick
              }, item))
            });
          },
          rowSelection: {
            columnWidth: 0,
            onChange: function onChange(selectedRowKeys, selectedRows, info) {
              var activeRow = selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows[0];
              if (!activeRow || activeRow[isAllKey]) {
                onItemClick === null || onItemClick === void 0 || onItemClick();
              } else {
                var _activeRow$data;
                onItemClick === null || onItemClick === void 0 || onItemClick(activeRow === null || activeRow === void 0 || (_activeRow$data = activeRow.data) === null || _activeRow$data === void 0 ? void 0 : _activeRow$data.name);
              }
            },
            type: 'radio'
            // defaultSelectedRowKeys: [allValue],
          },
          request: ( /*#__PURE__*/function () {
            var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params) {
              var _pagination, filters, res, data;
              return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                while (1) switch (_context2.prev = _context2.next) {
                  case 0:
                    _context2.prev = 0;
                    filters = [];
                    if (projectId) {
                      filters.push([constanst/* DOCTYPE_ERP */.lH.iotZone, 'project_id', '=', projectId]);
                    }
                    if (keyWordsRef.current) {
                      filters.push([constanst/* DOCTYPE_ERP */.lH.iotZone, 'label', 'like', "%".concat(keyWordsRef.current, "%")]);
                    }
                    _context2.next = 6;
                    return (0,zones/* zoneList */.ly)({
                      filters: filters,
                      page: params.current,
                      size: params.pageSize,
                      fields: ['*'],
                      order_by: 'creation'
                    });
                  case 6:
                    res = _context2.sent;
                    data = toConsumableArray_default()(res.data).map(formatRender);
                    return _context2.abrupt("return", {
                      data: data,
                      total: res === null || res === void 0 || (_pagination = res.pagination) === null || _pagination === void 0 ? void 0 : _pagination.totalElements
                    });
                  case 11:
                    _context2.prev = 11;
                    _context2.t0 = _context2["catch"](0);
                    message/* default */.ZP.error('C\xF3 l\u1ED7i x\u1EA3y ra, vui l\xF2ng th\u1EED l\u1EA1i');
                    return _context2.abrupt("return", {
                      data: []
                    });
                  case 15:
                  case "end":
                    return _context2.stop();
                }
              }, _callee2, null, [[0, 11]]);
            }));
            return function (_x) {
              return _ref5.apply(this, arguments);
            };
          }()),
          grid: {
            gutter: 20,
            xs: 2,
            md: 2,
            lg: 2,
            xl: 3,
            xxl: 3,
            column: 2
          },
          metas: {
            title: {},
            subTitle: {},
            type: {},
            avatar: {
              render: function render(dom, entity, index, action, schema) {
                return /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
                  src: entity.avatar,
                  size: "large",
                  style: {
                    marginRight: 7
                  }
                });
              }
            },
            content: {},
            actions: {
              cardActionProps: 'actions'
            }
          },
          rowKey: 'name'
        })
      })
    })]
  });
};
/* harmony default export */ var AllZone_ListZone = (ListZone);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/Zone/AllZone/index.tsx









var ZoneInProject = function ZoneInProject(_ref) {
  var children = _ref.children,
    projectId = _ref.projectId;
  var _useState = (0,react.useState)(),
    _useState2 = slicedToArray_default()(_useState, 2),
    zoneId = _useState2[0],
    setZoneId = _useState2[1];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(AllZone_ListZone, {
      projectId: projectId,
      onItemClick: setZoneId
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), !!zoneId && /*#__PURE__*/(0,jsx_runtime.jsx)(AllZone_ListCropInZone, {
      title: formatMessage({
        id: 'common.crop_list'
      }),
      zoneId: zoneId,
      projectId: projectId
    })]
  });
};
/* harmony default export */ var AllZone = (ZoneInProject);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzE0NTQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtRTtBQUVoQjtBQUNjO0FBQ2hCO0FBQ0Y7QUFDZ0M7QUFDVztBQUFBO0FBUTFGLElBQU1nQixjQUFtQyxHQUFHLFNBQXRDQSxjQUFtQ0EsQ0FBQUMsSUFBQSxFQUFxQztFQUFBLElBQS9CQyxNQUFNLEdBQUFELElBQUEsQ0FBTkMsTUFBTTtJQUFFQyxTQUFTLEdBQUFGLElBQUEsQ0FBVEUsU0FBUztJQUFFQyxLQUFLLEdBQUFILElBQUEsQ0FBTEcsS0FBSztFQUNyRSxJQUFBQyxRQUFBLEdBQTBCbEIsbUNBQU8sQ0FBQyxDQUFDO0lBQTNCbUIsYUFBYSxHQUFBRCxRQUFBLENBQWJDLGFBQWE7RUFDckIsSUFBQUMsV0FBQSxHQUFvQmxCLGtCQUFHLENBQUNtQixNQUFNLENBQUMsQ0FBQztJQUF4QkMsT0FBTyxHQUFBRixXQUFBLENBQVBFLE9BQU87RUFDZixJQUFNQyxTQUFTLEdBQUdkLGdCQUFNLENBQWEsSUFBSSxDQUFDO0VBQzFDLElBQU1lLFdBQVcsR0FBR2hCLGlCQUFPLENBQ3pCO0lBQUEsT0FBTztNQUNMaUIsT0FBTyxFQUFFVjtJQUNYLENBQUM7RUFBQSxDQUFDLEVBQ0YsQ0FBQ0EsTUFBTSxDQUNULENBQUM7RUFDRCxJQUFBVyxXQUFBLEdBQStCekIsc0NBQVUsQ0FDdkM7TUFBQSxPQUFNSixpREFBeUIsQ0FBQzJCLFdBQWtCLENBQUM7SUFBQSxHQUNuRDtNQUNFRyxTQUFTLEVBQUUsU0FBQUEsVUFBQ0MsWUFBaUIsRUFBSztRQUNoQztRQUNBO1FBQ0E7UUFDQTtNQUFBLENBQ0Q7TUFDREMsT0FBTyxXQUFBQSxRQUFBLEVBQUc7UUFDUjtNQUFBLENBQ0Q7TUFDREMsTUFBTSxFQUFFO0lBQ1YsQ0FDRixDQUFDO0lBZE9DLElBQUksR0FBQUwsV0FBQSxDQUFKSyxJQUFJO0lBQUVDLE9BQU8sR0FBQU4sV0FBQSxDQUFQTSxPQUFPO0lBQUVDLEdBQUcsR0FBQVAsV0FBQSxDQUFITyxHQUFHO0VBZTFCLElBQU1DLFlBQVksR0FBRzVCLHFCQUFXLENBQUMsWUFBTTtJQUNyQztJQUNBMkIsR0FBRyxDQUFDLENBQUM7RUFDUCxDQUFDLEVBQUUsRUFBRSxDQUFDO0VBQ04xQixtQkFBUyxDQUFDLFlBQU07SUFDZDJCLFlBQVksQ0FBQyxDQUFDO0VBQ2hCLENBQUMsRUFBRSxDQUFDVixXQUFXLENBQUMsQ0FBQztFQUNqQixvQkFDRVosbUJBQUEsQ0FBQ1IsbUJBQUk7SUFDSGEsS0FBSyxFQUFFQSxLQUFNO0lBQ2JrQixTQUFTLEVBQUU7TUFDVEMsYUFBYSxFQUFFO0lBQ2pCLENBQUU7SUFDRkMsS0FBSyxlQUFFekIsbUJBQUEsQ0FBQ1QseUJBQU07TUFBQ21DLElBQUksZUFBRTFCLG1CQUFBLENBQUNkLDZCQUFjLElBQUUsQ0FBRTtNQUFDeUMsT0FBTyxFQUFFTDtJQUFhLENBQUUsQ0FBRTtJQUFBTSxRQUFBLGVBRW5FNUIsbUJBQUEsQ0FBQ2Isa0JBQU87TUFDTmlDLE9BQU8sRUFBRUEsT0FBUTtNQUNqQlQsU0FBUyxFQUFFQSxTQUFVO01BQ3JCa0IsTUFBTSxFQUFFLE1BQU87TUFDZkMsVUFBVSxFQUFFLFNBQUFBLFdBQUNDLElBQUk7UUFBQSxvQkFDZi9CLG1CQUFBLENBQUNQLG1CQUFJLENBQUN1QyxJQUFJO1VBQUFKLFFBQUEsZUFFUjVCLG1CQUFBLENBQUNGLGtDQUFtQixFQUFBbUMsdUJBQUEsS0FBS0YsSUFBSSxDQUFHO1FBQUMsQ0FDeEIsQ0FBQztNQUFBLENBQ1o7TUFDRkcsSUFBSSxFQUFFO1FBQ0pDLE1BQU0sRUFBRSxFQUFFO1FBQ1ZDLEVBQUUsRUFBRSxDQUFDO1FBQ0xDLEVBQUUsRUFBRSxDQUFDO1FBQ0xDLEVBQUUsRUFBRSxDQUFDO1FBQ0xDLEVBQUUsRUFBRSxDQUFDO1FBQ0xDLEdBQUcsRUFBRSxDQUFDO1FBQ05DLE1BQU0sRUFBRTtNQUNWLENBQUU7TUFDRkMsVUFBVSxFQUFFdkI7TUFDWjtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBOztNQUVBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQUE7TUFDQXdCLFVBQVUsRUFBRTtRQUNWQyxRQUFRLEVBQUU7TUFDWjtJQUFFLENBQ0g7RUFBQyxDQUNFLENBQUM7QUFFWCxDQUFDO0FBRUQsMkRBQWUzQyxjQUFjLEU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUNsSDZCO0FBQ0c7QUFDWjtBQUNBO0FBQ2tCO0FBQ1Q7QUFDWjtBQUNLO0FBQ3lCO0FBQ1Q7QUFDNUI7QUFDMkQ7QUFDbEU7QUFDNEM7QUFBQTtBQUFBO0FBQUE7QUFNNUUsSUFBTW1FLFFBQVEsR0FBR0MsTUFBTSxDQUFDLEtBQUssQ0FBQztBQUM5QixJQUFNQyxRQUFRLEdBQUdULGdDQUFNLENBQUMsQ0FBQztBQUN6QixJQUFNVSxRQU1KLEdBQUcsU0FOQ0EsUUFNSkEsQ0FBQXJFLElBQUEsRUFBMkQ7RUFBQSxJQUFyRHNFLE1BQU0sR0FBQXRFLElBQUEsQ0FBTnNFLE1BQU07SUFBRUMsSUFBSSxHQUFBdkUsSUFBQSxDQUFKdUUsSUFBSTtJQUFFcEUsS0FBSyxHQUFBSCxJQUFBLENBQUxHLEtBQUs7SUFBRXFFLE9BQU8sR0FBQXhFLElBQUEsQ0FBUHdFLE9BQU87SUFBRS9DLFFBQU8sR0FBQXpCLElBQUEsQ0FBUHlCLE9BQU87SUFBS0ksSUFBSSxHQUFBNEMsaUNBQUEsQ0FBQXpFLElBQUEsRUFBQTBFLFNBQUE7RUFDcEQsSUFBQXRFLFFBQUEsR0FBMEJsQixtQ0FBTyxDQUFDLENBQUM7SUFBM0JtQixhQUFhLEdBQUFELFFBQUEsQ0FBYkMsYUFBYTtFQUNyQixJQUFBTyxXQUFBLEdBQWlDekIsc0NBQVUsZUFBQXdGLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBQyxTQUFBQyxRQUFBO01BQUEsSUFBQUMsR0FBQTtNQUFBLE9BQUFILDRCQUFBLEdBQUFJLElBQUEsVUFBQUMsU0FBQUMsUUFBQTtRQUFBLGtCQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO1VBQUE7WUFBQUYsUUFBQSxDQUFBRSxJQUFBO1lBQUEsT0FDeEJyRyxpREFBeUIsQ0FBQztjQUMxQzRCLE9BQU8sRUFBRTRELElBQUk7Y0FDYmMsSUFBSSxFQUFFLENBQUM7Y0FDUEMsSUFBSSxFQUFFLENBQUM7Y0FDUEMsTUFBTSxFQUFFLENBQUMsTUFBTTtZQUNqQixDQUFRLENBQUM7VUFBQTtZQUxIUixHQUFHLEdBQUFHLFFBQUEsQ0FBQU0sSUFBQTtZQUFBLE9BQUFOLFFBQUEsQ0FBQU8sTUFBQSxXQU1GO2NBQ0x4RSxJQUFJLEVBQUU4RCxHQUFHLENBQUN0QyxVQUFVLENBQUNpRDtZQUN2QixDQUFDO1VBQUE7VUFBQTtZQUFBLE9BQUFSLFFBQUEsQ0FBQVMsSUFBQTtRQUFBO01BQUEsR0FBQWIsT0FBQTtJQUFBLENBQ0YsR0FBQztJQVZNN0QsSUFBSSxHQUFBTCxXQUFBLENBQUpLLElBQUk7SUFBRUMsT0FBTyxHQUFBTixXQUFBLENBQVBNLE9BQU87SUFBRTBFLEtBQUssR0FBQWhGLFdBQUEsQ0FBTGdGLEtBQUs7RUFXNUIsb0JBQ0U5RixtQkFBQSxDQUFDbUQsd0JBQVM7SUFDUnhCLE9BQU8sRUFBRSxTQUFBQSxRQUFBO01BQUEsT0FBTUEsUUFBTyxDQUFDOEMsSUFBSSxDQUFDO0lBQUEsQ0FBQztJQUM3QnNCLEtBQUssRUFBRTtNQUNMQyxLQUFLLEVBQUUsTUFBTTtNQUNiO01BQ0FDLFNBQVMsRUFBRSxHQUFHO01BQ2RDLE9BQU8sRUFBRSxNQUFNO01BQ2ZDLGFBQWEsRUFBRSxRQUFRO01BQ3ZCQyxjQUFjLEVBQUU7SUFDbEIsQ0FBRTtJQUNGL0YsS0FBSyxFQUFFQSxLQUFNO0lBQ2JtRSxNQUFNLGVBQUV4RSxtQkFBQSxDQUFDc0Qsd0JBQU07TUFBQytDLEdBQUcsRUFBRTdCLE1BQU87TUFBQ2dCLElBQUksRUFBQztJQUFPLENBQUUsQ0FBRTtJQUM3Q2MsV0FBVyxlQUNUdEcsbUJBQUE7TUFDRStGLEtBQUssRUFBRTtRQUNMUSxVQUFVLEVBQUU7TUFDZCxDQUFFO01BQUEzRSxRQUFBLEVBRURSLE9BQU8sZ0JBQ05wQixtQkFBQSxDQUFDMEQsdUJBQVEsQ0FBQ0YsS0FBSyxJQUFFLENBQUMsR0FDaEJzQyxLQUFLLGdCQUNQOUYsbUJBQUEsQ0FBQzRELGtCQUFHO1FBQUM0QyxLQUFLLEVBQUMsT0FBTztRQUFBNUUsUUFBQSxFQUFFckIsYUFBYSxDQUFDO1VBQUVrRyxFQUFFLEVBQUU7UUFBZSxDQUFDO01BQUMsQ0FBTSxDQUFDLGdCQUVoRXhDLG9CQUFBLENBQUNMLGtCQUFHO1FBQUM0QyxLQUFLLEVBQUMsU0FBUztRQUFBNUUsUUFBQSxHQUNqQnJCLGFBQWEsQ0FBQztVQUFFa0csRUFBRSxFQUFFO1FBQXFDLENBQUMsQ0FBQyxFQUFDLElBQUUsRUFBQ3RGLElBQUk7TUFBQSxDQUNqRTtJQUNOLENBQ0UsQ0FDTjtJQUNEdUYsS0FBSyxFQUFFakMsSUFBSztJQUNaQyxPQUFPLEVBQUVBO0VBQVEsQ0FDbEIsQ0FBQztBQUVOLENBQUM7QUFDRCxJQUFNaUMsUUFBaUMsR0FBRyxTQUFwQ0EsUUFBaUNBLENBQUFDLEtBQUEsRUFBbUM7RUFBQSxJQUE3QkMsV0FBVyxHQUFBRCxLQUFBLENBQVhDLFdBQVc7SUFBRXpHLFNBQVMsR0FBQXdHLEtBQUEsQ0FBVHhHLFNBQVM7RUFDakUsSUFBQTBHLFNBQUEsR0FBd0QvQyxrQkFBUSxDQUFVLEtBQUssQ0FBQztJQUFBZ0QsVUFBQSxHQUFBQyx1QkFBQSxDQUFBRixTQUFBO0lBQXpFRyxvQkFBb0IsR0FBQUYsVUFBQTtJQUFFRyx1QkFBdUIsR0FBQUgsVUFBQTtFQUNwRCxJQUFBSSxVQUFBLEdBQXdDcEQsa0JBQVEsQ0FBc0IsSUFBSSxDQUFDO0lBQUFxRCxVQUFBLEdBQUFKLHVCQUFBLENBQUFHLFVBQUE7SUFBcEVFLFlBQVksR0FBQUQsVUFBQTtJQUFFRSxlQUFlLEdBQUFGLFVBQUE7RUFFcEMsSUFBTXpHLFNBQVMsR0FBR2QsZ0JBQU0sQ0FBYSxDQUFDO0VBQ3RDLElBQU15QixZQUFZLEdBQUcsU0FBZkEsWUFBWUEsQ0FBQSxFQUFTO0lBQUEsSUFBQWlHLGtCQUFBO0lBQ3pCLENBQUFBLGtCQUFBLEdBQUE1RyxTQUFTLENBQUM2RyxPQUFPLGNBQUFELGtCQUFBLGVBQWpCQSxrQkFBQSxDQUFtQkUsTUFBTSxDQUFDLENBQUM7RUFDN0IsQ0FBQztFQUNELElBQU1DLGlCQUFpQixHQUFHLFNBQXBCQSxpQkFBaUJBLENBQUlDLElBQWtCLEVBQUs7SUFDaERMLGVBQWUsQ0FBQ0ssSUFBSSxDQUFDO0lBQ3JCVCx1QkFBdUIsQ0FBQyxJQUFJLENBQUM7RUFDL0IsQ0FBQztFQUNELElBQU1VLG1CQUFtQixHQUFHLFNBQXRCQSxtQkFBbUJBLENBQUEsRUFBUztJQUNoQztJQUNBO0lBQ0FDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGdCQUFnQixFQUFFVCxZQUFZLENBQUM7O0lBRTNDO0lBQ0FILHVCQUF1QixDQUFDLEtBQUssQ0FBQztFQUNoQyxDQUFDO0VBRUQsSUFBTWEsWUFBWSxHQUFHckkscUJBQVcsQ0FDOUIsVUFDRXFDO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUFBO0lBQUEsT0FDSTtNQUNKO01BQ0EwQyxJQUFJLEVBQUUxQyxJQUFJLENBQUMwQyxJQUFJO01BQ2ZELE1BQU0sRUFBRXpDLElBQUksQ0FBQ2lHLEtBQUssR0FBRzlFLDhCQUFjLENBQUNuQixJQUFJLENBQUNpRyxLQUFLLENBQUMsR0FBR2xGLCtCQUFvQjtNQUN0RTNCLElBQUksRUFBRVksSUFBSTtNQUNWMUIsS0FBSyxlQUNITCxtQkFBQTtRQUNFK0YsS0FBSyxFQUFFO1VBQ0xrQyxVQUFVLEVBQUU7UUFDZCxDQUFFO1FBQUFyRyxRQUFBLEVBRURHLElBQUksQ0FBQ21HO01BQUssQ0FDUixDQUNOO01BQ0Q7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0F4RCxPQUFPLEVBQUczQyxJQUFJLENBQVNxQyxRQUFRLENBQUMsR0FDNUIsRUFBRSxHQUNGLGNBQ0VwRSxtQkFBQSxDQUFDMkQsb0JBQUs7UUFFSm9DLEtBQUssRUFBRTtVQUNMRyxPQUFPLEVBQUUsTUFBTTtVQUNmaUMsR0FBRyxFQUFFLENBQUM7VUFDTkMsVUFBVSxFQUFFLFFBQVE7VUFDcEJoQyxjQUFjLEVBQUU7UUFDbEIsQ0FBRTtRQUFBeEUsUUFBQSxlQUdGNUIsbUJBQUEsQ0FBQ2dELHlCQUFVO1VBQ1RqQyxTQUFTLEVBQUVPLFlBQWE7VUFFeEIrRyxVQUFVLEVBQUU7WUFDVmxJLE1BQU0sRUFBRTRCLElBQUksQ0FBQzBDO1VBQ2Y7UUFBRSxHQUhFLE1BSUw7TUFBQyxHQWZFLFFBZ0JDO01BQ1A7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQ0E7TUFDQTtNQUNBO01BQUE7SUFFUixDQUFDO0VBQUEsQ0FBQyxFQUNGLEVBQ0YsQ0FBQztFQUNEOUUsbUJBQVMsQ0FBQyxZQUFNO0lBQ2QyQixZQUFZLENBQUMsQ0FBQztFQUNoQixDQUFDLEVBQUUsQ0FBQ2xCLFNBQVMsQ0FBQyxDQUFDO0VBQ2YsSUFBTWtJLFdBQVcsR0FBR3pJLGdCQUFNLENBQUMsRUFBRSxDQUFDO0VBQzlCLElBQUEwSSxjQUFBLEdBQTBCbEYsZ0NBQWEsQ0FDckMsVUFBQ21GLENBQXNDLEVBQUs7TUFDMUMsSUFBTTlCLEtBQUssR0FBRzhCLENBQUMsQ0FBQ0MsTUFBTSxDQUFDL0IsS0FBSztNQUM1QjRCLFdBQVcsQ0FBQ2QsT0FBTyxHQUFHZCxLQUFLO01BQzNCcEYsWUFBWSxDQUFDLENBQUM7SUFDaEIsQ0FBQyxFQUNEO01BQ0VvSCxJQUFJLEVBQUU7SUFDUixDQUNGLENBQUM7SUFUWUMsUUFBUSxHQUFBSixjQUFBLENBQWJsSCxHQUFHO0VBV1gsb0JBQ0U0QyxvQkFBQSxDQUFBRSxvQkFBQTtJQUFBdkMsUUFBQSxnQkFDRTVCLG1CQUFBLENBQUNSLG1CQUFJO01BQ0hvSixRQUFRO01BQ1I3QyxLQUFLLEVBQUU7UUFDTDhDLFlBQVksRUFBRTtNQUNoQixDQUFFO01BQUFqSCxRQUFBLGVBRUZxQyxvQkFBQSxDQUFDUixrQkFBRztRQUFDcUYsT0FBTyxFQUFDLGVBQWU7UUFBQzNHLE1BQU0sRUFBRSxFQUFHO1FBQUM0RyxLQUFLLEVBQUMsUUFBUTtRQUFBbkgsUUFBQSxnQkFDckQ1QixtQkFBQSxDQUFDdUQsa0JBQUc7VUFBQ3lGLElBQUksRUFBRSxDQUFFO1VBQUNDLElBQUksRUFBQyxTQUFTO1VBQUFySCxRQUFBLGVBQzFCNUIsbUJBQUEsQ0FBQ3dELG9CQUFLO1lBQUMwRixXQUFXLGVBQUVsSixtQkFBQSxDQUFDb0Qsd0NBQWdCO2NBQUNxRCxFQUFFLEVBQUU7WUFBbUIsQ0FBRSxDQUFFO1lBQUMwQyxRQUFRLEVBQUVSO1VBQVMsQ0FBRTtRQUFDLENBQ3JGLENBQUMsZUFDTjNJLG1CQUFBLENBQUN1RCxrQkFBRztVQUFDeUYsSUFBSSxFQUFFLENBQUU7VUFBQ2pELEtBQUssRUFBRTtZQUFFcUQsU0FBUyxFQUFFO1VBQVEsQ0FBRTtVQUFBeEgsUUFBQSxlQUMxQ3FDLG9CQUFBLENBQUNOLG9CQUFLO1lBQUEvQixRQUFBLGdCQUNKNUIsbUJBQUEsQ0FBQytDLHlCQUFVO2NBQ1RoQyxTQUFTLEVBQUVPLFlBQWE7Y0FDeEIrRyxVQUFVLEVBQUU7Z0JBQ1ZnQixZQUFZLEVBQUU7a0JBQ1pDLFVBQVUsRUFBRWxKO2dCQUNkO2NBQ0Y7WUFBRSxDQUNILENBQUMsZUFDRkosbUJBQUEsQ0FBQ1QseUJBQU07Y0FBQ21DLElBQUksZUFBRTFCLG1CQUFBLENBQUNkLDZCQUFjLElBQUUsQ0FBRTtjQUFDeUMsT0FBTyxFQUFFTDtZQUFhLENBQUUsQ0FBQztVQUFBLENBQ3REO1FBQUMsQ0FDTCxDQUFDO01BQUEsQ0FDSDtJQUFDLENBQ0YsQ0FBQyxlQUVQdEIsbUJBQUEsQ0FBQ1IsbUJBQUk7TUFDSCtCLFNBQVMsRUFBRTtRQUNUQyxhQUFhLEVBQUU7TUFDakIsQ0FBRTtNQUFBSSxRQUFBLGVBRUY1QixtQkFBQSxDQUFDbUQsd0JBQVMsQ0FBQ29HLEtBQUs7UUFBQ3hELEtBQUssRUFBRTtVQUFFQyxLQUFLLEVBQUU7UUFBTyxDQUFFO1FBQUFwRSxRQUFBLGVBQ3hDNUIsbUJBQUEsQ0FBQ2Isa0JBQU87VUFjTndCLFNBQVMsRUFBRUEsU0FBVTtVQUNyQjZJLGdCQUFnQixFQUFFLFNBQUFBLGlCQUFBQyxLQUFBLEVBQXVDO1lBQUEsSUFBQUMsY0FBQTtZQUFBLElBQXBDQyxZQUFZLEdBQUFGLEtBQUEsQ0FBWkUsWUFBWTtjQUFFQyxlQUFlLEdBQUFILEtBQUEsQ0FBZkcsZUFBZTtZQUNoRCxvQkFBTzNGLG9CQUFBLENBQUFFLG9CQUFBO2NBQUF2QyxRQUFBLEdBQUUsdUJBQVEsRUFBQyxDQUFBK0gsWUFBWSxhQUFaQSxZQUFZLGdCQUFBRCxjQUFBLEdBQVpDLFlBQVksQ0FBRyxDQUFDLENBQUMsY0FBQUQsY0FBQSx1QkFBakJBLGNBQUEsQ0FBbUJySixLQUFLLEtBQUksZ0JBQWdCO1lBQUEsQ0FBRyxDQUFDO1VBQ3BFLENBQUU7VUFDRndKLGFBQWEsRUFBRTtZQUNiQyxLQUFLLEVBQUU7VUFDVCxDQUFFO1VBQ0ZDLFdBQVcsRUFBQyxPQUFPO1VBQ25CQyxzQkFBc0IsRUFBRSxTQUFBQSx1QkFBQTtZQUFBLE9BQU0sSUFBSTtVQUFBLENBQUM7VUFDbkNySCxVQUFVLEVBQUU7WUFDVkMsUUFBUSxFQUFFO1VBQ1osQ0FBRTtVQUNGZCxVQUFVLEVBQUUsU0FBQUEsV0FBQ0MsSUFBSTtZQUFBLG9CQUNmL0IsbUJBQUEsQ0FBQ1AsbUJBQUksQ0FBQ3VDLElBQUk7Y0FBQUosUUFBQSxlQUVSNUIsbUJBQUEsQ0FBQ3VFLFFBQVEsRUFBQXRDLHVCQUFBO2dCQUFDTixPQUFPLEVBQUVrRjtjQUFZLEdBQUs5RSxJQUFJLENBQUc7WUFBQyxDQUNuQyxDQUFDO1VBQUEsQ0FDWjtVQUNGa0ksWUFBWSxFQUFFO1lBQ1pDLFdBQVcsRUFBRSxDQUFDO1lBQ2RmLFFBQVEsV0FBQUEsU0FBQ2dCLGVBQWUsRUFBRVIsWUFBWSxFQUFFUyxJQUFJLEVBQUU7Y0FDNUMsSUFBTUMsU0FBUyxHQUFHVixZQUFZLGFBQVpBLFlBQVksdUJBQVpBLFlBQVksQ0FBRyxDQUFDLENBQUM7Y0FFbkMsSUFBSSxDQUFDVSxTQUFTLElBQUlBLFNBQVMsQ0FBQ2pHLFFBQVEsQ0FBQyxFQUFFO2dCQUNyQ3lDLFdBQVcsYUFBWEEsV0FBVyxlQUFYQSxXQUFXLENBQUcsQ0FBQztjQUNqQixDQUFDLE1BQU07Z0JBQUEsSUFBQXlELGVBQUE7Z0JBQ0x6RCxXQUFXLGFBQVhBLFdBQVcsZUFBWEEsV0FBVyxDQUFHd0QsU0FBUyxhQUFUQSxTQUFTLGdCQUFBQyxlQUFBLEdBQVRELFNBQVMsQ0FBRWxKLElBQUksY0FBQW1KLGVBQUEsdUJBQWZBLGVBQUEsQ0FBaUI3RixJQUFJLENBQUM7Y0FDdEM7WUFDRixDQUFDO1lBQ0Q4RixJQUFJLEVBQUU7WUFDTjtVQUNGLENBQUU7VUFDRkMsT0FBTztZQUFBLElBQUFDLEtBQUEsR0FBQTVGLDBCQUFBLGVBQUFDLDRCQUFBLEdBQUFDLElBQUEsQ0FBRSxTQUFBMkYsU0FBT0MsTUFBTTtjQUFBLElBQUFDLFdBQUEsRUFBQUMsT0FBQSxFQUFBNUYsR0FBQSxFQUFBOUQsSUFBQTtjQUFBLE9BQUEyRCw0QkFBQSxHQUFBSSxJQUFBLFVBQUE0RixVQUFBQyxTQUFBO2dCQUFBLGtCQUFBQSxTQUFBLENBQUExRixJQUFBLEdBQUEwRixTQUFBLENBQUF6RixJQUFBO2tCQUFBO29CQUFBeUYsU0FBQSxDQUFBMUYsSUFBQTtvQkFFWndGLE9BQXFCLEdBQUcsRUFBRTtvQkFDaEMsSUFBSXpLLFNBQVMsRUFBRTtzQkFDYnlLLE9BQU8sQ0FBQ0csSUFBSSxDQUFDLENBQUNuSSw2QkFBVyxDQUFDb0ksT0FBTyxFQUFFLFlBQVksRUFBRSxHQUFHLEVBQUU3SyxTQUFTLENBQUMsQ0FBQztvQkFDbkU7b0JBQ0EsSUFBSWtJLFdBQVcsQ0FBQ2QsT0FBTyxFQUFFO3NCQUN2QnFELE9BQU8sQ0FBQ0csSUFBSSxDQUFDLENBQUNuSSw2QkFBVyxDQUFDb0ksT0FBTyxFQUFFLE9BQU8sRUFBRSxNQUFNLE1BQUFDLE1BQUEsQ0FBTTVDLFdBQVcsQ0FBQ2QsT0FBTyxPQUFJLENBQUM7b0JBQ2xGO29CQUFDdUQsU0FBQSxDQUFBekYsSUFBQTtvQkFBQSxPQUNpQnJDLDBCQUFRLENBQUM7c0JBQ3pCNEgsT0FBTyxFQUFFQSxPQUFPO3NCQUNoQnRGLElBQUksRUFBRW9GLE1BQU0sQ0FBQ25ELE9BQU87c0JBQ3BCaEMsSUFBSSxFQUFFbUYsTUFBTSxDQUFDL0gsUUFBUTtzQkFDckI2QyxNQUFNLEVBQUUsQ0FBQyxHQUFHLENBQUM7c0JBQ2IwRixRQUFRLEVBQUU7b0JBQ1osQ0FBQyxDQUFDO2tCQUFBO29CQU5JbEcsR0FBRyxHQUFBOEYsU0FBQSxDQUFBckYsSUFBQTtvQkFPSHZFLElBQUksR0FBR2lLLDJCQUFBLENBT1JuRyxHQUFHLENBQUM5RCxJQUFJLEVBQ1hrSyxHQUFHLENBQUN0RCxZQUFZLENBQUM7b0JBQUEsT0FBQWdELFNBQUEsQ0FBQXBGLE1BQUEsV0FDWjtzQkFDTHhFLElBQUksRUFBRUEsSUFBSTtzQkFDVm1LLEtBQUssRUFBR3JHLEdBQUcsYUFBSEEsR0FBRyxnQkFBQTJGLFdBQUEsR0FBSDNGLEdBQUcsQ0FBVXRDLFVBQVUsY0FBQWlJLFdBQUEsdUJBQXhCQSxXQUFBLENBQTBCaEY7b0JBQ25DLENBQUM7a0JBQUE7b0JBQUFtRixTQUFBLENBQUExRixJQUFBO29CQUFBMEYsU0FBQSxDQUFBUSxFQUFBLEdBQUFSLFNBQUE7b0JBRURySyx1QkFBTyxDQUFDb0YsS0FBSyxDQUFDLGlDQUFpQyxDQUFDO29CQUFDLE9BQUFpRixTQUFBLENBQUFwRixNQUFBLFdBQzFDO3NCQUNMeEUsSUFBSSxFQUFFO29CQUNSLENBQUM7a0JBQUE7a0JBQUE7b0JBQUEsT0FBQTRKLFNBQUEsQ0FBQWxGLElBQUE7Z0JBQUE7Y0FBQSxHQUFBNkUsUUFBQTtZQUFBLENBRUo7WUFBQSxpQkFBQWMsRUFBQTtjQUFBLE9BQUFmLEtBQUEsQ0FBQWdCLEtBQUEsT0FBQUMsU0FBQTtZQUFBO1VBQUEsSUFBQztVQUNGeEosSUFBSSxFQUFFO1lBQ0pDLE1BQU0sRUFBRSxFQUFFO1lBQ1ZDLEVBQUUsRUFBRSxDQUFDO1lBQ0xDLEVBQUUsRUFBRSxDQUFDO1lBQ0xDLEVBQUUsRUFBRSxDQUFDO1lBQ0xDLEVBQUUsRUFBRSxDQUFDO1lBQ0xDLEdBQUcsRUFBRSxDQUFDO1lBQ05DLE1BQU0sRUFBRTtVQUNWLENBQUU7VUFDRmtKLEtBQUssRUFBRTtZQUNMdEwsS0FBSyxFQUFFLENBQUMsQ0FBQztZQUNUdUwsUUFBUSxFQUFFLENBQUMsQ0FBQztZQUNackIsSUFBSSxFQUFFLENBQUMsQ0FBQztZQUNSL0YsTUFBTSxFQUFFO2NBQ05xSCxNQUFNLFdBQUFBLE9BQUNDLEdBQUcsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFO2dCQUN6QyxvQkFBT2xNLG1CQUFBLENBQUNzRCx3QkFBTTtrQkFBQytDLEdBQUcsRUFBRTBGLE1BQU0sQ0FBQ3ZILE1BQU87a0JBQUNnQixJQUFJLEVBQUMsT0FBTztrQkFBQ08sS0FBSyxFQUFFO29CQUFFb0csV0FBVyxFQUFFO2tCQUFFO2dCQUFFLENBQUUsQ0FBQztjQUMvRTtZQUNGLENBQUM7WUFDREMsT0FBTyxFQUFFLENBQUMsQ0FBQztZQUNYMUgsT0FBTyxFQUFFO2NBQ1AySCxlQUFlLEVBQUU7WUFDbkI7VUFDRixDQUFFO1VBQ0Z4SyxNQUFNLEVBQUU7UUFBTyxDQUNoQjtNQUFDLENBQ2E7SUFBQyxDQUNkLENBQUM7RUFBQSxDQUNQLENBQUM7QUFFUCxDQUFDO0FBRUQscURBQWU4RSxRQUFRLEU7OztBQzVVYztBQUNOO0FBQ2lCO0FBQ0Y7QUFDWjtBQUFBO0FBQUE7QUFBQTtBQU9sQyxJQUFNNEYsYUFBcUMsR0FBRyxTQUF4Q0EsYUFBcUNBLENBQUFyTSxJQUFBLEVBQWdDO0VBQUEsSUFBMUIwQixRQUFRLEdBQUExQixJQUFBLENBQVIwQixRQUFRO0lBQUV4QixTQUFTLEdBQUFGLElBQUEsQ0FBVEUsU0FBUztFQUNsRSxJQUFBMEcsU0FBQSxHQUE0Qi9DLGtCQUFRLENBQXFCLENBQUM7SUFBQWdELFVBQUEsR0FBQUMsdUJBQUEsQ0FBQUYsU0FBQTtJQUFuRDNHLE1BQU0sR0FBQTRHLFVBQUE7SUFBRXlGLFNBQVMsR0FBQXpGLFVBQUE7RUFDeEIsSUFBQXpHLFFBQUEsR0FBMEJsQixtQ0FBTyxDQUFDLENBQUM7SUFBM0JtQixhQUFhLEdBQUFELFFBQUEsQ0FBYkMsYUFBYTtFQUNyQixvQkFDRTBELG9CQUFBLENBQUFFLG9CQUFBO0lBQUF2QyxRQUFBLGdCQUNFNUIsbUJBQUEsQ0FBQzJHLGdCQUFRO01BQUN2RyxTQUFTLEVBQUVBLFNBQVU7TUFBQ3lHLFdBQVcsRUFBRTJGO0lBQVUsQ0FBRSxDQUFDLGVBQzFEeE0sbUJBQUEsQ0FBQ3NNLHNCQUFPLElBQUUsQ0FBQyxFQUNWLENBQUMsQ0FBQ25NLE1BQU0saUJBQ1BILG1CQUFBLENBQUNDLHNCQUFjO01BQ2JJLEtBQUssRUFBRUUsYUFBYSxDQUFDO1FBQ25Ca0csRUFBRSxFQUFFO01BQ04sQ0FBQyxDQUFFO01BQ0h0RyxNQUFNLEVBQUVBLE1BQU87TUFDZkMsU0FBUyxFQUFFQTtJQUFVLENBQ3RCLENBQ0Y7RUFBQSxDQUNELENBQUM7QUFFUCxDQUFDO0FBRUQsNENBQWVtTSxhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9zcmMvcGFnZXMvRmFybWluZ01hbmFnZW1lbnQvWm9uZS9BbGxab25lL0xpc3RDcm9wSW5ab25lLnRzeD81MDIyIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL3BhZ2VzL0Zhcm1pbmdNYW5hZ2VtZW50L1pvbmUvQWxsWm9uZS9MaXN0Wm9uZS50c3g/MWU4NyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL3NyYy9wYWdlcy9GYXJtaW5nTWFuYWdlbWVudC9ab25lL0FsbFpvbmUvaW5kZXgudHN4PzAxMDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0Q3JvcE1hbmFnZW1lbnRJbmZvTGlzdCB9IGZyb20gJ0Avc2VydmljZXMvY3JvcE1hbmFnZXInO1xyXG5pbXBvcnQgeyBJX0lPVERldmljZSB9IGZyb20gJ0Avc2VydmljZXMvZGV2aWNlcyc7XHJcbmltcG9ydCB7IFJlbG9hZE91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xyXG5pbXBvcnQgeyBBY3Rpb25UeXBlLCBQcm9MaXN0IH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyB1c2VJbnRsLCB1c2VSZXF1ZXN0IH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IEFwcCwgQnV0dG9uLCBDYXJkLCBMaXN0IH0gZnJvbSAnYW50ZCc7XHJcbmltcG9ydCB7IEZDLCBSZWFjdE5vZGUsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IEdlbmVyYWxTZWFzb25hbENhcmQgZnJvbSAnLi4vLi4vU2Vhc29uYWxNYW5hZ2VtZW50L2NvbXBvbmVudHMvR2VuZXJhbFNlYXNvbmFsQ2FyZCc7XHJcblxyXG5pbnRlcmZhY2UgTGlzdERldmljZVByb3BzIHtcclxuICB6b25lSWQ/OiBzdHJpbmc7XHJcbiAgcHJvamVjdElkPzogc3RyaW5nO1xyXG4gIHRpdGxlPzogUmVhY3ROb2RlO1xyXG59XHJcblxyXG5jb25zdCBMaXN0Q3JvcEluWm9uZTogRkM8TGlzdERldmljZVByb3BzPiA9ICh7IHpvbmVJZCwgcHJvamVjdElkLCB0aXRsZSB9KSA9PiB7XHJcbiAgY29uc3QgeyBmb3JtYXRNZXNzYWdlIH0gPSB1c2VJbnRsKCk7XHJcbiAgY29uc3QgeyBtZXNzYWdlIH0gPSBBcHAudXNlQXBwKCk7XHJcbiAgY29uc3QgYWN0aW9uUmVmID0gdXNlUmVmPEFjdGlvblR5cGU+KG51bGwpO1xyXG4gIGNvbnN0IHF1ZXJ5UGFyYW1zID0gdXNlTWVtbyhcclxuICAgICgpID0+ICh7XHJcbiAgICAgIHpvbmVfaWQ6IHpvbmVJZCxcclxuICAgIH0pLFxyXG4gICAgW3pvbmVJZF0sXHJcbiAgKTtcclxuICBjb25zdCB7IGRhdGEsIGxvYWRpbmcsIHJ1biB9ID0gdXNlUmVxdWVzdChcclxuICAgICgpID0+IGdldENyb3BNYW5hZ2VtZW50SW5mb0xpc3QocXVlcnlQYXJhbXMgYXMgYW55KSxcclxuICAgIHtcclxuICAgICAgb25TdWNjZXNzOiAocmVzcG9uc2VEYXRhOiBhbnkpID0+IHtcclxuICAgICAgICAvLyBpZiAocmVzcG9uc2VEYXRhKSB7XHJcbiAgICAgICAgLy8gICBzZXRDcm9wTGlzdChyZXNwb25zZURhdGEpO1xyXG4gICAgICAgIC8vICAgc2V0Rmlyc3RMb2FkKGZhbHNlKTtcclxuICAgICAgICAvLyB9XHJcbiAgICAgIH0sXHJcbiAgICAgIG9uRXJyb3IoKSB7XHJcbiAgICAgICAgLy8gc2V0Rmlyc3RMb2FkKGZhbHNlKTtcclxuICAgICAgfSxcclxuICAgICAgbWFudWFsOiB0cnVlLFxyXG4gICAgfSxcclxuICApO1xyXG4gIGNvbnN0IGhhbmRsZVJlbG9hZCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIC8vIGFjdGlvblJlZi5jdXJyZW50Py5yZWxvYWQoKTtcclxuICAgIHJ1bigpO1xyXG4gIH0sIFtdKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaGFuZGxlUmVsb2FkKCk7XHJcbiAgfSwgW3F1ZXJ5UGFyYW1zXSk7XHJcbiAgcmV0dXJuIChcclxuICAgIDxDYXJkXHJcbiAgICAgIHRpdGxlPXt0aXRsZX1cclxuICAgICAgYm9keVN0eWxlPXt7XHJcbiAgICAgICAgcGFkZGluZ0lubGluZTogMCxcclxuICAgICAgfX1cclxuICAgICAgZXh0cmE9ezxCdXR0b24gaWNvbj17PFJlbG9hZE91dGxpbmVkIC8+fSBvbkNsaWNrPXtoYW5kbGVSZWxvYWR9IC8+fVxyXG4gICAgPlxyXG4gICAgICA8UHJvTGlzdDxJX0lPVERldmljZT5cclxuICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxyXG4gICAgICAgIGFjdGlvblJlZj17YWN0aW9uUmVmfVxyXG4gICAgICAgIHJvd0tleT17J25hbWUnfVxyXG4gICAgICAgIHJlbmRlckl0ZW09eyhpdGVtKSA9PiAoXHJcbiAgICAgICAgICA8TGlzdC5JdGVtPlxyXG4gICAgICAgICAgICB7LyogPERldmljZUNhcmQgb25Qb3dlckNoYW5nZT17aGFuZGxlUmVsb2FkfSBkYXRhPXtpdGVtfSAvPiAqL31cclxuICAgICAgICAgICAgPEdlbmVyYWxTZWFzb25hbENhcmQgey4uLml0ZW19IC8+XHJcbiAgICAgICAgICA8L0xpc3QuSXRlbT5cclxuICAgICAgICApfVxyXG4gICAgICAgIGdyaWQ9e3tcclxuICAgICAgICAgIGd1dHRlcjogMjAsXHJcbiAgICAgICAgICB4czogMixcclxuICAgICAgICAgIG1kOiAyLFxyXG4gICAgICAgICAgbGc6IDIsXHJcbiAgICAgICAgICB4bDogMyxcclxuICAgICAgICAgIHh4bDogMyxcclxuICAgICAgICAgIGNvbHVtbjogMixcclxuICAgICAgICB9fVxyXG4gICAgICAgIGRhdGFTb3VyY2U9e2RhdGEgYXMgYW55fVxyXG4gICAgICAgIC8vIHJlcXVlc3Q9e2FzeW5jIChwYXJhbXMpID0+IHtcclxuICAgICAgICAvLyAgIHRyeSB7XHJcbiAgICAgICAgLy8gICAgIGNvbnN0IGZpbHRlcnM6IEFycmF5PGFueVtdPiA9IFtdO1xyXG4gICAgICAgIC8vICAgICBpZiAoem9uZUlkKSB7XHJcbiAgICAgICAgLy8gICAgICAgZmlsdGVycy5wdXNoKFtET0NUWVBFX0VSUC5pb3REZXZpY2UsICd6b25lX2lkJywgJz0nLCB6b25lSWRdKTtcclxuICAgICAgICAvLyAgICAgfVxyXG4gICAgICAgIC8vICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRDcm9wKHtcclxuICAgICAgICAvLyAgICAgICBmaWx0ZXJzOiBmaWx0ZXJzLFxyXG4gICAgICAgIC8vICAgICAgIHBhZ2U6IHBhcmFtcy5jdXJyZW50LFxyXG4gICAgICAgIC8vICAgICAgIHNpemU6IHBhcmFtcy5wYWdlU2l6ZSxcclxuICAgICAgICAvLyAgICAgICBmaWVsZHM6IFsnKiddLFxyXG4gICAgICAgIC8vICAgICAgIG9yZGVyX2J5OiAnY3JlYXRpb24nLFxyXG4gICAgICAgIC8vICAgICAgIHByb2plY3RfaWQ6IHByb2plY3RJZCxcclxuICAgICAgICAvLyAgICAgfSk7XHJcblxyXG4gICAgICAgIC8vICAgICAvLyBjb25zdCBkYXRhID0gcmVzLmRhdGEubWFwKGZvcm1hdFJlbmRlcik7XHJcbiAgICAgICAgLy8gICAgIHJldHVybiB7XHJcbiAgICAgICAgLy8gICAgICAgZGF0YTogcmVzLFxyXG4gICAgICAgIC8vICAgICAgIHRvdGFsOiByZXMubGVuZ3RoLFxyXG4gICAgICAgIC8vICAgICB9O1xyXG4gICAgICAgIC8vICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAvLyAgICAgbWVzc2FnZS5lcnJvcihcclxuICAgICAgICAvLyAgICAgICBmb3JtYXRNZXNzYWdlKHtcclxuICAgICAgICAvLyAgICAgICAgIGlkOiAnY29tbW9uLmVycm9yJyxcclxuICAgICAgICAvLyAgICAgICB9KSxcclxuICAgICAgICAvLyAgICAgKTtcclxuICAgICAgICAvLyAgICAgcmV0dXJuIHtcclxuICAgICAgICAvLyAgICAgICBkYXRhOiBbXSxcclxuICAgICAgICAvLyAgICAgfTtcclxuICAgICAgICAvLyAgIH1cclxuICAgICAgICAvLyB9fVxyXG4gICAgICAgIHBhZ2luYXRpb249e3tcclxuICAgICAgICAgIHBhZ2VTaXplOiAyMCxcclxuICAgICAgICB9fVxyXG4gICAgICAvPlxyXG4gICAgPC9DYXJkPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMaXN0Q3JvcEluWm9uZTtcclxuIiwiaW1wb3J0IHsgRE9DVFlQRV9FUlAgfSBmcm9tICdAL2NvbW1vbi9jb250YW5zdC9jb25zdGFuc3QnO1xyXG5pbXBvcnQgeyBERUZBVUxUX0ZBTExCQUNLX0lNRyB9IGZyb20gJ0AvY29tbW9uL2NvbnRhbnN0L2ltZyc7XHJcbmltcG9ydCBBZGROZXdab25lIGZyb20gJ0AvcGFnZXMvWm9uZS9BZGROZXdab25lJztcclxuaW1wb3J0IFVwZGF0ZVpvbmUgZnJvbSAnQC9wYWdlcy9ab25lL1VwZGF0ZVpvbmUnO1xyXG5pbXBvcnQgeyBnZXRDcm9wTWFuYWdlbWVudEluZm9MaXN0IH0gZnJvbSAnQC9zZXJ2aWNlcy9jcm9wTWFuYWdlcic7XHJcbmltcG9ydCB7IElab25lUmVzRGF0YSwgem9uZUxpc3QgfSBmcm9tICdAL3NlcnZpY2VzL3pvbmVzJztcclxuaW1wb3J0IHsgZ2VuRG93bmxvYWRVcmwgfSBmcm9tICdAL3V0aWxzL2ZpbGUnO1xyXG5pbXBvcnQgeyBSZWxvYWRPdXRsaW5lZCB9IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcclxuaW1wb3J0IHsgQWN0aW9uVHlwZSwgQ2hlY2tDYXJkLCBQcm9MaXN0IH0gZnJvbSAnQGFudC1kZXNpZ24vcHJvLWNvbXBvbmVudHMnO1xyXG5pbXBvcnQgeyBGb3JtYXR0ZWRNZXNzYWdlLCB1c2VJbnRsLCB1c2VSZXF1ZXN0IH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IHVzZURlYm91bmNlRm4gfSBmcm9tICdhaG9va3MnO1xyXG5pbXBvcnQgeyBBdmF0YXIsIEJ1dHRvbiwgQ2FyZCwgQ29sLCBJbnB1dCwgTGlzdCwgbWVzc2FnZSwgUm93LCBTa2VsZXRvbiwgU3BhY2UsIFRhZyB9IGZyb20gJ2FudGQnO1xyXG5pbXBvcnQgeyBuYW5vaWQgfSBmcm9tICduYW5vaWQnO1xyXG5pbXBvcnQgUmVhY3QsIHsgRkMsIHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5pbnRlcmZhY2UgTGlzdFpvbmVQcm9wcyB7XHJcbiAgb25JdGVtQ2xpY2s/OiAoem9uZVNlbGVjdGVkPzogc3RyaW5nKSA9PiB2b2lkO1xyXG4gIHByb2plY3RJZD86IHN0cmluZztcclxufVxyXG5jb25zdCBpc0FsbEtleSA9IFN5bWJvbCgnYWxsJyk7XHJcbmNvbnN0IGFsbFZhbHVlID0gbmFub2lkKCk7XHJcbmNvbnN0IFpvbmVDYXJkOiBGQzx7XHJcbiAgYXZhdGFyOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgYWN0aW9uczogYW55O1xyXG4gIG9uQ2xpY2s6IChpZDogc3RyaW5nKSA9PiB2b2lkO1xyXG59PiA9ICh7IGF2YXRhciwgbmFtZSwgdGl0bGUsIGFjdGlvbnMsIG9uQ2xpY2ssIC4uLml0ZW0gfSkgPT4ge1xyXG4gIGNvbnN0IHsgZm9ybWF0TWVzc2FnZSB9ID0gdXNlSW50bCgpO1xyXG4gIGNvbnN0IHsgZGF0YSwgbG9hZGluZywgZXJyb3IgfSA9IHVzZVJlcXVlc3QoYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0Q3JvcE1hbmFnZW1lbnRJbmZvTGlzdCh7XHJcbiAgICAgIHpvbmVfaWQ6IG5hbWUsXHJcbiAgICAgIHBhZ2U6IDEsXHJcbiAgICAgIHNpemU6IDEsXHJcbiAgICAgIGZpZWxkczogWyduYW1lJ10sXHJcbiAgICB9IGFzIGFueSk7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBkYXRhOiByZXMucGFnaW5hdGlvbi50b3RhbEVsZW1lbnRzLFxyXG4gICAgfTtcclxuICB9KTtcclxuICByZXR1cm4gKFxyXG4gICAgPENoZWNrQ2FyZFxyXG4gICAgICBvbkNsaWNrPXsoKSA9PiBvbkNsaWNrKG5hbWUpfVxyXG4gICAgICBzdHlsZT17e1xyXG4gICAgICAgIHdpZHRoOiAnMTAwJScsXHJcbiAgICAgICAgLy8gaGVpZ2h0OiAnMTAwJScsXHJcbiAgICAgICAgbWluSGVpZ2h0OiAxNjAsXHJcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxyXG4gICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxyXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXHJcbiAgICAgIH19XHJcbiAgICAgIHRpdGxlPXt0aXRsZX1cclxuICAgICAgYXZhdGFyPXs8QXZhdGFyIHNyYz17YXZhdGFyfSBzaXplPVwibGFyZ2VcIiAvPn1cclxuICAgICAgZGVzY3JpcHRpb249e1xyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgIHBhZGRpbmdUb3A6IDEwLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7bG9hZGluZyA/IChcclxuICAgICAgICAgICAgPFNrZWxldG9uLklucHV0IC8+XHJcbiAgICAgICAgICApIDogZXJyb3IgPyAoXHJcbiAgICAgICAgICAgIDxUYWcgY29sb3I9XCJlcnJvclwiPntmb3JtYXRNZXNzYWdlKHsgaWQ6ICdjb21tb24uZXJyb3InIH0pfTwvVGFnPlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPFRhZyBjb2xvcj1cInN1Y2Nlc3NcIj5cclxuICAgICAgICAgICAgICB7Zm9ybWF0TWVzc2FnZSh7IGlkOiAnY29tbW9uLm51bWJlcl9vZl9jcm9wc19pbl90aGVfem9uZScgfSl9OiB7ZGF0YX1cclxuICAgICAgICAgICAgPC9UYWc+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICB9XHJcbiAgICAgIHZhbHVlPXtuYW1lfVxyXG4gICAgICBhY3Rpb25zPXthY3Rpb25zfVxyXG4gICAgLz5cclxuICApO1xyXG59O1xyXG5jb25zdCBMaXN0Wm9uZTogUmVhY3QuRkM8TGlzdFpvbmVQcm9wcz4gPSAoeyBvbkl0ZW1DbGljaywgcHJvamVjdElkIH0pID0+IHtcclxuICBjb25zdCBbZGVsZXRlQ29uZmlybVZpc2libGUsIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBbZGVsZXRpbmdab25lLCBzZXREZWxldGluZ1pvbmVdID0gdXNlU3RhdGU8SVpvbmVSZXNEYXRhIHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIGNvbnN0IGFjdGlvblJlZiA9IHVzZVJlZjxBY3Rpb25UeXBlPigpO1xyXG4gIGNvbnN0IGhhbmRsZVJlbG9hZCA9ICgpID0+IHtcclxuICAgIGFjdGlvblJlZi5jdXJyZW50Py5yZWxvYWQoKTtcclxuICB9O1xyXG4gIGNvbnN0IHNob3dEZWxldGVDb25maXJtID0gKHpvbmU6IElab25lUmVzRGF0YSkgPT4ge1xyXG4gICAgc2V0RGVsZXRpbmdab25lKHpvbmUpO1xyXG4gICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUodHJ1ZSk7XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVEZWxldGVDb25maXJtID0gKCkgPT4ge1xyXG4gICAgLy8gUGVyZm9ybSB0aGUgZGVsZXRlIGFjdGlvbiBoZXJlICh5b3UgbWlnaHQgd2FudCB0byBjYWxsIGFuIEFQSSBvciB1cGRhdGUgc3RhdGUpXHJcbiAgICAvLyBGb3Igbm93LCBsZXQncyBqdXN0IGxvZyBhIG1lc3NhZ2VcclxuICAgIGNvbnNvbGUubG9nKCdEZWxldGluZyB6b25lOicsIGRlbGV0aW5nWm9uZSk7XHJcblxyXG4gICAgLy8gQ2xvc2UgdGhlIGRlbGV0ZSBjb25maXJtYXRpb24gbW9kYWxcclxuICAgIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBmb3JtYXRSZW5kZXIgPSB1c2VDYWxsYmFjayhcclxuICAgIChcclxuICAgICAgaXRlbTogSVpvbmVSZXNEYXRhLFxyXG4gICAgICAvLyB8IHtcclxuICAgICAgLy8gICAgIGxhYmVsOiBzdHJpbmc7XHJcbiAgICAgIC8vICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICAgIC8vICAgICBbaXNBbGxLZXldOiBib29sZWFuO1xyXG4gICAgICAvLyAgIH0sXHJcbiAgICApID0+ICh7XHJcbiAgICAgIC8vIC4uLml0ZW0sXHJcbiAgICAgIG5hbWU6IGl0ZW0ubmFtZSxcclxuICAgICAgYXZhdGFyOiBpdGVtLmltYWdlID8gZ2VuRG93bmxvYWRVcmwoaXRlbS5pbWFnZSkgOiBERUZBVUxUX0ZBTExCQUNLX0lNRyxcclxuICAgICAgZGF0YTogaXRlbSxcclxuICAgICAgdGl0bGU6IChcclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICB3aGl0ZVNwYWNlOiAnbm9ybWFsJyxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAge2l0ZW0ubGFiZWx9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICksXHJcbiAgICAgIC8vIGNvbnRlbnQ6IChcclxuICAgICAgLy8gICA8c3BhblxyXG4gICAgICAvLyAgICAgc3R5bGU9e3tcclxuICAgICAgLy8gICAgICAgd29yZEJyZWFrOiAnbm9ybWFsJyxcclxuICAgICAgLy8gICAgIH19XHJcbiAgICAgIC8vICAgPlxyXG4gICAgICAvLyAgICAgeyhpdGVtIGFzIGFueSlbaXNBbGxLZXldID8gJ0zhu41jIHRoZW8gdOG6pXQgY+G6oyBraHUgduG7sWMnIDogYEzhu41jIHRoZW8ga2h1IHbhu7FjICR7aXRlbS5sYWJlbH1gfVxyXG4gICAgICAvLyAgIDwvc3Bhbj5cclxuICAgICAgLy8gKSxcclxuICAgICAgYWN0aW9uczogKGl0ZW0gYXMgYW55KVtpc0FsbEtleV1cclxuICAgICAgICA/IFtdXHJcbiAgICAgICAgOiBbXHJcbiAgICAgICAgICAgIDxTcGFjZVxyXG4gICAgICAgICAgICAgIGtleT1cImFjdGlvblwiXHJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICAgICAgICAgIGdhcDogNixcclxuICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1hcm91bmQnLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7LyogPERlbGV0ZVpvbmVJblByb2plY3Qgem9uZUlkPXtpdGVtLm5hbWV9IG9uU3VjY2Vzcz17aGFuZGxlUmVsb2FkfSBrZXk9XCJkZWxldGVcIiAvPiAqL31cclxuICAgICAgICAgICAgICA8VXBkYXRlWm9uZVxyXG4gICAgICAgICAgICAgICAgb25TdWNjZXNzPXtoYW5kbGVSZWxvYWR9XHJcbiAgICAgICAgICAgICAgICBrZXk9XCJlZGl0XCJcclxuICAgICAgICAgICAgICAgIG1vZGFsUHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgem9uZUlkOiBpdGVtLm5hbWUsXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvU3BhY2U+LFxyXG4gICAgICAgICAgICAvLyA8PlxyXG4gICAgICAgICAgICAvLyAgIHtjYW5VcGRhdGVDYXRlZ29yeSAmJiAoXHJcbiAgICAgICAgICAgIC8vICAgICA8VXBkYXRlQ2F0ZWdvcnlcclxuICAgICAgICAgICAgLy8gICAgICAga2V5PXsnZWRpdCcgKyBpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgIC8vICAgICAgIHJlZnJlc2hGbmM9e2hhbmRsZVJlbG9hZH1cclxuICAgICAgICAgICAgLy8gICAgICAgdmFsdWU9e2l0ZW19XHJcbiAgICAgICAgICAgIC8vICAgICA+PC9VcGRhdGVDYXRlZ29yeT5cclxuICAgICAgICAgICAgLy8gICApfVxyXG4gICAgICAgICAgICAvLyA8Lz4sXHJcbiAgICAgICAgICAgIC8vIDw+XHJcbiAgICAgICAgICAgIC8vICAge2NhbkRlbGV0ZUNhdGVnb3J5ICYmIChcclxuICAgICAgICAgICAgLy8gICAgIDxEZWxldGVDYXRlZ29yeVxyXG4gICAgICAgICAgICAvLyAgICAgICBrZXk9eydyZW1vdmUnICsgaXRlbS5uYW1lfVxyXG4gICAgICAgICAgICAvLyAgICAgICByZWZyZXNoRm5jPXtoYW5kbGVSZWxvYWR9XHJcbiAgICAgICAgICAgIC8vICAgICAgIHZhbHVlPXtpdGVtfVxyXG4gICAgICAgICAgICAvLyAgICAgLz5cclxuICAgICAgICAgICAgLy8gICApfVxyXG4gICAgICAgICAgICAvLyA8Lz4sXHJcbiAgICAgICAgICBdLFxyXG4gICAgfSksXHJcbiAgICBbXSxcclxuICApO1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBoYW5kbGVSZWxvYWQoKTtcclxuICB9LCBbcHJvamVjdElkXSk7XHJcbiAgY29uc3Qga2V5V29yZHNSZWYgPSB1c2VSZWYoJycpO1xyXG4gIGNvbnN0IHsgcnVuOiBvblNlYXJjaCB9ID0gdXNlRGVib3VuY2VGbihcclxuICAgIChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xyXG4gICAgICBjb25zdCB2YWx1ZSA9IGUudGFyZ2V0LnZhbHVlO1xyXG4gICAgICBrZXlXb3Jkc1JlZi5jdXJyZW50ID0gdmFsdWU7XHJcbiAgICAgIGhhbmRsZVJlbG9hZCgpO1xyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgd2FpdDogNDAwLFxyXG4gICAgfSxcclxuICApO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPENhcmRcclxuICAgICAgICBib3JkZXJlZFxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBtYXJnaW5Cb3R0b206IDIwLFxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICA8Um93IGp1c3RpZnk9XCJzcGFjZS1iZXR3ZWVuXCIgZ3V0dGVyPXsxNn0gYWxpZ249XCJtaWRkbGVcIj5cclxuICAgICAgICAgIDxDb2wgc3Bhbj17OH0gZmxleD1cIjEgMCAyNSVcIj5cclxuICAgICAgICAgICAgPElucHV0IGFkZG9uQmVmb3JlPXs8Rm9ybWF0dGVkTWVzc2FnZSBpZD17J2NvbW1vbi56b25lX25hbWUnfSAvPn0gb25DaGFuZ2U9e29uU2VhcmNofSAvPlxyXG4gICAgICAgICAgPC9Db2w+XHJcbiAgICAgICAgICA8Q29sIHNwYW49ezh9IHN0eWxlPXt7IHRleHRBbGlnbjogJ3JpZ2h0JyB9fT5cclxuICAgICAgICAgICAgPFNwYWNlPlxyXG4gICAgICAgICAgICAgIDxBZGROZXdab25lXHJcbiAgICAgICAgICAgICAgICBvblN1Y2Nlc3M9e2hhbmRsZVJlbG9hZH1cclxuICAgICAgICAgICAgICAgIG1vZGFsUHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcHJvamVjdF9pZDogcHJvamVjdElkLFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxCdXR0b24gaWNvbj17PFJlbG9hZE91dGxpbmVkIC8+fSBvbkNsaWNrPXtoYW5kbGVSZWxvYWR9IC8+XHJcbiAgICAgICAgICAgIDwvU3BhY2U+XHJcbiAgICAgICAgICA8L0NvbD5cclxuICAgICAgICA8L1Jvdz5cclxuICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgPENhcmRcclxuICAgICAgICBib2R5U3R5bGU9e3tcclxuICAgICAgICAgIHBhZGRpbmdJbmxpbmU6IDAsXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIDxDaGVja0NhcmQuR3JvdXAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fT5cclxuICAgICAgICAgIDxQcm9MaXN0PHtcclxuICAgICAgICAgICAgbmFtZTogc3RyaW5nO1xyXG4gICAgICAgICAgICB0aXRsZTogYW55O1xyXG4gICAgICAgICAgICBhdmF0YXI/OiBzdHJpbmcgfCBudWxsO1xyXG4gICAgICAgICAgICBbaXNBbGxLZXldPzogYm9vbGVhbjtcclxuICAgICAgICAgICAgY29udGVudDogYW55O1xyXG4gICAgICAgICAgICBkYXRhPzpcclxuICAgICAgICAgICAgICB8IElab25lUmVzRGF0YVxyXG4gICAgICAgICAgICAgIHwge1xyXG4gICAgICAgICAgICAgICAgICBsYWJlbDogc3RyaW5nO1xyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBzdHJpbmc7XHJcbiAgICAgICAgICAgICAgICAgIFtpc0FsbEtleV06IGJvb2xlYW47XHJcbiAgICAgICAgICAgICAgICB9O1xyXG4gICAgICAgICAgfT5cclxuICAgICAgICAgICAgYWN0aW9uUmVmPXthY3Rpb25SZWZ9XHJcbiAgICAgICAgICAgIHRhYmxlQWxlcnRSZW5kZXI9eyh7IHNlbGVjdGVkUm93cywgb25DbGVhblNlbGVjdGVkIH0pID0+IHtcclxuICAgICAgICAgICAgICByZXR1cm4gPD7EkMOjIGNo4buNbiB7c2VsZWN0ZWRSb3dzPy5bMF0/LnRpdGxlIHx8ICdU4bqldCBj4bqjIGtodSB24buxYyd9PC8+O1xyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBpdGVtQ2FyZFByb3BzPXt7XHJcbiAgICAgICAgICAgICAgZ2hvc3Q6IHRydWUsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHNob3dBY3Rpb25zPVwiaG92ZXJcIlxyXG4gICAgICAgICAgICB0YWJsZUFsZXJ0T3B0aW9uUmVuZGVyPXsoKSA9PiBudWxsfVxyXG4gICAgICAgICAgICBwYWdpbmF0aW9uPXt7XHJcbiAgICAgICAgICAgICAgcGFnZVNpemU6IDIwLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICByZW5kZXJJdGVtPXsoaXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgIDxMaXN0Lkl0ZW0+XHJcbiAgICAgICAgICAgICAgICB7LyogPERldmljZUNhcmQgb25Qb3dlckNoYW5nZT17aGFuZGxlUmVsb2FkfSBkYXRhPXtpdGVtfSAvPiAqL31cclxuICAgICAgICAgICAgICAgIDxab25lQ2FyZCBvbkNsaWNrPXtvbkl0ZW1DbGlja30gey4uLml0ZW19IC8+XHJcbiAgICAgICAgICAgICAgPC9MaXN0Lkl0ZW0+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHJvd1NlbGVjdGlvbj17e1xyXG4gICAgICAgICAgICAgIGNvbHVtbldpZHRoOiAwLFxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlKHNlbGVjdGVkUm93S2V5cywgc2VsZWN0ZWRSb3dzLCBpbmZvKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBhY3RpdmVSb3cgPSBzZWxlY3RlZFJvd3M/LlswXTtcclxuXHJcbiAgICAgICAgICAgICAgICBpZiAoIWFjdGl2ZVJvdyB8fCBhY3RpdmVSb3dbaXNBbGxLZXldKSB7XHJcbiAgICAgICAgICAgICAgICAgIG9uSXRlbUNsaWNrPy4oKTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgIG9uSXRlbUNsaWNrPy4oYWN0aXZlUm93Py5kYXRhPy5uYW1lKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIHR5cGU6ICdyYWRpbycsXHJcbiAgICAgICAgICAgICAgLy8gZGVmYXVsdFNlbGVjdGVkUm93S2V5czogW2FsbFZhbHVlXSxcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgcmVxdWVzdD17YXN5bmMgKHBhcmFtcykgPT4ge1xyXG4gICAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBmaWx0ZXJzOiBBcnJheTxhbnlbXT4gPSBbXTtcclxuICAgICAgICAgICAgICAgIGlmIChwcm9qZWN0SWQpIHtcclxuICAgICAgICAgICAgICAgICAgZmlsdGVycy5wdXNoKFtET0NUWVBFX0VSUC5pb3Rab25lLCAncHJvamVjdF9pZCcsICc9JywgcHJvamVjdElkXSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBpZiAoa2V5V29yZHNSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICAgICAgICBmaWx0ZXJzLnB1c2goW0RPQ1RZUEVfRVJQLmlvdFpvbmUsICdsYWJlbCcsICdsaWtlJywgYCUke2tleVdvcmRzUmVmLmN1cnJlbnR9JWBdKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHpvbmVMaXN0KHtcclxuICAgICAgICAgICAgICAgICAgZmlsdGVyczogZmlsdGVycyxcclxuICAgICAgICAgICAgICAgICAgcGFnZTogcGFyYW1zLmN1cnJlbnQsXHJcbiAgICAgICAgICAgICAgICAgIHNpemU6IHBhcmFtcy5wYWdlU2l6ZSxcclxuICAgICAgICAgICAgICAgICAgZmllbGRzOiBbJyonXSxcclxuICAgICAgICAgICAgICAgICAgb3JkZXJfYnk6ICdjcmVhdGlvbicsXHJcbiAgICAgICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBbXHJcbiAgICAgICAgICAgICAgICAgIC8vIHtcclxuICAgICAgICAgICAgICAgICAgLy8gICBsYWJlbDogJ1ThuqV0IGPhuqMga2h1IHbhu7FjJyxcclxuXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgbmFtZTogYWxsVmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgIC8vICAgW2lzQWxsS2V5XTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgLy8gfSxcclxuICAgICAgICAgICAgICAgICAgLi4ucmVzLmRhdGEsXHJcbiAgICAgICAgICAgICAgICBdLm1hcChmb3JtYXRSZW5kZXIpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgZGF0YTogZGF0YSxcclxuICAgICAgICAgICAgICAgICAgdG90YWw6IChyZXMgYXMgYW55KT8ucGFnaW5hdGlvbj8udG90YWxFbGVtZW50cyxcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgICAgIG1lc3NhZ2UuZXJyb3IoJ0PDsyBs4buXaSB44bqjeSByYSwgdnVpIGzDsm5nIHRo4butIGzhuqFpJyk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgICAgICBkYXRhOiBbXSxcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBncmlkPXt7XHJcbiAgICAgICAgICAgICAgZ3V0dGVyOiAyMCxcclxuICAgICAgICAgICAgICB4czogMixcclxuICAgICAgICAgICAgICBtZDogMixcclxuICAgICAgICAgICAgICBsZzogMixcclxuICAgICAgICAgICAgICB4bDogMyxcclxuICAgICAgICAgICAgICB4eGw6IDMsXHJcbiAgICAgICAgICAgICAgY29sdW1uOiAyLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBtZXRhcz17e1xyXG4gICAgICAgICAgICAgIHRpdGxlOiB7fSxcclxuICAgICAgICAgICAgICBzdWJUaXRsZToge30sXHJcbiAgICAgICAgICAgICAgdHlwZToge30sXHJcbiAgICAgICAgICAgICAgYXZhdGFyOiB7XHJcbiAgICAgICAgICAgICAgICByZW5kZXIoZG9tLCBlbnRpdHksIGluZGV4LCBhY3Rpb24sIHNjaGVtYSkge1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm4gPEF2YXRhciBzcmM9e2VudGl0eS5hdmF0YXJ9IHNpemU9XCJsYXJnZVwiIHN0eWxlPXt7IG1hcmdpblJpZ2h0OiA3IH19IC8+O1xyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IHt9LFxyXG4gICAgICAgICAgICAgIGFjdGlvbnM6IHtcclxuICAgICAgICAgICAgICAgIGNhcmRBY3Rpb25Qcm9wczogJ2FjdGlvbnMnLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIHJvd0tleT17J25hbWUnfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0NoZWNrQ2FyZC5Hcm91cD5cclxuICAgICAgPC9DYXJkPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IExpc3Rab25lO1xyXG4iLCJpbXBvcnQgeyB1c2VJbnRsIH0gZnJvbSAnQHVtaWpzL21heCc7XHJcbmltcG9ydCB7IERpdmlkZXIgfSBmcm9tICdhbnRkJztcclxuaW1wb3J0IHsgRkMsIFJlYWN0Tm9kZSwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBMaXN0Q3JvcEluWm9uZSBmcm9tICcuL0xpc3RDcm9wSW5ab25lJztcclxuaW1wb3J0IExpc3Rab25lIGZyb20gJy4vTGlzdFpvbmUnO1xyXG5cclxuaW50ZXJmYWNlIFpvbmVJblByb2plY3RQcm9wcyB7XHJcbiAgY2hpbGRyZW4/OiBSZWFjdE5vZGU7XHJcbiAgcHJvamVjdElkPzogc3RyaW5nO1xyXG59XHJcblxyXG5jb25zdCBab25lSW5Qcm9qZWN0OiBGQzxab25lSW5Qcm9qZWN0UHJvcHM+ID0gKHsgY2hpbGRyZW4sIHByb2plY3RJZCB9KSA9PiB7XHJcbiAgY29uc3QgW3pvbmVJZCwgc2V0Wm9uZUlkXSA9IHVzZVN0YXRlPHVuZGVmaW5lZCB8IHN0cmluZz4oKTtcclxuICBjb25zdCB7IGZvcm1hdE1lc3NhZ2UgfSA9IHVzZUludGwoKTtcclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPExpc3Rab25lIHByb2plY3RJZD17cHJvamVjdElkfSBvbkl0ZW1DbGljaz17c2V0Wm9uZUlkfSAvPlxyXG4gICAgICA8RGl2aWRlciAvPlxyXG4gICAgICB7ISF6b25lSWQgJiYgKFxyXG4gICAgICAgIDxMaXN0Q3JvcEluWm9uZVxyXG4gICAgICAgICAgdGl0bGU9e2Zvcm1hdE1lc3NhZ2Uoe1xyXG4gICAgICAgICAgICBpZDogJ2NvbW1vbi5jcm9wX2xpc3QnLFxyXG4gICAgICAgICAgfSl9XHJcbiAgICAgICAgICB6b25lSWQ9e3pvbmVJZH1cclxuICAgICAgICAgIHByb2plY3RJZD17cHJvamVjdElkfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICl9XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgWm9uZUluUHJvamVjdDtcclxuIl0sIm5hbWVzIjpbImdldENyb3BNYW5hZ2VtZW50SW5mb0xpc3QiLCJSZWxvYWRPdXRsaW5lZCIsIlByb0xpc3QiLCJ1c2VJbnRsIiwidXNlUmVxdWVzdCIsIkFwcCIsIkJ1dHRvbiIsIkNhcmQiLCJMaXN0IiwidXNlQ2FsbGJhY2siLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwiR2VuZXJhbFNlYXNvbmFsQ2FyZCIsImpzeCIsIl9qc3giLCJMaXN0Q3JvcEluWm9uZSIsIl9yZWYiLCJ6b25lSWQiLCJwcm9qZWN0SWQiLCJ0aXRsZSIsIl91c2VJbnRsIiwiZm9ybWF0TWVzc2FnZSIsIl9BcHAkdXNlQXBwIiwidXNlQXBwIiwibWVzc2FnZSIsImFjdGlvblJlZiIsInF1ZXJ5UGFyYW1zIiwiem9uZV9pZCIsIl91c2VSZXF1ZXN0Iiwib25TdWNjZXNzIiwicmVzcG9uc2VEYXRhIiwib25FcnJvciIsIm1hbnVhbCIsImRhdGEiLCJsb2FkaW5nIiwicnVuIiwiaGFuZGxlUmVsb2FkIiwiYm9keVN0eWxlIiwicGFkZGluZ0lubGluZSIsImV4dHJhIiwiaWNvbiIsIm9uQ2xpY2siLCJjaGlsZHJlbiIsInJvd0tleSIsInJlbmRlckl0ZW0iLCJpdGVtIiwiSXRlbSIsIl9vYmplY3RTcHJlYWQiLCJncmlkIiwiZ3V0dGVyIiwieHMiLCJtZCIsImxnIiwieGwiLCJ4eGwiLCJjb2x1bW4iLCJkYXRhU291cmNlIiwicGFnaW5hdGlvbiIsInBhZ2VTaXplIiwiRE9DVFlQRV9FUlAiLCJERUZBVUxUX0ZBTExCQUNLX0lNRyIsIkFkZE5ld1pvbmUiLCJVcGRhdGVab25lIiwiem9uZUxpc3QiLCJnZW5Eb3dubG9hZFVybCIsIkNoZWNrQ2FyZCIsIkZvcm1hdHRlZE1lc3NhZ2UiLCJ1c2VEZWJvdW5jZUZuIiwiQXZhdGFyIiwiQ29sIiwiSW5wdXQiLCJSb3ciLCJTa2VsZXRvbiIsIlNwYWNlIiwiVGFnIiwibmFub2lkIiwiUmVhY3QiLCJ1c2VTdGF0ZSIsImpzeHMiLCJfanN4cyIsIkZyYWdtZW50IiwiX0ZyYWdtZW50IiwiaXNBbGxLZXkiLCJTeW1ib2wiLCJhbGxWYWx1ZSIsIlpvbmVDYXJkIiwiYXZhdGFyIiwibmFtZSIsImFjdGlvbnMiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMiLCJfZXhjbHVkZWQiLCJfYXN5bmNUb0dlbmVyYXRvciIsIl9yZWdlbmVyYXRvclJ1bnRpbWUiLCJtYXJrIiwiX2NhbGxlZSIsInJlcyIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJwYWdlIiwic2l6ZSIsImZpZWxkcyIsInNlbnQiLCJhYnJ1cHQiLCJ0b3RhbEVsZW1lbnRzIiwic3RvcCIsImVycm9yIiwic3R5bGUiLCJ3aWR0aCIsIm1pbkhlaWdodCIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwianVzdGlmeUNvbnRlbnQiLCJzcmMiLCJkZXNjcmlwdGlvbiIsInBhZGRpbmdUb3AiLCJjb2xvciIsImlkIiwidmFsdWUiLCJMaXN0Wm9uZSIsIl9yZWYzIiwib25JdGVtQ2xpY2siLCJfdXNlU3RhdGUiLCJfdXNlU3RhdGUyIiwiX3NsaWNlZFRvQXJyYXkiLCJkZWxldGVDb25maXJtVmlzaWJsZSIsInNldERlbGV0ZUNvbmZpcm1WaXNpYmxlIiwiX3VzZVN0YXRlMyIsIl91c2VTdGF0ZTQiLCJkZWxldGluZ1pvbmUiLCJzZXREZWxldGluZ1pvbmUiLCJfYWN0aW9uUmVmJGN1cnJlbnQiLCJjdXJyZW50IiwicmVsb2FkIiwic2hvd0RlbGV0ZUNvbmZpcm0iLCJ6b25lIiwiaGFuZGxlRGVsZXRlQ29uZmlybSIsImNvbnNvbGUiLCJsb2ciLCJmb3JtYXRSZW5kZXIiLCJpbWFnZSIsIndoaXRlU3BhY2UiLCJsYWJlbCIsImdhcCIsImFsaWduSXRlbXMiLCJtb2RhbFByb3BzIiwia2V5V29yZHNSZWYiLCJfdXNlRGVib3VuY2VGbiIsImUiLCJ0YXJnZXQiLCJ3YWl0Iiwib25TZWFyY2giLCJib3JkZXJlZCIsIm1hcmdpbkJvdHRvbSIsImp1c3RpZnkiLCJhbGlnbiIsInNwYW4iLCJmbGV4IiwiYWRkb25CZWZvcmUiLCJvbkNoYW5nZSIsInRleHRBbGlnbiIsImRlZmF1bHRWYWx1ZSIsInByb2plY3RfaWQiLCJHcm91cCIsInRhYmxlQWxlcnRSZW5kZXIiLCJfcmVmNCIsIl9zZWxlY3RlZFJvd3MkIiwic2VsZWN0ZWRSb3dzIiwib25DbGVhblNlbGVjdGVkIiwiaXRlbUNhcmRQcm9wcyIsImdob3N0Iiwic2hvd0FjdGlvbnMiLCJ0YWJsZUFsZXJ0T3B0aW9uUmVuZGVyIiwicm93U2VsZWN0aW9uIiwiY29sdW1uV2lkdGgiLCJzZWxlY3RlZFJvd0tleXMiLCJpbmZvIiwiYWN0aXZlUm93IiwiX2FjdGl2ZVJvdyRkYXRhIiwidHlwZSIsInJlcXVlc3QiLCJfcmVmNSIsIl9jYWxsZWUyIiwicGFyYW1zIiwiX3BhZ2luYXRpb24iLCJmaWx0ZXJzIiwiX2NhbGxlZTIkIiwiX2NvbnRleHQyIiwicHVzaCIsImlvdFpvbmUiLCJjb25jYXQiLCJvcmRlcl9ieSIsIl90b0NvbnN1bWFibGVBcnJheSIsIm1hcCIsInRvdGFsIiwidDAiLCJfeCIsImFwcGx5IiwiYXJndW1lbnRzIiwibWV0YXMiLCJzdWJUaXRsZSIsInJlbmRlciIsImRvbSIsImVudGl0eSIsImluZGV4IiwiYWN0aW9uIiwic2NoZW1hIiwibWFyZ2luUmlnaHQiLCJjb250ZW50IiwiY2FyZEFjdGlvblByb3BzIiwiRGl2aWRlciIsIlpvbmVJblByb2plY3QiLCJzZXRab25lSWQiXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///31454
`)}}]);
