"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1471],{11471:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`__webpack_require__.r(__webpack_exports__);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(38925);
/* harmony import */ var antd_use_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(38513);
/* harmony import */ var _components_CollapsibleInfoCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(68177);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(85893);




var useStyles = (0,antd_use_styles__WEBPACK_IMPORTED_MODULE_0__/* .createStyles */ .k)(function (_ref) {
  var token = _ref.token;
  return {
    wrapper: {
      display: 'flex',
      flexDirection: 'column',
      gap: token.margin
    }
  };
});
var CareInstructions = function CareInstructions(_ref2) {
  var children = _ref2.children,
    guides = _ref2.guides;
  var styles = useStyles();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)("div", {
    className: styles.wrapper,
    children: guides && guides.length ? guides.map(function (guide) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_components_CollapsibleInfoCard__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, {
        cardInfo: guide
      }, guide.name);
    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, {
      message: "C\\xE2y tr\\u1ED3ng ch\\u01B0a \\u0111\\u01B0\\u1EE3c th\\xEAm h\\u01B0\\u1EDBng d\\u1EABn",
      type: "info"
    })
  });
};
/* harmony default export */ __webpack_exports__["default"] = (CareInstructions);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///11471
`)}}]);
