"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1499],{5717:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var EyeOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z" } }] }, "name": "eye", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (EyeOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTcxNy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLG9CQUFvQixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQix3ZUFBd2UsR0FBRztBQUMvbkIsc0RBQWUsV0FBVyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZC5qcz80MTU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIEV5ZU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk05NDIuMiA0ODYuMkM4NDcuNCAyODYuNSA3MDQuMSAxODYgNTEyIDE4NmMtMTkyLjIgMC0zMzUuNCAxMDAuNS00MzAuMiAzMDAuM2E2MC4zIDYwLjMgMCAwMDAgNTEuNUMxNzYuNiA3MzcuNSAzMTkuOSA4MzggNTEyIDgzOGMxOTIuMiAwIDMzNS40LTEwMC41IDQzMC4yLTMwMC4zIDcuNy0xNi4yIDcuNy0zNSAwLTUxLjV6TTUxMiA3NjZjLTE2MS4zIDAtMjc5LjQtODEuOC0zNjIuNy0yNTRDMjMyLjYgMzM5LjggMzUwLjcgMjU4IDUxMiAyNThjMTYxLjMgMCAyNzkuNCA4MS44IDM2Mi43IDI1NEM3OTEuNSA2ODQuMiA2NzMuNCA3NjYgNTEyIDc2NnptLTQtNDMwYy05Ny4yIDAtMTc2IDc4LjgtMTc2IDE3NnM3OC44IDE3NiAxNzYgMTc2IDE3Ni03OC44IDE3Ni0xNzYtNzguOC0xNzYtMTc2LTE3NnptMCAyODhjLTYxLjkgMC0xMTItNTAuMS0xMTItMTEyczUwLjEtMTEyIDExMi0xMTIgMTEyIDUwLjEgMTEyIDExMi01MC4xIDExMi0xMTIgMTEyelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiZXllXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBFeWVPdXRsaW5lZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///5717
`)},11499:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ es_image; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(1208);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/css.js
var css = __webpack_require__(27678);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/common.js
var COMMON_PROPS = ['crossOrigin', 'decoding', 'draggable', 'loading', 'referrerPolicy', 'sizes', 'srcSet', 'useMap', 'alt'];
;// CONCATENATED MODULE: ./node_modules/rc-image/es/context.js

var PreviewGroupContext = /*#__PURE__*/react.createContext(null);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/useRegisterImage.js



var uid = 0;
function useRegisterImage(canPreview, data) {
  var _React$useState = react.useState(function () {
      uid += 1;
      return String(uid);
    }),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 1),
    id = _React$useState2[0];
  var groupContext = react.useContext(PreviewGroupContext);
  var registerData = {
    data: data,
    canPreview: canPreview
  };

  // Keep order start
  // Resolve https://github.com/ant-design/ant-design/issues/28881
  // Only need unRegister when component unMount
  react.useEffect(function () {
    if (groupContext) {
      return groupContext.register(id, registerData);
    }
  }, []);
  react.useEffect(function () {
    if (groupContext) {
      groupContext.register(id, registerData);
    }
  }, [canPreview, data]);
  return id;
}
;// CONCATENATED MODULE: ./node_modules/rc-image/es/util.js
function isImageValid(src) {
  return new Promise(function (resolve) {
    var img = document.createElement('img');
    img.onerror = function () {
      return resolve(false);
    };
    img.onload = function () {
      return resolve(true);
    };
    img.src = src;
  });
}
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/useStatus.js



function useStatus(_ref) {
  var src = _ref.src,
    isCustomPlaceholder = _ref.isCustomPlaceholder,
    fallback = _ref.fallback;
  var _useState = (0,react.useState)(isCustomPlaceholder ? 'loading' : 'normal'),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    status = _useState2[0],
    setStatus = _useState2[1];
  var isLoaded = (0,react.useRef)(false);
  var isError = status === 'error';

  // https://github.com/react-component/image/pull/187
  (0,react.useEffect)(function () {
    var isCurrentSrc = true;
    isImageValid(src).then(function (isValid) {
      // https://github.com/ant-design/ant-design/issues/44948
      // If src changes, the previous setStatus should not be triggered
      if (!isValid && isCurrentSrc) {
        setStatus('error');
      }
    });
    return function () {
      isCurrentSrc = false;
    };
  }, [src]);
  (0,react.useEffect)(function () {
    if (isCustomPlaceholder && !isLoaded.current) {
      setStatus('loading');
    } else if (isError) {
      setStatus('normal');
    }
  }, [src]);
  var onLoad = function onLoad() {
    setStatus('normal');
  };
  var getImgRef = function getImgRef(img) {
    isLoaded.current = false;
    if (status === 'loading' && img !== null && img !== void 0 && img.complete && (img.naturalWidth || img.naturalHeight)) {
      isLoaded.current = true;
      onLoad();
    }
  };
  var srcAndOnload = isError && fallback ? {
    src: fallback
  } : {
    onLoad: onLoad,
    src: src
  };
  return [getImgRef, srcAndOnload, status];
}
// EXTERNAL MODULE: ./node_modules/@rc-component/portal/es/index.js + 6 modules
var es = __webpack_require__(2788);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/context.js

var RefContext = /*#__PURE__*/react.createContext({});
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/contains.js
var contains = __webpack_require__(94999);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useId.js
var useId = __webpack_require__(7028);
// EXTERNAL MODULE: ./node_modules/rc-util/es/KeyCode.js
var KeyCode = __webpack_require__(15105);
// EXTERNAL MODULE: ./node_modules/rc-util/es/pickAttrs.js
var pickAttrs = __webpack_require__(64217);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/util.js
// =============================== Motion ===============================
function getMotionName(prefixCls, transitionName, animationName) {
  var motionName = transitionName;
  if (!motionName && animationName) {
    motionName = "".concat(prefixCls, "-").concat(animationName);
  }
  return motionName;
}

// =============================== Offset ===============================
function getScroll(w, top) {
  var ret = w["page".concat(top ? 'Y' : 'X', "Offset")];
  var method = "scroll".concat(top ? 'Top' : 'Left');
  if (typeof ret !== 'number') {
    var d = w.document;
    ret = d.documentElement[method];
    if (typeof ret !== 'number') {
      ret = d.body[method];
    }
  }
  return ret;
}
function offset(el) {
  var rect = el.getBoundingClientRect();
  var pos = {
    left: rect.left,
    top: rect.top
  };
  var doc = el.ownerDocument;
  var w = doc.defaultView || doc.parentWindow;
  pos.left += getScroll(w);
  pos.top += getScroll(w, true);
  return pos;
}
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(82225);
// EXTERNAL MODULE: ./node_modules/rc-util/es/ref.js
var es_ref = __webpack_require__(42550);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/Dialog/Content/MemoChildren.js

/* harmony default export */ var MemoChildren = (/*#__PURE__*/react.memo(function (_ref) {
  var children = _ref.children;
  return children;
}, function (_, _ref2) {
  var shouldUpdate = _ref2.shouldUpdate;
  return !shouldUpdate;
}));
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/Dialog/Content/Panel.js







var sentinelStyle = {
  width: 0,
  height: 0,
  overflow: 'hidden',
  outline: 'none'
};
var Panel = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var prefixCls = props.prefixCls,
    className = props.className,
    style = props.style,
    title = props.title,
    ariaId = props.ariaId,
    footer = props.footer,
    closable = props.closable,
    closeIcon = props.closeIcon,
    onClose = props.onClose,
    children = props.children,
    bodyStyle = props.bodyStyle,
    bodyProps = props.bodyProps,
    modalRender = props.modalRender,
    onMouseDown = props.onMouseDown,
    onMouseUp = props.onMouseUp,
    holderRef = props.holderRef,
    visible = props.visible,
    forceRender = props.forceRender,
    width = props.width,
    height = props.height,
    modalClassNames = props.classNames,
    modalStyles = props.styles;

  // ================================= Refs =================================
  var _React$useContext = react.useContext(RefContext),
    panelRef = _React$useContext.panel;
  var mergedRef = (0,es_ref/* useComposeRef */.x1)(holderRef, panelRef);
  var sentinelStartRef = (0,react.useRef)();
  var sentinelEndRef = (0,react.useRef)();
  react.useImperativeHandle(ref, function () {
    return {
      focus: function focus() {
        var _sentinelStartRef$cur;
        (_sentinelStartRef$cur = sentinelStartRef.current) === null || _sentinelStartRef$cur === void 0 || _sentinelStartRef$cur.focus();
      },
      changeActive: function changeActive(next) {
        var _document = document,
          activeElement = _document.activeElement;
        if (next && activeElement === sentinelEndRef.current) {
          sentinelStartRef.current.focus();
        } else if (!next && activeElement === sentinelStartRef.current) {
          sentinelEndRef.current.focus();
        }
      }
    };
  });

  // ================================ Style =================================
  var contentStyle = {};
  if (width !== undefined) {
    contentStyle.width = width;
  }
  if (height !== undefined) {
    contentStyle.height = height;
  }
  // ================================ Render ================================
  var footerNode;
  if (footer) {
    footerNode = /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-footer"), modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.footer),
      style: (0,objectSpread2/* default */.Z)({}, modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.footer)
    }, footer);
  }
  var headerNode;
  if (title) {
    headerNode = /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-header"), modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.header),
      style: (0,objectSpread2/* default */.Z)({}, modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.header)
    }, /*#__PURE__*/react.createElement("div", {
      className: "".concat(prefixCls, "-title"),
      id: ariaId
    }, title));
  }
  var closer;
  if (closable) {
    closer = /*#__PURE__*/react.createElement("button", {
      type: "button",
      onClick: onClose,
      "aria-label": "Close",
      className: "".concat(prefixCls, "-close")
    }, closeIcon || /*#__PURE__*/react.createElement("span", {
      className: "".concat(prefixCls, "-close-x")
    }));
  }
  var content = /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-content"), modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.content),
    style: modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.content
  }, closer, headerNode, /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
    className: classnames_default()("".concat(prefixCls, "-body"), modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.body),
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, bodyStyle), modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.body)
  }, bodyProps), children), footerNode);
  return /*#__PURE__*/react.createElement("div", {
    key: "dialog-element",
    role: "dialog",
    "aria-labelledby": title ? ariaId : null,
    "aria-modal": "true",
    ref: mergedRef,
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, style), contentStyle),
    className: classnames_default()(prefixCls, className),
    onMouseDown: onMouseDown,
    onMouseUp: onMouseUp
  }, /*#__PURE__*/react.createElement("div", {
    tabIndex: 0,
    ref: sentinelStartRef,
    style: sentinelStyle,
    "aria-hidden": "true"
  }), /*#__PURE__*/react.createElement(MemoChildren, {
    shouldUpdate: visible || forceRender
  }, modalRender ? modalRender(content) : content), /*#__PURE__*/react.createElement("div", {
    tabIndex: 0,
    ref: sentinelEndRef,
    style: sentinelStyle,
    "aria-hidden": "true"
  }));
});
if (false) {}
/* harmony default export */ var Content_Panel = (Panel);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/Dialog/Content/index.js









var Content = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var prefixCls = props.prefixCls,
    title = props.title,
    style = props.style,
    className = props.className,
    visible = props.visible,
    forceRender = props.forceRender,
    destroyOnClose = props.destroyOnClose,
    motionName = props.motionName,
    ariaId = props.ariaId,
    onVisibleChanged = props.onVisibleChanged,
    mousePosition = props.mousePosition;
  var dialogRef = (0,react.useRef)();

  // ============================= Style ==============================
  var _React$useState = react.useState(),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    transformOrigin = _React$useState2[0],
    setTransformOrigin = _React$useState2[1];
  var contentStyle = {};
  if (transformOrigin) {
    contentStyle.transformOrigin = transformOrigin;
  }
  function onPrepare() {
    var elementOffset = offset(dialogRef.current);
    setTransformOrigin(mousePosition ? "".concat(mousePosition.x - elementOffset.left, "px ").concat(mousePosition.y - elementOffset.top, "px") : '');
  }

  // ============================= Render =============================
  return /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, {
    visible: visible,
    onVisibleChanged: onVisibleChanged,
    onAppearPrepare: onPrepare,
    onEnterPrepare: onPrepare,
    forceRender: forceRender,
    motionName: motionName,
    removeOnLeave: destroyOnClose,
    ref: dialogRef
  }, function (_ref, motionRef) {
    var motionClassName = _ref.className,
      motionStyle = _ref.style;
    return /*#__PURE__*/react.createElement(Content_Panel, (0,esm_extends/* default */.Z)({}, props, {
      ref: ref,
      title: title,
      ariaId: ariaId,
      prefixCls: prefixCls,
      holderRef: motionRef,
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, motionStyle), style), contentStyle),
      className: classnames_default()(className, motionClassName)
    }));
  });
});
Content.displayName = 'Content';
/* harmony default export */ var Dialog_Content = (Content);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/Dialog/Mask.js





function Mask(props) {
  var prefixCls = props.prefixCls,
    style = props.style,
    visible = props.visible,
    maskProps = props.maskProps,
    motionName = props.motionName,
    className = props.className;
  return /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, {
    key: "mask",
    visible: visible,
    motionName: motionName,
    leavedClassName: "".concat(prefixCls, "-mask-hidden")
  }, function (_ref, ref) {
    var motionClassName = _ref.className,
      motionStyle = _ref.style;
    return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
      ref: ref,
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, motionStyle), style),
      className: classnames_default()("".concat(prefixCls, "-mask"), motionClassName, className)
    }, maskProps));
  });
}
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/Dialog/index.js














function Dialog(props) {
  var _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-dialog' : _props$prefixCls,
    zIndex = props.zIndex,
    _props$visible = props.visible,
    visible = _props$visible === void 0 ? false : _props$visible,
    _props$keyboard = props.keyboard,
    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,
    _props$focusTriggerAf = props.focusTriggerAfterClose,
    focusTriggerAfterClose = _props$focusTriggerAf === void 0 ? true : _props$focusTriggerAf,
    wrapStyle = props.wrapStyle,
    wrapClassName = props.wrapClassName,
    wrapProps = props.wrapProps,
    onClose = props.onClose,
    afterOpenChange = props.afterOpenChange,
    afterClose = props.afterClose,
    transitionName = props.transitionName,
    animation = props.animation,
    _props$closable = props.closable,
    closable = _props$closable === void 0 ? true : _props$closable,
    _props$mask = props.mask,
    mask = _props$mask === void 0 ? true : _props$mask,
    maskTransitionName = props.maskTransitionName,
    maskAnimation = props.maskAnimation,
    _props$maskClosable = props.maskClosable,
    maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable,
    maskStyle = props.maskStyle,
    maskProps = props.maskProps,
    rootClassName = props.rootClassName,
    modalClassNames = props.classNames,
    modalStyles = props.styles;
  if (false) {}
  var lastOutSideActiveElementRef = (0,react.useRef)();
  var wrapperRef = (0,react.useRef)();
  var contentRef = (0,react.useRef)();
  var _React$useState = react.useState(visible),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    animatedVisible = _React$useState2[0],
    setAnimatedVisible = _React$useState2[1];

  // ========================== Init ==========================
  var ariaId = (0,useId/* default */.Z)();
  function saveLastOutSideActiveElementRef() {
    if (!(0,contains/* default */.Z)(wrapperRef.current, document.activeElement)) {
      lastOutSideActiveElementRef.current = document.activeElement;
    }
  }
  function focusDialogContent() {
    if (!(0,contains/* default */.Z)(wrapperRef.current, document.activeElement)) {
      var _contentRef$current;
      (_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 || _contentRef$current.focus();
    }
  }

  // ========================= Events =========================
  function onDialogVisibleChanged(newVisible) {
    // Try to focus
    if (newVisible) {
      focusDialogContent();
    } else {
      // Clean up scroll bar & focus back
      setAnimatedVisible(false);
      if (mask && lastOutSideActiveElementRef.current && focusTriggerAfterClose) {
        try {
          lastOutSideActiveElementRef.current.focus({
            preventScroll: true
          });
        } catch (e) {
          // Do nothing
        }
        lastOutSideActiveElementRef.current = null;
      }

      // Trigger afterClose only when change visible from true to false
      if (animatedVisible) {
        afterClose === null || afterClose === void 0 || afterClose();
      }
    }
    afterOpenChange === null || afterOpenChange === void 0 || afterOpenChange(newVisible);
  }
  function onInternalClose(e) {
    onClose === null || onClose === void 0 || onClose(e);
  }

  // >>> Content
  var contentClickRef = (0,react.useRef)(false);
  var contentTimeoutRef = (0,react.useRef)();

  // We need record content click incase content popup out of dialog
  var onContentMouseDown = function onContentMouseDown() {
    clearTimeout(contentTimeoutRef.current);
    contentClickRef.current = true;
  };
  var onContentMouseUp = function onContentMouseUp() {
    contentTimeoutRef.current = setTimeout(function () {
      contentClickRef.current = false;
    });
  };

  // >>> Wrapper
  // Close only when element not on dialog
  var onWrapperClick = null;
  if (maskClosable) {
    onWrapperClick = function onWrapperClick(e) {
      if (contentClickRef.current) {
        contentClickRef.current = false;
      } else if (wrapperRef.current === e.target) {
        onInternalClose(e);
      }
    };
  }
  function onWrapperKeyDown(e) {
    if (keyboard && e.keyCode === KeyCode/* default */.Z.ESC) {
      e.stopPropagation();
      onInternalClose(e);
      return;
    }

    // keep focus inside dialog
    if (visible) {
      if (e.keyCode === KeyCode/* default */.Z.TAB) {
        contentRef.current.changeActive(!e.shiftKey);
      }
    }
  }

  // ========================= Effect =========================
  (0,react.useEffect)(function () {
    if (visible) {
      setAnimatedVisible(true);
      saveLastOutSideActiveElementRef();
    }
  }, [visible]);

  // Remove direct should also check the scroll bar update
  (0,react.useEffect)(function () {
    return function () {
      clearTimeout(contentTimeoutRef.current);
    };
  }, []);

  // ========================= Render =========================
  return /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
    className: classnames_default()("".concat(prefixCls, "-root"), rootClassName)
  }, (0,pickAttrs/* default */.Z)(props, {
    data: true
  })), /*#__PURE__*/react.createElement(Mask, {
    prefixCls: prefixCls,
    visible: mask && visible,
    motionName: getMotionName(prefixCls, maskTransitionName, maskAnimation),
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      zIndex: zIndex
    }, maskStyle), modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.mask),
    maskProps: maskProps,
    className: modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.mask
  }), /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({
    tabIndex: -1,
    onKeyDown: onWrapperKeyDown,
    className: classnames_default()("".concat(prefixCls, "-wrap"), wrapClassName, modalClassNames === null || modalClassNames === void 0 ? void 0 : modalClassNames.wrapper),
    ref: wrapperRef,
    onClick: onWrapperClick,
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      zIndex: zIndex
    }, wrapStyle), modalStyles === null || modalStyles === void 0 ? void 0 : modalStyles.wrapper), {}, {
      display: !animatedVisible ? 'none' : null
    })
  }, wrapProps), /*#__PURE__*/react.createElement(Dialog_Content, (0,esm_extends/* default */.Z)({}, props, {
    onMouseDown: onContentMouseDown,
    onMouseUp: onContentMouseUp,
    ref: contentRef,
    closable: closable,
    ariaId: ariaId,
    prefixCls: prefixCls,
    visible: visible && animatedVisible,
    onClose: onInternalClose,
    onVisibleChanged: onDialogVisibleChanged,
    motionName: getMotionName(prefixCls, transitionName, animation)
  }))));
}
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/DialogWrap.js






// fix issue #10656
/*
 * getContainer remarks
 * Custom container should not be return, because in the Portal component, it will remove the
 * return container element here, if the custom container is the only child of it's component,
 * like issue #10656, It will has a conflict with removeChild method in react-dom.
 * So here should add a child (div element) to custom container.
 * */
var DialogWrap = function DialogWrap(props) {
  var visible = props.visible,
    getContainer = props.getContainer,
    forceRender = props.forceRender,
    _props$destroyOnClose = props.destroyOnClose,
    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,
    _afterClose = props.afterClose,
    panelRef = props.panelRef;
  var _React$useState = react.useState(visible),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    animatedVisible = _React$useState2[0],
    setAnimatedVisible = _React$useState2[1];
  var refContext = react.useMemo(function () {
    return {
      panel: panelRef
    };
  }, [panelRef]);
  react.useEffect(function () {
    if (visible) {
      setAnimatedVisible(true);
    }
  }, [visible]);

  // Destroy on close will remove wrapped div
  if (!forceRender && destroyOnClose && !animatedVisible) {
    return null;
  }
  return /*#__PURE__*/react.createElement(RefContext.Provider, {
    value: refContext
  }, /*#__PURE__*/react.createElement(es/* default */.Z, {
    open: visible || forceRender || animatedVisible,
    autoDestroy: false,
    getContainer: getContainer,
    autoLock: visible || animatedVisible
  }, /*#__PURE__*/react.createElement(Dialog, (0,esm_extends/* default */.Z)({}, props, {
    destroyOnClose: destroyOnClose,
    afterClose: function afterClose() {
      _afterClose === null || _afterClose === void 0 || _afterClose();
      setAnimatedVisible(false);
    }
  }))));
};
DialogWrap.displayName = 'Dialog';
/* harmony default export */ var es_DialogWrap = (DialogWrap);
;// CONCATENATED MODULE: ./node_modules/rc-image/node_modules/rc-dialog/es/index.js



/* harmony default export */ var rc_dialog_es = (es_DialogWrap);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/addEventListener.js
var addEventListener = __webpack_require__(64019);
// EXTERNAL MODULE: ./node_modules/rc-util/es/isEqual.js
var isEqual = __webpack_require__(91881);
// EXTERNAL MODULE: ./node_modules/rc-util/es/raf.js
var raf = __webpack_require__(75164);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/useImageTransform.js






var initialTransform = {
  x: 0,
  y: 0,
  rotate: 0,
  scale: 1,
  flipX: false,
  flipY: false
};
function useImageTransform(imgRef, minScale, maxScale, onTransform) {
  var frame = (0,react.useRef)(null);
  var queue = (0,react.useRef)([]);
  var _useState = (0,react.useState)(initialTransform),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    transform = _useState2[0],
    setTransform = _useState2[1];
  var resetTransform = function resetTransform(action) {
    setTransform(initialTransform);
    if (onTransform && !(0,isEqual/* default */.Z)(initialTransform, transform)) {
      onTransform({
        transform: initialTransform,
        action: action
      });
    }
  };

  /** Direct update transform */
  var updateTransform = function updateTransform(newTransform, action) {
    if (frame.current === null) {
      queue.current = [];
      frame.current = (0,raf/* default */.Z)(function () {
        setTransform(function (preState) {
          var memoState = preState;
          queue.current.forEach(function (queueState) {
            memoState = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, memoState), queueState);
          });
          frame.current = null;
          onTransform === null || onTransform === void 0 || onTransform({
            transform: memoState,
            action: action
          });
          return memoState;
        });
      });
    }
    queue.current.push((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, transform), newTransform));
  };

  /** Scale according to the position of centerX and centerY */
  var dispatchZoomChange = function dispatchZoomChange(ratio, action, centerX, centerY, isTouch) {
    var _imgRef$current = imgRef.current,
      width = _imgRef$current.width,
      height = _imgRef$current.height,
      offsetWidth = _imgRef$current.offsetWidth,
      offsetHeight = _imgRef$current.offsetHeight,
      offsetLeft = _imgRef$current.offsetLeft,
      offsetTop = _imgRef$current.offsetTop;
    var newRatio = ratio;
    var newScale = transform.scale * ratio;
    if (newScale > maxScale) {
      newScale = maxScale;
      newRatio = maxScale / transform.scale;
    } else if (newScale < minScale) {
      // For mobile interactions, allow scaling down to the minimum scale.
      newScale = isTouch ? newScale : minScale;
      newRatio = newScale / transform.scale;
    }

    /** Default center point scaling */
    var mergedCenterX = centerX !== null && centerX !== void 0 ? centerX : innerWidth / 2;
    var mergedCenterY = centerY !== null && centerY !== void 0 ? centerY : innerHeight / 2;
    var diffRatio = newRatio - 1;
    /** Deviation calculated from image size */
    var diffImgX = diffRatio * width * 0.5;
    var diffImgY = diffRatio * height * 0.5;
    /** The difference between the click position and the edge of the document */
    var diffOffsetLeft = diffRatio * (mergedCenterX - transform.x - offsetLeft);
    var diffOffsetTop = diffRatio * (mergedCenterY - transform.y - offsetTop);
    /** Final positioning */
    var newX = transform.x - (diffOffsetLeft - diffImgX);
    var newY = transform.y - (diffOffsetTop - diffImgY);

    /**
     * When zooming the image
     * When the image size is smaller than the width and height of the window, the position is initialized
     */
    if (ratio < 1 && newScale === 1) {
      var mergedWidth = offsetWidth * newScale;
      var mergedHeight = offsetHeight * newScale;
      var _getClientSize = (0,css/* getClientSize */.g1)(),
        clientWidth = _getClientSize.width,
        clientHeight = _getClientSize.height;
      if (mergedWidth <= clientWidth && mergedHeight <= clientHeight) {
        newX = 0;
        newY = 0;
      }
    }
    updateTransform({
      x: newX,
      y: newY,
      scale: newScale
    }, action);
  };
  return {
    transform: transform,
    resetTransform: resetTransform,
    updateTransform: updateTransform,
    dispatchZoomChange: dispatchZoomChange
  };
}
;// CONCATENATED MODULE: ./node_modules/rc-image/es/getFixScaleEleTransPosition.js



function fixPoint(key, start, width, clientWidth) {
  var startAddWidth = start + width;
  var offsetStart = (width - clientWidth) / 2;
  if (width > clientWidth) {
    if (start > 0) {
      return (0,defineProperty/* default */.Z)({}, key, offsetStart);
    }
    if (start < 0 && startAddWidth < clientWidth) {
      return (0,defineProperty/* default */.Z)({}, key, -offsetStart);
    }
  } else if (start < 0 || startAddWidth > clientWidth) {
    return (0,defineProperty/* default */.Z)({}, key, start < 0 ? offsetStart : -offsetStart);
  }
  return {};
}

/**
 * Fix positon x,y point when
 *
 * Ele width && height < client
 * - Back origin
 *
 * - Ele width | height > clientWidth | clientHeight
 * - left | top > 0 -> Back 0
 * - left | top + width | height < clientWidth | clientHeight -> Back left | top + width | height === clientWidth | clientHeight
 *
 * Regardless of other
 */
function getFixScaleEleTransPosition(width, height, left, top) {
  var _getClientSize = (0,css/* getClientSize */.g1)(),
    clientWidth = _getClientSize.width,
    clientHeight = _getClientSize.height;
  var fixPos = null;
  if (width <= clientWidth && height <= clientHeight) {
    fixPos = {
      x: 0,
      y: 0
    };
  } else if (width > clientWidth || height > clientHeight) {
    fixPos = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, fixPoint('x', left, width, clientWidth)), fixPoint('y', top, height, clientHeight));
  }
  return fixPos;
}
;// CONCATENATED MODULE: ./node_modules/rc-image/es/previewConfig.js
/** Scale the ratio base */
var BASE_SCALE_RATIO = 1;
/** The maximum zoom ratio when the mouse zooms in, adjustable */
var WHEEL_MAX_SCALE_RATIO = 1;
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/useMouseEvent.js







function useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange) {
  var rotate = transform.rotate,
    scale = transform.scale,
    x = transform.x,
    y = transform.y;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    isMoving = _useState2[0],
    setMoving = _useState2[1];
  var startPositionInfo = (0,react.useRef)({
    diffX: 0,
    diffY: 0,
    transformX: 0,
    transformY: 0
  });
  var onMouseDown = function onMouseDown(event) {
    // Only allow main button
    if (!movable || event.button !== 0) return;
    event.preventDefault();
    event.stopPropagation();
    startPositionInfo.current = {
      diffX: event.pageX - x,
      diffY: event.pageY - y,
      transformX: x,
      transformY: y
    };
    setMoving(true);
  };
  var onMouseMove = function onMouseMove(event) {
    if (visible && isMoving) {
      updateTransform({
        x: event.pageX - startPositionInfo.current.diffX,
        y: event.pageY - startPositionInfo.current.diffY
      }, 'move');
    }
  };
  var onMouseUp = function onMouseUp() {
    if (visible && isMoving) {
      setMoving(false);

      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */
      var _startPositionInfo$cu = startPositionInfo.current,
        transformX = _startPositionInfo$cu.transformX,
        transformY = _startPositionInfo$cu.transformY;
      var hasChangedPosition = x !== transformX && y !== transformY;
      if (!hasChangedPosition) return;
      var width = imgRef.current.offsetWidth * scale;
      var height = imgRef.current.offsetHeight * scale;
      // eslint-disable-next-line @typescript-eslint/no-shadow
      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),
        left = _imgRef$current$getBo.left,
        top = _imgRef$current$getBo.top;
      var isRotate = rotate % 180 !== 0;
      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);
      if (fixState) {
        updateTransform((0,objectSpread2/* default */.Z)({}, fixState), 'dragRebound');
      }
    }
  };
  var onWheel = function onWheel(event) {
    if (!visible || event.deltaY == 0) return;
    // Scale ratio depends on the deltaY size
    var scaleRatio = Math.abs(event.deltaY / 100);
    // Limit the maximum scale ratio
    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);
    // Scale the ratio each time
    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;
    if (event.deltaY > 0) {
      ratio = BASE_SCALE_RATIO / ratio;
    }
    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);
  };
  (0,react.useEffect)(function () {
    var onTopMouseUpListener;
    var onTopMouseMoveListener;
    var onMouseUpListener;
    var onMouseMoveListener;
    if (movable) {
      onMouseUpListener = (0,addEventListener/* default */.Z)(window, 'mouseup', onMouseUp, false);
      onMouseMoveListener = (0,addEventListener/* default */.Z)(window, 'mousemove', onMouseMove, false);
      try {
        // Resolve if in iframe lost event
        /* istanbul ignore next */
        if (window.top !== window.self) {
          onTopMouseUpListener = (0,addEventListener/* default */.Z)(window.top, 'mouseup', onMouseUp, false);
          onTopMouseMoveListener = (0,addEventListener/* default */.Z)(window.top, 'mousemove', onMouseMove, false);
        }
      } catch (error) {
        /* istanbul ignore next */
        (0,warning/* warning */.Kp)(false, "[rc-image] ".concat(error));
      }
    }
    return function () {
      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;
      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 || _onMouseUpListener.remove();
      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 || _onMouseMoveListener.remove();
      /* istanbul ignore next */
      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 || _onTopMouseUpListener.remove();
      /* istanbul ignore next */
      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 || _onTopMouseMoveListen.remove();
    };
  }, [visible, isMoving, x, y, rotate, movable]);
  return {
    isMoving: isMoving,
    onMouseDown: onMouseDown,
    onMouseMove: onMouseMove,
    onMouseUp: onMouseUp,
    onWheel: onWheel
  };
}
;
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/useTouchEvent.js





function getDistance(a, b) {
  var x = a.x - b.x;
  var y = a.y - b.y;
  return Math.hypot(x, y);
}
function getCenter(oldPoint1, oldPoint2, newPoint1, newPoint2) {
  // Calculate the distance each point has moved
  var distance1 = getDistance(oldPoint1, newPoint1);
  var distance2 = getDistance(oldPoint2, newPoint2);

  // If both distances are 0, return the original points
  if (distance1 === 0 && distance2 === 0) {
    return [oldPoint1.x, oldPoint1.y];
  }

  // Calculate the ratio of the distances
  var ratio = distance1 / (distance1 + distance2);

  // Calculate the new center point based on the ratio
  var x = oldPoint1.x + ratio * (oldPoint2.x - oldPoint1.x);
  var y = oldPoint1.y + ratio * (oldPoint2.y - oldPoint1.y);
  return [x, y];
}
function useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange) {
  var rotate = transform.rotate,
    scale = transform.scale,
    x = transform.x,
    y = transform.y;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    isTouching = _useState2[0],
    setIsTouching = _useState2[1];
  var touchPointInfo = (0,react.useRef)({
    point1: {
      x: 0,
      y: 0
    },
    point2: {
      x: 0,
      y: 0
    },
    eventType: 'none'
  });
  var updateTouchPointInfo = function updateTouchPointInfo(values) {
    touchPointInfo.current = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, touchPointInfo.current), values);
  };
  var onTouchStart = function onTouchStart(event) {
    if (!movable) return;
    event.stopPropagation();
    setIsTouching(true);
    var _event$touches = event.touches,
      touches = _event$touches === void 0 ? [] : _event$touches;
    if (touches.length > 1) {
      // touch zoom
      updateTouchPointInfo({
        point1: {
          x: touches[0].clientX,
          y: touches[0].clientY
        },
        point2: {
          x: touches[1].clientX,
          y: touches[1].clientY
        },
        eventType: 'touchZoom'
      });
    } else {
      // touch move
      updateTouchPointInfo({
        point1: {
          x: touches[0].clientX - x,
          y: touches[0].clientY - y
        },
        eventType: 'move'
      });
    }
  };
  var onTouchMove = function onTouchMove(event) {
    var _event$touches2 = event.touches,
      touches = _event$touches2 === void 0 ? [] : _event$touches2;
    var _touchPointInfo$curre = touchPointInfo.current,
      point1 = _touchPointInfo$curre.point1,
      point2 = _touchPointInfo$curre.point2,
      eventType = _touchPointInfo$curre.eventType;
    if (touches.length > 1 && eventType === 'touchZoom') {
      // touch zoom
      var newPoint1 = {
        x: touches[0].clientX,
        y: touches[0].clientY
      };
      var newPoint2 = {
        x: touches[1].clientX,
        y: touches[1].clientY
      };
      var _getCenter = getCenter(point1, point2, newPoint1, newPoint2),
        _getCenter2 = (0,slicedToArray/* default */.Z)(_getCenter, 2),
        centerX = _getCenter2[0],
        centerY = _getCenter2[1];
      var ratio = getDistance(newPoint1, newPoint2) / getDistance(point1, point2);
      dispatchZoomChange(ratio, 'touchZoom', centerX, centerY, true);
      updateTouchPointInfo({
        point1: newPoint1,
        point2: newPoint2,
        eventType: 'touchZoom'
      });
    } else if (eventType === 'move') {
      // touch move
      updateTransform({
        x: touches[0].clientX - point1.x,
        y: touches[0].clientY - point1.y
      }, 'move');
      updateTouchPointInfo({
        eventType: 'move'
      });
    }
  };
  var onTouchEnd = function onTouchEnd() {
    if (!visible) return;
    if (isTouching) {
      setIsTouching(false);
    }
    updateTouchPointInfo({
      eventType: 'none'
    });
    if (minScale > scale) {
      /** When the scaling ratio is less than the minimum scaling ratio, reset the scaling ratio */
      return updateTransform({
        x: 0,
        y: 0,
        scale: minScale
      }, 'touchZoom');
    }
    var width = imgRef.current.offsetWidth * scale;
    var height = imgRef.current.offsetHeight * scale;
    // eslint-disable-next-line @typescript-eslint/no-shadow
    var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),
      left = _imgRef$current$getBo.left,
      top = _imgRef$current$getBo.top;
    var isRotate = rotate % 180 !== 0;
    var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);
    if (fixState) {
      updateTransform((0,objectSpread2/* default */.Z)({}, fixState), 'dragRebound');
    }
  };
  (0,react.useEffect)(function () {
    var onTouchMoveListener;
    if (visible && movable) {
      onTouchMoveListener = (0,addEventListener/* default */.Z)(window, 'touchmove', function (e) {
        return e.preventDefault();
      }, {
        passive: false
      });
    }
    return function () {
      var _onTouchMoveListener;
      (_onTouchMoveListener = onTouchMoveListener) === null || _onTouchMoveListener === void 0 || _onTouchMoveListener.remove();
    };
  }, [visible, movable]);
  return {
    isTouching: isTouching,
    onTouchStart: onTouchStart,
    onTouchMove: onTouchMove,
    onTouchEnd: onTouchEnd
  };
}
;
;// CONCATENATED MODULE: ./node_modules/rc-image/es/Operations.js









var Operations = function Operations(props) {
  var visible = props.visible,
    maskTransitionName = props.maskTransitionName,
    getContainer = props.getContainer,
    prefixCls = props.prefixCls,
    rootClassName = props.rootClassName,
    icons = props.icons,
    countRender = props.countRender,
    showSwitch = props.showSwitch,
    showProgress = props.showProgress,
    current = props.current,
    transform = props.transform,
    count = props.count,
    scale = props.scale,
    minScale = props.minScale,
    maxScale = props.maxScale,
    closeIcon = props.closeIcon,
    onSwitchLeft = props.onSwitchLeft,
    onSwitchRight = props.onSwitchRight,
    onClose = props.onClose,
    onZoomIn = props.onZoomIn,
    onZoomOut = props.onZoomOut,
    onRotateRight = props.onRotateRight,
    onRotateLeft = props.onRotateLeft,
    onFlipX = props.onFlipX,
    onFlipY = props.onFlipY,
    toolbarRender = props.toolbarRender,
    zIndex = props.zIndex;
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var rotateLeft = icons.rotateLeft,
    rotateRight = icons.rotateRight,
    zoomIn = icons.zoomIn,
    zoomOut = icons.zoomOut,
    close = icons.close,
    left = icons.left,
    right = icons.right,
    flipX = icons.flipX,
    flipY = icons.flipY;
  var toolClassName = "".concat(prefixCls, "-operations-operation");
  react.useEffect(function () {
    var onKeyDown = function onKeyDown(e) {
      if (e.keyCode === KeyCode/* default */.Z.ESC) {
        onClose();
      }
    };
    if (visible) {
      window.addEventListener('keydown', onKeyDown);
    }
    return function () {
      window.removeEventListener('keydown', onKeyDown);
    };
  }, [visible]);
  var tools = [{
    icon: flipY,
    onClick: onFlipY,
    type: 'flipY'
  }, {
    icon: flipX,
    onClick: onFlipX,
    type: 'flipX'
  }, {
    icon: rotateLeft,
    onClick: onRotateLeft,
    type: 'rotateLeft'
  }, {
    icon: rotateRight,
    onClick: onRotateRight,
    type: 'rotateRight'
  }, {
    icon: zoomOut,
    onClick: onZoomOut,
    type: 'zoomOut',
    disabled: scale <= minScale
  }, {
    icon: zoomIn,
    onClick: onZoomIn,
    type: 'zoomIn',
    disabled: scale === maxScale
  }];
  var toolsNode = tools.map(function (_ref) {
    var _classnames;
    var icon = _ref.icon,
      onClick = _ref.onClick,
      type = _ref.type,
      disabled = _ref.disabled;
    return /*#__PURE__*/react.createElement("div", {
      className: classnames_default()(toolClassName, (_classnames = {}, (0,defineProperty/* default */.Z)(_classnames, "".concat(prefixCls, "-operations-operation-").concat(type), true), (0,defineProperty/* default */.Z)(_classnames, "".concat(prefixCls, "-operations-operation-disabled"), !!disabled), _classnames)),
      onClick: onClick,
      key: type
    }, icon);
  });
  var toolbarNode = /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-operations")
  }, toolsNode);
  return /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, {
    visible: visible,
    motionName: maskTransitionName
  }, function (_ref2) {
    var className = _ref2.className,
      style = _ref2.style;
    return /*#__PURE__*/react.createElement(es/* default */.Z, {
      open: true,
      getContainer: getContainer !== null && getContainer !== void 0 ? getContainer : document.body
    }, /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-operations-wrapper"), className, rootClassName),
      style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, style), {}, {
        zIndex: zIndex
      })
    }, closeIcon === null ? null : /*#__PURE__*/react.createElement("button", {
      className: "".concat(prefixCls, "-close"),
      onClick: onClose
    }, closeIcon || close), showSwitch && /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-switch-left"), (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-switch-left-disabled"), current === 0)),
      onClick: onSwitchLeft
    }, left), /*#__PURE__*/react.createElement("div", {
      className: classnames_default()("".concat(prefixCls, "-switch-right"), (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-switch-right-disabled"), current === count - 1)),
      onClick: onSwitchRight
    }, right)), /*#__PURE__*/react.createElement("div", {
      className: "".concat(prefixCls, "-footer")
    }, showProgress && /*#__PURE__*/react.createElement("div", {
      className: "".concat(prefixCls, "-progress")
    }, countRender ? countRender(current + 1, count) : "".concat(current + 1, " / ").concat(count)), toolbarRender ? toolbarRender(toolbarNode, (0,objectSpread2/* default */.Z)({
      icons: {
        flipYIcon: toolsNode[0],
        flipXIcon: toolsNode[1],
        rotateLeftIcon: toolsNode[2],
        rotateRightIcon: toolsNode[3],
        zoomOutIcon: toolsNode[4],
        zoomInIcon: toolsNode[5]
      },
      actions: {
        onFlipY: onFlipY,
        onFlipX: onFlipX,
        onRotateLeft: onRotateLeft,
        onRotateRight: onRotateRight,
        onZoomOut: onZoomOut,
        onZoomIn: onZoomIn
      },
      transform: transform
    }, groupContext ? {
      current: current,
      total: count
    } : {})) : toolbarNode)));
  });
};
/* harmony default export */ var es_Operations = (Operations);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/Preview.js





var _excluded = ["fallback", "src", "imgRef"],
  _excluded2 = ["prefixCls", "src", "alt", "fallback", "movable", "onClose", "visible", "icons", "rootClassName", "closeIcon", "getContainer", "current", "count", "countRender", "scaleStep", "minScale", "maxScale", "transitionName", "maskTransitionName", "imageRender", "imgCommonProps", "toolbarRender", "onTransform", "onChange"];












var PreviewImage = function PreviewImage(_ref) {
  var fallback = _ref.fallback,
    src = _ref.src,
    imgRef = _ref.imgRef,
    props = (0,objectWithoutProperties/* default */.Z)(_ref, _excluded);
  var _useStatus = useStatus({
      src: src,
      fallback: fallback
    }),
    _useStatus2 = (0,slicedToArray/* default */.Z)(_useStatus, 2),
    getImgRef = _useStatus2[0],
    srcAndOnload = _useStatus2[1];
  return /*#__PURE__*/react.createElement("img", (0,esm_extends/* default */.Z)({
    ref: function ref(_ref2) {
      imgRef.current = _ref2;
      getImgRef(_ref2);
    }
  }, props, srcAndOnload));
};
var Preview = function Preview(props) {
  var prefixCls = props.prefixCls,
    src = props.src,
    alt = props.alt,
    fallback = props.fallback,
    _props$movable = props.movable,
    movable = _props$movable === void 0 ? true : _props$movable,
    onClose = props.onClose,
    visible = props.visible,
    _props$icons = props.icons,
    icons = _props$icons === void 0 ? {} : _props$icons,
    rootClassName = props.rootClassName,
    closeIcon = props.closeIcon,
    getContainer = props.getContainer,
    _props$current = props.current,
    current = _props$current === void 0 ? 0 : _props$current,
    _props$count = props.count,
    count = _props$count === void 0 ? 1 : _props$count,
    countRender = props.countRender,
    _props$scaleStep = props.scaleStep,
    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,
    _props$minScale = props.minScale,
    minScale = _props$minScale === void 0 ? 1 : _props$minScale,
    _props$maxScale = props.maxScale,
    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,
    _props$transitionName = props.transitionName,
    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,
    _props$maskTransition = props.maskTransitionName,
    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,
    imageRender = props.imageRender,
    imgCommonProps = props.imgCommonProps,
    toolbarRender = props.toolbarRender,
    onTransform = props.onTransform,
    onChange = props.onChange,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded2);
  var imgRef = (0,react.useRef)();
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var showLeftOrRightSwitches = groupContext && count > 1;
  var showOperationsProgress = groupContext && count >= 1;
  var _useState = (0,react.useState)(true),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    enableTransition = _useState2[0],
    setEnableTransition = _useState2[1];
  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),
    transform = _useImageTransform.transform,
    resetTransform = _useImageTransform.resetTransform,
    updateTransform = _useImageTransform.updateTransform,
    dispatchZoomChange = _useImageTransform.dispatchZoomChange;
  var _useMouseEvent = useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange),
    isMoving = _useMouseEvent.isMoving,
    onMouseDown = _useMouseEvent.onMouseDown,
    onWheel = _useMouseEvent.onWheel;
  var _useTouchEvent = useTouchEvent(imgRef, movable, visible, minScale, transform, updateTransform, dispatchZoomChange),
    isTouching = _useTouchEvent.isTouching,
    onTouchStart = _useTouchEvent.onTouchStart,
    onTouchMove = _useTouchEvent.onTouchMove,
    onTouchEnd = _useTouchEvent.onTouchEnd;
  var rotate = transform.rotate,
    scale = transform.scale;
  var wrapClassName = classnames_default()((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-moving"), isMoving));
  (0,react.useEffect)(function () {
    if (!enableTransition) {
      setEnableTransition(true);
    }
  }, [enableTransition]);
  var onAfterClose = function onAfterClose() {
    resetTransform('close');
  };
  var onZoomIn = function onZoomIn() {
    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');
  };
  var onZoomOut = function onZoomOut() {
    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');
  };
  var onRotateRight = function onRotateRight() {
    updateTransform({
      rotate: rotate + 90
    }, 'rotateRight');
  };
  var onRotateLeft = function onRotateLeft() {
    updateTransform({
      rotate: rotate - 90
    }, 'rotateLeft');
  };
  var onFlipX = function onFlipX() {
    updateTransform({
      flipX: !transform.flipX
    }, 'flipX');
  };
  var onFlipY = function onFlipY() {
    updateTransform({
      flipY: !transform.flipY
    }, 'flipY');
  };
  var onSwitchLeft = function onSwitchLeft(event) {
    event === null || event === void 0 || event.preventDefault();
    event === null || event === void 0 || event.stopPropagation();
    if (current > 0) {
      setEnableTransition(false);
      resetTransform('prev');
      onChange === null || onChange === void 0 || onChange(current - 1, current);
    }
  };
  var onSwitchRight = function onSwitchRight(event) {
    event === null || event === void 0 || event.preventDefault();
    event === null || event === void 0 || event.stopPropagation();
    if (current < count - 1) {
      setEnableTransition(false);
      resetTransform('next');
      onChange === null || onChange === void 0 || onChange(current + 1, current);
    }
  };
  var onKeyDown = function onKeyDown(event) {
    if (!visible || !showLeftOrRightSwitches) return;
    if (event.keyCode === KeyCode/* default */.Z.LEFT) {
      onSwitchLeft();
    } else if (event.keyCode === KeyCode/* default */.Z.RIGHT) {
      onSwitchRight();
    }
  };
  var onDoubleClick = function onDoubleClick(event) {
    if (visible) {
      if (scale !== 1) {
        updateTransform({
          x: 0,
          y: 0,
          scale: 1
        }, 'doubleClick');
      } else {
        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);
      }
    }
  };
  (0,react.useEffect)(function () {
    var onKeyDownListener = (0,addEventListener/* default */.Z)(window, 'keydown', onKeyDown, false);
    return function () {
      onKeyDownListener.remove();
    };
  }, [visible, showLeftOrRightSwitches, current]);
  var imgNode = /*#__PURE__*/react.createElement(PreviewImage, (0,esm_extends/* default */.Z)({}, imgCommonProps, {
    width: props.width,
    height: props.height,
    imgRef: imgRef,
    className: "".concat(prefixCls, "-img"),
    alt: alt,
    style: {
      transform: "translate3d(".concat(transform.x, "px, ").concat(transform.y, "px, 0) scale3d(").concat(transform.flipX ? '-' : '').concat(scale, ", ").concat(transform.flipY ? '-' : '').concat(scale, ", 1) rotate(").concat(rotate, "deg)"),
      transitionDuration: (!enableTransition || isTouching) && '0s'
    },
    fallback: fallback,
    src: src,
    onWheel: onWheel,
    onMouseDown: onMouseDown,
    onDoubleClick: onDoubleClick,
    onTouchStart: onTouchStart,
    onTouchMove: onTouchMove,
    onTouchEnd: onTouchEnd,
    onTouchCancel: onTouchEnd
  }));
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(rc_dialog_es, (0,esm_extends/* default */.Z)({
    transitionName: transitionName,
    maskTransitionName: maskTransitionName,
    closable: false,
    keyboard: true,
    prefixCls: prefixCls,
    onClose: onClose,
    visible: visible,
    classNames: {
      wrapper: wrapClassName
    },
    rootClassName: rootClassName,
    getContainer: getContainer
  }, restProps, {
    afterClose: onAfterClose
  }), /*#__PURE__*/react.createElement("div", {
    className: "".concat(prefixCls, "-img-wrapper")
  }, imageRender ? imageRender(imgNode, (0,objectSpread2/* default */.Z)({
    transform: transform
  }, groupContext ? {
    current: current
  } : {})) : imgNode)), /*#__PURE__*/react.createElement(es_Operations, {
    visible: visible,
    transform: transform,
    maskTransitionName: maskTransitionName,
    closeIcon: closeIcon,
    getContainer: getContainer,
    prefixCls: prefixCls,
    rootClassName: rootClassName,
    icons: icons,
    countRender: countRender,
    showSwitch: showLeftOrRightSwitches,
    showProgress: showOperationsProgress,
    current: current,
    count: count,
    scale: scale,
    minScale: minScale,
    maxScale: maxScale,
    toolbarRender: toolbarRender,
    onSwitchLeft: onSwitchLeft,
    onSwitchRight: onSwitchRight,
    onZoomIn: onZoomIn,
    onZoomOut: onZoomOut,
    onRotateRight: onRotateRight,
    onRotateLeft: onRotateLeft,
    onFlipX: onFlipX,
    onFlipY: onFlipY,
    onClose: onClose,
    zIndex: restProps.zIndex !== undefined ? restProps.zIndex + 1 : undefined
  }));
};
/* harmony default export */ var es_Preview = (Preview);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/hooks/usePreviewItems.js






/**
 * Merge props provided \`items\` or context collected images
 */
function usePreviewItems(items) {
  // Context collection image data
  var _React$useState = react.useState({}),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    images = _React$useState2[0],
    setImages = _React$useState2[1];
  var registerImage = react.useCallback(function (id, data) {
    setImages(function (imgs) {
      return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, imgs), {}, (0,defineProperty/* default */.Z)({}, id, data));
    });
    return function () {
      setImages(function (imgs) {
        var cloneImgs = (0,objectSpread2/* default */.Z)({}, imgs);
        delete cloneImgs[id];
        return cloneImgs;
      });
    };
  }, []);

  // items
  var mergedItems = react.useMemo(function () {
    if (items) {
      return items.map(function (item) {
        if (typeof item === 'string') {
          return {
            data: {
              src: item
            }
          };
        }
        var data = {};
        Object.keys(item).forEach(function (key) {
          if (['src'].concat((0,toConsumableArray/* default */.Z)(COMMON_PROPS)).includes(key)) {
            data[key] = item[key];
          }
        });
        return {
          data: data
        };
      });
    }
    return Object.keys(images).reduce(function (total, id) {
      var _images$id = images[id],
        canPreview = _images$id.canPreview,
        data = _images$id.data;
      if (canPreview) {
        total.push({
          data: data,
          id: id
        });
      }
      return total;
    }, []);
  }, [items, images]);
  return [mergedItems, registerImage];
}
;// CONCATENATED MODULE: ./node_modules/rc-image/es/PreviewGroup.js




var PreviewGroup_excluded = ["visible", "onVisibleChange", "getContainer", "current", "movable", "minScale", "maxScale", "countRender", "closeIcon", "onChange", "onTransform", "toolbarRender", "imageRender"],
  PreviewGroup_excluded2 = ["src"];






var Group = function Group(_ref) {
  var _mergedItems$current;
  var _ref$previewPrefixCls = _ref.previewPrefixCls,
    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,
    children = _ref.children,
    _ref$icons = _ref.icons,
    icons = _ref$icons === void 0 ? {} : _ref$icons,
    items = _ref.items,
    preview = _ref.preview,
    fallback = _ref.fallback;
  var _ref2 = (0,esm_typeof/* default */.Z)(preview) === 'object' ? preview : {},
    previewVisible = _ref2.visible,
    onVisibleChange = _ref2.onVisibleChange,
    getContainer = _ref2.getContainer,
    currentIndex = _ref2.current,
    movable = _ref2.movable,
    minScale = _ref2.minScale,
    maxScale = _ref2.maxScale,
    countRender = _ref2.countRender,
    closeIcon = _ref2.closeIcon,
    onChange = _ref2.onChange,
    onTransform = _ref2.onTransform,
    toolbarRender = _ref2.toolbarRender,
    imageRender = _ref2.imageRender,
    dialogProps = (0,objectWithoutProperties/* default */.Z)(_ref2, PreviewGroup_excluded);

  // ========================== Items ===========================
  var _usePreviewItems = usePreviewItems(items),
    _usePreviewItems2 = (0,slicedToArray/* default */.Z)(_usePreviewItems, 2),
    mergedItems = _usePreviewItems2[0],
    register = _usePreviewItems2[1];

  // ========================= Preview ==========================
  // >>> Index
  var _useMergedState = (0,useMergedState/* default */.Z)(0, {
      value: currentIndex
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    current = _useMergedState2[0],
    setCurrent = _useMergedState2[1];
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    keepOpenIndex = _useState2[0],
    setKeepOpenIndex = _useState2[1];

  // >>> Image
  var _ref3 = ((_mergedItems$current = mergedItems[current]) === null || _mergedItems$current === void 0 ? void 0 : _mergedItems$current.data) || {},
    src = _ref3.src,
    imgCommonProps = (0,objectWithoutProperties/* default */.Z)(_ref3, PreviewGroup_excluded2);
  // >>> Visible
  var _useMergedState3 = (0,useMergedState/* default */.Z)(!!previewVisible, {
      value: previewVisible,
      onChange: function onChange(val, prevVal) {
        onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(val, prevVal, current);
      }
    }),
    _useMergedState4 = (0,slicedToArray/* default */.Z)(_useMergedState3, 2),
    isShowPreview = _useMergedState4[0],
    setShowPreview = _useMergedState4[1];

  // >>> Position
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.Z)(_useState3, 2),
    mousePosition = _useState4[0],
    setMousePosition = _useState4[1];
  var onPreviewFromImage = react.useCallback(function (id, mouseX, mouseY) {
    var index = mergedItems.findIndex(function (item) {
      return item.id === id;
    });
    setShowPreview(true);
    setMousePosition({
      x: mouseX,
      y: mouseY
    });
    setCurrent(index < 0 ? 0 : index);
    setKeepOpenIndex(true);
  }, [mergedItems]);

  // Reset current when reopen
  react.useEffect(function () {
    if (isShowPreview) {
      if (!keepOpenIndex) {
        setCurrent(0);
      }
    } else {
      setKeepOpenIndex(false);
    }
  }, [isShowPreview]);

  // ========================== Events ==========================
  var onInternalChange = function onInternalChange(next, prev) {
    setCurrent(next);
    onChange === null || onChange === void 0 || onChange(next, prev);
  };
  var onPreviewClose = function onPreviewClose() {
    setShowPreview(false);
    setMousePosition(null);
  };

  // ========================= Context ==========================
  var previewGroupContext = react.useMemo(function () {
    return {
      register: register,
      onPreview: onPreviewFromImage
    };
  }, [register, onPreviewFromImage]);

  // ========================== Render ==========================
  return /*#__PURE__*/react.createElement(PreviewGroupContext.Provider, {
    value: previewGroupContext
  }, children, /*#__PURE__*/react.createElement(es_Preview, (0,esm_extends/* default */.Z)({
    "aria-hidden": !isShowPreview,
    movable: movable,
    visible: isShowPreview,
    prefixCls: previewPrefixCls,
    closeIcon: closeIcon,
    onClose: onPreviewClose,
    mousePosition: mousePosition,
    imgCommonProps: imgCommonProps,
    src: src,
    fallback: fallback,
    icons: icons,
    minScale: minScale,
    maxScale: maxScale,
    getContainer: getContainer,
    current: current,
    count: mergedItems.length,
    countRender: countRender,
    onTransform: onTransform,
    toolbarRender: toolbarRender,
    imageRender: imageRender,
    onChange: onInternalChange
  }, dialogProps)));
};
/* harmony default export */ var PreviewGroup = (Group);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/Image.js






var Image_excluded = ["src", "alt", "onPreviewClose", "prefixCls", "previewPrefixCls", "placeholder", "fallback", "width", "height", "style", "preview", "className", "onClick", "onError", "wrapperClassName", "wrapperStyle", "rootClassName"],
  Image_excluded2 = ["src", "visible", "onVisibleChange", "getContainer", "mask", "maskClassName", "movable", "icons", "scaleStep", "minScale", "maxScale", "imageRender", "toolbarRender"];











var ImageInternal = function ImageInternal(props) {
  var imgSrc = props.src,
    alt = props.alt,
    onInitialPreviewClose = props.onPreviewClose,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-image' : _props$prefixCls,
    _props$previewPrefixC = props.previewPrefixCls,
    previewPrefixCls = _props$previewPrefixC === void 0 ? "".concat(prefixCls, "-preview") : _props$previewPrefixC,
    placeholder = props.placeholder,
    fallback = props.fallback,
    width = props.width,
    height = props.height,
    style = props.style,
    _props$preview = props.preview,
    preview = _props$preview === void 0 ? true : _props$preview,
    className = props.className,
    onClick = props.onClick,
    onError = props.onError,
    wrapperClassName = props.wrapperClassName,
    wrapperStyle = props.wrapperStyle,
    rootClassName = props.rootClassName,
    otherProps = (0,objectWithoutProperties/* default */.Z)(props, Image_excluded);
  var isCustomPlaceholder = placeholder && placeholder !== true;
  var _ref = (0,esm_typeof/* default */.Z)(preview) === 'object' ? preview : {},
    previewSrc = _ref.src,
    _ref$visible = _ref.visible,
    previewVisible = _ref$visible === void 0 ? undefined : _ref$visible,
    _ref$onVisibleChange = _ref.onVisibleChange,
    onPreviewVisibleChange = _ref$onVisibleChange === void 0 ? onInitialPreviewClose : _ref$onVisibleChange,
    _ref$getContainer = _ref.getContainer,
    getPreviewContainer = _ref$getContainer === void 0 ? undefined : _ref$getContainer,
    previewMask = _ref.mask,
    maskClassName = _ref.maskClassName,
    movable = _ref.movable,
    icons = _ref.icons,
    scaleStep = _ref.scaleStep,
    minScale = _ref.minScale,
    maxScale = _ref.maxScale,
    imageRender = _ref.imageRender,
    toolbarRender = _ref.toolbarRender,
    dialogProps = (0,objectWithoutProperties/* default */.Z)(_ref, Image_excluded2);
  var src = previewSrc !== null && previewSrc !== void 0 ? previewSrc : imgSrc;
  var _useMergedState = (0,useMergedState/* default */.Z)(!!previewVisible, {
      value: previewVisible,
      onChange: onPreviewVisibleChange
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    isShowPreview = _useMergedState2[0],
    setShowPreview = _useMergedState2[1];
  var _useStatus = useStatus({
      src: imgSrc,
      isCustomPlaceholder: isCustomPlaceholder,
      fallback: fallback
    }),
    _useStatus2 = (0,slicedToArray/* default */.Z)(_useStatus, 3),
    getImgRef = _useStatus2[0],
    srcAndOnload = _useStatus2[1],
    status = _useStatus2[2];
  var _useState = (0,react.useState)(null),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    mousePosition = _useState2[0],
    setMousePosition = _useState2[1];
  var groupContext = (0,react.useContext)(PreviewGroupContext);
  var canPreview = !!preview;
  var onPreviewClose = function onPreviewClose() {
    setShowPreview(false);
    setMousePosition(null);
  };
  var wrapperClass = classnames_default()(prefixCls, wrapperClassName, rootClassName, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-error"), status === 'error'));

  // ========================= ImageProps =========================
  var imgCommonProps = (0,react.useMemo)(function () {
    var obj = {};
    COMMON_PROPS.forEach(function (prop) {
      if (props[prop] !== undefined) {
        obj[prop] = props[prop];
      }
    });
    return obj;
  }, COMMON_PROPS.map(function (prop) {
    return props[prop];
  }));

  // ========================== Register ==========================
  var registerData = (0,react.useMemo)(function () {
    return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, imgCommonProps), {}, {
      src: src
    });
  }, [src, imgCommonProps]);
  var imageId = useRegisterImage(canPreview, registerData);

  // ========================== Preview ===========================
  var onPreview = function onPreview(e) {
    var _getOffset = (0,css/* getOffset */.os)(e.target),
      left = _getOffset.left,
      top = _getOffset.top;
    if (groupContext) {
      groupContext.onPreview(imageId, left, top);
    } else {
      setMousePosition({
        x: left,
        y: top
      });
      setShowPreview(true);
    }
    onClick === null || onClick === void 0 || onClick(e);
  };

  // =========================== Render ===========================
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", (0,esm_extends/* default */.Z)({}, otherProps, {
    className: wrapperClass,
    onClick: canPreview ? onPreview : onClick,
    style: (0,objectSpread2/* default */.Z)({
      width: width,
      height: height
    }, wrapperStyle)
  }), /*#__PURE__*/react.createElement("img", (0,esm_extends/* default */.Z)({}, imgCommonProps, {
    className: classnames_default()("".concat(prefixCls, "-img"), (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-img-placeholder"), placeholder === true), className),
    style: (0,objectSpread2/* default */.Z)({
      height: height
    }, style),
    ref: getImgRef
  }, srcAndOnload, {
    width: width,
    height: height,
    onError: onError
  })), status === 'loading' && /*#__PURE__*/react.createElement("div", {
    "aria-hidden": "true",
    className: "".concat(prefixCls, "-placeholder")
  }, placeholder), previewMask && canPreview && /*#__PURE__*/react.createElement("div", {
    className: classnames_default()("".concat(prefixCls, "-mask"), maskClassName),
    style: {
      display: (style === null || style === void 0 ? void 0 : style.display) === 'none' ? 'none' : undefined
    }
  }, previewMask)), !groupContext && canPreview && /*#__PURE__*/react.createElement(es_Preview, (0,esm_extends/* default */.Z)({
    "aria-hidden": !isShowPreview,
    visible: isShowPreview,
    prefixCls: previewPrefixCls,
    onClose: onPreviewClose,
    mousePosition: mousePosition,
    src: src,
    alt: alt,
    fallback: fallback,
    getContainer: getPreviewContainer,
    icons: icons,
    movable: movable,
    scaleStep: scaleStep,
    minScale: minScale,
    maxScale: maxScale,
    rootClassName: rootClassName,
    imageRender: imageRender,
    imgCommonProps: imgCommonProps,
    toolbarRender: toolbarRender
  }, dialogProps)));
};
ImageInternal.PreviewGroup = PreviewGroup;
ImageInternal.displayName = 'Image';
/* harmony default export */ var Image = (ImageInternal);
;// CONCATENATED MODULE: ./node_modules/rc-image/es/index.js


/* harmony default export */ var rc_image_es = (Image);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/hooks/useZIndex.js
var useZIndex = __webpack_require__(87263);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var motion = __webpack_require__(33603);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/en_US.js + 1 modules
var en_US = __webpack_require__(24457);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseOutlined.js
var CloseOutlined = __webpack_require__(62208);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/LeftOutlined.js + 1 modules
var LeftOutlined = __webpack_require__(62946);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/RightOutlined.js
var RightOutlined = __webpack_require__(62994);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/RotateLeftOutlined.js
// This icon file is generated automatically.
var RotateLeftOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "defs", "attrs": {}, "children": [{ "tag": "style", "attrs": {} }] }, { "tag": "path", "attrs": { "d": "M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z" } }, { "tag": "path", "attrs": { "d": "M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z" } }] }, "name": "rotate-left", "theme": "outlined" };
/* harmony default export */ var asn_RotateLeftOutlined = (RotateLeftOutlined);

// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/components/AntdIcon.js + 3 modules
var AntdIcon = __webpack_require__(93771);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/RotateLeftOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var RotateLeftOutlined_RotateLeftOutlined = function RotateLeftOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_RotateLeftOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_RotateLeftOutlined = (/*#__PURE__*/react.forwardRef(RotateLeftOutlined_RotateLeftOutlined));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/RotateRightOutlined.js
// This icon file is generated automatically.
var RotateRightOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "defs", "attrs": {}, "children": [{ "tag": "style", "attrs": {} }] }, { "tag": "path", "attrs": { "d": "M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z" } }, { "tag": "path", "attrs": { "d": "M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z" } }] }, "name": "rotate-right", "theme": "outlined" };
/* harmony default export */ var asn_RotateRightOutlined = (RotateRightOutlined);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/RotateRightOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var RotateRightOutlined_RotateRightOutlined = function RotateRightOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_RotateRightOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_RotateRightOutlined = (/*#__PURE__*/react.forwardRef(RotateRightOutlined_RotateRightOutlined));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/SwapOutlined.js
// This icon file is generated automatically.
var SwapOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z" } }] }, "name": "swap", "theme": "outlined" };
/* harmony default export */ var asn_SwapOutlined = (SwapOutlined);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/SwapOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var SwapOutlined_SwapOutlined = function SwapOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_SwapOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_SwapOutlined = (/*#__PURE__*/react.forwardRef(SwapOutlined_SwapOutlined));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ZoomInOutlined.js
// This icon file is generated automatically.
var ZoomInOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z" } }] }, "name": "zoom-in", "theme": "outlined" };
/* harmony default export */ var asn_ZoomInOutlined = (ZoomInOutlined);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/ZoomInOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var ZoomInOutlined_ZoomInOutlined = function ZoomInOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_ZoomInOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_ZoomInOutlined = (/*#__PURE__*/react.forwardRef(ZoomInOutlined_ZoomInOutlined));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ZoomOutOutlined.js
// This icon file is generated automatically.
var ZoomOutOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z" } }] }, "name": "zoom-out", "theme": "outlined" };
/* harmony default export */ var asn_ZoomOutOutlined = (ZoomOutOutlined);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/ZoomOutOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var ZoomOutOutlined_ZoomOutOutlined = function ZoomOutOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_ZoomOutOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_ZoomOutOutlined = (/*#__PURE__*/react.forwardRef(ZoomOutOutlined_ZoomOutOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/index.js
var dist_module = __webpack_require__(10274);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/style/index.js
var style = __webpack_require__(71194);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var es_style = __webpack_require__(14747);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/zoom.js
var zoom = __webpack_require__(50438);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/fade.js
var fade = __webpack_require__(16932);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/statistic.js
var statistic = __webpack_require__(45503);
;// CONCATENATED MODULE: ./node_modules/antd/es/image/style/index.js






const genBoxStyle = position => ({
  position: position || 'absolute',
  inset: 0
});
const genImageMaskStyle = token => {
  const {
    iconCls,
    motionDurationSlow,
    paddingXXS,
    marginXXS,
    prefixCls,
    colorTextLightSolid
  } = token;
  return {
    position: 'absolute',
    inset: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: colorTextLightSolid,
    background: new dist_module/* TinyColor */.C('#000').setAlpha(0.5).toRgbString(),
    cursor: 'pointer',
    opacity: 0,
    transition: \`opacity \${motionDurationSlow}\`,
    [\`.\${prefixCls}-mask-info\`]: Object.assign(Object.assign({}, es_style/* textEllipsis */.vS), {
      padding: \`0 \${(0,cssinjs_es/* unit */.bf)(paddingXXS)}\`,
      [iconCls]: {
        marginInlineEnd: marginXXS,
        svg: {
          verticalAlign: 'baseline'
        }
      }
    })
  };
};
const genPreviewOperationsStyle = token => {
  const {
    previewCls,
    modalMaskBg,
    paddingSM,
    marginXL,
    margin,
    paddingLG,
    previewOperationColorDisabled,
    previewOperationHoverColor,
    motionDurationSlow,
    iconCls,
    colorTextLightSolid
  } = token;
  const operationBg = new dist_module/* TinyColor */.C(modalMaskBg).setAlpha(0.1);
  const operationBgHover = operationBg.clone().setAlpha(0.2);
  return {
    [\`\${previewCls}-footer\`]: {
      position: 'fixed',
      bottom: marginXL,
      left: {
        _skip_check_: true,
        value: 0
      },
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      color: token.previewOperationColor
    },
    [\`\${previewCls}-progress\`]: {
      marginBottom: margin
    },
    [\`\${previewCls}-close\`]: {
      position: 'fixed',
      top: marginXL,
      right: {
        _skip_check_: true,
        value: marginXL
      },
      display: 'flex',
      color: colorTextLightSolid,
      backgroundColor: operationBg.toRgbString(),
      borderRadius: '50%',
      padding: paddingSM,
      outline: 0,
      border: 0,
      cursor: 'pointer',
      transition: \`all \${motionDurationSlow}\`,
      '&:hover': {
        backgroundColor: operationBgHover.toRgbString()
      },
      [\`& > \${iconCls}\`]: {
        fontSize: token.previewOperationSize
      }
    },
    [\`\${previewCls}-operations\`]: {
      display: 'flex',
      alignItems: 'center',
      padding: \`0 \${(0,cssinjs_es/* unit */.bf)(paddingLG)}\`,
      backgroundColor: operationBg.toRgbString(),
      borderRadius: 100,
      '&-operation': {
        marginInlineStart: paddingSM,
        padding: paddingSM,
        cursor: 'pointer',
        transition: \`all \${motionDurationSlow}\`,
        userSelect: 'none',
        [\`&:not(\${previewCls}-operations-operation-disabled):hover > \${iconCls}\`]: {
          color: previewOperationHoverColor
        },
        '&-disabled': {
          color: previewOperationColorDisabled,
          cursor: 'not-allowed'
        },
        '&:first-of-type': {
          marginInlineStart: 0
        },
        [\`& > \${iconCls}\`]: {
          fontSize: token.previewOperationSize
        }
      }
    }
  };
};
const genPreviewSwitchStyle = token => {
  const {
    modalMaskBg,
    iconCls,
    previewOperationColorDisabled,
    previewCls,
    zIndexPopup,
    motionDurationSlow
  } = token;
  const operationBg = new dist_module/* TinyColor */.C(modalMaskBg).setAlpha(0.1);
  const operationBgHover = operationBg.clone().setAlpha(0.2);
  return {
    [\`\${previewCls}-switch-left, \${previewCls}-switch-right\`]: {
      position: 'fixed',
      insetBlockStart: '50%',
      zIndex: token.calc(zIndexPopup).add(1).equal({
        unit: false
      }),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: token.imagePreviewSwitchSize,
      height: token.imagePreviewSwitchSize,
      marginTop: token.calc(token.imagePreviewSwitchSize).mul(-1).div(2).equal(),
      color: token.previewOperationColor,
      background: operationBg.toRgbString(),
      borderRadius: '50%',
      transform: \`translateY(-50%)\`,
      cursor: 'pointer',
      transition: \`all \${motionDurationSlow}\`,
      userSelect: 'none',
      '&:hover': {
        background: operationBgHover.toRgbString()
      },
      [\`&-disabled\`]: {
        '&, &:hover': {
          color: previewOperationColorDisabled,
          background: 'transparent',
          cursor: 'not-allowed',
          [\`> \${iconCls}\`]: {
            cursor: 'not-allowed'
          }
        }
      },
      [\`> \${iconCls}\`]: {
        fontSize: token.previewOperationSize
      }
    },
    [\`\${previewCls}-switch-left\`]: {
      insetInlineStart: token.marginSM
    },
    [\`\${previewCls}-switch-right\`]: {
      insetInlineEnd: token.marginSM
    }
  };
};
const genImagePreviewStyle = token => {
  const {
    motionEaseOut,
    previewCls,
    motionDurationSlow,
    componentCls
  } = token;
  return [{
    [\`\${componentCls}-preview-root\`]: {
      [previewCls]: {
        height: '100%',
        textAlign: 'center',
        pointerEvents: 'none'
      },
      [\`\${previewCls}-body\`]: Object.assign(Object.assign({}, genBoxStyle()), {
        overflow: 'hidden'
      }),
      [\`\${previewCls}-img\`]: {
        maxWidth: '100%',
        maxHeight: '70%',
        verticalAlign: 'middle',
        transform: 'scale3d(1, 1, 1)',
        cursor: 'grab',
        transition: \`transform \${motionDurationSlow} \${motionEaseOut} 0s\`,
        userSelect: 'none',
        '&-wrapper': Object.assign(Object.assign({}, genBoxStyle()), {
          transition: \`transform \${motionDurationSlow} \${motionEaseOut} 0s\`,
          // https://github.com/ant-design/ant-design/issues/39913
          // TailwindCSS will reset img default style.
          // Let's set back.
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          '& > *': {
            pointerEvents: 'auto'
          },
          '&::before': {
            display: 'inline-block',
            width: 1,
            height: '50%',
            marginInlineEnd: -1,
            content: '""'
          }
        })
      },
      [\`\${previewCls}-moving\`]: {
        [\`\${previewCls}-preview-img\`]: {
          cursor: 'grabbing',
          '&-wrapper': {
            transitionDuration: '0s'
          }
        }
      }
    }
  },
  // Override
  {
    [\`\${componentCls}-preview-root\`]: {
      [\`\${previewCls}-wrap\`]: {
        zIndex: token.zIndexPopup
      }
    }
  },
  // Preview operations & switch
  {
    [\`\${componentCls}-preview-operations-wrapper\`]: {
      position: 'fixed',
      zIndex: token.calc(token.zIndexPopup).add(1).equal({
        unit: false
      })
    },
    '&': [genPreviewOperationsStyle(token), genPreviewSwitchStyle(token)]
  }];
};
const genImageStyle = token => {
  const {
    componentCls
  } = token;
  return {
    // ============================== image ==============================
    [componentCls]: {
      position: 'relative',
      display: 'inline-block',
      [\`\${componentCls}-img\`]: {
        width: '100%',
        height: 'auto',
        verticalAlign: 'middle'
      },
      [\`\${componentCls}-img-placeholder\`]: {
        backgroundColor: token.colorBgContainerDisabled,
        backgroundImage: "url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center center',
        backgroundSize: '30%'
      },
      [\`\${componentCls}-mask\`]: Object.assign({}, genImageMaskStyle(token)),
      [\`\${componentCls}-mask:hover\`]: {
        opacity: 1
      },
      [\`\${componentCls}-placeholder\`]: Object.assign({}, genBoxStyle())
    }
  };
};
const genPreviewMotion = token => {
  const {
    previewCls
  } = token;
  return {
    [\`\${previewCls}-root\`]: (0,zoom/* initZoomMotion */._y)(token, 'zoom'),
    [\`&\`]: (0,fade/* initFadeMotion */.J$)(token, true)
  };
};
// ============================== Export ==============================
const prepareComponentToken = token => ({
  zIndexPopup: token.zIndexPopupBase + 80,
  previewOperationColor: new dist_module/* TinyColor */.C(token.colorTextLightSolid).setAlpha(0.65).toRgbString(),
  previewOperationHoverColor: new dist_module/* TinyColor */.C(token.colorTextLightSolid).setAlpha(0.85).toRgbString(),
  previewOperationColorDisabled: new dist_module/* TinyColor */.C(token.colorTextLightSolid).setAlpha(0.25).toRgbString(),
  previewOperationSize: token.fontSizeIcon * 1.5 // FIXME: fontSizeIconLG
});
/* harmony default export */ var image_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('Image', token => {
  const previewCls = \`\${token.componentCls}-preview\`;
  const imageToken = (0,statistic/* merge */.TS)(token, {
    previewCls,
    modalMaskBg: new dist_module/* TinyColor */.C('#000').setAlpha(0.45).toRgbString(),
    // FIXME: Shared Token
    imagePreviewSwitchSize: token.controlHeightLG
  });
  return [genImageStyle(imageToken), genImagePreviewStyle(imageToken), (0,style/* genModalMaskStyle */.QA)((0,statistic/* merge */.TS)(imageToken, {
    componentCls: previewCls
  })), genPreviewMotion(imageToken)];
}, prepareComponentToken));
;// CONCATENATED MODULE: ./node_modules/antd/es/image/PreviewGroup.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
















const icons = {
  rotateLeft: /*#__PURE__*/react.createElement(icons_RotateLeftOutlined, null),
  rotateRight: /*#__PURE__*/react.createElement(icons_RotateRightOutlined, null),
  zoomIn: /*#__PURE__*/react.createElement(icons_ZoomInOutlined, null),
  zoomOut: /*#__PURE__*/react.createElement(icons_ZoomOutOutlined, null),
  close: /*#__PURE__*/react.createElement(CloseOutlined/* default */.Z, null),
  left: /*#__PURE__*/react.createElement(LeftOutlined/* default */.Z, null),
  right: /*#__PURE__*/react.createElement(RightOutlined/* default */.Z, null),
  flipX: /*#__PURE__*/react.createElement(icons_SwapOutlined, null),
  flipY: /*#__PURE__*/react.createElement(icons_SwapOutlined, {
    rotate: 90
  })
};
const InternalPreviewGroup = _a => {
  var {
      previewPrefixCls: customizePrefixCls,
      preview
    } = _a,
    otherProps = __rest(_a, ["previewPrefixCls", "preview"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('image', customizePrefixCls);
  const previewPrefixCls = \`\${prefixCls}-preview\`;
  const rootPrefixCls = getPrefixCls();
  const rootCls = (0,useCSSVarCls/* default */.Z)(prefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = image_style(prefixCls, rootCls);
  const [zIndex] = (0,useZIndex/* useZIndex */.Cn)('ImagePreview', typeof preview === 'object' ? preview.zIndex : undefined);
  const mergedPreview = react.useMemo(() => {
    var _a;
    if (preview === false) {
      return preview;
    }
    const _preview = typeof preview === 'object' ? preview : {};
    const mergedRootClassName = classnames_default()(hashId, cssVarCls, rootCls, (_a = _preview.rootClassName) !== null && _a !== void 0 ? _a : '');
    return Object.assign(Object.assign({}, _preview), {
      transitionName: (0,motion/* getTransitionName */.m)(rootPrefixCls, 'zoom', _preview.transitionName),
      maskTransitionName: (0,motion/* getTransitionName */.m)(rootPrefixCls, 'fade', _preview.maskTransitionName),
      rootClassName: mergedRootClassName,
      zIndex
    });
  }, [preview]);
  return wrapCSSVar( /*#__PURE__*/react.createElement(rc_image_es.PreviewGroup, Object.assign({
    preview: mergedPreview,
    previewPrefixCls: previewPrefixCls,
    icons: icons
  }, otherProps)));
};
/* harmony default export */ var image_PreviewGroup = (InternalPreviewGroup);
;// CONCATENATED MODULE: ./node_modules/antd/es/image/index.js
"use client";

var image_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











const image_Image = props => {
  var _a;
  const {
      prefixCls: customizePrefixCls,
      preview,
      className,
      rootClassName,
      style
    } = props,
    otherProps = image_rest(props, ["prefixCls", "preview", "className", "rootClassName", "style"]);
  const {
    getPrefixCls,
    locale: contextLocale = en_US/* default */.Z,
    getPopupContainer: getContextPopupContainer,
    image
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('image', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const imageLocale = contextLocale.Image || en_US/* default */.Z.Image;
  // Style
  const rootCls = (0,useCSSVarCls/* default */.Z)(prefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = image_style(prefixCls, rootCls);
  const mergedRootClassName = classnames_default()(rootClassName, hashId, cssVarCls, rootCls);
  const mergedClassName = classnames_default()(className, hashId, image === null || image === void 0 ? void 0 : image.className);
  const [zIndex] = (0,useZIndex/* useZIndex */.Cn)('ImagePreview', typeof preview === 'object' ? preview.zIndex : undefined);
  const mergedPreview = react.useMemo(() => {
    var _a;
    if (preview === false) {
      return preview;
    }
    const _preview = typeof preview === 'object' ? preview : {};
    const {
        getContainer,
        closeIcon
      } = _preview,
      restPreviewProps = image_rest(_preview, ["getContainer", "closeIcon"]);
    return Object.assign(Object.assign({
      mask: ( /*#__PURE__*/react.createElement("div", {
        className: \`\${prefixCls}-mask-info\`
      }, /*#__PURE__*/react.createElement(EyeOutlined/* default */.Z, null), imageLocale === null || imageLocale === void 0 ? void 0 : imageLocale.preview)),
      icons: icons
    }, restPreviewProps), {
      getContainer: getContainer !== null && getContainer !== void 0 ? getContainer : getContextPopupContainer,
      transitionName: (0,motion/* getTransitionName */.m)(rootPrefixCls, 'zoom', _preview.transitionName),
      maskTransitionName: (0,motion/* getTransitionName */.m)(rootPrefixCls, 'fade', _preview.maskTransitionName),
      zIndex,
      closeIcon: closeIcon !== null && closeIcon !== void 0 ? closeIcon : (_a = image === null || image === void 0 ? void 0 : image.preview) === null || _a === void 0 ? void 0 : _a.closeIcon
    });
  }, [preview, imageLocale, (_a = image === null || image === void 0 ? void 0 : image.preview) === null || _a === void 0 ? void 0 : _a.closeIcon]);
  const mergedStyle = Object.assign(Object.assign({}, image === null || image === void 0 ? void 0 : image.style), style);
  return wrapCSSVar( /*#__PURE__*/react.createElement(rc_image_es, Object.assign({
    prefixCls: prefixCls,
    preview: mergedPreview,
    rootClassName: mergedRootClassName,
    className: mergedClassName,
    style: mergedStyle
  }, otherProps)));
};
image_Image.PreviewGroup = image_PreviewGroup;
if (false) {}
/* harmony default export */ var es_image = (image_Image);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTE0OTkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU8sNkg7O0FDQXdCO0FBQ3hCLHVDQUF1QyxtQkFBbUIsTzs7QUNESztBQUN2QztBQUNrQjtBQUNqRDtBQUNlO0FBQ2Ysd0JBQXdCLGNBQWM7QUFDdEM7QUFDQTtBQUNBLEtBQUs7QUFDTCx1QkFBdUIsZ0NBQWM7QUFDckM7QUFDQSxxQkFBcUIsZ0JBQWdCLENBQUMsbUJBQW1CO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsZUFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxlQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLEM7O0FDL0JPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEM7O0FDWHNFO0FBQ2xCO0FBQ2I7QUFDeEI7QUFDZjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isa0JBQVE7QUFDMUIsaUJBQWlCLGdDQUFjO0FBQy9CO0FBQ0E7QUFDQSxpQkFBaUIsZ0JBQU07QUFDdkI7O0FBRUE7QUFDQSxFQUFFLG1CQUFTO0FBQ1g7QUFDQSxJQUFJLFlBQVk7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxtQkFBUztBQUNYO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDOzs7O0FDcEQrQjtBQUN4Qiw4QkFBOEIsbUJBQW1CLEdBQUcsRTs7Ozs7Ozs7OztBQ0QzRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEM7Ozs7OztBQ2pDK0I7QUFDL0IsOERBQTRCLFVBQVU7QUFDdEM7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQyxDQUFDLEU7O0FDUHdEO0FBQ1c7QUFDakM7QUFDVztBQUNUO0FBQ0s7QUFDRDtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsZ0JBQWdCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMEJBQTBCLGdCQUFnQixDQUFDLFVBQVU7QUFDckQ7QUFDQSxrQkFBa0IsZ0NBQWE7QUFDL0IseUJBQXlCLGdCQUFNO0FBQy9CLHVCQUF1QixnQkFBTTtBQUM3QixFQUFFLHlCQUF5QjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLG1CQUFtQjtBQUNqRCxpQkFBaUIsb0JBQVU7QUFDM0IsYUFBYSxnQ0FBYSxHQUFHO0FBQzdCLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsbUJBQW1CO0FBQ2pELGlCQUFpQixvQkFBVTtBQUMzQixhQUFhLGdDQUFhLEdBQUc7QUFDN0IsS0FBSyxlQUFlLG1CQUFtQjtBQUN2QztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixtQkFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLDRCQUE0QixtQkFBbUI7QUFDcEQ7QUFDQSxLQUFLO0FBQ0w7QUFDQSw2QkFBNkIsbUJBQW1CO0FBQ2hELGVBQWUsb0JBQVU7QUFDekI7QUFDQSxHQUFHLG1DQUFtQyxtQkFBbUIsUUFBUSw4QkFBUTtBQUN6RSxlQUFlLG9CQUFVO0FBQ3pCLFdBQVcsZ0NBQWEsQ0FBQyxnQ0FBYSxHQUFHO0FBQ3pDLEdBQUc7QUFDSCxzQkFBc0IsbUJBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGdDQUFhLENBQUMsZ0NBQWEsR0FBRztBQUN6QyxlQUFlLG9CQUFVO0FBQ3pCO0FBQ0E7QUFDQSxHQUFHLGVBQWUsbUJBQW1CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxnQkFBZ0IsbUJBQW1CLENBQUMsWUFBWTtBQUNuRDtBQUNBLEdBQUcsOERBQThELG1CQUFtQjtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0QsSUFBSSxLQUFxQyxFQUFFLEVBRTFDO0FBQ0Qsa0RBQWUsS0FBSyxFOztBQ3BJc0M7QUFDVztBQUNDO0FBQ3ZDO0FBQ0E7QUFDSztBQUNGO0FBQ0U7QUFDUjtBQUM1QiwyQkFBMkIsZ0JBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsZ0JBQU07O0FBRXhCO0FBQ0Esd0JBQXdCLGNBQWM7QUFDdEMsdUJBQXVCLGdDQUFjO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE1BQU07QUFDOUI7QUFDQTs7QUFFQTtBQUNBLHNCQUFzQixtQkFBbUIsQ0FBQyw0QkFBUztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSx3QkFBd0IsbUJBQW1CLENBQUMsYUFBSyxFQUFFLDhCQUFRLEdBQUc7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsZ0NBQWEsQ0FBQyxnQ0FBYSxDQUFDLGdDQUFhLEdBQUc7QUFDekQsaUJBQWlCLG9CQUFVO0FBQzNCLEtBQUs7QUFDTCxHQUFHO0FBQ0gsQ0FBQztBQUNEO0FBQ0EsbURBQWUsT0FBTyxFOztBQzlEb0M7QUFDVztBQUN0QztBQUNLO0FBQ0Y7QUFDbkI7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsbUJBQW1CLENBQUMsNEJBQVM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLHdCQUF3QixtQkFBbUIsUUFBUSw4QkFBUTtBQUMzRDtBQUNBLGFBQWEsZ0NBQWEsQ0FBQyxnQ0FBYSxHQUFHO0FBQzNDLGlCQUFpQixvQkFBVTtBQUMzQixLQUFLO0FBQ0wsR0FBRztBQUNILEM7Ozs7QUMxQjBEO0FBQ1c7QUFDQztBQUNsQztBQUNXO0FBQ0o7QUFDRjtBQUNJO0FBQ2Q7QUFDVztBQUNGO0FBQ1I7QUFDTjtBQUNtQjtBQUM5QjtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sS0FBcUMsRUFBRSxFQVExQztBQUNILG9DQUFvQyxnQkFBTTtBQUMxQyxtQkFBbUIsZ0JBQU07QUFDekIsbUJBQW1CLGdCQUFNO0FBQ3pCLHdCQUF3QixjQUFjO0FBQ3RDLHVCQUF1QixnQ0FBYztBQUNyQztBQUNBOztBQUVBO0FBQ0EsZUFBZSx3QkFBSztBQUNwQjtBQUNBLFNBQVMsMkJBQVE7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDJCQUFRO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHdCQUF3QixnQkFBTTtBQUM5QiwwQkFBMEIsZ0JBQU07O0FBRWhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxzQkFBTztBQUN6QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCLHNCQUFPO0FBQy9CO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsRUFBRSxtQkFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLEVBQUUsbUJBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0Esc0JBQXNCLG1CQUFtQixRQUFRLDhCQUFRO0FBQ3pELGVBQWUsb0JBQVU7QUFDekIsR0FBRyxFQUFFLDRCQUFTO0FBQ2Q7QUFDQSxHQUFHLGlCQUFpQixtQkFBbUIsQ0FBQyxJQUFJO0FBQzVDO0FBQ0E7QUFDQSxnQkFBZ0IsYUFBYTtBQUM3QixXQUFXLGdDQUFhLENBQUMsZ0NBQWE7QUFDdEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEdBQUcsZ0JBQWdCLG1CQUFtQixRQUFRLDhCQUFRO0FBQ3REO0FBQ0E7QUFDQSxlQUFlLG9CQUFVO0FBQ3pCO0FBQ0E7QUFDQSxXQUFXLGdDQUFhLENBQUMsZ0NBQWEsQ0FBQyxnQ0FBYTtBQUNwRDtBQUNBLEtBQUssZ0dBQWdHO0FBQ3JHO0FBQ0EsS0FBSztBQUNMLEdBQUcsMkJBQTJCLG1CQUFtQixDQUFDLGNBQU8sRUFBRSw4QkFBUSxHQUFHO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCLEdBQUc7QUFDSCxDOztBQ3hNMEQ7QUFDWTtBQUM1QjtBQUNYO0FBQ1E7QUFDVDtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixjQUFjO0FBQ3RDLHVCQUF1QixnQ0FBYztBQUNyQztBQUNBO0FBQ0EsbUJBQW1CLGFBQWE7QUFDaEM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZUFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLG1CQUFtQixDQUFDLFVBQVU7QUFDcEQ7QUFDQSxHQUFHLGVBQWUsbUJBQW1CLENBQUMsaUJBQU07QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLGVBQWUsbUJBQW1CLENBQUMsTUFBTSxFQUFFLDhCQUFRLEdBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0Esa0RBQWUsVUFBVSxFOztBQ3pEYTtBQUNLO0FBQzFCO0FBQ2pCLGlEQUFlLGFBQVUsRTs7Ozs7Ozs7QUNINEM7QUFDQztBQUNuQjtBQUNWO0FBQ1I7QUFDUTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZixjQUFjLGdCQUFNO0FBQ3BCLGNBQWMsZ0JBQU07QUFDcEIsa0JBQWtCLGtCQUFRO0FBQzFCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QiwwQkFBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixzQkFBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0NBQWEsQ0FBQyxnQ0FBYSxHQUFHO0FBQ3RELFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx1QkFBdUIsZ0NBQWEsQ0FBQyxnQ0FBYSxHQUFHO0FBQ3JEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsNkJBQWE7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEM7O0FDbEhxRTtBQUNHO0FBQ3JCO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlDQUFlLEdBQUc7QUFDL0I7QUFDQTtBQUNBLGFBQWEsaUNBQWUsR0FBRztBQUMvQjtBQUNBLElBQUk7QUFDSixXQUFXLGlDQUFlLEdBQUc7QUFDN0I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2YsdUJBQXVCLDZCQUFhO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osYUFBYSxnQ0FBYSxDQUFDLGdDQUFhLEdBQUc7QUFDM0M7QUFDQTtBQUNBLEM7O0FDN0NBO0FBQ087QUFDUDtBQUNPLDhCOztBQ0g4RDtBQUNDO0FBQ2xCO0FBQ1c7QUFDbEI7QUFDNEI7QUFDRTtBQUM1RDtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFRO0FBQzFCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBO0FBQ0EsMEJBQTBCLGdCQUFNO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDJCQUEyQjtBQUNoRDtBQUNBLHdCQUF3QixnQ0FBYSxHQUFHO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QscUJBQXFCO0FBQ3JFO0FBQ0EsZ0JBQWdCLGdCQUFnQjtBQUNoQztBQUNBLGNBQWMsZ0JBQWdCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbUJBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLG1DQUFnQjtBQUMxQyw0QkFBNEIsbUNBQWdCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLG1DQUFnQjtBQUNqRCxtQ0FBbUMsbUNBQWdCO0FBQ25EO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUSwyQkFBTztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEM7O0FDckhxRTtBQUNDO0FBQ2xCO0FBQ1c7QUFDVTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixrQkFBUTtBQUMxQixpQkFBaUIsZ0NBQWM7QUFDL0I7QUFDQTtBQUNBLHVCQUF1QixnQkFBTTtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNkJBQTZCLGdDQUFhLENBQUMsZ0NBQWEsR0FBRztBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnQ0FBYztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwyQkFBMkI7QUFDOUM7QUFDQSxzQkFBc0IsZ0NBQWEsR0FBRztBQUN0QztBQUNBO0FBQ0EsRUFBRSxtQkFBUztBQUNYO0FBQ0E7QUFDQSw0QkFBNEIsbUNBQWdCO0FBQzVDO0FBQ0EsT0FBTztBQUNQO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDOztBQ3pLcUU7QUFDRztBQUM5QjtBQUNOO0FBQ0Y7QUFDTztBQUNWO0FBQ0k7QUFDYTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixvQkFBVSxDQUFDLG1CQUFtQjtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsZUFBZTtBQUNqQjtBQUNBLHdCQUF3QixzQkFBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG1CQUFtQjtBQUMzQyxpQkFBaUIsb0JBQVUsaUNBQWlDLEVBQUUsaUNBQWUsa0ZBQWtGLGlDQUFlO0FBQzlLO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILGlDQUFpQyxtQkFBbUI7QUFDcEQ7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLG1CQUFtQixDQUFDLDRCQUFTO0FBQ25EO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLHdCQUF3QixtQkFBbUIsQ0FBQyxpQkFBTTtBQUNsRDtBQUNBO0FBQ0EsS0FBSyxlQUFlLG1CQUFtQjtBQUN2QyxpQkFBaUIsb0JBQVU7QUFDM0IsYUFBYSxnQ0FBYSxDQUFDLGdDQUFhLEdBQUcsWUFBWTtBQUN2RDtBQUNBLE9BQU87QUFDUCxLQUFLLDJDQUEyQyxtQkFBbUI7QUFDbkU7QUFDQTtBQUNBLEtBQUssa0RBQWtELG1CQUFtQixDQUFDLGNBQWMscUJBQXFCLG1CQUFtQjtBQUNqSSxpQkFBaUIsb0JBQVUsdUNBQXVDLGlDQUFlLEdBQUc7QUFDcEY7QUFDQSxLQUFLLHNCQUFzQixtQkFBbUI7QUFDOUMsaUJBQWlCLG9CQUFVLHdDQUF3QyxpQ0FBZSxHQUFHO0FBQ3JGO0FBQ0EsS0FBSyx3QkFBd0IsbUJBQW1CO0FBQ2hEO0FBQ0EsS0FBSywrQkFBK0IsbUJBQW1CO0FBQ3ZEO0FBQ0EsS0FBSywySUFBMkksZ0NBQWE7QUFDN0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLE1BQU0sSUFBSTtBQUNWLEdBQUc7QUFDSDtBQUNBLGtEQUFlLFVBQVUsRTs7QUMxSjRDO0FBQ0c7QUFDZDtBQUNZO0FBQ29CO0FBQzFGO0FBQ0E7QUFDb0M7QUFDTDtBQUNnQztBQUN0QjtBQUM4QjtBQUN2QjtBQUNVO0FBQ1I7QUFDQTtBQUNSO0FBQ0o7QUFDYTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksMENBQXdCO0FBQ3BDLG1CQUFtQixTQUFTO0FBQzVCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsa0JBQWtCLGdDQUFjO0FBQ2hDO0FBQ0E7QUFDQSxzQkFBc0IsbUJBQW1CLFFBQVEsOEJBQVE7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDBDQUF3QjtBQUN4QyxlQUFlLGdCQUFNO0FBQ3JCLHFCQUFxQixvQkFBVSxDQUFDLG1CQUFtQjtBQUNuRDtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFRO0FBQzFCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBO0FBQ0EsMkJBQTJCLGlCQUFpQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixhQUFhO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixhQUFhO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixvQkFBVSxDQUFDLGlDQUFlLEdBQUc7QUFDbkQsRUFBRSxtQkFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixnQkFBZ0I7QUFDdkM7QUFDQTtBQUNBLHVCQUF1QixnQkFBZ0IsSUFBSSxnQkFBZ0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHNCQUFPO0FBQ2pDO0FBQ0EsTUFBTSwyQkFBMkIsc0JBQU87QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSLDJCQUEyQixnQkFBZ0I7QUFDM0M7QUFDQTtBQUNBO0FBQ0EsRUFBRSxtQkFBUztBQUNYLDRCQUE0QixtQ0FBZ0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILDZCQUE2QixtQkFBbUIsZUFBZSw4QkFBUSxHQUFHO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHNCQUFzQixtQkFBbUIsQ0FBQyxjQUFjLHFCQUFxQixtQkFBbUIsQ0FBQyxZQUFNLEVBQUUsOEJBQVE7QUFDakg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxHQUFHLGdCQUFnQixtQkFBbUI7QUFDdEM7QUFDQSxHQUFHLHFDQUFxQyxnQ0FBYTtBQUNyRDtBQUNBLEdBQUc7QUFDSDtBQUNBLElBQUksSUFBSSw2QkFBNkIsbUJBQW1CLENBQUMsYUFBVTtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSwrQ0FBZSxPQUFPLEU7Ozs7QUN4UHdEO0FBQ047QUFDSDtBQUNDO0FBQ3ZDO0FBQ1U7QUFDekM7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBLHdCQUF3QixjQUFjLEdBQUc7QUFDekMsdUJBQXVCLGdDQUFjO0FBQ3JDO0FBQ0E7QUFDQSxzQkFBc0IsaUJBQWlCO0FBQ3ZDO0FBQ0EsYUFBYSxnQ0FBYSxDQUFDLGdDQUFhLEdBQUcsV0FBVyxFQUFFLGlDQUFlLEdBQUc7QUFDMUUsS0FBSztBQUNMO0FBQ0E7QUFDQSx3QkFBd0IsZ0NBQWEsR0FBRztBQUN4QztBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLG9CQUFvQixhQUFhO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsb0NBQWtCLENBQUMsWUFBWTtBQUM1RDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQSxDOztBQ2hFMEQ7QUFDWTtBQUNkO0FBQ2tDO0FBQzFGLElBQUkscUJBQVM7QUFDYixFQUFFLHNCQUFVO0FBQ2lEO0FBQzlCO0FBQ0U7QUFDZTtBQUNNO0FBQ3RCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQSxjQUFjLDZCQUFPLHFDQUFxQztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwwQ0FBd0IsUUFBUSxxQkFBUzs7QUFFM0Q7QUFDQSx5QkFBeUIsZUFBZTtBQUN4Qyx3QkFBd0IsZ0NBQWM7QUFDdEM7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCLGlDQUFjO0FBQ3RDO0FBQ0EsS0FBSztBQUNMLHVCQUF1QixnQ0FBYztBQUNyQztBQUNBO0FBQ0Esa0JBQWtCLGtCQUFRO0FBQzFCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBOztBQUVBO0FBQ0Esb0pBQW9KO0FBQ3BKO0FBQ0EscUJBQXFCLDBDQUF3QixRQUFRLHNCQUFVO0FBQy9EO0FBQ0EseUJBQXlCLGlDQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLHVCQUF1QixnQ0FBYztBQUNyQztBQUNBOztBQUVBO0FBQ0EsbUJBQW1CLGtCQUFRO0FBQzNCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBO0FBQ0EsMkJBQTJCLGlCQUFpQjtBQUM1QztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EsRUFBRSxlQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDRCQUE0QixhQUFhO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHNCQUFzQixtQkFBbUIsQ0FBQyxtQkFBbUI7QUFDN0Q7QUFDQSxHQUFHLHlCQUF5QixtQkFBbUIsQ0FBQyxVQUFPLEVBQUUsOEJBQVE7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsaURBQWUsS0FBSyxFOztBQ2xKc0M7QUFDVztBQUNHO0FBQ0Y7QUFDZDtBQUNrQztBQUMxRixJQUFJLGNBQVM7QUFDYixFQUFFLGVBQVU7QUFDZ0I7QUFDbUI7QUFDYztBQUM5QjtBQUN1QjtBQUNkO0FBQ1E7QUFDUTtBQUNkO0FBQ1Y7QUFDVTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsMENBQXdCLFFBQVEsY0FBUztBQUMxRDtBQUNBLGFBQWEsNkJBQU8scUNBQXFDO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDBDQUF3QixPQUFPLGVBQVU7QUFDM0Q7QUFDQSx3QkFBd0IsaUNBQWM7QUFDdEM7QUFDQTtBQUNBLEtBQUs7QUFDTCx1QkFBdUIsZ0NBQWM7QUFDckM7QUFDQTtBQUNBLG1CQUFtQixTQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxrQkFBa0IsZ0NBQWM7QUFDaEM7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFRO0FBQzFCLGlCQUFpQixnQ0FBYztBQUMvQjtBQUNBO0FBQ0EscUJBQXFCLG9CQUFVLENBQUMsbUJBQW1CO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsb0JBQUUsNkNBQTZDLGlDQUFlLEdBQUc7O0FBRXRGO0FBQ0EsdUJBQXVCLGlCQUFPO0FBQzlCO0FBQ0EsSUFBSSxZQUFZO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUcsRUFBRSxZQUFZO0FBQ2pCO0FBQ0EsR0FBRzs7QUFFSDtBQUNBLHFCQUFxQixpQkFBTztBQUM1QixXQUFXLGdDQUFhLENBQUMsZ0NBQWEsR0FBRyxxQkFBcUI7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILGdCQUFnQixnQkFBZ0I7O0FBRWhDO0FBQ0E7QUFDQSxxQkFBcUIseUJBQVM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esc0JBQXNCLG1CQUFtQixDQUFDLGNBQWMscUJBQXFCLG1CQUFtQixRQUFRLDhCQUFRLEdBQUc7QUFDbkg7QUFDQTtBQUNBLFdBQVcsZ0NBQWE7QUFDeEI7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHLGdCQUFnQixtQkFBbUIsUUFBUSw4QkFBUSxHQUFHO0FBQ3pELGVBQWUsb0JBQUUsK0JBQStCLGlDQUFlLEdBQUc7QUFDbEUsV0FBVyxnQ0FBYTtBQUN4QjtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHLHlDQUF5QyxtQkFBbUI7QUFDL0Q7QUFDQTtBQUNBLEdBQUcsMERBQTBELG1CQUFtQjtBQUNoRixlQUFlLG9CQUFFO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLEdBQUcsNkRBQTZELG1CQUFtQixDQUFDLFVBQU8sRUFBRSw4QkFBUTtBQUNyRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSw2QkFBNkIsWUFBWTtBQUN6QztBQUNBLDBDQUFlLGFBQWEsRTs7QUNoTEE7QUFDSjtBQUN4QixnREFBZSxLQUFLLEU7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQ0ZwQjtBQUNBLDJCQUEyQixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQixpQkFBaUIsNkJBQTZCLEdBQUcsSUFBSSwwQkFBMEIsa0pBQWtKLElBQUksMEJBQTBCLDRVQUE0VSxHQUFHO0FBQ3p1QiwyREFBZSxrQkFBa0IsRUFBQzs7Ozs7QUNGd0I7QUFDMUQ7QUFDQTs7QUFFK0I7QUFDcUQ7QUFDdEM7QUFDOUMsSUFBSSxxQ0FBa0I7QUFDdEIsc0JBQXNCLG1CQUFtQixDQUFDLHVCQUFRLEVBQUUsOEJBQVEsR0FBRztBQUMvRDtBQUNBLFVBQVUsc0JBQXFCO0FBQy9CLEdBQUc7QUFDSDtBQUNBLElBQUksS0FBcUMsRUFBRSxFQUUxQztBQUNELDBFQUE0QixnQkFBZ0IsQ0FBQyxxQ0FBa0IsQ0FBQyxFOztBQ2hCaEU7QUFDQSw0QkFBNEIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsaUJBQWlCLDZCQUE2QixHQUFHLElBQUksMEJBQTBCLGtXQUFrVyxJQUFJLDBCQUEwQixrSkFBa0osR0FBRztBQUNod0IsNERBQWUsbUJBQW1CLEVBQUM7OztBQ0Z1QjtBQUMxRDtBQUNBOztBQUUrQjtBQUN1RDtBQUN4QztBQUM5QyxJQUFJLHVDQUFtQjtBQUN2QixzQkFBc0IsbUJBQW1CLENBQUMsdUJBQVEsRUFBRSw4QkFBUSxHQUFHO0FBQy9EO0FBQ0EsVUFBVSx1QkFBc0I7QUFDaEMsR0FBRztBQUNIO0FBQ0EsSUFBSSxLQUFxQyxFQUFFLEVBRTFDO0FBQ0QsMkVBQTRCLGdCQUFnQixDQUFDLHVDQUFtQixDQUFDLEU7O0FDaEJqRTtBQUNBLHFCQUFxQixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQixrVUFBa1UsR0FBRztBQUMxZCxxREFBZSxZQUFZLEVBQUM7OztBQ0Y4QjtBQUMxRDtBQUNBOztBQUUrQjtBQUN5QztBQUMxQjtBQUM5QyxJQUFJLHlCQUFZO0FBQ2hCLHNCQUFzQixtQkFBbUIsQ0FBQyx1QkFBUSxFQUFFLDhCQUFRLEdBQUc7QUFDL0Q7QUFDQSxVQUFVLGdCQUFlO0FBQ3pCLEdBQUc7QUFDSDtBQUNBLElBQUksS0FBcUMsRUFBRSxFQUUxQztBQUNELG9FQUE0QixnQkFBZ0IsQ0FBQyx5QkFBWSxDQUFDLEU7O0FDaEIxRDtBQUNBLHVCQUF1QixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQixxZ0JBQXFnQixHQUFHO0FBQy9wQix1REFBZSxjQUFjLEVBQUM7OztBQ0Y0QjtBQUMxRDtBQUNBOztBQUUrQjtBQUM2QztBQUM5QjtBQUM5QyxJQUFJLDZCQUFjO0FBQ2xCLHNCQUFzQixtQkFBbUIsQ0FBQyx1QkFBUSxFQUFFLDhCQUFRLEdBQUc7QUFDL0Q7QUFDQSxVQUFVLGtCQUFpQjtBQUMzQixHQUFHO0FBQ0g7QUFDQSxJQUFJLEtBQXFDLEVBQUUsRUFFMUM7QUFDRCxzRUFBNEIsZ0JBQWdCLENBQUMsNkJBQWMsQ0FBQyxFOztBQ2hCNUQ7QUFDQSx3QkFBd0IsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIscWFBQXFhLEdBQUc7QUFDaGtCLHdEQUFlLGVBQWUsRUFBQzs7O0FDRjJCO0FBQzFEO0FBQ0E7O0FBRStCO0FBQytDO0FBQ2hDO0FBQzlDLElBQUksK0JBQWU7QUFDbkIsc0JBQXNCLG1CQUFtQixDQUFDLHVCQUFRLEVBQUUsOEJBQVEsR0FBRztBQUMvRDtBQUNBLFVBQVUsbUJBQWtCO0FBQzVCLEdBQUc7QUFDSDtBQUNBLElBQUksS0FBcUMsRUFBRSxFQUUxQztBQUNELHVFQUE0QixnQkFBZ0IsQ0FBQywrQkFBZSxDQUFDLEU7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQ2hCbEI7QUFDQztBQUNVO0FBQ1g7QUFDeUI7QUFDSDtBQUMxRDtBQUNQO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiw0QkFBUztBQUM3QjtBQUNBO0FBQ0EsMkJBQTJCLG1CQUFtQjtBQUM5QyxTQUFTLFVBQVUsNENBQTRDLEVBQUUsNkJBQVk7QUFDN0Usb0JBQW9CLDJCQUFJLGFBQWE7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiwwQkFBMEIsNEJBQVM7QUFDbkM7QUFDQTtBQUNBLFFBQVEsV0FBVztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsUUFBUSxXQUFXO0FBQ25CO0FBQ0EsS0FBSztBQUNMLFFBQVEsV0FBVztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsbUJBQW1CO0FBQzVDO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsY0FBYyxRQUFRO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsUUFBUSxXQUFXO0FBQ25CO0FBQ0E7QUFDQSxvQkFBb0IsMkJBQUksWUFBWTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsbUJBQW1CO0FBQzlDO0FBQ0Esa0JBQWtCLFdBQVcsMENBQTBDLFFBQVE7QUFDL0U7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsZ0JBQWdCLFFBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiwwQkFBMEIsNEJBQVM7QUFDbkM7QUFDQTtBQUNBLFFBQVEsV0FBVyxnQkFBZ0IsV0FBVztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLG1CQUFtQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixRQUFRO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBLEtBQUs7QUFDTCxRQUFRLFdBQVc7QUFDbkI7QUFDQSxLQUFLO0FBQ0wsUUFBUSxXQUFXO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsUUFBUSxhQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLFVBQVUsV0FBVyx1Q0FBdUM7QUFDNUQ7QUFDQSxPQUFPO0FBQ1AsVUFBVSxXQUFXO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsb0JBQW9CLEVBQUUsZUFBZTtBQUN0RTtBQUNBLG1EQUFtRDtBQUNuRCxtQ0FBbUMsb0JBQW9CLEVBQUUsZUFBZTtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUCxVQUFVLFdBQVc7QUFDckIsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxRQUFRLGFBQWE7QUFDckIsVUFBVSxXQUFXO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsUUFBUSxhQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsYUFBYTtBQUN2QjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsVUFBVSxhQUFhO0FBQ3ZCO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxVQUFVLGFBQWEseUJBQXlCO0FBQ2hELFVBQVUsYUFBYTtBQUN2QjtBQUNBLE9BQU87QUFDUCxVQUFVLGFBQWEsZ0NBQWdDO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLFFBQVEsV0FBVyxTQUFTLCtCQUFjO0FBQzFDLFdBQVcsK0JBQWM7QUFDekI7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLDZCQUE2Qiw0QkFBUztBQUN0QyxrQ0FBa0MsNEJBQVM7QUFDM0MscUNBQXFDLDRCQUFTO0FBQzlDO0FBQ0EsQ0FBQztBQUNELGdEQUFlLCtDQUFhO0FBQzVCLHdCQUF3QixtQkFBbUI7QUFDM0MscUJBQXFCLDJCQUFVO0FBQy9CO0FBQ0EscUJBQXFCLDRCQUFTO0FBQzlCO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsdUVBQXVFLG1DQUFpQixDQUFDLDJCQUFVO0FBQ25HO0FBQ0EsR0FBRztBQUNILENBQUMsd0JBQXdCLEU7O0FDelR6Qjs7QUFFQSxhQUFhLFNBQUksSUFBSSxTQUFJO0FBQ3pCO0FBQ0E7QUFDQSw0SEFBNEgsY0FBYztBQUMxSTtBQUNBO0FBQ0E7QUFDQTtBQUMrQjtBQUNzQztBQUNGO0FBQ0U7QUFDVTtBQUNFO0FBQ2Q7QUFDSTtBQUNFO0FBQ3JDO0FBQ0w7QUFDc0I7QUFDRDtBQUNEO0FBQ2M7QUFDbEM7QUFDeEI7QUFDUCwyQkFBMkIsbUJBQW1CLENBQUMsd0JBQWtCO0FBQ2pFLDRCQUE0QixtQkFBbUIsQ0FBQyx5QkFBbUI7QUFDbkUsdUJBQXVCLG1CQUFtQixDQUFDLG9CQUFjO0FBQ3pELHdCQUF3QixtQkFBbUIsQ0FBQyxxQkFBZTtBQUMzRCxzQkFBc0IsbUJBQW1CLENBQUMsNEJBQWE7QUFDdkQscUJBQXFCLG1CQUFtQixDQUFDLDJCQUFZO0FBQ3JELHNCQUFzQixtQkFBbUIsQ0FBQyw0QkFBYTtBQUN2RCxzQkFBc0IsbUJBQW1CLENBQUMsa0JBQVk7QUFDdEQsc0JBQXNCLG1CQUFtQixDQUFDLGtCQUFZO0FBQ3REO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsSUFBSSxFQUFFLGdCQUFnQixDQUFDLDZCQUFhO0FBQ3BDO0FBQ0EsOEJBQThCLFVBQVU7QUFDeEM7QUFDQSxrQkFBa0IsK0JBQVk7QUFDOUIsMENBQTBDLFdBQVE7QUFDbEQsbUJBQW1CLCtCQUFTO0FBQzVCLHdCQUF3QixhQUFhO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msb0JBQVU7QUFDMUMseUNBQXlDO0FBQ3pDLHNCQUFzQixtQ0FBaUI7QUFDdkMsMEJBQTBCLG1DQUFpQjtBQUMzQztBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSCxrQ0FBa0MsbUJBQW1CLENBQUMsV0FBTztBQUM3RDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSx1REFBZSxvQkFBb0IsRTs7QUMxRW5DOztBQUVBLElBQUksVUFBTSxHQUFHLFNBQUksSUFBSSxTQUFJO0FBQ3pCO0FBQ0E7QUFDQSw0SEFBNEgsY0FBYztBQUMxSTtBQUNBO0FBQ0E7QUFDQTtBQUMrQjtBQUNrQztBQUM3QjtBQUNMO0FBQ3NCO0FBQ0Q7QUFDRDtBQUNjO0FBQ3JCO0FBQ1M7QUFDdEI7QUFDL0IsTUFBTSxXQUFLO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04saUJBQWlCLFVBQU07QUFDdkI7QUFDQTtBQUNBLDRCQUE0QixvQkFBYTtBQUN6QztBQUNBO0FBQ0EsSUFBSSxFQUFFLGdCQUFnQixDQUFDLDZCQUFhO0FBQ3BDO0FBQ0E7QUFDQSw2Q0FBNkMsb0JBQWE7QUFDMUQ7QUFDQSxrQkFBa0IsK0JBQVk7QUFDOUIsMENBQTBDLFdBQVE7QUFDbEQsOEJBQThCLG9CQUFVO0FBQ3hDLDBCQUEwQixvQkFBVTtBQUNwQyxtQkFBbUIsK0JBQVM7QUFDNUIsd0JBQXdCLGFBQWE7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUix5QkFBeUIsVUFBTTtBQUMvQjtBQUNBLDJCQUEyQixtQkFBbUI7QUFDOUMsc0JBQXNCLFVBQVU7QUFDaEMsT0FBTyxlQUFlLG1CQUFtQixDQUFDLDBCQUFXO0FBQ3JELFdBQVc7QUFDWCxLQUFLO0FBQ0w7QUFDQSxzQkFBc0IsbUNBQWlCO0FBQ3ZDLDBCQUEwQixtQ0FBaUI7QUFDM0M7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0gsb0RBQW9EO0FBQ3BELGtDQUFrQyxtQkFBbUIsQ0FBQyxXQUFPO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxXQUFLLGdCQUFnQixrQkFBWTtBQUNqQyxJQUFJLEtBQXFDLEVBQUUsRUFFMUM7QUFDRCw2Q0FBZSxXQUFLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvY29tbW9uLmpzPzQxZjIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvY29udGV4dC5qcz9iZmRjIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL2VzL2hvb2tzL3VzZVJlZ2lzdGVySW1hZ2UuanM/OTU3MSIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9lcy91dGlsLmpzPzgxZjIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvaG9va3MvdXNlU3RhdHVzLmpzPzEwMDciLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2Uvbm9kZV9tb2R1bGVzL3JjLWRpYWxvZy9lcy9jb250ZXh0LmpzP2Y0ZmEiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2Uvbm9kZV9tb2R1bGVzL3JjLWRpYWxvZy9lcy91dGlsLmpzPzQ4ZDkiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2Uvbm9kZV9tb2R1bGVzL3JjLWRpYWxvZy9lcy9EaWFsb2cvQ29udGVudC9NZW1vQ2hpbGRyZW4uanM/OWU1OCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9ub2RlX21vZHVsZXMvcmMtZGlhbG9nL2VzL0RpYWxvZy9Db250ZW50L1BhbmVsLmpzP2YyMDIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2Uvbm9kZV9tb2R1bGVzL3JjLWRpYWxvZy9lcy9EaWFsb2cvQ29udGVudC9pbmRleC5qcz9jMWFiIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL25vZGVfbW9kdWxlcy9yYy1kaWFsb2cvZXMvRGlhbG9nL01hc2suanM/ZDQ4ZCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9ub2RlX21vZHVsZXMvcmMtZGlhbG9nL2VzL0RpYWxvZy9pbmRleC5qcz8wMjZiIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL25vZGVfbW9kdWxlcy9yYy1kaWFsb2cvZXMvRGlhbG9nV3JhcC5qcz8zNDU4Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL25vZGVfbW9kdWxlcy9yYy1kaWFsb2cvZXMvaW5kZXguanM/ZDZkNyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9lcy9ob29rcy91c2VJbWFnZVRyYW5zZm9ybS5qcz82ZGQ4Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL2VzL2dldEZpeFNjYWxlRWxlVHJhbnNQb3NpdGlvbi5qcz9mN2Q4Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL2VzL3ByZXZpZXdDb25maWcuanM/MDg4YyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9lcy9ob29rcy91c2VNb3VzZUV2ZW50LmpzP2QwZGYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvaG9va3MvdXNlVG91Y2hFdmVudC5qcz9mZDRmIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLWltYWdlL2VzL09wZXJhdGlvbnMuanM/YTVhZiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9lcy9QcmV2aWV3LmpzPzQxOTYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvaG9va3MvdXNlUHJldmlld0l0ZW1zLmpzPzZlN2IiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvUHJldmlld0dyb3VwLmpzP2FhN2EiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvcmMtaW1hZ2UvZXMvSW1hZ2UuanM/OTQ5MyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9yYy1pbWFnZS9lcy9pbmRleC5qcz8zNDZiIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vUm90YXRlTGVmdE91dGxpbmVkLmpzPzVhMjEiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUm90YXRlTGVmdE91dGxpbmVkLmpzP2ZkNTYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9Sb3RhdGVSaWdodE91dGxpbmVkLmpzP2MxMGQiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUm90YXRlUmlnaHRPdXRsaW5lZC5qcz8zNzI5Iiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vU3dhcE91dGxpbmVkLmpzPzE0ODUiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvU3dhcE91dGxpbmVkLmpzPzUwZTYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9ab29tSW5PdXRsaW5lZC5qcz9lOTRiIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL2FudGQvbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zL2VzL2ljb25zL1pvb21Jbk91dGxpbmVkLmpzPzU1NzYiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9ab29tT3V0T3V0bGluZWQuanM/MmMyZSIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy9lcy9pY29ucy9ab29tT3V0T3V0bGluZWQuanM/NWU3YiIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2ltYWdlL3N0eWxlL2luZGV4LmpzPzQ4NDkiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbWFnZS9QcmV2aWV3R3JvdXAuanM/Yzk1ZCIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2ltYWdlL2luZGV4LmpzPzQyMmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBDT01NT05fUFJPUFMgPSBbJ2Nyb3NzT3JpZ2luJywgJ2RlY29kaW5nJywgJ2RyYWdnYWJsZScsICdsb2FkaW5nJywgJ3JlZmVycmVyUG9saWN5JywgJ3NpemVzJywgJ3NyY1NldCcsICd1c2VNYXAnLCAnYWx0J107IiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBQcmV2aWV3R3JvdXBDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7IiwiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBQcmV2aWV3R3JvdXBDb250ZXh0IH0gZnJvbSBcIi4uL2NvbnRleHRcIjtcbnZhciB1aWQgPSAwO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUmVnaXN0ZXJJbWFnZShjYW5QcmV2aWV3LCBkYXRhKSB7XG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZShmdW5jdGlvbiAoKSB7XG4gICAgICB1aWQgKz0gMTtcbiAgICAgIHJldHVybiBTdHJpbmcodWlkKTtcbiAgICB9KSxcbiAgICBfUmVhY3QkdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlLCAxKSxcbiAgICBpZCA9IF9SZWFjdCR1c2VTdGF0ZTJbMF07XG4gIHZhciBncm91cENvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KFByZXZpZXdHcm91cENvbnRleHQpO1xuICB2YXIgcmVnaXN0ZXJEYXRhID0ge1xuICAgIGRhdGE6IGRhdGEsXG4gICAgY2FuUHJldmlldzogY2FuUHJldmlld1xuICB9O1xuXG4gIC8vIEtlZXAgb3JkZXIgc3RhcnRcbiAgLy8gUmVzb2x2ZSBodHRwczovL2dpdGh1Yi5jb20vYW50LWRlc2lnbi9hbnQtZGVzaWduL2lzc3Vlcy8yODg4MVxuICAvLyBPbmx5IG5lZWQgdW5SZWdpc3RlciB3aGVuIGNvbXBvbmVudCB1bk1vdW50XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKGdyb3VwQ29udGV4dCkge1xuICAgICAgcmV0dXJuIGdyb3VwQ29udGV4dC5yZWdpc3RlcihpZCwgcmVnaXN0ZXJEYXRhKTtcbiAgICB9XG4gIH0sIFtdKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoZ3JvdXBDb250ZXh0KSB7XG4gICAgICBncm91cENvbnRleHQucmVnaXN0ZXIoaWQsIHJlZ2lzdGVyRGF0YSk7XG4gICAgfVxuICB9LCBbY2FuUHJldmlldywgZGF0YV0pO1xuICByZXR1cm4gaWQ7XG59IiwiZXhwb3J0IGZ1bmN0aW9uIGlzSW1hZ2VWYWxpZChzcmMpIHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7XG4gICAgdmFyIGltZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2ltZycpO1xuICAgIGltZy5vbmVycm9yID0gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIHJlc29sdmUoZmFsc2UpO1xuICAgIH07XG4gICAgaW1nLm9ubG9hZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiByZXNvbHZlKHRydWUpO1xuICAgIH07XG4gICAgaW1nLnNyYyA9IHNyYztcbiAgfSk7XG59IiwiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpc0ltYWdlVmFsaWQgfSBmcm9tIFwiLi4vdXRpbFwiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlU3RhdHVzKF9yZWYpIHtcbiAgdmFyIHNyYyA9IF9yZWYuc3JjLFxuICAgIGlzQ3VzdG9tUGxhY2Vob2xkZXIgPSBfcmVmLmlzQ3VzdG9tUGxhY2Vob2xkZXIsXG4gICAgZmFsbGJhY2sgPSBfcmVmLmZhbGxiYWNrO1xuICB2YXIgX3VzZVN0YXRlID0gdXNlU3RhdGUoaXNDdXN0b21QbGFjZWhvbGRlciA/ICdsb2FkaW5nJyA6ICdub3JtYWwnKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBzdGF0dXMgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldFN0YXR1cyA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBpc0xvYWRlZCA9IHVzZVJlZihmYWxzZSk7XG4gIHZhciBpc0Vycm9yID0gc3RhdHVzID09PSAnZXJyb3InO1xuXG4gIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9yZWFjdC1jb21wb25lbnQvaW1hZ2UvcHVsbC8xODdcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgaXNDdXJyZW50U3JjID0gdHJ1ZTtcbiAgICBpc0ltYWdlVmFsaWQoc3JjKS50aGVuKGZ1bmN0aW9uIChpc1ZhbGlkKSB7XG4gICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYW50LWRlc2lnbi9hbnQtZGVzaWduL2lzc3Vlcy80NDk0OFxuICAgICAgLy8gSWYgc3JjIGNoYW5nZXMsIHRoZSBwcmV2aW91cyBzZXRTdGF0dXMgc2hvdWxkIG5vdCBiZSB0cmlnZ2VyZWRcbiAgICAgIGlmICghaXNWYWxpZCAmJiBpc0N1cnJlbnRTcmMpIHtcbiAgICAgICAgc2V0U3RhdHVzKCdlcnJvcicpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBpc0N1cnJlbnRTcmMgPSBmYWxzZTtcbiAgICB9O1xuICB9LCBbc3JjXSk7XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKGlzQ3VzdG9tUGxhY2Vob2xkZXIgJiYgIWlzTG9hZGVkLmN1cnJlbnQpIHtcbiAgICAgIHNldFN0YXR1cygnbG9hZGluZycpO1xuICAgIH0gZWxzZSBpZiAoaXNFcnJvcikge1xuICAgICAgc2V0U3RhdHVzKCdub3JtYWwnKTtcbiAgICB9XG4gIH0sIFtzcmNdKTtcbiAgdmFyIG9uTG9hZCA9IGZ1bmN0aW9uIG9uTG9hZCgpIHtcbiAgICBzZXRTdGF0dXMoJ25vcm1hbCcpO1xuICB9O1xuICB2YXIgZ2V0SW1nUmVmID0gZnVuY3Rpb24gZ2V0SW1nUmVmKGltZykge1xuICAgIGlzTG9hZGVkLmN1cnJlbnQgPSBmYWxzZTtcbiAgICBpZiAoc3RhdHVzID09PSAnbG9hZGluZycgJiYgaW1nICE9PSBudWxsICYmIGltZyAhPT0gdm9pZCAwICYmIGltZy5jb21wbGV0ZSAmJiAoaW1nLm5hdHVyYWxXaWR0aCB8fCBpbWcubmF0dXJhbEhlaWdodCkpIHtcbiAgICAgIGlzTG9hZGVkLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgb25Mb2FkKCk7XG4gICAgfVxuICB9O1xuICB2YXIgc3JjQW5kT25sb2FkID0gaXNFcnJvciAmJiBmYWxsYmFjayA/IHtcbiAgICBzcmM6IGZhbGxiYWNrXG4gIH0gOiB7XG4gICAgb25Mb2FkOiBvbkxvYWQsXG4gICAgc3JjOiBzcmNcbiAgfTtcbiAgcmV0dXJuIFtnZXRJbWdSZWYsIHNyY0FuZE9ubG9hZCwgc3RhdHVzXTtcbn0iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIFJlZkNvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dCh7fSk7IiwiLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBNb3Rpb24gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuZXhwb3J0IGZ1bmN0aW9uIGdldE1vdGlvbk5hbWUocHJlZml4Q2xzLCB0cmFuc2l0aW9uTmFtZSwgYW5pbWF0aW9uTmFtZSkge1xuICB2YXIgbW90aW9uTmFtZSA9IHRyYW5zaXRpb25OYW1lO1xuICBpZiAoIW1vdGlvbk5hbWUgJiYgYW5pbWF0aW9uTmFtZSkge1xuICAgIG1vdGlvbk5hbWUgPSBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLVwiKS5jb25jYXQoYW5pbWF0aW9uTmFtZSk7XG4gIH1cbiAgcmV0dXJuIG1vdGlvbk5hbWU7XG59XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gT2Zmc2V0ID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbmZ1bmN0aW9uIGdldFNjcm9sbCh3LCB0b3ApIHtcbiAgdmFyIHJldCA9IHdbXCJwYWdlXCIuY29uY2F0KHRvcCA/ICdZJyA6ICdYJywgXCJPZmZzZXRcIildO1xuICB2YXIgbWV0aG9kID0gXCJzY3JvbGxcIi5jb25jYXQodG9wID8gJ1RvcCcgOiAnTGVmdCcpO1xuICBpZiAodHlwZW9mIHJldCAhPT0gJ251bWJlcicpIHtcbiAgICB2YXIgZCA9IHcuZG9jdW1lbnQ7XG4gICAgcmV0ID0gZC5kb2N1bWVudEVsZW1lbnRbbWV0aG9kXTtcbiAgICBpZiAodHlwZW9mIHJldCAhPT0gJ251bWJlcicpIHtcbiAgICAgIHJldCA9IGQuYm9keVttZXRob2RdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gcmV0O1xufVxuZXhwb3J0IGZ1bmN0aW9uIG9mZnNldChlbCkge1xuICB2YXIgcmVjdCA9IGVsLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICB2YXIgcG9zID0ge1xuICAgIGxlZnQ6IHJlY3QubGVmdCxcbiAgICB0b3A6IHJlY3QudG9wXG4gIH07XG4gIHZhciBkb2MgPSBlbC5vd25lckRvY3VtZW50O1xuICB2YXIgdyA9IGRvYy5kZWZhdWx0VmlldyB8fCBkb2MucGFyZW50V2luZG93O1xuICBwb3MubGVmdCArPSBnZXRTY3JvbGwodyk7XG4gIHBvcy50b3AgKz0gZ2V0U2Nyb2xsKHcsIHRydWUpO1xuICByZXR1cm4gcG9zO1xufSIsImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5tZW1vKGZ1bmN0aW9uIChfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW47XG4gIHJldHVybiBjaGlsZHJlbjtcbn0sIGZ1bmN0aW9uIChfLCBfcmVmMikge1xuICB2YXIgc2hvdWxkVXBkYXRlID0gX3JlZjIuc2hvdWxkVXBkYXRlO1xuICByZXR1cm4gIXNob3VsZFVwZGF0ZTtcbn0pOyIsImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZVJlZiB9IGZyb20gXCJyYy11dGlsL2VzL3JlZlwiO1xuaW1wb3J0IFJlYWN0LCB7IHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFJlZkNvbnRleHQgfSBmcm9tIFwiLi4vLi4vY29udGV4dFwiO1xuaW1wb3J0IE1lbW9DaGlsZHJlbiBmcm9tIFwiLi9NZW1vQ2hpbGRyZW5cIjtcbnZhciBzZW50aW5lbFN0eWxlID0ge1xuICB3aWR0aDogMCxcbiAgaGVpZ2h0OiAwLFxuICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gIG91dGxpbmU6ICdub25lJ1xufTtcbnZhciBQYW5lbCA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgY2xhc3NOYW1lID0gcHJvcHMuY2xhc3NOYW1lLFxuICAgIHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgdGl0bGUgPSBwcm9wcy50aXRsZSxcbiAgICBhcmlhSWQgPSBwcm9wcy5hcmlhSWQsXG4gICAgZm9vdGVyID0gcHJvcHMuZm9vdGVyLFxuICAgIGNsb3NhYmxlID0gcHJvcHMuY2xvc2FibGUsXG4gICAgY2xvc2VJY29uID0gcHJvcHMuY2xvc2VJY29uLFxuICAgIG9uQ2xvc2UgPSBwcm9wcy5vbkNsb3NlLFxuICAgIGNoaWxkcmVuID0gcHJvcHMuY2hpbGRyZW4sXG4gICAgYm9keVN0eWxlID0gcHJvcHMuYm9keVN0eWxlLFxuICAgIGJvZHlQcm9wcyA9IHByb3BzLmJvZHlQcm9wcyxcbiAgICBtb2RhbFJlbmRlciA9IHByb3BzLm1vZGFsUmVuZGVyLFxuICAgIG9uTW91c2VEb3duID0gcHJvcHMub25Nb3VzZURvd24sXG4gICAgb25Nb3VzZVVwID0gcHJvcHMub25Nb3VzZVVwLFxuICAgIGhvbGRlclJlZiA9IHByb3BzLmhvbGRlclJlZixcbiAgICB2aXNpYmxlID0gcHJvcHMudmlzaWJsZSxcbiAgICBmb3JjZVJlbmRlciA9IHByb3BzLmZvcmNlUmVuZGVyLFxuICAgIHdpZHRoID0gcHJvcHMud2lkdGgsXG4gICAgaGVpZ2h0ID0gcHJvcHMuaGVpZ2h0LFxuICAgIG1vZGFsQ2xhc3NOYW1lcyA9IHByb3BzLmNsYXNzTmFtZXMsXG4gICAgbW9kYWxTdHlsZXMgPSBwcm9wcy5zdHlsZXM7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IFJlZnMgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBfUmVhY3QkdXNlQ29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoUmVmQ29udGV4dCksXG4gICAgcGFuZWxSZWYgPSBfUmVhY3QkdXNlQ29udGV4dC5wYW5lbDtcbiAgdmFyIG1lcmdlZFJlZiA9IHVzZUNvbXBvc2VSZWYoaG9sZGVyUmVmLCBwYW5lbFJlZik7XG4gIHZhciBzZW50aW5lbFN0YXJ0UmVmID0gdXNlUmVmKCk7XG4gIHZhciBzZW50aW5lbEVuZFJlZiA9IHVzZVJlZigpO1xuICBSZWFjdC51c2VJbXBlcmF0aXZlSGFuZGxlKHJlZiwgZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiB7XG4gICAgICBmb2N1czogZnVuY3Rpb24gZm9jdXMoKSB7XG4gICAgICAgIHZhciBfc2VudGluZWxTdGFydFJlZiRjdXI7XG4gICAgICAgIChfc2VudGluZWxTdGFydFJlZiRjdXIgPSBzZW50aW5lbFN0YXJ0UmVmLmN1cnJlbnQpID09PSBudWxsIHx8IF9zZW50aW5lbFN0YXJ0UmVmJGN1ciA9PT0gdm9pZCAwIHx8IF9zZW50aW5lbFN0YXJ0UmVmJGN1ci5mb2N1cygpO1xuICAgICAgfSxcbiAgICAgIGNoYW5nZUFjdGl2ZTogZnVuY3Rpb24gY2hhbmdlQWN0aXZlKG5leHQpIHtcbiAgICAgICAgdmFyIF9kb2N1bWVudCA9IGRvY3VtZW50LFxuICAgICAgICAgIGFjdGl2ZUVsZW1lbnQgPSBfZG9jdW1lbnQuYWN0aXZlRWxlbWVudDtcbiAgICAgICAgaWYgKG5leHQgJiYgYWN0aXZlRWxlbWVudCA9PT0gc2VudGluZWxFbmRSZWYuY3VycmVudCkge1xuICAgICAgICAgIHNlbnRpbmVsU3RhcnRSZWYuY3VycmVudC5mb2N1cygpO1xuICAgICAgICB9IGVsc2UgaWYgKCFuZXh0ICYmIGFjdGl2ZUVsZW1lbnQgPT09IHNlbnRpbmVsU3RhcnRSZWYuY3VycmVudCkge1xuICAgICAgICAgIHNlbnRpbmVsRW5kUmVmLmN1cnJlbnQuZm9jdXMoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gIH0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IFN0eWxlID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgY29udGVudFN0eWxlID0ge307XG4gIGlmICh3aWR0aCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29udGVudFN0eWxlLndpZHRoID0gd2lkdGg7XG4gIH1cbiAgaWYgKGhlaWdodCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29udGVudFN0eWxlLmhlaWdodCA9IGhlaWdodDtcbiAgfVxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIGZvb3Rlck5vZGU7XG4gIGlmIChmb290ZXIpIHtcbiAgICBmb290ZXJOb2RlID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgY2xhc3NOYW1lOiBjbGFzc05hbWVzKFwiXCIuY29uY2F0KHByZWZpeENscywgXCItZm9vdGVyXCIpLCBtb2RhbENsYXNzTmFtZXMgPT09IG51bGwgfHwgbW9kYWxDbGFzc05hbWVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RhbENsYXNzTmFtZXMuZm9vdGVyKSxcbiAgICAgIHN0eWxlOiBfb2JqZWN0U3ByZWFkKHt9LCBtb2RhbFN0eWxlcyA9PT0gbnVsbCB8fCBtb2RhbFN0eWxlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kYWxTdHlsZXMuZm9vdGVyKVxuICAgIH0sIGZvb3Rlcik7XG4gIH1cbiAgdmFyIGhlYWRlck5vZGU7XG4gIGlmICh0aXRsZSkge1xuICAgIGhlYWRlck5vZGUgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMoXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1oZWFkZXJcIiksIG1vZGFsQ2xhc3NOYW1lcyA9PT0gbnVsbCB8fCBtb2RhbENsYXNzTmFtZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGFsQ2xhc3NOYW1lcy5oZWFkZXIpLFxuICAgICAgc3R5bGU6IF9vYmplY3RTcHJlYWQoe30sIG1vZGFsU3R5bGVzID09PSBudWxsIHx8IG1vZGFsU3R5bGVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RhbFN0eWxlcy5oZWFkZXIpXG4gICAgfSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXRpdGxlXCIpLFxuICAgICAgaWQ6IGFyaWFJZFxuICAgIH0sIHRpdGxlKSk7XG4gIH1cbiAgdmFyIGNsb3NlcjtcbiAgaWYgKGNsb3NhYmxlKSB7XG4gICAgY2xvc2VyID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJidXR0b25cIiwge1xuICAgICAgdHlwZTogXCJidXR0b25cIixcbiAgICAgIG9uQ2xpY2s6IG9uQ2xvc2UsXG4gICAgICBcImFyaWEtbGFiZWxcIjogXCJDbG9zZVwiLFxuICAgICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWNsb3NlXCIpXG4gICAgfSwgY2xvc2VJY29uIHx8IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3BhblwiLCB7XG4gICAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItY2xvc2UteFwiKVxuICAgIH0pKTtcbiAgfVxuICB2YXIgY29udGVudCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMoXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1jb250ZW50XCIpLCBtb2RhbENsYXNzTmFtZXMgPT09IG51bGwgfHwgbW9kYWxDbGFzc05hbWVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RhbENsYXNzTmFtZXMuY29udGVudCksXG4gICAgc3R5bGU6IG1vZGFsU3R5bGVzID09PSBudWxsIHx8IG1vZGFsU3R5bGVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBtb2RhbFN0eWxlcy5jb250ZW50XG4gIH0sIGNsb3NlciwgaGVhZGVyTm9kZSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX2V4dGVuZHMoe1xuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWJvZHlcIiksIG1vZGFsQ2xhc3NOYW1lcyA9PT0gbnVsbCB8fCBtb2RhbENsYXNzTmFtZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGFsQ2xhc3NOYW1lcy5ib2R5KSxcbiAgICBzdHlsZTogX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBib2R5U3R5bGUpLCBtb2RhbFN0eWxlcyA9PT0gbnVsbCB8fCBtb2RhbFN0eWxlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kYWxTdHlsZXMuYm9keSlcbiAgfSwgYm9keVByb3BzKSwgY2hpbGRyZW4pLCBmb290ZXJOb2RlKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBrZXk6IFwiZGlhbG9nLWVsZW1lbnRcIixcbiAgICByb2xlOiBcImRpYWxvZ1wiLFxuICAgIFwiYXJpYS1sYWJlbGxlZGJ5XCI6IHRpdGxlID8gYXJpYUlkIDogbnVsbCxcbiAgICBcImFyaWEtbW9kYWxcIjogXCJ0cnVlXCIsXG4gICAgcmVmOiBtZXJnZWRSZWYsXG4gICAgc3R5bGU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgc3R5bGUpLCBjb250ZW50U3R5bGUpLFxuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhwcmVmaXhDbHMsIGNsYXNzTmFtZSksXG4gICAgb25Nb3VzZURvd246IG9uTW91c2VEb3duLFxuICAgIG9uTW91c2VVcDogb25Nb3VzZVVwXG4gIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICB0YWJJbmRleDogMCxcbiAgICByZWY6IHNlbnRpbmVsU3RhcnRSZWYsXG4gICAgc3R5bGU6IHNlbnRpbmVsU3R5bGUsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIlxuICB9KSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTWVtb0NoaWxkcmVuLCB7XG4gICAgc2hvdWxkVXBkYXRlOiB2aXNpYmxlIHx8IGZvcmNlUmVuZGVyXG4gIH0sIG1vZGFsUmVuZGVyID8gbW9kYWxSZW5kZXIoY29udGVudCkgOiBjb250ZW50KSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIHRhYkluZGV4OiAwLFxuICAgIHJlZjogc2VudGluZWxFbmRSZWYsXG4gICAgc3R5bGU6IHNlbnRpbmVsU3R5bGUsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIlxuICB9KSk7XG59KTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFBhbmVsLmRpc3BsYXlOYW1lID0gJ1BhbmVsJztcbn1cbmV4cG9ydCBkZWZhdWx0IFBhbmVsOyIsImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgQ1NTTW90aW9uIGZyb20gJ3JjLW1vdGlvbic7XG5pbXBvcnQgeyBvZmZzZXQgfSBmcm9tIFwiLi4vLi4vdXRpbFwiO1xuaW1wb3J0IFBhbmVsIGZyb20gXCIuL1BhbmVsXCI7XG52YXIgQ29udGVudCA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgdGl0bGUgPSBwcm9wcy50aXRsZSxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICB2aXNpYmxlID0gcHJvcHMudmlzaWJsZSxcbiAgICBmb3JjZVJlbmRlciA9IHByb3BzLmZvcmNlUmVuZGVyLFxuICAgIGRlc3Ryb3lPbkNsb3NlID0gcHJvcHMuZGVzdHJveU9uQ2xvc2UsXG4gICAgbW90aW9uTmFtZSA9IHByb3BzLm1vdGlvbk5hbWUsXG4gICAgYXJpYUlkID0gcHJvcHMuYXJpYUlkLFxuICAgIG9uVmlzaWJsZUNoYW5nZWQgPSBwcm9wcy5vblZpc2libGVDaGFuZ2VkLFxuICAgIG1vdXNlUG9zaXRpb24gPSBwcm9wcy5tb3VzZVBvc2l0aW9uO1xuICB2YXIgZGlhbG9nUmVmID0gdXNlUmVmKCk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gU3R5bGUgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZSgpLFxuICAgIF9SZWFjdCR1c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfUmVhY3QkdXNlU3RhdGUsIDIpLFxuICAgIHRyYW5zZm9ybU9yaWdpbiA9IF9SZWFjdCR1c2VTdGF0ZTJbMF0sXG4gICAgc2V0VHJhbnNmb3JtT3JpZ2luID0gX1JlYWN0JHVzZVN0YXRlMlsxXTtcbiAgdmFyIGNvbnRlbnRTdHlsZSA9IHt9O1xuICBpZiAodHJhbnNmb3JtT3JpZ2luKSB7XG4gICAgY29udGVudFN0eWxlLnRyYW5zZm9ybU9yaWdpbiA9IHRyYW5zZm9ybU9yaWdpbjtcbiAgfVxuICBmdW5jdGlvbiBvblByZXBhcmUoKSB7XG4gICAgdmFyIGVsZW1lbnRPZmZzZXQgPSBvZmZzZXQoZGlhbG9nUmVmLmN1cnJlbnQpO1xuICAgIHNldFRyYW5zZm9ybU9yaWdpbihtb3VzZVBvc2l0aW9uID8gXCJcIi5jb25jYXQobW91c2VQb3NpdGlvbi54IC0gZWxlbWVudE9mZnNldC5sZWZ0LCBcInB4IFwiKS5jb25jYXQobW91c2VQb3NpdGlvbi55IC0gZWxlbWVudE9mZnNldC50b3AsIFwicHhcIikgOiAnJyk7XG4gIH1cblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENTU01vdGlvbiwge1xuICAgIHZpc2libGU6IHZpc2libGUsXG4gICAgb25WaXNpYmxlQ2hhbmdlZDogb25WaXNpYmxlQ2hhbmdlZCxcbiAgICBvbkFwcGVhclByZXBhcmU6IG9uUHJlcGFyZSxcbiAgICBvbkVudGVyUHJlcGFyZTogb25QcmVwYXJlLFxuICAgIGZvcmNlUmVuZGVyOiBmb3JjZVJlbmRlcixcbiAgICBtb3Rpb25OYW1lOiBtb3Rpb25OYW1lLFxuICAgIHJlbW92ZU9uTGVhdmU6IGRlc3Ryb3lPbkNsb3NlLFxuICAgIHJlZjogZGlhbG9nUmVmXG4gIH0sIGZ1bmN0aW9uIChfcmVmLCBtb3Rpb25SZWYpIHtcbiAgICB2YXIgbW90aW9uQ2xhc3NOYW1lID0gX3JlZi5jbGFzc05hbWUsXG4gICAgICBtb3Rpb25TdHlsZSA9IF9yZWYuc3R5bGU7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFBhbmVsLCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICAgIHJlZjogcmVmLFxuICAgICAgdGl0bGU6IHRpdGxlLFxuICAgICAgYXJpYUlkOiBhcmlhSWQsXG4gICAgICBwcmVmaXhDbHM6IHByZWZpeENscyxcbiAgICAgIGhvbGRlclJlZjogbW90aW9uUmVmLFxuICAgICAgc3R5bGU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBtb3Rpb25TdHlsZSksIHN0eWxlKSwgY29udGVudFN0eWxlKSxcbiAgICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhjbGFzc05hbWUsIG1vdGlvbkNsYXNzTmFtZSlcbiAgICB9KSk7XG4gIH0pO1xufSk7XG5Db250ZW50LmRpc3BsYXlOYW1lID0gJ0NvbnRlbnQnO1xuZXhwb3J0IGRlZmF1bHQgQ29udGVudDsiLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgY2xhc3NOYW1lcyBmcm9tICdjbGFzc25hbWVzJztcbmltcG9ydCBDU1NNb3Rpb24gZnJvbSAncmMtbW90aW9uJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hc2socHJvcHMpIHtcbiAgdmFyIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscyxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIHZpc2libGUgPSBwcm9wcy52aXNpYmxlLFxuICAgIG1hc2tQcm9wcyA9IHByb3BzLm1hc2tQcm9wcyxcbiAgICBtb3Rpb25OYW1lID0gcHJvcHMubW90aW9uTmFtZSxcbiAgICBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWU7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDU1NNb3Rpb24sIHtcbiAgICBrZXk6IFwibWFza1wiLFxuICAgIHZpc2libGU6IHZpc2libGUsXG4gICAgbW90aW9uTmFtZTogbW90aW9uTmFtZSxcbiAgICBsZWF2ZWRDbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItbWFzay1oaWRkZW5cIilcbiAgfSwgZnVuY3Rpb24gKF9yZWYsIHJlZikge1xuICAgIHZhciBtb3Rpb25DbGFzc05hbWUgPSBfcmVmLmNsYXNzTmFtZSxcbiAgICAgIG1vdGlvblN0eWxlID0gX3JlZi5zdHlsZTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX2V4dGVuZHMoe1xuICAgICAgcmVmOiByZWYsXG4gICAgICBzdHlsZTogX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBtb3Rpb25TdHlsZSksIHN0eWxlKSxcbiAgICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW1hc2tcIiksIG1vdGlvbkNsYXNzTmFtZSwgY2xhc3NOYW1lKVxuICAgIH0sIG1hc2tQcm9wcykpO1xuICB9KTtcbn0iLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IGNvbnRhaW5zIGZyb20gXCJyYy11dGlsL2VzL0RvbS9jb250YWluc1wiO1xuaW1wb3J0IHVzZUlkIGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZUlkXCI7XG5pbXBvcnQgS2V5Q29kZSBmcm9tIFwicmMtdXRpbC9lcy9LZXlDb2RlXCI7XG5pbXBvcnQgcGlja0F0dHJzIGZyb20gXCJyYy11dGlsL2VzL3BpY2tBdHRyc1wiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBnZXRNb3Rpb25OYW1lIH0gZnJvbSBcIi4uL3V0aWxcIjtcbmltcG9ydCBDb250ZW50IGZyb20gXCIuL0NvbnRlbnRcIjtcbmltcG9ydCBNYXNrIGZyb20gXCIuL01hc2tcIjtcbmltcG9ydCB7IHdhcm5pbmcgfSBmcm9tIFwicmMtdXRpbC9lcy93YXJuaW5nXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEaWFsb2cocHJvcHMpIHtcbiAgdmFyIF9wcm9wcyRwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgcHJlZml4Q2xzID0gX3Byb3BzJHByZWZpeENscyA9PT0gdm9pZCAwID8gJ3JjLWRpYWxvZycgOiBfcHJvcHMkcHJlZml4Q2xzLFxuICAgIHpJbmRleCA9IHByb3BzLnpJbmRleCxcbiAgICBfcHJvcHMkdmlzaWJsZSA9IHByb3BzLnZpc2libGUsXG4gICAgdmlzaWJsZSA9IF9wcm9wcyR2aXNpYmxlID09PSB2b2lkIDAgPyBmYWxzZSA6IF9wcm9wcyR2aXNpYmxlLFxuICAgIF9wcm9wcyRrZXlib2FyZCA9IHByb3BzLmtleWJvYXJkLFxuICAgIGtleWJvYXJkID0gX3Byb3BzJGtleWJvYXJkID09PSB2b2lkIDAgPyB0cnVlIDogX3Byb3BzJGtleWJvYXJkLFxuICAgIF9wcm9wcyRmb2N1c1RyaWdnZXJBZiA9IHByb3BzLmZvY3VzVHJpZ2dlckFmdGVyQ2xvc2UsXG4gICAgZm9jdXNUcmlnZ2VyQWZ0ZXJDbG9zZSA9IF9wcm9wcyRmb2N1c1RyaWdnZXJBZiA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRmb2N1c1RyaWdnZXJBZixcbiAgICB3cmFwU3R5bGUgPSBwcm9wcy53cmFwU3R5bGUsXG4gICAgd3JhcENsYXNzTmFtZSA9IHByb3BzLndyYXBDbGFzc05hbWUsXG4gICAgd3JhcFByb3BzID0gcHJvcHMud3JhcFByb3BzLFxuICAgIG9uQ2xvc2UgPSBwcm9wcy5vbkNsb3NlLFxuICAgIGFmdGVyT3BlbkNoYW5nZSA9IHByb3BzLmFmdGVyT3BlbkNoYW5nZSxcbiAgICBhZnRlckNsb3NlID0gcHJvcHMuYWZ0ZXJDbG9zZSxcbiAgICB0cmFuc2l0aW9uTmFtZSA9IHByb3BzLnRyYW5zaXRpb25OYW1lLFxuICAgIGFuaW1hdGlvbiA9IHByb3BzLmFuaW1hdGlvbixcbiAgICBfcHJvcHMkY2xvc2FibGUgPSBwcm9wcy5jbG9zYWJsZSxcbiAgICBjbG9zYWJsZSA9IF9wcm9wcyRjbG9zYWJsZSA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRjbG9zYWJsZSxcbiAgICBfcHJvcHMkbWFzayA9IHByb3BzLm1hc2ssXG4gICAgbWFzayA9IF9wcm9wcyRtYXNrID09PSB2b2lkIDAgPyB0cnVlIDogX3Byb3BzJG1hc2ssXG4gICAgbWFza1RyYW5zaXRpb25OYW1lID0gcHJvcHMubWFza1RyYW5zaXRpb25OYW1lLFxuICAgIG1hc2tBbmltYXRpb24gPSBwcm9wcy5tYXNrQW5pbWF0aW9uLFxuICAgIF9wcm9wcyRtYXNrQ2xvc2FibGUgPSBwcm9wcy5tYXNrQ2xvc2FibGUsXG4gICAgbWFza0Nsb3NhYmxlID0gX3Byb3BzJG1hc2tDbG9zYWJsZSA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRtYXNrQ2xvc2FibGUsXG4gICAgbWFza1N0eWxlID0gcHJvcHMubWFza1N0eWxlLFxuICAgIG1hc2tQcm9wcyA9IHByb3BzLm1hc2tQcm9wcyxcbiAgICByb290Q2xhc3NOYW1lID0gcHJvcHMucm9vdENsYXNzTmFtZSxcbiAgICBtb2RhbENsYXNzTmFtZXMgPSBwcm9wcy5jbGFzc05hbWVzLFxuICAgIG1vZGFsU3R5bGVzID0gcHJvcHMuc3R5bGVzO1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIFtcIndyYXBTdHlsZVwiLCBcImJvZHlTdHlsZVwiLCBcIm1hc2tTdHlsZVwiXS5mb3JFYWNoKGZ1bmN0aW9uIChwcm9wKSB7XG4gICAgICAvLyAocHJvcCBpbiBwcm9wcykgJiYgY29uc29sZS5lcnJvcihgV2FybmluZzogJHtwcm9wfSBpcyBkZXByZWNhdGVkLCBwbGVhc2UgdXNlIHN0eWxlcyBpbnN0ZWFkLmApXG4gICAgICB3YXJuaW5nKCEocHJvcCBpbiBwcm9wcyksIFwiXCIuY29uY2F0KHByb3AsIFwiIGlzIGRlcHJlY2F0ZWQsIHBsZWFzZSB1c2Ugc3R5bGVzIGluc3RlYWQuXCIpKTtcbiAgICB9KTtcbiAgICBpZiAoXCJ3cmFwQ2xhc3NOYW1lXCIgaW4gcHJvcHMpIHtcbiAgICAgIHdhcm5pbmcoZmFsc2UsIFwid3JhcENsYXNzTmFtZSBpcyBkZXByZWNhdGVkLCBwbGVhc2UgdXNlIGNsYXNzTmFtZXMgaW5zdGVhZC5cIik7XG4gICAgfVxuICB9XG4gIHZhciBsYXN0T3V0U2lkZUFjdGl2ZUVsZW1lbnRSZWYgPSB1c2VSZWYoKTtcbiAgdmFyIHdyYXBwZXJSZWYgPSB1c2VSZWYoKTtcbiAgdmFyIGNvbnRlbnRSZWYgPSB1c2VSZWYoKTtcbiAgdmFyIF9SZWFjdCR1c2VTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKHZpc2libGUpLFxuICAgIF9SZWFjdCR1c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfUmVhY3QkdXNlU3RhdGUsIDIpLFxuICAgIGFuaW1hdGVkVmlzaWJsZSA9IF9SZWFjdCR1c2VTdGF0ZTJbMF0sXG4gICAgc2V0QW5pbWF0ZWRWaXNpYmxlID0gX1JlYWN0JHVzZVN0YXRlMlsxXTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PSBJbml0ID09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBhcmlhSWQgPSB1c2VJZCgpO1xuICBmdW5jdGlvbiBzYXZlTGFzdE91dFNpZGVBY3RpdmVFbGVtZW50UmVmKCkge1xuICAgIGlmICghY29udGFpbnMod3JhcHBlclJlZi5jdXJyZW50LCBkb2N1bWVudC5hY3RpdmVFbGVtZW50KSkge1xuICAgICAgbGFzdE91dFNpZGVBY3RpdmVFbGVtZW50UmVmLmN1cnJlbnQgPSBkb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBmb2N1c0RpYWxvZ0NvbnRlbnQoKSB7XG4gICAgaWYgKCFjb250YWlucyh3cmFwcGVyUmVmLmN1cnJlbnQsIGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQpKSB7XG4gICAgICB2YXIgX2NvbnRlbnRSZWYkY3VycmVudDtcbiAgICAgIChfY29udGVudFJlZiRjdXJyZW50ID0gY29udGVudFJlZi5jdXJyZW50KSA9PT0gbnVsbCB8fCBfY29udGVudFJlZiRjdXJyZW50ID09PSB2b2lkIDAgfHwgX2NvbnRlbnRSZWYkY3VycmVudC5mb2N1cygpO1xuICAgIH1cbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gRXZlbnRzID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgZnVuY3Rpb24gb25EaWFsb2dWaXNpYmxlQ2hhbmdlZChuZXdWaXNpYmxlKSB7XG4gICAgLy8gVHJ5IHRvIGZvY3VzXG4gICAgaWYgKG5ld1Zpc2libGUpIHtcbiAgICAgIGZvY3VzRGlhbG9nQ29udGVudCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBDbGVhbiB1cCBzY3JvbGwgYmFyICYgZm9jdXMgYmFja1xuICAgICAgc2V0QW5pbWF0ZWRWaXNpYmxlKGZhbHNlKTtcbiAgICAgIGlmIChtYXNrICYmIGxhc3RPdXRTaWRlQWN0aXZlRWxlbWVudFJlZi5jdXJyZW50ICYmIGZvY3VzVHJpZ2dlckFmdGVyQ2xvc2UpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBsYXN0T3V0U2lkZUFjdGl2ZUVsZW1lbnRSZWYuY3VycmVudC5mb2N1cyh7XG4gICAgICAgICAgICBwcmV2ZW50U2Nyb2xsOiB0cnVlXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAvLyBEbyBub3RoaW5nXG4gICAgICAgIH1cbiAgICAgICAgbGFzdE91dFNpZGVBY3RpdmVFbGVtZW50UmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgfVxuXG4gICAgICAvLyBUcmlnZ2VyIGFmdGVyQ2xvc2Ugb25seSB3aGVuIGNoYW5nZSB2aXNpYmxlIGZyb20gdHJ1ZSB0byBmYWxzZVxuICAgICAgaWYgKGFuaW1hdGVkVmlzaWJsZSkge1xuICAgICAgICBhZnRlckNsb3NlID09PSBudWxsIHx8IGFmdGVyQ2xvc2UgPT09IHZvaWQgMCB8fCBhZnRlckNsb3NlKCk7XG4gICAgICB9XG4gICAgfVxuICAgIGFmdGVyT3BlbkNoYW5nZSA9PT0gbnVsbCB8fCBhZnRlck9wZW5DaGFuZ2UgPT09IHZvaWQgMCB8fCBhZnRlck9wZW5DaGFuZ2UobmV3VmlzaWJsZSk7XG4gIH1cbiAgZnVuY3Rpb24gb25JbnRlcm5hbENsb3NlKGUpIHtcbiAgICBvbkNsb3NlID09PSBudWxsIHx8IG9uQ2xvc2UgPT09IHZvaWQgMCB8fCBvbkNsb3NlKGUpO1xuICB9XG5cbiAgLy8gPj4+IENvbnRlbnRcbiAgdmFyIGNvbnRlbnRDbGlja1JlZiA9IHVzZVJlZihmYWxzZSk7XG4gIHZhciBjb250ZW50VGltZW91dFJlZiA9IHVzZVJlZigpO1xuXG4gIC8vIFdlIG5lZWQgcmVjb3JkIGNvbnRlbnQgY2xpY2sgaW5jYXNlIGNvbnRlbnQgcG9wdXAgb3V0IG9mIGRpYWxvZ1xuICB2YXIgb25Db250ZW50TW91c2VEb3duID0gZnVuY3Rpb24gb25Db250ZW50TW91c2VEb3duKCkge1xuICAgIGNsZWFyVGltZW91dChjb250ZW50VGltZW91dFJlZi5jdXJyZW50KTtcbiAgICBjb250ZW50Q2xpY2tSZWYuY3VycmVudCA9IHRydWU7XG4gIH07XG4gIHZhciBvbkNvbnRlbnRNb3VzZVVwID0gZnVuY3Rpb24gb25Db250ZW50TW91c2VVcCgpIHtcbiAgICBjb250ZW50VGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7XG4gICAgICBjb250ZW50Q2xpY2tSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vID4+PiBXcmFwcGVyXG4gIC8vIENsb3NlIG9ubHkgd2hlbiBlbGVtZW50IG5vdCBvbiBkaWFsb2dcbiAgdmFyIG9uV3JhcHBlckNsaWNrID0gbnVsbDtcbiAgaWYgKG1hc2tDbG9zYWJsZSkge1xuICAgIG9uV3JhcHBlckNsaWNrID0gZnVuY3Rpb24gb25XcmFwcGVyQ2xpY2soZSkge1xuICAgICAgaWYgKGNvbnRlbnRDbGlja1JlZi5jdXJyZW50KSB7XG4gICAgICAgIGNvbnRlbnRDbGlja1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICB9IGVsc2UgaWYgKHdyYXBwZXJSZWYuY3VycmVudCA9PT0gZS50YXJnZXQpIHtcbiAgICAgICAgb25JbnRlcm5hbENsb3NlKGUpO1xuICAgICAgfVxuICAgIH07XG4gIH1cbiAgZnVuY3Rpb24gb25XcmFwcGVyS2V5RG93bihlKSB7XG4gICAgaWYgKGtleWJvYXJkICYmIGUua2V5Q29kZSA9PT0gS2V5Q29kZS5FU0MpIHtcbiAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICBvbkludGVybmFsQ2xvc2UoZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8ga2VlcCBmb2N1cyBpbnNpZGUgZGlhbG9nXG4gICAgaWYgKHZpc2libGUpIHtcbiAgICAgIGlmIChlLmtleUNvZGUgPT09IEtleUNvZGUuVEFCKSB7XG4gICAgICAgIGNvbnRlbnRSZWYuY3VycmVudC5jaGFuZ2VBY3RpdmUoIWUuc2hpZnRLZXkpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gRWZmZWN0ID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAodmlzaWJsZSkge1xuICAgICAgc2V0QW5pbWF0ZWRWaXNpYmxlKHRydWUpO1xuICAgICAgc2F2ZUxhc3RPdXRTaWRlQWN0aXZlRWxlbWVudFJlZigpO1xuICAgIH1cbiAgfSwgW3Zpc2libGVdKTtcblxuICAvLyBSZW1vdmUgZGlyZWN0IHNob3VsZCBhbHNvIGNoZWNrIHRoZSBzY3JvbGwgYmFyIHVwZGF0ZVxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBjbGVhclRpbWVvdXQoY29udGVudFRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gUmVuZGVyID09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIF9leHRlbmRzKHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZXMoXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1yb290XCIpLCByb290Q2xhc3NOYW1lKVxuICB9LCBwaWNrQXR0cnMocHJvcHMsIHtcbiAgICBkYXRhOiB0cnVlXG4gIH0pKSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoTWFzaywge1xuICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgIHZpc2libGU6IG1hc2sgJiYgdmlzaWJsZSxcbiAgICBtb3Rpb25OYW1lOiBnZXRNb3Rpb25OYW1lKHByZWZpeENscywgbWFza1RyYW5zaXRpb25OYW1lLCBtYXNrQW5pbWF0aW9uKSxcbiAgICBzdHlsZTogX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHtcbiAgICAgIHpJbmRleDogekluZGV4XG4gICAgfSwgbWFza1N0eWxlKSwgbW9kYWxTdHlsZXMgPT09IG51bGwgfHwgbW9kYWxTdHlsZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGFsU3R5bGVzLm1hc2spLFxuICAgIG1hc2tQcm9wczogbWFza1Byb3BzLFxuICAgIGNsYXNzTmFtZTogbW9kYWxDbGFzc05hbWVzID09PSBudWxsIHx8IG1vZGFsQ2xhc3NOYW1lcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogbW9kYWxDbGFzc05hbWVzLm1hc2tcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIF9leHRlbmRzKHtcbiAgICB0YWJJbmRleDogLTEsXG4gICAgb25LZXlEb3duOiBvbldyYXBwZXJLZXlEb3duLFxuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lcyhcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXdyYXBcIiksIHdyYXBDbGFzc05hbWUsIG1vZGFsQ2xhc3NOYW1lcyA9PT0gbnVsbCB8fCBtb2RhbENsYXNzTmFtZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGFsQ2xhc3NOYW1lcy53cmFwcGVyKSxcbiAgICByZWY6IHdyYXBwZXJSZWYsXG4gICAgb25DbGljazogb25XcmFwcGVyQ2xpY2ssXG4gICAgc3R5bGU6IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHtcbiAgICAgIHpJbmRleDogekluZGV4XG4gICAgfSwgd3JhcFN0eWxlKSwgbW9kYWxTdHlsZXMgPT09IG51bGwgfHwgbW9kYWxTdHlsZXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG1vZGFsU3R5bGVzLndyYXBwZXIpLCB7fSwge1xuICAgICAgZGlzcGxheTogIWFuaW1hdGVkVmlzaWJsZSA/ICdub25lJyA6IG51bGxcbiAgICB9KVxuICB9LCB3cmFwUHJvcHMpLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChDb250ZW50LCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICBvbk1vdXNlRG93bjogb25Db250ZW50TW91c2VEb3duLFxuICAgIG9uTW91c2VVcDogb25Db250ZW50TW91c2VVcCxcbiAgICByZWY6IGNvbnRlbnRSZWYsXG4gICAgY2xvc2FibGU6IGNsb3NhYmxlLFxuICAgIGFyaWFJZDogYXJpYUlkLFxuICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgIHZpc2libGU6IHZpc2libGUgJiYgYW5pbWF0ZWRWaXNpYmxlLFxuICAgIG9uQ2xvc2U6IG9uSW50ZXJuYWxDbG9zZSxcbiAgICBvblZpc2libGVDaGFuZ2VkOiBvbkRpYWxvZ1Zpc2libGVDaGFuZ2VkLFxuICAgIG1vdGlvbk5hbWU6IGdldE1vdGlvbk5hbWUocHJlZml4Q2xzLCB0cmFuc2l0aW9uTmFtZSwgYW5pbWF0aW9uKVxuICB9KSkpKTtcbn0iLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IFBvcnRhbCBmcm9tICdAcmMtY29tcG9uZW50L3BvcnRhbCc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBSZWZDb250ZXh0IH0gZnJvbSBcIi4vY29udGV4dFwiO1xuaW1wb3J0IERpYWxvZyBmcm9tIFwiLi9EaWFsb2dcIjtcbi8vIGZpeCBpc3N1ZSAjMTA2NTZcbi8qXG4gKiBnZXRDb250YWluZXIgcmVtYXJrc1xuICogQ3VzdG9tIGNvbnRhaW5lciBzaG91bGQgbm90IGJlIHJldHVybiwgYmVjYXVzZSBpbiB0aGUgUG9ydGFsIGNvbXBvbmVudCwgaXQgd2lsbCByZW1vdmUgdGhlXG4gKiByZXR1cm4gY29udGFpbmVyIGVsZW1lbnQgaGVyZSwgaWYgdGhlIGN1c3RvbSBjb250YWluZXIgaXMgdGhlIG9ubHkgY2hpbGQgb2YgaXQncyBjb21wb25lbnQsXG4gKiBsaWtlIGlzc3VlICMxMDY1NiwgSXQgd2lsbCBoYXMgYSBjb25mbGljdCB3aXRoIHJlbW92ZUNoaWxkIG1ldGhvZCBpbiByZWFjdC1kb20uXG4gKiBTbyBoZXJlIHNob3VsZCBhZGQgYSBjaGlsZCAoZGl2IGVsZW1lbnQpIHRvIGN1c3RvbSBjb250YWluZXIuXG4gKiAqL1xudmFyIERpYWxvZ1dyYXAgPSBmdW5jdGlvbiBEaWFsb2dXcmFwKHByb3BzKSB7XG4gIHZhciB2aXNpYmxlID0gcHJvcHMudmlzaWJsZSxcbiAgICBnZXRDb250YWluZXIgPSBwcm9wcy5nZXRDb250YWluZXIsXG4gICAgZm9yY2VSZW5kZXIgPSBwcm9wcy5mb3JjZVJlbmRlcixcbiAgICBfcHJvcHMkZGVzdHJveU9uQ2xvc2UgPSBwcm9wcy5kZXN0cm95T25DbG9zZSxcbiAgICBkZXN0cm95T25DbG9zZSA9IF9wcm9wcyRkZXN0cm95T25DbG9zZSA9PT0gdm9pZCAwID8gZmFsc2UgOiBfcHJvcHMkZGVzdHJveU9uQ2xvc2UsXG4gICAgX2FmdGVyQ2xvc2UgPSBwcm9wcy5hZnRlckNsb3NlLFxuICAgIHBhbmVsUmVmID0gcHJvcHMucGFuZWxSZWY7XG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZSh2aXNpYmxlKSxcbiAgICBfUmVhY3QkdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlLCAyKSxcbiAgICBhbmltYXRlZFZpc2libGUgPSBfUmVhY3QkdXNlU3RhdGUyWzBdLFxuICAgIHNldEFuaW1hdGVkVmlzaWJsZSA9IF9SZWFjdCR1c2VTdGF0ZTJbMV07XG4gIHZhciByZWZDb250ZXh0ID0gUmVhY3QudXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHBhbmVsOiBwYW5lbFJlZlxuICAgIH07XG4gIH0sIFtwYW5lbFJlZl0pO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIGlmICh2aXNpYmxlKSB7XG4gICAgICBzZXRBbmltYXRlZFZpc2libGUodHJ1ZSk7XG4gICAgfVxuICB9LCBbdmlzaWJsZV0pO1xuXG4gIC8vIERlc3Ryb3kgb24gY2xvc2Ugd2lsbCByZW1vdmUgd3JhcHBlZCBkaXZcbiAgaWYgKCFmb3JjZVJlbmRlciAmJiBkZXN0cm95T25DbG9zZSAmJiAhYW5pbWF0ZWRWaXNpYmxlKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlZkNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogcmVmQ29udGV4dFxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChQb3J0YWwsIHtcbiAgICBvcGVuOiB2aXNpYmxlIHx8IGZvcmNlUmVuZGVyIHx8IGFuaW1hdGVkVmlzaWJsZSxcbiAgICBhdXRvRGVzdHJveTogZmFsc2UsXG4gICAgZ2V0Q29udGFpbmVyOiBnZXRDb250YWluZXIsXG4gICAgYXV0b0xvY2s6IHZpc2libGUgfHwgYW5pbWF0ZWRWaXNpYmxlXG4gIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KERpYWxvZywgX2V4dGVuZHMoe30sIHByb3BzLCB7XG4gICAgZGVzdHJveU9uQ2xvc2U6IGRlc3Ryb3lPbkNsb3NlLFxuICAgIGFmdGVyQ2xvc2U6IGZ1bmN0aW9uIGFmdGVyQ2xvc2UoKSB7XG4gICAgICBfYWZ0ZXJDbG9zZSA9PT0gbnVsbCB8fCBfYWZ0ZXJDbG9zZSA9PT0gdm9pZCAwIHx8IF9hZnRlckNsb3NlKCk7XG4gICAgICBzZXRBbmltYXRlZFZpc2libGUoZmFsc2UpO1xuICAgIH1cbiAgfSkpKSk7XG59O1xuRGlhbG9nV3JhcC5kaXNwbGF5TmFtZSA9ICdEaWFsb2cnO1xuZXhwb3J0IGRlZmF1bHQgRGlhbG9nV3JhcDsiLCJpbXBvcnQgRGlhbG9nV3JhcCBmcm9tIFwiLi9EaWFsb2dXcmFwXCI7XG5pbXBvcnQgUGFuZWwgZnJvbSBcIi4vRGlhbG9nL0NvbnRlbnQvUGFuZWxcIjtcbmV4cG9ydCB7IFBhbmVsIH07XG5leHBvcnQgZGVmYXVsdCBEaWFsb2dXcmFwOyIsImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCB7IGdldENsaWVudFNpemUgfSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vY3NzXCI7XG5pbXBvcnQgaXNFcXVhbCBmcm9tIFwicmMtdXRpbC9lcy9pc0VxdWFsXCI7XG5pbXBvcnQgcmFmIGZyb20gXCJyYy11dGlsL2VzL3JhZlwiO1xuaW1wb3J0IHsgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbnZhciBpbml0aWFsVHJhbnNmb3JtID0ge1xuICB4OiAwLFxuICB5OiAwLFxuICByb3RhdGU6IDAsXG4gIHNjYWxlOiAxLFxuICBmbGlwWDogZmFsc2UsXG4gIGZsaXBZOiBmYWxzZVxufTtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUltYWdlVHJhbnNmb3JtKGltZ1JlZiwgbWluU2NhbGUsIG1heFNjYWxlLCBvblRyYW5zZm9ybSkge1xuICB2YXIgZnJhbWUgPSB1c2VSZWYobnVsbCk7XG4gIHZhciBxdWV1ZSA9IHVzZVJlZihbXSk7XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShpbml0aWFsVHJhbnNmb3JtKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICB0cmFuc2Zvcm0gPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldFRyYW5zZm9ybSA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciByZXNldFRyYW5zZm9ybSA9IGZ1bmN0aW9uIHJlc2V0VHJhbnNmb3JtKGFjdGlvbikge1xuICAgIHNldFRyYW5zZm9ybShpbml0aWFsVHJhbnNmb3JtKTtcbiAgICBpZiAob25UcmFuc2Zvcm0gJiYgIWlzRXF1YWwoaW5pdGlhbFRyYW5zZm9ybSwgdHJhbnNmb3JtKSkge1xuICAgICAgb25UcmFuc2Zvcm0oe1xuICAgICAgICB0cmFuc2Zvcm06IGluaXRpYWxUcmFuc2Zvcm0sXG4gICAgICAgIGFjdGlvbjogYWN0aW9uXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgLyoqIERpcmVjdCB1cGRhdGUgdHJhbnNmb3JtICovXG4gIHZhciB1cGRhdGVUcmFuc2Zvcm0gPSBmdW5jdGlvbiB1cGRhdGVUcmFuc2Zvcm0obmV3VHJhbnNmb3JtLCBhY3Rpb24pIHtcbiAgICBpZiAoZnJhbWUuY3VycmVudCA9PT0gbnVsbCkge1xuICAgICAgcXVldWUuY3VycmVudCA9IFtdO1xuICAgICAgZnJhbWUuY3VycmVudCA9IHJhZihmdW5jdGlvbiAoKSB7XG4gICAgICAgIHNldFRyYW5zZm9ybShmdW5jdGlvbiAocHJlU3RhdGUpIHtcbiAgICAgICAgICB2YXIgbWVtb1N0YXRlID0gcHJlU3RhdGU7XG4gICAgICAgICAgcXVldWUuY3VycmVudC5mb3JFYWNoKGZ1bmN0aW9uIChxdWV1ZVN0YXRlKSB7XG4gICAgICAgICAgICBtZW1vU3RhdGUgPSBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIG1lbW9TdGF0ZSksIHF1ZXVlU3RhdGUpO1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIGZyYW1lLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICAgIG9uVHJhbnNmb3JtID09PSBudWxsIHx8IG9uVHJhbnNmb3JtID09PSB2b2lkIDAgfHwgb25UcmFuc2Zvcm0oe1xuICAgICAgICAgICAgdHJhbnNmb3JtOiBtZW1vU3RhdGUsXG4gICAgICAgICAgICBhY3Rpb246IGFjdGlvblxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiBtZW1vU3RhdGU7XG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgfVxuICAgIHF1ZXVlLmN1cnJlbnQucHVzaChfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHRyYW5zZm9ybSksIG5ld1RyYW5zZm9ybSkpO1xuICB9O1xuXG4gIC8qKiBTY2FsZSBhY2NvcmRpbmcgdG8gdGhlIHBvc2l0aW9uIG9mIGNlbnRlclggYW5kIGNlbnRlclkgKi9cbiAgdmFyIGRpc3BhdGNoWm9vbUNoYW5nZSA9IGZ1bmN0aW9uIGRpc3BhdGNoWm9vbUNoYW5nZShyYXRpbywgYWN0aW9uLCBjZW50ZXJYLCBjZW50ZXJZLCBpc1RvdWNoKSB7XG4gICAgdmFyIF9pbWdSZWYkY3VycmVudCA9IGltZ1JlZi5jdXJyZW50LFxuICAgICAgd2lkdGggPSBfaW1nUmVmJGN1cnJlbnQud2lkdGgsXG4gICAgICBoZWlnaHQgPSBfaW1nUmVmJGN1cnJlbnQuaGVpZ2h0LFxuICAgICAgb2Zmc2V0V2lkdGggPSBfaW1nUmVmJGN1cnJlbnQub2Zmc2V0V2lkdGgsXG4gICAgICBvZmZzZXRIZWlnaHQgPSBfaW1nUmVmJGN1cnJlbnQub2Zmc2V0SGVpZ2h0LFxuICAgICAgb2Zmc2V0TGVmdCA9IF9pbWdSZWYkY3VycmVudC5vZmZzZXRMZWZ0LFxuICAgICAgb2Zmc2V0VG9wID0gX2ltZ1JlZiRjdXJyZW50Lm9mZnNldFRvcDtcbiAgICB2YXIgbmV3UmF0aW8gPSByYXRpbztcbiAgICB2YXIgbmV3U2NhbGUgPSB0cmFuc2Zvcm0uc2NhbGUgKiByYXRpbztcbiAgICBpZiAobmV3U2NhbGUgPiBtYXhTY2FsZSkge1xuICAgICAgbmV3U2NhbGUgPSBtYXhTY2FsZTtcbiAgICAgIG5ld1JhdGlvID0gbWF4U2NhbGUgLyB0cmFuc2Zvcm0uc2NhbGU7XG4gICAgfSBlbHNlIGlmIChuZXdTY2FsZSA8IG1pblNjYWxlKSB7XG4gICAgICAvLyBGb3IgbW9iaWxlIGludGVyYWN0aW9ucywgYWxsb3cgc2NhbGluZyBkb3duIHRvIHRoZSBtaW5pbXVtIHNjYWxlLlxuICAgICAgbmV3U2NhbGUgPSBpc1RvdWNoID8gbmV3U2NhbGUgOiBtaW5TY2FsZTtcbiAgICAgIG5ld1JhdGlvID0gbmV3U2NhbGUgLyB0cmFuc2Zvcm0uc2NhbGU7XG4gICAgfVxuXG4gICAgLyoqIERlZmF1bHQgY2VudGVyIHBvaW50IHNjYWxpbmcgKi9cbiAgICB2YXIgbWVyZ2VkQ2VudGVyWCA9IGNlbnRlclggIT09IG51bGwgJiYgY2VudGVyWCAhPT0gdm9pZCAwID8gY2VudGVyWCA6IGlubmVyV2lkdGggLyAyO1xuICAgIHZhciBtZXJnZWRDZW50ZXJZID0gY2VudGVyWSAhPT0gbnVsbCAmJiBjZW50ZXJZICE9PSB2b2lkIDAgPyBjZW50ZXJZIDogaW5uZXJIZWlnaHQgLyAyO1xuICAgIHZhciBkaWZmUmF0aW8gPSBuZXdSYXRpbyAtIDE7XG4gICAgLyoqIERldmlhdGlvbiBjYWxjdWxhdGVkIGZyb20gaW1hZ2Ugc2l6ZSAqL1xuICAgIHZhciBkaWZmSW1nWCA9IGRpZmZSYXRpbyAqIHdpZHRoICogMC41O1xuICAgIHZhciBkaWZmSW1nWSA9IGRpZmZSYXRpbyAqIGhlaWdodCAqIDAuNTtcbiAgICAvKiogVGhlIGRpZmZlcmVuY2UgYmV0d2VlbiB0aGUgY2xpY2sgcG9zaXRpb24gYW5kIHRoZSBlZGdlIG9mIHRoZSBkb2N1bWVudCAqL1xuICAgIHZhciBkaWZmT2Zmc2V0TGVmdCA9IGRpZmZSYXRpbyAqIChtZXJnZWRDZW50ZXJYIC0gdHJhbnNmb3JtLnggLSBvZmZzZXRMZWZ0KTtcbiAgICB2YXIgZGlmZk9mZnNldFRvcCA9IGRpZmZSYXRpbyAqIChtZXJnZWRDZW50ZXJZIC0gdHJhbnNmb3JtLnkgLSBvZmZzZXRUb3ApO1xuICAgIC8qKiBGaW5hbCBwb3NpdGlvbmluZyAqL1xuICAgIHZhciBuZXdYID0gdHJhbnNmb3JtLnggLSAoZGlmZk9mZnNldExlZnQgLSBkaWZmSW1nWCk7XG4gICAgdmFyIG5ld1kgPSB0cmFuc2Zvcm0ueSAtIChkaWZmT2Zmc2V0VG9wIC0gZGlmZkltZ1kpO1xuXG4gICAgLyoqXG4gICAgICogV2hlbiB6b29taW5nIHRoZSBpbWFnZVxuICAgICAqIFdoZW4gdGhlIGltYWdlIHNpemUgaXMgc21hbGxlciB0aGFuIHRoZSB3aWR0aCBhbmQgaGVpZ2h0IG9mIHRoZSB3aW5kb3csIHRoZSBwb3NpdGlvbiBpcyBpbml0aWFsaXplZFxuICAgICAqL1xuICAgIGlmIChyYXRpbyA8IDEgJiYgbmV3U2NhbGUgPT09IDEpIHtcbiAgICAgIHZhciBtZXJnZWRXaWR0aCA9IG9mZnNldFdpZHRoICogbmV3U2NhbGU7XG4gICAgICB2YXIgbWVyZ2VkSGVpZ2h0ID0gb2Zmc2V0SGVpZ2h0ICogbmV3U2NhbGU7XG4gICAgICB2YXIgX2dldENsaWVudFNpemUgPSBnZXRDbGllbnRTaXplKCksXG4gICAgICAgIGNsaWVudFdpZHRoID0gX2dldENsaWVudFNpemUud2lkdGgsXG4gICAgICAgIGNsaWVudEhlaWdodCA9IF9nZXRDbGllbnRTaXplLmhlaWdodDtcbiAgICAgIGlmIChtZXJnZWRXaWR0aCA8PSBjbGllbnRXaWR0aCAmJiBtZXJnZWRIZWlnaHQgPD0gY2xpZW50SGVpZ2h0KSB7XG4gICAgICAgIG5ld1ggPSAwO1xuICAgICAgICBuZXdZID0gMDtcbiAgICAgIH1cbiAgICB9XG4gICAgdXBkYXRlVHJhbnNmb3JtKHtcbiAgICAgIHg6IG5ld1gsXG4gICAgICB5OiBuZXdZLFxuICAgICAgc2NhbGU6IG5ld1NjYWxlXG4gICAgfSwgYWN0aW9uKTtcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zZm9ybSxcbiAgICByZXNldFRyYW5zZm9ybTogcmVzZXRUcmFuc2Zvcm0sXG4gICAgdXBkYXRlVHJhbnNmb3JtOiB1cGRhdGVUcmFuc2Zvcm0sXG4gICAgZGlzcGF0Y2hab29tQ2hhbmdlOiBkaXNwYXRjaFpvb21DaGFuZ2VcbiAgfTtcbn0iLCJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbmltcG9ydCB7IGdldENsaWVudFNpemUgfSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vY3NzXCI7XG5mdW5jdGlvbiBmaXhQb2ludChrZXksIHN0YXJ0LCB3aWR0aCwgY2xpZW50V2lkdGgpIHtcbiAgdmFyIHN0YXJ0QWRkV2lkdGggPSBzdGFydCArIHdpZHRoO1xuICB2YXIgb2Zmc2V0U3RhcnQgPSAod2lkdGggLSBjbGllbnRXaWR0aCkgLyAyO1xuICBpZiAod2lkdGggPiBjbGllbnRXaWR0aCkge1xuICAgIGlmIChzdGFydCA+IDApIHtcbiAgICAgIHJldHVybiBfZGVmaW5lUHJvcGVydHkoe30sIGtleSwgb2Zmc2V0U3RhcnQpO1xuICAgIH1cbiAgICBpZiAoc3RhcnQgPCAwICYmIHN0YXJ0QWRkV2lkdGggPCBjbGllbnRXaWR0aCkge1xuICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eSh7fSwga2V5LCAtb2Zmc2V0U3RhcnQpO1xuICAgIH1cbiAgfSBlbHNlIGlmIChzdGFydCA8IDAgfHwgc3RhcnRBZGRXaWR0aCA+IGNsaWVudFdpZHRoKSB7XG4gICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eSh7fSwga2V5LCBzdGFydCA8IDAgPyBvZmZzZXRTdGFydCA6IC1vZmZzZXRTdGFydCk7XG4gIH1cbiAgcmV0dXJuIHt9O1xufVxuXG4vKipcbiAqIEZpeCBwb3NpdG9uIHgseSBwb2ludCB3aGVuXG4gKlxuICogRWxlIHdpZHRoICYmIGhlaWdodCA8IGNsaWVudFxuICogLSBCYWNrIG9yaWdpblxuICpcbiAqIC0gRWxlIHdpZHRoIHwgaGVpZ2h0ID4gY2xpZW50V2lkdGggfCBjbGllbnRIZWlnaHRcbiAqIC0gbGVmdCB8IHRvcCA+IDAgLT4gQmFjayAwXG4gKiAtIGxlZnQgfCB0b3AgKyB3aWR0aCB8IGhlaWdodCA8IGNsaWVudFdpZHRoIHwgY2xpZW50SGVpZ2h0IC0+IEJhY2sgbGVmdCB8IHRvcCArIHdpZHRoIHwgaGVpZ2h0ID09PSBjbGllbnRXaWR0aCB8IGNsaWVudEhlaWdodFxuICpcbiAqIFJlZ2FyZGxlc3Mgb2Ygb3RoZXJcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0Rml4U2NhbGVFbGVUcmFuc1Bvc2l0aW9uKHdpZHRoLCBoZWlnaHQsIGxlZnQsIHRvcCkge1xuICB2YXIgX2dldENsaWVudFNpemUgPSBnZXRDbGllbnRTaXplKCksXG4gICAgY2xpZW50V2lkdGggPSBfZ2V0Q2xpZW50U2l6ZS53aWR0aCxcbiAgICBjbGllbnRIZWlnaHQgPSBfZ2V0Q2xpZW50U2l6ZS5oZWlnaHQ7XG4gIHZhciBmaXhQb3MgPSBudWxsO1xuICBpZiAod2lkdGggPD0gY2xpZW50V2lkdGggJiYgaGVpZ2h0IDw9IGNsaWVudEhlaWdodCkge1xuICAgIGZpeFBvcyA9IHtcbiAgICAgIHg6IDAsXG4gICAgICB5OiAwXG4gICAgfTtcbiAgfSBlbHNlIGlmICh3aWR0aCA+IGNsaWVudFdpZHRoIHx8IGhlaWdodCA+IGNsaWVudEhlaWdodCkge1xuICAgIGZpeFBvcyA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgZml4UG9pbnQoJ3gnLCBsZWZ0LCB3aWR0aCwgY2xpZW50V2lkdGgpKSwgZml4UG9pbnQoJ3knLCB0b3AsIGhlaWdodCwgY2xpZW50SGVpZ2h0KSk7XG4gIH1cbiAgcmV0dXJuIGZpeFBvcztcbn0iLCIvKiogU2NhbGUgdGhlIHJhdGlvIGJhc2UgKi9cbmV4cG9ydCB2YXIgQkFTRV9TQ0FMRV9SQVRJTyA9IDE7XG4vKiogVGhlIG1heGltdW0gem9vbSByYXRpbyB3aGVuIHRoZSBtb3VzZSB6b29tcyBpbiwgYWRqdXN0YWJsZSAqL1xuZXhwb3J0IHZhciBXSEVFTF9NQVhfU0NBTEVfUkFUSU8gPSAxOyIsImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBhZGRFdmVudExpc3RlbmVyIGZyb20gXCJyYy11dGlsL2VzL0RvbS9hZGRFdmVudExpc3RlbmVyXCI7XG5pbXBvcnQgeyB3YXJuaW5nIH0gZnJvbSBcInJjLXV0aWwvZXMvd2FybmluZ1wiO1xuaW1wb3J0IGdldEZpeFNjYWxlRWxlVHJhbnNQb3NpdGlvbiBmcm9tIFwiLi4vZ2V0Rml4U2NhbGVFbGVUcmFuc1Bvc2l0aW9uXCI7XG5pbXBvcnQgeyBCQVNFX1NDQUxFX1JBVElPLCBXSEVFTF9NQVhfU0NBTEVfUkFUSU8gfSBmcm9tIFwiLi4vcHJldmlld0NvbmZpZ1wiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlTW91c2VFdmVudChpbWdSZWYsIG1vdmFibGUsIHZpc2libGUsIHNjYWxlU3RlcCwgdHJhbnNmb3JtLCB1cGRhdGVUcmFuc2Zvcm0sIGRpc3BhdGNoWm9vbUNoYW5nZSkge1xuICB2YXIgcm90YXRlID0gdHJhbnNmb3JtLnJvdGF0ZSxcbiAgICBzY2FsZSA9IHRyYW5zZm9ybS5zY2FsZSxcbiAgICB4ID0gdHJhbnNmb3JtLngsXG4gICAgeSA9IHRyYW5zZm9ybS55O1xuICB2YXIgX3VzZVN0YXRlID0gdXNlU3RhdGUoZmFsc2UpLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIGlzTW92aW5nID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRNb3ZpbmcgPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgc3RhcnRQb3NpdGlvbkluZm8gPSB1c2VSZWYoe1xuICAgIGRpZmZYOiAwLFxuICAgIGRpZmZZOiAwLFxuICAgIHRyYW5zZm9ybVg6IDAsXG4gICAgdHJhbnNmb3JtWTogMFxuICB9KTtcbiAgdmFyIG9uTW91c2VEb3duID0gZnVuY3Rpb24gb25Nb3VzZURvd24oZXZlbnQpIHtcbiAgICAvLyBPbmx5IGFsbG93IG1haW4gYnV0dG9uXG4gICAgaWYgKCFtb3ZhYmxlIHx8IGV2ZW50LmJ1dHRvbiAhPT0gMCkgcmV0dXJuO1xuICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgc3RhcnRQb3NpdGlvbkluZm8uY3VycmVudCA9IHtcbiAgICAgIGRpZmZYOiBldmVudC5wYWdlWCAtIHgsXG4gICAgICBkaWZmWTogZXZlbnQucGFnZVkgLSB5LFxuICAgICAgdHJhbnNmb3JtWDogeCxcbiAgICAgIHRyYW5zZm9ybVk6IHlcbiAgICB9O1xuICAgIHNldE1vdmluZyh0cnVlKTtcbiAgfTtcbiAgdmFyIG9uTW91c2VNb3ZlID0gZnVuY3Rpb24gb25Nb3VzZU1vdmUoZXZlbnQpIHtcbiAgICBpZiAodmlzaWJsZSAmJiBpc01vdmluZykge1xuICAgICAgdXBkYXRlVHJhbnNmb3JtKHtcbiAgICAgICAgeDogZXZlbnQucGFnZVggLSBzdGFydFBvc2l0aW9uSW5mby5jdXJyZW50LmRpZmZYLFxuICAgICAgICB5OiBldmVudC5wYWdlWSAtIHN0YXJ0UG9zaXRpb25JbmZvLmN1cnJlbnQuZGlmZllcbiAgICAgIH0sICdtb3ZlJyk7XG4gICAgfVxuICB9O1xuICB2YXIgb25Nb3VzZVVwID0gZnVuY3Rpb24gb25Nb3VzZVVwKCkge1xuICAgIGlmICh2aXNpYmxlICYmIGlzTW92aW5nKSB7XG4gICAgICBzZXRNb3ZpbmcoZmFsc2UpO1xuXG4gICAgICAvKiogTm8gbmVlZCB0byByZXN0b3JlIHRoZSBwb3NpdGlvbiB3aGVuIHRoZSBwaWN0dXJlIGlzIG5vdCBtb3ZlZCwgU28gYXMgbm90IHRvIGludGVyZmVyZSB3aXRoIHRoZSBjbGljayAqL1xuICAgICAgdmFyIF9zdGFydFBvc2l0aW9uSW5mbyRjdSA9IHN0YXJ0UG9zaXRpb25JbmZvLmN1cnJlbnQsXG4gICAgICAgIHRyYW5zZm9ybVggPSBfc3RhcnRQb3NpdGlvbkluZm8kY3UudHJhbnNmb3JtWCxcbiAgICAgICAgdHJhbnNmb3JtWSA9IF9zdGFydFBvc2l0aW9uSW5mbyRjdS50cmFuc2Zvcm1ZO1xuICAgICAgdmFyIGhhc0NoYW5nZWRQb3NpdGlvbiA9IHggIT09IHRyYW5zZm9ybVggJiYgeSAhPT0gdHJhbnNmb3JtWTtcbiAgICAgIGlmICghaGFzQ2hhbmdlZFBvc2l0aW9uKSByZXR1cm47XG4gICAgICB2YXIgd2lkdGggPSBpbWdSZWYuY3VycmVudC5vZmZzZXRXaWR0aCAqIHNjYWxlO1xuICAgICAgdmFyIGhlaWdodCA9IGltZ1JlZi5jdXJyZW50Lm9mZnNldEhlaWdodCAqIHNjYWxlO1xuICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1zaGFkb3dcbiAgICAgIHZhciBfaW1nUmVmJGN1cnJlbnQkZ2V0Qm8gPSBpbWdSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKSxcbiAgICAgICAgbGVmdCA9IF9pbWdSZWYkY3VycmVudCRnZXRCby5sZWZ0LFxuICAgICAgICB0b3AgPSBfaW1nUmVmJGN1cnJlbnQkZ2V0Qm8udG9wO1xuICAgICAgdmFyIGlzUm90YXRlID0gcm90YXRlICUgMTgwICE9PSAwO1xuICAgICAgdmFyIGZpeFN0YXRlID0gZ2V0Rml4U2NhbGVFbGVUcmFuc1Bvc2l0aW9uKGlzUm90YXRlID8gaGVpZ2h0IDogd2lkdGgsIGlzUm90YXRlID8gd2lkdGggOiBoZWlnaHQsIGxlZnQsIHRvcCk7XG4gICAgICBpZiAoZml4U3RhdGUpIHtcbiAgICAgICAgdXBkYXRlVHJhbnNmb3JtKF9vYmplY3RTcHJlYWQoe30sIGZpeFN0YXRlKSwgJ2RyYWdSZWJvdW5kJyk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuICB2YXIgb25XaGVlbCA9IGZ1bmN0aW9uIG9uV2hlZWwoZXZlbnQpIHtcbiAgICBpZiAoIXZpc2libGUgfHwgZXZlbnQuZGVsdGFZID09IDApIHJldHVybjtcbiAgICAvLyBTY2FsZSByYXRpbyBkZXBlbmRzIG9uIHRoZSBkZWx0YVkgc2l6ZVxuICAgIHZhciBzY2FsZVJhdGlvID0gTWF0aC5hYnMoZXZlbnQuZGVsdGFZIC8gMTAwKTtcbiAgICAvLyBMaW1pdCB0aGUgbWF4aW11bSBzY2FsZSByYXRpb1xuICAgIHZhciBtZXJnZWRTY2FsZVJhdGlvID0gTWF0aC5taW4oc2NhbGVSYXRpbywgV0hFRUxfTUFYX1NDQUxFX1JBVElPKTtcbiAgICAvLyBTY2FsZSB0aGUgcmF0aW8gZWFjaCB0aW1lXG4gICAgdmFyIHJhdGlvID0gQkFTRV9TQ0FMRV9SQVRJTyArIG1lcmdlZFNjYWxlUmF0aW8gKiBzY2FsZVN0ZXA7XG4gICAgaWYgKGV2ZW50LmRlbHRhWSA+IDApIHtcbiAgICAgIHJhdGlvID0gQkFTRV9TQ0FMRV9SQVRJTyAvIHJhdGlvO1xuICAgIH1cbiAgICBkaXNwYXRjaFpvb21DaGFuZ2UocmF0aW8sICd3aGVlbCcsIGV2ZW50LmNsaWVudFgsIGV2ZW50LmNsaWVudFkpO1xuICB9O1xuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHZhciBvblRvcE1vdXNlVXBMaXN0ZW5lcjtcbiAgICB2YXIgb25Ub3BNb3VzZU1vdmVMaXN0ZW5lcjtcbiAgICB2YXIgb25Nb3VzZVVwTGlzdGVuZXI7XG4gICAgdmFyIG9uTW91c2VNb3ZlTGlzdGVuZXI7XG4gICAgaWYgKG1vdmFibGUpIHtcbiAgICAgIG9uTW91c2VVcExpc3RlbmVyID0gYWRkRXZlbnRMaXN0ZW5lcih3aW5kb3csICdtb3VzZXVwJywgb25Nb3VzZVVwLCBmYWxzZSk7XG4gICAgICBvbk1vdXNlTW92ZUxpc3RlbmVyID0gYWRkRXZlbnRMaXN0ZW5lcih3aW5kb3csICdtb3VzZW1vdmUnLCBvbk1vdXNlTW92ZSwgZmFsc2UpO1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gUmVzb2x2ZSBpZiBpbiBpZnJhbWUgbG9zdCBldmVudFxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgICAgICBpZiAod2luZG93LnRvcCAhPT0gd2luZG93LnNlbGYpIHtcbiAgICAgICAgICBvblRvcE1vdXNlVXBMaXN0ZW5lciA9IGFkZEV2ZW50TGlzdGVuZXIod2luZG93LnRvcCwgJ21vdXNldXAnLCBvbk1vdXNlVXAsIGZhbHNlKTtcbiAgICAgICAgICBvblRvcE1vdXNlTW92ZUxpc3RlbmVyID0gYWRkRXZlbnRMaXN0ZW5lcih3aW5kb3cudG9wLCAnbW91c2Vtb3ZlJywgb25Nb3VzZU1vdmUsIGZhbHNlKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgICAgICAgd2FybmluZyhmYWxzZSwgXCJbcmMtaW1hZ2VdIFwiLmNvbmNhdChlcnJvcikpO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIF9vbk1vdXNlVXBMaXN0ZW5lciwgX29uTW91c2VNb3ZlTGlzdGVuZXIsIF9vblRvcE1vdXNlVXBMaXN0ZW5lciwgX29uVG9wTW91c2VNb3ZlTGlzdGVuO1xuICAgICAgKF9vbk1vdXNlVXBMaXN0ZW5lciA9IG9uTW91c2VVcExpc3RlbmVyKSA9PT0gbnVsbCB8fCBfb25Nb3VzZVVwTGlzdGVuZXIgPT09IHZvaWQgMCB8fCBfb25Nb3VzZVVwTGlzdGVuZXIucmVtb3ZlKCk7XG4gICAgICAoX29uTW91c2VNb3ZlTGlzdGVuZXIgPSBvbk1vdXNlTW92ZUxpc3RlbmVyKSA9PT0gbnVsbCB8fCBfb25Nb3VzZU1vdmVMaXN0ZW5lciA9PT0gdm9pZCAwIHx8IF9vbk1vdXNlTW92ZUxpc3RlbmVyLnJlbW92ZSgpO1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgICAgIChfb25Ub3BNb3VzZVVwTGlzdGVuZXIgPSBvblRvcE1vdXNlVXBMaXN0ZW5lcikgPT09IG51bGwgfHwgX29uVG9wTW91c2VVcExpc3RlbmVyID09PSB2b2lkIDAgfHwgX29uVG9wTW91c2VVcExpc3RlbmVyLnJlbW92ZSgpO1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgICAgIChfb25Ub3BNb3VzZU1vdmVMaXN0ZW4gPSBvblRvcE1vdXNlTW92ZUxpc3RlbmVyKSA9PT0gbnVsbCB8fCBfb25Ub3BNb3VzZU1vdmVMaXN0ZW4gPT09IHZvaWQgMCB8fCBfb25Ub3BNb3VzZU1vdmVMaXN0ZW4ucmVtb3ZlKCk7XG4gICAgfTtcbiAgfSwgW3Zpc2libGUsIGlzTW92aW5nLCB4LCB5LCByb3RhdGUsIG1vdmFibGVdKTtcbiAgcmV0dXJuIHtcbiAgICBpc01vdmluZzogaXNNb3ZpbmcsXG4gICAgb25Nb3VzZURvd246IG9uTW91c2VEb3duLFxuICAgIG9uTW91c2VNb3ZlOiBvbk1vdXNlTW92ZSxcbiAgICBvbk1vdXNlVXA6IG9uTW91c2VVcCxcbiAgICBvbldoZWVsOiBvbldoZWVsXG4gIH07XG59XG47IiwiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGFkZEV2ZW50TGlzdGVuZXIgZnJvbSBcInJjLXV0aWwvZXMvRG9tL2FkZEV2ZW50TGlzdGVuZXJcIjtcbmltcG9ydCBnZXRGaXhTY2FsZUVsZVRyYW5zUG9zaXRpb24gZnJvbSBcIi4uL2dldEZpeFNjYWxlRWxlVHJhbnNQb3NpdGlvblwiO1xuZnVuY3Rpb24gZ2V0RGlzdGFuY2UoYSwgYikge1xuICB2YXIgeCA9IGEueCAtIGIueDtcbiAgdmFyIHkgPSBhLnkgLSBiLnk7XG4gIHJldHVybiBNYXRoLmh5cG90KHgsIHkpO1xufVxuZnVuY3Rpb24gZ2V0Q2VudGVyKG9sZFBvaW50MSwgb2xkUG9pbnQyLCBuZXdQb2ludDEsIG5ld1BvaW50Mikge1xuICAvLyBDYWxjdWxhdGUgdGhlIGRpc3RhbmNlIGVhY2ggcG9pbnQgaGFzIG1vdmVkXG4gIHZhciBkaXN0YW5jZTEgPSBnZXREaXN0YW5jZShvbGRQb2ludDEsIG5ld1BvaW50MSk7XG4gIHZhciBkaXN0YW5jZTIgPSBnZXREaXN0YW5jZShvbGRQb2ludDIsIG5ld1BvaW50Mik7XG5cbiAgLy8gSWYgYm90aCBkaXN0YW5jZXMgYXJlIDAsIHJldHVybiB0aGUgb3JpZ2luYWwgcG9pbnRzXG4gIGlmIChkaXN0YW5jZTEgPT09IDAgJiYgZGlzdGFuY2UyID09PSAwKSB7XG4gICAgcmV0dXJuIFtvbGRQb2ludDEueCwgb2xkUG9pbnQxLnldO1xuICB9XG5cbiAgLy8gQ2FsY3VsYXRlIHRoZSByYXRpbyBvZiB0aGUgZGlzdGFuY2VzXG4gIHZhciByYXRpbyA9IGRpc3RhbmNlMSAvIChkaXN0YW5jZTEgKyBkaXN0YW5jZTIpO1xuXG4gIC8vIENhbGN1bGF0ZSB0aGUgbmV3IGNlbnRlciBwb2ludCBiYXNlZCBvbiB0aGUgcmF0aW9cbiAgdmFyIHggPSBvbGRQb2ludDEueCArIHJhdGlvICogKG9sZFBvaW50Mi54IC0gb2xkUG9pbnQxLngpO1xuICB2YXIgeSA9IG9sZFBvaW50MS55ICsgcmF0aW8gKiAob2xkUG9pbnQyLnkgLSBvbGRQb2ludDEueSk7XG4gIHJldHVybiBbeCwgeV07XG59XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VUb3VjaEV2ZW50KGltZ1JlZiwgbW92YWJsZSwgdmlzaWJsZSwgbWluU2NhbGUsIHRyYW5zZm9ybSwgdXBkYXRlVHJhbnNmb3JtLCBkaXNwYXRjaFpvb21DaGFuZ2UpIHtcbiAgdmFyIHJvdGF0ZSA9IHRyYW5zZm9ybS5yb3RhdGUsXG4gICAgc2NhbGUgPSB0cmFuc2Zvcm0uc2NhbGUsXG4gICAgeCA9IHRyYW5zZm9ybS54LFxuICAgIHkgPSB0cmFuc2Zvcm0ueTtcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKGZhbHNlKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBpc1RvdWNoaW5nID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRJc1RvdWNoaW5nID0gX3VzZVN0YXRlMlsxXTtcbiAgdmFyIHRvdWNoUG9pbnRJbmZvID0gdXNlUmVmKHtcbiAgICBwb2ludDE6IHtcbiAgICAgIHg6IDAsXG4gICAgICB5OiAwXG4gICAgfSxcbiAgICBwb2ludDI6IHtcbiAgICAgIHg6IDAsXG4gICAgICB5OiAwXG4gICAgfSxcbiAgICBldmVudFR5cGU6ICdub25lJ1xuICB9KTtcbiAgdmFyIHVwZGF0ZVRvdWNoUG9pbnRJbmZvID0gZnVuY3Rpb24gdXBkYXRlVG91Y2hQb2ludEluZm8odmFsdWVzKSB7XG4gICAgdG91Y2hQb2ludEluZm8uY3VycmVudCA9IF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdG91Y2hQb2ludEluZm8uY3VycmVudCksIHZhbHVlcyk7XG4gIH07XG4gIHZhciBvblRvdWNoU3RhcnQgPSBmdW5jdGlvbiBvblRvdWNoU3RhcnQoZXZlbnQpIHtcbiAgICBpZiAoIW1vdmFibGUpIHJldHVybjtcbiAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBzZXRJc1RvdWNoaW5nKHRydWUpO1xuICAgIHZhciBfZXZlbnQkdG91Y2hlcyA9IGV2ZW50LnRvdWNoZXMsXG4gICAgICB0b3VjaGVzID0gX2V2ZW50JHRvdWNoZXMgPT09IHZvaWQgMCA/IFtdIDogX2V2ZW50JHRvdWNoZXM7XG4gICAgaWYgKHRvdWNoZXMubGVuZ3RoID4gMSkge1xuICAgICAgLy8gdG91Y2ggem9vbVxuICAgICAgdXBkYXRlVG91Y2hQb2ludEluZm8oe1xuICAgICAgICBwb2ludDE6IHtcbiAgICAgICAgICB4OiB0b3VjaGVzWzBdLmNsaWVudFgsXG4gICAgICAgICAgeTogdG91Y2hlc1swXS5jbGllbnRZXG4gICAgICAgIH0sXG4gICAgICAgIHBvaW50Mjoge1xuICAgICAgICAgIHg6IHRvdWNoZXNbMV0uY2xpZW50WCxcbiAgICAgICAgICB5OiB0b3VjaGVzWzFdLmNsaWVudFlcbiAgICAgICAgfSxcbiAgICAgICAgZXZlbnRUeXBlOiAndG91Y2hab29tJ1xuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIHRvdWNoIG1vdmVcbiAgICAgIHVwZGF0ZVRvdWNoUG9pbnRJbmZvKHtcbiAgICAgICAgcG9pbnQxOiB7XG4gICAgICAgICAgeDogdG91Y2hlc1swXS5jbGllbnRYIC0geCxcbiAgICAgICAgICB5OiB0b3VjaGVzWzBdLmNsaWVudFkgLSB5XG4gICAgICAgIH0sXG4gICAgICAgIGV2ZW50VHlwZTogJ21vdmUnXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG4gIHZhciBvblRvdWNoTW92ZSA9IGZ1bmN0aW9uIG9uVG91Y2hNb3ZlKGV2ZW50KSB7XG4gICAgdmFyIF9ldmVudCR0b3VjaGVzMiA9IGV2ZW50LnRvdWNoZXMsXG4gICAgICB0b3VjaGVzID0gX2V2ZW50JHRvdWNoZXMyID09PSB2b2lkIDAgPyBbXSA6IF9ldmVudCR0b3VjaGVzMjtcbiAgICB2YXIgX3RvdWNoUG9pbnRJbmZvJGN1cnJlID0gdG91Y2hQb2ludEluZm8uY3VycmVudCxcbiAgICAgIHBvaW50MSA9IF90b3VjaFBvaW50SW5mbyRjdXJyZS5wb2ludDEsXG4gICAgICBwb2ludDIgPSBfdG91Y2hQb2ludEluZm8kY3VycmUucG9pbnQyLFxuICAgICAgZXZlbnRUeXBlID0gX3RvdWNoUG9pbnRJbmZvJGN1cnJlLmV2ZW50VHlwZTtcbiAgICBpZiAodG91Y2hlcy5sZW5ndGggPiAxICYmIGV2ZW50VHlwZSA9PT0gJ3RvdWNoWm9vbScpIHtcbiAgICAgIC8vIHRvdWNoIHpvb21cbiAgICAgIHZhciBuZXdQb2ludDEgPSB7XG4gICAgICAgIHg6IHRvdWNoZXNbMF0uY2xpZW50WCxcbiAgICAgICAgeTogdG91Y2hlc1swXS5jbGllbnRZXG4gICAgICB9O1xuICAgICAgdmFyIG5ld1BvaW50MiA9IHtcbiAgICAgICAgeDogdG91Y2hlc1sxXS5jbGllbnRYLFxuICAgICAgICB5OiB0b3VjaGVzWzFdLmNsaWVudFlcbiAgICAgIH07XG4gICAgICB2YXIgX2dldENlbnRlciA9IGdldENlbnRlcihwb2ludDEsIHBvaW50MiwgbmV3UG9pbnQxLCBuZXdQb2ludDIpLFxuICAgICAgICBfZ2V0Q2VudGVyMiA9IF9zbGljZWRUb0FycmF5KF9nZXRDZW50ZXIsIDIpLFxuICAgICAgICBjZW50ZXJYID0gX2dldENlbnRlcjJbMF0sXG4gICAgICAgIGNlbnRlclkgPSBfZ2V0Q2VudGVyMlsxXTtcbiAgICAgIHZhciByYXRpbyA9IGdldERpc3RhbmNlKG5ld1BvaW50MSwgbmV3UG9pbnQyKSAvIGdldERpc3RhbmNlKHBvaW50MSwgcG9pbnQyKTtcbiAgICAgIGRpc3BhdGNoWm9vbUNoYW5nZShyYXRpbywgJ3RvdWNoWm9vbScsIGNlbnRlclgsIGNlbnRlclksIHRydWUpO1xuICAgICAgdXBkYXRlVG91Y2hQb2ludEluZm8oe1xuICAgICAgICBwb2ludDE6IG5ld1BvaW50MSxcbiAgICAgICAgcG9pbnQyOiBuZXdQb2ludDIsXG4gICAgICAgIGV2ZW50VHlwZTogJ3RvdWNoWm9vbSdcbiAgICAgIH0pO1xuICAgIH0gZWxzZSBpZiAoZXZlbnRUeXBlID09PSAnbW92ZScpIHtcbiAgICAgIC8vIHRvdWNoIG1vdmVcbiAgICAgIHVwZGF0ZVRyYW5zZm9ybSh7XG4gICAgICAgIHg6IHRvdWNoZXNbMF0uY2xpZW50WCAtIHBvaW50MS54LFxuICAgICAgICB5OiB0b3VjaGVzWzBdLmNsaWVudFkgLSBwb2ludDEueVxuICAgICAgfSwgJ21vdmUnKTtcbiAgICAgIHVwZGF0ZVRvdWNoUG9pbnRJbmZvKHtcbiAgICAgICAgZXZlbnRUeXBlOiAnbW92ZSdcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcbiAgdmFyIG9uVG91Y2hFbmQgPSBmdW5jdGlvbiBvblRvdWNoRW5kKCkge1xuICAgIGlmICghdmlzaWJsZSkgcmV0dXJuO1xuICAgIGlmIChpc1RvdWNoaW5nKSB7XG4gICAgICBzZXRJc1RvdWNoaW5nKGZhbHNlKTtcbiAgICB9XG4gICAgdXBkYXRlVG91Y2hQb2ludEluZm8oe1xuICAgICAgZXZlbnRUeXBlOiAnbm9uZSdcbiAgICB9KTtcbiAgICBpZiAobWluU2NhbGUgPiBzY2FsZSkge1xuICAgICAgLyoqIFdoZW4gdGhlIHNjYWxpbmcgcmF0aW8gaXMgbGVzcyB0aGFuIHRoZSBtaW5pbXVtIHNjYWxpbmcgcmF0aW8sIHJlc2V0IHRoZSBzY2FsaW5nIHJhdGlvICovXG4gICAgICByZXR1cm4gdXBkYXRlVHJhbnNmb3JtKHtcbiAgICAgICAgeDogMCxcbiAgICAgICAgeTogMCxcbiAgICAgICAgc2NhbGU6IG1pblNjYWxlXG4gICAgICB9LCAndG91Y2hab29tJyk7XG4gICAgfVxuICAgIHZhciB3aWR0aCA9IGltZ1JlZi5jdXJyZW50Lm9mZnNldFdpZHRoICogc2NhbGU7XG4gICAgdmFyIGhlaWdodCA9IGltZ1JlZi5jdXJyZW50Lm9mZnNldEhlaWdodCAqIHNjYWxlO1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tc2hhZG93XG4gICAgdmFyIF9pbWdSZWYkY3VycmVudCRnZXRCbyA9IGltZ1JlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLFxuICAgICAgbGVmdCA9IF9pbWdSZWYkY3VycmVudCRnZXRCby5sZWZ0LFxuICAgICAgdG9wID0gX2ltZ1JlZiRjdXJyZW50JGdldEJvLnRvcDtcbiAgICB2YXIgaXNSb3RhdGUgPSByb3RhdGUgJSAxODAgIT09IDA7XG4gICAgdmFyIGZpeFN0YXRlID0gZ2V0Rml4U2NhbGVFbGVUcmFuc1Bvc2l0aW9uKGlzUm90YXRlID8gaGVpZ2h0IDogd2lkdGgsIGlzUm90YXRlID8gd2lkdGggOiBoZWlnaHQsIGxlZnQsIHRvcCk7XG4gICAgaWYgKGZpeFN0YXRlKSB7XG4gICAgICB1cGRhdGVUcmFuc2Zvcm0oX29iamVjdFNwcmVhZCh7fSwgZml4U3RhdGUpLCAnZHJhZ1JlYm91bmQnKTtcbiAgICB9XG4gIH07XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgdmFyIG9uVG91Y2hNb3ZlTGlzdGVuZXI7XG4gICAgaWYgKHZpc2libGUgJiYgbW92YWJsZSkge1xuICAgICAgb25Ub3VjaE1vdmVMaXN0ZW5lciA9IGFkZEV2ZW50TGlzdGVuZXIod2luZG93LCAndG91Y2htb3ZlJywgZnVuY3Rpb24gKGUpIHtcbiAgICAgICAgcmV0dXJuIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIH0sIHtcbiAgICAgICAgcGFzc2l2ZTogZmFsc2VcbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgdmFyIF9vblRvdWNoTW92ZUxpc3RlbmVyO1xuICAgICAgKF9vblRvdWNoTW92ZUxpc3RlbmVyID0gb25Ub3VjaE1vdmVMaXN0ZW5lcikgPT09IG51bGwgfHwgX29uVG91Y2hNb3ZlTGlzdGVuZXIgPT09IHZvaWQgMCB8fCBfb25Ub3VjaE1vdmVMaXN0ZW5lci5yZW1vdmUoKTtcbiAgICB9O1xuICB9LCBbdmlzaWJsZSwgbW92YWJsZV0pO1xuICByZXR1cm4ge1xuICAgIGlzVG91Y2hpbmc6IGlzVG91Y2hpbmcsXG4gICAgb25Ub3VjaFN0YXJ0OiBvblRvdWNoU3RhcnQsXG4gICAgb25Ub3VjaE1vdmU6IG9uVG91Y2hNb3ZlLFxuICAgIG9uVG91Y2hFbmQ6IG9uVG91Y2hFbmRcbiAgfTtcbn1cbjsiLCJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZGVmaW5lUHJvcGVydHlcIjtcbmltcG9ydCBQb3J0YWwgZnJvbSAnQHJjLWNvbXBvbmVudC9wb3J0YWwnO1xuaW1wb3J0IGNsYXNzbmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgQ1NTTW90aW9uIGZyb20gJ3JjLW1vdGlvbic7XG5pbXBvcnQgS2V5Q29kZSBmcm9tIFwicmMtdXRpbC9lcy9LZXlDb2RlXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUHJldmlld0dyb3VwQ29udGV4dCB9IGZyb20gXCIuL2NvbnRleHRcIjtcbnZhciBPcGVyYXRpb25zID0gZnVuY3Rpb24gT3BlcmF0aW9ucyhwcm9wcykge1xuICB2YXIgdmlzaWJsZSA9IHByb3BzLnZpc2libGUsXG4gICAgbWFza1RyYW5zaXRpb25OYW1lID0gcHJvcHMubWFza1RyYW5zaXRpb25OYW1lLFxuICAgIGdldENvbnRhaW5lciA9IHByb3BzLmdldENvbnRhaW5lcixcbiAgICBwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgcm9vdENsYXNzTmFtZSA9IHByb3BzLnJvb3RDbGFzc05hbWUsXG4gICAgaWNvbnMgPSBwcm9wcy5pY29ucyxcbiAgICBjb3VudFJlbmRlciA9IHByb3BzLmNvdW50UmVuZGVyLFxuICAgIHNob3dTd2l0Y2ggPSBwcm9wcy5zaG93U3dpdGNoLFxuICAgIHNob3dQcm9ncmVzcyA9IHByb3BzLnNob3dQcm9ncmVzcyxcbiAgICBjdXJyZW50ID0gcHJvcHMuY3VycmVudCxcbiAgICB0cmFuc2Zvcm0gPSBwcm9wcy50cmFuc2Zvcm0sXG4gICAgY291bnQgPSBwcm9wcy5jb3VudCxcbiAgICBzY2FsZSA9IHByb3BzLnNjYWxlLFxuICAgIG1pblNjYWxlID0gcHJvcHMubWluU2NhbGUsXG4gICAgbWF4U2NhbGUgPSBwcm9wcy5tYXhTY2FsZSxcbiAgICBjbG9zZUljb24gPSBwcm9wcy5jbG9zZUljb24sXG4gICAgb25Td2l0Y2hMZWZ0ID0gcHJvcHMub25Td2l0Y2hMZWZ0LFxuICAgIG9uU3dpdGNoUmlnaHQgPSBwcm9wcy5vblN3aXRjaFJpZ2h0LFxuICAgIG9uQ2xvc2UgPSBwcm9wcy5vbkNsb3NlLFxuICAgIG9uWm9vbUluID0gcHJvcHMub25ab29tSW4sXG4gICAgb25ab29tT3V0ID0gcHJvcHMub25ab29tT3V0LFxuICAgIG9uUm90YXRlUmlnaHQgPSBwcm9wcy5vblJvdGF0ZVJpZ2h0LFxuICAgIG9uUm90YXRlTGVmdCA9IHByb3BzLm9uUm90YXRlTGVmdCxcbiAgICBvbkZsaXBYID0gcHJvcHMub25GbGlwWCxcbiAgICBvbkZsaXBZID0gcHJvcHMub25GbGlwWSxcbiAgICB0b29sYmFyUmVuZGVyID0gcHJvcHMudG9vbGJhclJlbmRlcixcbiAgICB6SW5kZXggPSBwcm9wcy56SW5kZXg7XG4gIHZhciBncm91cENvbnRleHQgPSB1c2VDb250ZXh0KFByZXZpZXdHcm91cENvbnRleHQpO1xuICB2YXIgcm90YXRlTGVmdCA9IGljb25zLnJvdGF0ZUxlZnQsXG4gICAgcm90YXRlUmlnaHQgPSBpY29ucy5yb3RhdGVSaWdodCxcbiAgICB6b29tSW4gPSBpY29ucy56b29tSW4sXG4gICAgem9vbU91dCA9IGljb25zLnpvb21PdXQsXG4gICAgY2xvc2UgPSBpY29ucy5jbG9zZSxcbiAgICBsZWZ0ID0gaWNvbnMubGVmdCxcbiAgICByaWdodCA9IGljb25zLnJpZ2h0LFxuICAgIGZsaXBYID0gaWNvbnMuZmxpcFgsXG4gICAgZmxpcFkgPSBpY29ucy5mbGlwWTtcbiAgdmFyIHRvb2xDbGFzc05hbWUgPSBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW9wZXJhdGlvbnMtb3BlcmF0aW9uXCIpO1xuICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHZhciBvbktleURvd24gPSBmdW5jdGlvbiBvbktleURvd24oZSkge1xuICAgICAgaWYgKGUua2V5Q29kZSA9PT0gS2V5Q29kZS5FU0MpIHtcbiAgICAgICAgb25DbG9zZSgpO1xuICAgICAgfVxuICAgIH07XG4gICAgaWYgKHZpc2libGUpIHtcbiAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdrZXlkb3duJywgb25LZXlEb3duKTtcbiAgICB9XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgb25LZXlEb3duKTtcbiAgICB9O1xuICB9LCBbdmlzaWJsZV0pO1xuICB2YXIgdG9vbHMgPSBbe1xuICAgIGljb246IGZsaXBZLFxuICAgIG9uQ2xpY2s6IG9uRmxpcFksXG4gICAgdHlwZTogJ2ZsaXBZJ1xuICB9LCB7XG4gICAgaWNvbjogZmxpcFgsXG4gICAgb25DbGljazogb25GbGlwWCxcbiAgICB0eXBlOiAnZmxpcFgnXG4gIH0sIHtcbiAgICBpY29uOiByb3RhdGVMZWZ0LFxuICAgIG9uQ2xpY2s6IG9uUm90YXRlTGVmdCxcbiAgICB0eXBlOiAncm90YXRlTGVmdCdcbiAgfSwge1xuICAgIGljb246IHJvdGF0ZVJpZ2h0LFxuICAgIG9uQ2xpY2s6IG9uUm90YXRlUmlnaHQsXG4gICAgdHlwZTogJ3JvdGF0ZVJpZ2h0J1xuICB9LCB7XG4gICAgaWNvbjogem9vbU91dCxcbiAgICBvbkNsaWNrOiBvblpvb21PdXQsXG4gICAgdHlwZTogJ3pvb21PdXQnLFxuICAgIGRpc2FibGVkOiBzY2FsZSA8PSBtaW5TY2FsZVxuICB9LCB7XG4gICAgaWNvbjogem9vbUluLFxuICAgIG9uQ2xpY2s6IG9uWm9vbUluLFxuICAgIHR5cGU6ICd6b29tSW4nLFxuICAgIGRpc2FibGVkOiBzY2FsZSA9PT0gbWF4U2NhbGVcbiAgfV07XG4gIHZhciB0b29sc05vZGUgPSB0b29scy5tYXAoZnVuY3Rpb24gKF9yZWYpIHtcbiAgICB2YXIgX2NsYXNzbmFtZXM7XG4gICAgdmFyIGljb24gPSBfcmVmLmljb24sXG4gICAgICBvbkNsaWNrID0gX3JlZi5vbkNsaWNrLFxuICAgICAgdHlwZSA9IF9yZWYudHlwZSxcbiAgICAgIGRpc2FibGVkID0gX3JlZi5kaXNhYmxlZDtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgY2xhc3NOYW1lOiBjbGFzc25hbWVzKHRvb2xDbGFzc05hbWUsIChfY2xhc3NuYW1lcyA9IHt9LCBfZGVmaW5lUHJvcGVydHkoX2NsYXNzbmFtZXMsIFwiXCIuY29uY2F0KHByZWZpeENscywgXCItb3BlcmF0aW9ucy1vcGVyYXRpb24tXCIpLmNvbmNhdCh0eXBlKSwgdHJ1ZSksIF9kZWZpbmVQcm9wZXJ0eShfY2xhc3NuYW1lcywgXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1vcGVyYXRpb25zLW9wZXJhdGlvbi1kaXNhYmxlZFwiKSwgISFkaXNhYmxlZCksIF9jbGFzc25hbWVzKSksXG4gICAgICBvbkNsaWNrOiBvbkNsaWNrLFxuICAgICAga2V5OiB0eXBlXG4gICAgfSwgaWNvbik7XG4gIH0pO1xuICB2YXIgdG9vbGJhck5vZGUgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW9wZXJhdGlvbnNcIilcbiAgfSwgdG9vbHNOb2RlKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENTU01vdGlvbiwge1xuICAgIHZpc2libGU6IHZpc2libGUsXG4gICAgbW90aW9uTmFtZTogbWFza1RyYW5zaXRpb25OYW1lXG4gIH0sIGZ1bmN0aW9uIChfcmVmMikge1xuICAgIHZhciBjbGFzc05hbWUgPSBfcmVmMi5jbGFzc05hbWUsXG4gICAgICBzdHlsZSA9IF9yZWYyLnN0eWxlO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChQb3J0YWwsIHtcbiAgICAgIG9wZW46IHRydWUsXG4gICAgICBnZXRDb250YWluZXI6IGdldENvbnRhaW5lciAhPT0gbnVsbCAmJiBnZXRDb250YWluZXIgIT09IHZvaWQgMCA/IGdldENvbnRhaW5lciA6IGRvY3VtZW50LmJvZHlcbiAgICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICBjbGFzc05hbWU6IGNsYXNzbmFtZXMoXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1vcGVyYXRpb25zLXdyYXBwZXJcIiksIGNsYXNzTmFtZSwgcm9vdENsYXNzTmFtZSksXG4gICAgICBzdHlsZTogX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBzdHlsZSksIHt9LCB7XG4gICAgICAgIHpJbmRleDogekluZGV4XG4gICAgICB9KVxuICAgIH0sIGNsb3NlSWNvbiA9PT0gbnVsbCA/IG51bGwgOiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImJ1dHRvblwiLCB7XG4gICAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItY2xvc2VcIiksXG4gICAgICBvbkNsaWNrOiBvbkNsb3NlXG4gICAgfSwgY2xvc2VJY29uIHx8IGNsb3NlKSwgc2hvd1N3aXRjaCAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgY2xhc3NOYW1lOiBjbGFzc25hbWVzKFwiXCIuY29uY2F0KHByZWZpeENscywgXCItc3dpdGNoLWxlZnRcIiksIF9kZWZpbmVQcm9wZXJ0eSh7fSwgXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1zd2l0Y2gtbGVmdC1kaXNhYmxlZFwiKSwgY3VycmVudCA9PT0gMCkpLFxuICAgICAgb25DbGljazogb25Td2l0Y2hMZWZ0XG4gICAgfSwgbGVmdCksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICAgIGNsYXNzTmFtZTogY2xhc3NuYW1lcyhcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXN3aXRjaC1yaWdodFwiKSwgX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXN3aXRjaC1yaWdodC1kaXNhYmxlZFwiKSwgY3VycmVudCA9PT0gY291bnQgLSAxKSksXG4gICAgICBvbkNsaWNrOiBvblN3aXRjaFJpZ2h0XG4gICAgfSwgcmlnaHQpKSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWZvb3RlclwiKVxuICAgIH0sIHNob3dQcm9ncmVzcyAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItcHJvZ3Jlc3NcIilcbiAgICB9LCBjb3VudFJlbmRlciA/IGNvdW50UmVuZGVyKGN1cnJlbnQgKyAxLCBjb3VudCkgOiBcIlwiLmNvbmNhdChjdXJyZW50ICsgMSwgXCIgLyBcIikuY29uY2F0KGNvdW50KSksIHRvb2xiYXJSZW5kZXIgPyB0b29sYmFyUmVuZGVyKHRvb2xiYXJOb2RlLCBfb2JqZWN0U3ByZWFkKHtcbiAgICAgIGljb25zOiB7XG4gICAgICAgIGZsaXBZSWNvbjogdG9vbHNOb2RlWzBdLFxuICAgICAgICBmbGlwWEljb246IHRvb2xzTm9kZVsxXSxcbiAgICAgICAgcm90YXRlTGVmdEljb246IHRvb2xzTm9kZVsyXSxcbiAgICAgICAgcm90YXRlUmlnaHRJY29uOiB0b29sc05vZGVbM10sXG4gICAgICAgIHpvb21PdXRJY29uOiB0b29sc05vZGVbNF0sXG4gICAgICAgIHpvb21Jbkljb246IHRvb2xzTm9kZVs1XVxuICAgICAgfSxcbiAgICAgIGFjdGlvbnM6IHtcbiAgICAgICAgb25GbGlwWTogb25GbGlwWSxcbiAgICAgICAgb25GbGlwWDogb25GbGlwWCxcbiAgICAgICAgb25Sb3RhdGVMZWZ0OiBvblJvdGF0ZUxlZnQsXG4gICAgICAgIG9uUm90YXRlUmlnaHQ6IG9uUm90YXRlUmlnaHQsXG4gICAgICAgIG9uWm9vbU91dDogb25ab29tT3V0LFxuICAgICAgICBvblpvb21Jbjogb25ab29tSW5cbiAgICAgIH0sXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zZm9ybVxuICAgIH0sIGdyb3VwQ29udGV4dCA/IHtcbiAgICAgIGN1cnJlbnQ6IGN1cnJlbnQsXG4gICAgICB0b3RhbDogY291bnRcbiAgICB9IDoge30pKSA6IHRvb2xiYXJOb2RlKSkpO1xuICB9KTtcbn07XG5leHBvcnQgZGVmYXVsdCBPcGVyYXRpb25zOyIsImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiZmFsbGJhY2tcIiwgXCJzcmNcIiwgXCJpbWdSZWZcIl0sXG4gIF9leGNsdWRlZDIgPSBbXCJwcmVmaXhDbHNcIiwgXCJzcmNcIiwgXCJhbHRcIiwgXCJmYWxsYmFja1wiLCBcIm1vdmFibGVcIiwgXCJvbkNsb3NlXCIsIFwidmlzaWJsZVwiLCBcImljb25zXCIsIFwicm9vdENsYXNzTmFtZVwiLCBcImNsb3NlSWNvblwiLCBcImdldENvbnRhaW5lclwiLCBcImN1cnJlbnRcIiwgXCJjb3VudFwiLCBcImNvdW50UmVuZGVyXCIsIFwic2NhbGVTdGVwXCIsIFwibWluU2NhbGVcIiwgXCJtYXhTY2FsZVwiLCBcInRyYW5zaXRpb25OYW1lXCIsIFwibWFza1RyYW5zaXRpb25OYW1lXCIsIFwiaW1hZ2VSZW5kZXJcIiwgXCJpbWdDb21tb25Qcm9wc1wiLCBcInRvb2xiYXJSZW5kZXJcIiwgXCJvblRyYW5zZm9ybVwiLCBcIm9uQ2hhbmdlXCJdO1xuaW1wb3J0IGNsYXNzbmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgRGlhbG9nIGZyb20gJ3JjLWRpYWxvZyc7XG5pbXBvcnQgYWRkRXZlbnRMaXN0ZW5lciBmcm9tIFwicmMtdXRpbC9lcy9Eb20vYWRkRXZlbnRMaXN0ZW5lclwiO1xuaW1wb3J0IEtleUNvZGUgZnJvbSBcInJjLXV0aWwvZXMvS2V5Q29kZVwiO1xuaW1wb3J0IFJlYWN0LCB7IHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFByZXZpZXdHcm91cENvbnRleHQgfSBmcm9tIFwiLi9jb250ZXh0XCI7XG5pbXBvcnQgdXNlSW1hZ2VUcmFuc2Zvcm0gZnJvbSBcIi4vaG9va3MvdXNlSW1hZ2VUcmFuc2Zvcm1cIjtcbmltcG9ydCB1c2VNb3VzZUV2ZW50IGZyb20gXCIuL2hvb2tzL3VzZU1vdXNlRXZlbnRcIjtcbmltcG9ydCB1c2VUb3VjaEV2ZW50IGZyb20gXCIuL2hvb2tzL3VzZVRvdWNoRXZlbnRcIjtcbmltcG9ydCB1c2VTdGF0dXMgZnJvbSBcIi4vaG9va3MvdXNlU3RhdHVzXCI7XG5pbXBvcnQgT3BlcmF0aW9ucyBmcm9tIFwiLi9PcGVyYXRpb25zXCI7XG5pbXBvcnQgeyBCQVNFX1NDQUxFX1JBVElPIH0gZnJvbSBcIi4vcHJldmlld0NvbmZpZ1wiO1xudmFyIFByZXZpZXdJbWFnZSA9IGZ1bmN0aW9uIFByZXZpZXdJbWFnZShfcmVmKSB7XG4gIHZhciBmYWxsYmFjayA9IF9yZWYuZmFsbGJhY2ssXG4gICAgc3JjID0gX3JlZi5zcmMsXG4gICAgaW1nUmVmID0gX3JlZi5pbWdSZWYsXG4gICAgcHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZiwgX2V4Y2x1ZGVkKTtcbiAgdmFyIF91c2VTdGF0dXMgPSB1c2VTdGF0dXMoe1xuICAgICAgc3JjOiBzcmMsXG4gICAgICBmYWxsYmFjazogZmFsbGJhY2tcbiAgICB9KSxcbiAgICBfdXNlU3RhdHVzMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0dXMsIDIpLFxuICAgIGdldEltZ1JlZiA9IF91c2VTdGF0dXMyWzBdLFxuICAgIHNyY0FuZE9ubG9hZCA9IF91c2VTdGF0dXMyWzFdO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJpbWdcIiwgX2V4dGVuZHMoe1xuICAgIHJlZjogZnVuY3Rpb24gcmVmKF9yZWYyKSB7XG4gICAgICBpbWdSZWYuY3VycmVudCA9IF9yZWYyO1xuICAgICAgZ2V0SW1nUmVmKF9yZWYyKTtcbiAgICB9XG4gIH0sIHByb3BzLCBzcmNBbmRPbmxvYWQpKTtcbn07XG52YXIgUHJldmlldyA9IGZ1bmN0aW9uIFByZXZpZXcocHJvcHMpIHtcbiAgdmFyIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscyxcbiAgICBzcmMgPSBwcm9wcy5zcmMsXG4gICAgYWx0ID0gcHJvcHMuYWx0LFxuICAgIGZhbGxiYWNrID0gcHJvcHMuZmFsbGJhY2ssXG4gICAgX3Byb3BzJG1vdmFibGUgPSBwcm9wcy5tb3ZhYmxlLFxuICAgIG1vdmFibGUgPSBfcHJvcHMkbW92YWJsZSA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRtb3ZhYmxlLFxuICAgIG9uQ2xvc2UgPSBwcm9wcy5vbkNsb3NlLFxuICAgIHZpc2libGUgPSBwcm9wcy52aXNpYmxlLFxuICAgIF9wcm9wcyRpY29ucyA9IHByb3BzLmljb25zLFxuICAgIGljb25zID0gX3Byb3BzJGljb25zID09PSB2b2lkIDAgPyB7fSA6IF9wcm9wcyRpY29ucyxcbiAgICByb290Q2xhc3NOYW1lID0gcHJvcHMucm9vdENsYXNzTmFtZSxcbiAgICBjbG9zZUljb24gPSBwcm9wcy5jbG9zZUljb24sXG4gICAgZ2V0Q29udGFpbmVyID0gcHJvcHMuZ2V0Q29udGFpbmVyLFxuICAgIF9wcm9wcyRjdXJyZW50ID0gcHJvcHMuY3VycmVudCxcbiAgICBjdXJyZW50ID0gX3Byb3BzJGN1cnJlbnQgPT09IHZvaWQgMCA/IDAgOiBfcHJvcHMkY3VycmVudCxcbiAgICBfcHJvcHMkY291bnQgPSBwcm9wcy5jb3VudCxcbiAgICBjb3VudCA9IF9wcm9wcyRjb3VudCA9PT0gdm9pZCAwID8gMSA6IF9wcm9wcyRjb3VudCxcbiAgICBjb3VudFJlbmRlciA9IHByb3BzLmNvdW50UmVuZGVyLFxuICAgIF9wcm9wcyRzY2FsZVN0ZXAgPSBwcm9wcy5zY2FsZVN0ZXAsXG4gICAgc2NhbGVTdGVwID0gX3Byb3BzJHNjYWxlU3RlcCA9PT0gdm9pZCAwID8gMC41IDogX3Byb3BzJHNjYWxlU3RlcCxcbiAgICBfcHJvcHMkbWluU2NhbGUgPSBwcm9wcy5taW5TY2FsZSxcbiAgICBtaW5TY2FsZSA9IF9wcm9wcyRtaW5TY2FsZSA9PT0gdm9pZCAwID8gMSA6IF9wcm9wcyRtaW5TY2FsZSxcbiAgICBfcHJvcHMkbWF4U2NhbGUgPSBwcm9wcy5tYXhTY2FsZSxcbiAgICBtYXhTY2FsZSA9IF9wcm9wcyRtYXhTY2FsZSA9PT0gdm9pZCAwID8gNTAgOiBfcHJvcHMkbWF4U2NhbGUsXG4gICAgX3Byb3BzJHRyYW5zaXRpb25OYW1lID0gcHJvcHMudHJhbnNpdGlvbk5hbWUsXG4gICAgdHJhbnNpdGlvbk5hbWUgPSBfcHJvcHMkdHJhbnNpdGlvbk5hbWUgPT09IHZvaWQgMCA/ICd6b29tJyA6IF9wcm9wcyR0cmFuc2l0aW9uTmFtZSxcbiAgICBfcHJvcHMkbWFza1RyYW5zaXRpb24gPSBwcm9wcy5tYXNrVHJhbnNpdGlvbk5hbWUsXG4gICAgbWFza1RyYW5zaXRpb25OYW1lID0gX3Byb3BzJG1hc2tUcmFuc2l0aW9uID09PSB2b2lkIDAgPyAnZmFkZScgOiBfcHJvcHMkbWFza1RyYW5zaXRpb24sXG4gICAgaW1hZ2VSZW5kZXIgPSBwcm9wcy5pbWFnZVJlbmRlcixcbiAgICBpbWdDb21tb25Qcm9wcyA9IHByb3BzLmltZ0NvbW1vblByb3BzLFxuICAgIHRvb2xiYXJSZW5kZXIgPSBwcm9wcy50b29sYmFyUmVuZGVyLFxuICAgIG9uVHJhbnNmb3JtID0gcHJvcHMub25UcmFuc2Zvcm0sXG4gICAgb25DaGFuZ2UgPSBwcm9wcy5vbkNoYW5nZSxcbiAgICByZXN0UHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMocHJvcHMsIF9leGNsdWRlZDIpO1xuICB2YXIgaW1nUmVmID0gdXNlUmVmKCk7XG4gIHZhciBncm91cENvbnRleHQgPSB1c2VDb250ZXh0KFByZXZpZXdHcm91cENvbnRleHQpO1xuICB2YXIgc2hvd0xlZnRPclJpZ2h0U3dpdGNoZXMgPSBncm91cENvbnRleHQgJiYgY291bnQgPiAxO1xuICB2YXIgc2hvd09wZXJhdGlvbnNQcm9ncmVzcyA9IGdyb3VwQ29udGV4dCAmJiBjb3VudCA+PSAxO1xuICB2YXIgX3VzZVN0YXRlID0gdXNlU3RhdGUodHJ1ZSksXG4gICAgX3VzZVN0YXRlMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGF0ZSwgMiksXG4gICAgZW5hYmxlVHJhbnNpdGlvbiA9IF91c2VTdGF0ZTJbMF0sXG4gICAgc2V0RW5hYmxlVHJhbnNpdGlvbiA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBfdXNlSW1hZ2VUcmFuc2Zvcm0gPSB1c2VJbWFnZVRyYW5zZm9ybShpbWdSZWYsIG1pblNjYWxlLCBtYXhTY2FsZSwgb25UcmFuc2Zvcm0pLFxuICAgIHRyYW5zZm9ybSA9IF91c2VJbWFnZVRyYW5zZm9ybS50cmFuc2Zvcm0sXG4gICAgcmVzZXRUcmFuc2Zvcm0gPSBfdXNlSW1hZ2VUcmFuc2Zvcm0ucmVzZXRUcmFuc2Zvcm0sXG4gICAgdXBkYXRlVHJhbnNmb3JtID0gX3VzZUltYWdlVHJhbnNmb3JtLnVwZGF0ZVRyYW5zZm9ybSxcbiAgICBkaXNwYXRjaFpvb21DaGFuZ2UgPSBfdXNlSW1hZ2VUcmFuc2Zvcm0uZGlzcGF0Y2hab29tQ2hhbmdlO1xuICB2YXIgX3VzZU1vdXNlRXZlbnQgPSB1c2VNb3VzZUV2ZW50KGltZ1JlZiwgbW92YWJsZSwgdmlzaWJsZSwgc2NhbGVTdGVwLCB0cmFuc2Zvcm0sIHVwZGF0ZVRyYW5zZm9ybSwgZGlzcGF0Y2hab29tQ2hhbmdlKSxcbiAgICBpc01vdmluZyA9IF91c2VNb3VzZUV2ZW50LmlzTW92aW5nLFxuICAgIG9uTW91c2VEb3duID0gX3VzZU1vdXNlRXZlbnQub25Nb3VzZURvd24sXG4gICAgb25XaGVlbCA9IF91c2VNb3VzZUV2ZW50Lm9uV2hlZWw7XG4gIHZhciBfdXNlVG91Y2hFdmVudCA9IHVzZVRvdWNoRXZlbnQoaW1nUmVmLCBtb3ZhYmxlLCB2aXNpYmxlLCBtaW5TY2FsZSwgdHJhbnNmb3JtLCB1cGRhdGVUcmFuc2Zvcm0sIGRpc3BhdGNoWm9vbUNoYW5nZSksXG4gICAgaXNUb3VjaGluZyA9IF91c2VUb3VjaEV2ZW50LmlzVG91Y2hpbmcsXG4gICAgb25Ub3VjaFN0YXJ0ID0gX3VzZVRvdWNoRXZlbnQub25Ub3VjaFN0YXJ0LFxuICAgIG9uVG91Y2hNb3ZlID0gX3VzZVRvdWNoRXZlbnQub25Ub3VjaE1vdmUsXG4gICAgb25Ub3VjaEVuZCA9IF91c2VUb3VjaEV2ZW50Lm9uVG91Y2hFbmQ7XG4gIHZhciByb3RhdGUgPSB0cmFuc2Zvcm0ucm90YXRlLFxuICAgIHNjYWxlID0gdHJhbnNmb3JtLnNjYWxlO1xuICB2YXIgd3JhcENsYXNzTmFtZSA9IGNsYXNzbmFtZXMoX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW1vdmluZ1wiKSwgaXNNb3ZpbmcpKTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoIWVuYWJsZVRyYW5zaXRpb24pIHtcbiAgICAgIHNldEVuYWJsZVRyYW5zaXRpb24odHJ1ZSk7XG4gICAgfVxuICB9LCBbZW5hYmxlVHJhbnNpdGlvbl0pO1xuICB2YXIgb25BZnRlckNsb3NlID0gZnVuY3Rpb24gb25BZnRlckNsb3NlKCkge1xuICAgIHJlc2V0VHJhbnNmb3JtKCdjbG9zZScpO1xuICB9O1xuICB2YXIgb25ab29tSW4gPSBmdW5jdGlvbiBvblpvb21JbigpIHtcbiAgICBkaXNwYXRjaFpvb21DaGFuZ2UoQkFTRV9TQ0FMRV9SQVRJTyArIHNjYWxlU3RlcCwgJ3pvb21JbicpO1xuICB9O1xuICB2YXIgb25ab29tT3V0ID0gZnVuY3Rpb24gb25ab29tT3V0KCkge1xuICAgIGRpc3BhdGNoWm9vbUNoYW5nZShCQVNFX1NDQUxFX1JBVElPIC8gKEJBU0VfU0NBTEVfUkFUSU8gKyBzY2FsZVN0ZXApLCAnem9vbU91dCcpO1xuICB9O1xuICB2YXIgb25Sb3RhdGVSaWdodCA9IGZ1bmN0aW9uIG9uUm90YXRlUmlnaHQoKSB7XG4gICAgdXBkYXRlVHJhbnNmb3JtKHtcbiAgICAgIHJvdGF0ZTogcm90YXRlICsgOTBcbiAgICB9LCAncm90YXRlUmlnaHQnKTtcbiAgfTtcbiAgdmFyIG9uUm90YXRlTGVmdCA9IGZ1bmN0aW9uIG9uUm90YXRlTGVmdCgpIHtcbiAgICB1cGRhdGVUcmFuc2Zvcm0oe1xuICAgICAgcm90YXRlOiByb3RhdGUgLSA5MFxuICAgIH0sICdyb3RhdGVMZWZ0Jyk7XG4gIH07XG4gIHZhciBvbkZsaXBYID0gZnVuY3Rpb24gb25GbGlwWCgpIHtcbiAgICB1cGRhdGVUcmFuc2Zvcm0oe1xuICAgICAgZmxpcFg6ICF0cmFuc2Zvcm0uZmxpcFhcbiAgICB9LCAnZmxpcFgnKTtcbiAgfTtcbiAgdmFyIG9uRmxpcFkgPSBmdW5jdGlvbiBvbkZsaXBZKCkge1xuICAgIHVwZGF0ZVRyYW5zZm9ybSh7XG4gICAgICBmbGlwWTogIXRyYW5zZm9ybS5mbGlwWVxuICAgIH0sICdmbGlwWScpO1xuICB9O1xuICB2YXIgb25Td2l0Y2hMZWZ0ID0gZnVuY3Rpb24gb25Td2l0Y2hMZWZ0KGV2ZW50KSB7XG4gICAgZXZlbnQgPT09IG51bGwgfHwgZXZlbnQgPT09IHZvaWQgMCB8fCBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgfHwgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgaWYgKGN1cnJlbnQgPiAwKSB7XG4gICAgICBzZXRFbmFibGVUcmFuc2l0aW9uKGZhbHNlKTtcbiAgICAgIHJlc2V0VHJhbnNmb3JtKCdwcmV2Jyk7XG4gICAgICBvbkNoYW5nZSA9PT0gbnVsbCB8fCBvbkNoYW5nZSA9PT0gdm9pZCAwIHx8IG9uQ2hhbmdlKGN1cnJlbnQgLSAxLCBjdXJyZW50KTtcbiAgICB9XG4gIH07XG4gIHZhciBvblN3aXRjaFJpZ2h0ID0gZnVuY3Rpb24gb25Td2l0Y2hSaWdodChldmVudCkge1xuICAgIGV2ZW50ID09PSBudWxsIHx8IGV2ZW50ID09PSB2b2lkIDAgfHwgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICBldmVudCA9PT0gbnVsbCB8fCBldmVudCA9PT0gdm9pZCAwIHx8IGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIGlmIChjdXJyZW50IDwgY291bnQgLSAxKSB7XG4gICAgICBzZXRFbmFibGVUcmFuc2l0aW9uKGZhbHNlKTtcbiAgICAgIHJlc2V0VHJhbnNmb3JtKCduZXh0Jyk7XG4gICAgICBvbkNoYW5nZSA9PT0gbnVsbCB8fCBvbkNoYW5nZSA9PT0gdm9pZCAwIHx8IG9uQ2hhbmdlKGN1cnJlbnQgKyAxLCBjdXJyZW50KTtcbiAgICB9XG4gIH07XG4gIHZhciBvbktleURvd24gPSBmdW5jdGlvbiBvbktleURvd24oZXZlbnQpIHtcbiAgICBpZiAoIXZpc2libGUgfHwgIXNob3dMZWZ0T3JSaWdodFN3aXRjaGVzKSByZXR1cm47XG4gICAgaWYgKGV2ZW50LmtleUNvZGUgPT09IEtleUNvZGUuTEVGVCkge1xuICAgICAgb25Td2l0Y2hMZWZ0KCk7XG4gICAgfSBlbHNlIGlmIChldmVudC5rZXlDb2RlID09PSBLZXlDb2RlLlJJR0hUKSB7XG4gICAgICBvblN3aXRjaFJpZ2h0KCk7XG4gICAgfVxuICB9O1xuICB2YXIgb25Eb3VibGVDbGljayA9IGZ1bmN0aW9uIG9uRG91YmxlQ2xpY2soZXZlbnQpIHtcbiAgICBpZiAodmlzaWJsZSkge1xuICAgICAgaWYgKHNjYWxlICE9PSAxKSB7XG4gICAgICAgIHVwZGF0ZVRyYW5zZm9ybSh7XG4gICAgICAgICAgeDogMCxcbiAgICAgICAgICB5OiAwLFxuICAgICAgICAgIHNjYWxlOiAxXG4gICAgICAgIH0sICdkb3VibGVDbGljaycpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZGlzcGF0Y2hab29tQ2hhbmdlKEJBU0VfU0NBTEVfUkFUSU8gKyBzY2FsZVN0ZXAsICdkb3VibGVDbGljaycsIGV2ZW50LmNsaWVudFgsIGV2ZW50LmNsaWVudFkpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgb25LZXlEb3duTGlzdGVuZXIgPSBhZGRFdmVudExpc3RlbmVyKHdpbmRvdywgJ2tleWRvd24nLCBvbktleURvd24sIGZhbHNlKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgb25LZXlEb3duTGlzdGVuZXIucmVtb3ZlKCk7XG4gICAgfTtcbiAgfSwgW3Zpc2libGUsIHNob3dMZWZ0T3JSaWdodFN3aXRjaGVzLCBjdXJyZW50XSk7XG4gIHZhciBpbWdOb2RlID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUHJldmlld0ltYWdlLCBfZXh0ZW5kcyh7fSwgaW1nQ29tbW9uUHJvcHMsIHtcbiAgICB3aWR0aDogcHJvcHMud2lkdGgsXG4gICAgaGVpZ2h0OiBwcm9wcy5oZWlnaHQsXG4gICAgaW1nUmVmOiBpbWdSZWYsXG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWltZ1wiKSxcbiAgICBhbHQ6IGFsdCxcbiAgICBzdHlsZToge1xuICAgICAgdHJhbnNmb3JtOiBcInRyYW5zbGF0ZTNkKFwiLmNvbmNhdCh0cmFuc2Zvcm0ueCwgXCJweCwgXCIpLmNvbmNhdCh0cmFuc2Zvcm0ueSwgXCJweCwgMCkgc2NhbGUzZChcIikuY29uY2F0KHRyYW5zZm9ybS5mbGlwWCA/ICctJyA6ICcnKS5jb25jYXQoc2NhbGUsIFwiLCBcIikuY29uY2F0KHRyYW5zZm9ybS5mbGlwWSA/ICctJyA6ICcnKS5jb25jYXQoc2NhbGUsIFwiLCAxKSByb3RhdGUoXCIpLmNvbmNhdChyb3RhdGUsIFwiZGVnKVwiKSxcbiAgICAgIHRyYW5zaXRpb25EdXJhdGlvbjogKCFlbmFibGVUcmFuc2l0aW9uIHx8IGlzVG91Y2hpbmcpICYmICcwcydcbiAgICB9LFxuICAgIGZhbGxiYWNrOiBmYWxsYmFjayxcbiAgICBzcmM6IHNyYyxcbiAgICBvbldoZWVsOiBvbldoZWVsLFxuICAgIG9uTW91c2VEb3duOiBvbk1vdXNlRG93bixcbiAgICBvbkRvdWJsZUNsaWNrOiBvbkRvdWJsZUNsaWNrLFxuICAgIG9uVG91Y2hTdGFydDogb25Ub3VjaFN0YXJ0LFxuICAgIG9uVG91Y2hNb3ZlOiBvblRvdWNoTW92ZSxcbiAgICBvblRvdWNoRW5kOiBvblRvdWNoRW5kLFxuICAgIG9uVG91Y2hDYW5jZWw6IG9uVG91Y2hFbmRcbiAgfSkpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUmVhY3QuRnJhZ21lbnQsIG51bGwsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KERpYWxvZywgX2V4dGVuZHMoe1xuICAgIHRyYW5zaXRpb25OYW1lOiB0cmFuc2l0aW9uTmFtZSxcbiAgICBtYXNrVHJhbnNpdGlvbk5hbWU6IG1hc2tUcmFuc2l0aW9uTmFtZSxcbiAgICBjbG9zYWJsZTogZmFsc2UsXG4gICAga2V5Ym9hcmQ6IHRydWUsXG4gICAgcHJlZml4Q2xzOiBwcmVmaXhDbHMsXG4gICAgb25DbG9zZTogb25DbG9zZSxcbiAgICB2aXNpYmxlOiB2aXNpYmxlLFxuICAgIGNsYXNzTmFtZXM6IHtcbiAgICAgIHdyYXBwZXI6IHdyYXBDbGFzc05hbWVcbiAgICB9LFxuICAgIHJvb3RDbGFzc05hbWU6IHJvb3RDbGFzc05hbWUsXG4gICAgZ2V0Q29udGFpbmVyOiBnZXRDb250YWluZXJcbiAgfSwgcmVzdFByb3BzLCB7XG4gICAgYWZ0ZXJDbG9zZTogb25BZnRlckNsb3NlXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgY2xhc3NOYW1lOiBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLWltZy13cmFwcGVyXCIpXG4gIH0sIGltYWdlUmVuZGVyID8gaW1hZ2VSZW5kZXIoaW1nTm9kZSwgX29iamVjdFNwcmVhZCh7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2Zvcm1cbiAgfSwgZ3JvdXBDb250ZXh0ID8ge1xuICAgIGN1cnJlbnQ6IGN1cnJlbnRcbiAgfSA6IHt9KSkgOiBpbWdOb2RlKSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE9wZXJhdGlvbnMsIHtcbiAgICB2aXNpYmxlOiB2aXNpYmxlLFxuICAgIHRyYW5zZm9ybTogdHJhbnNmb3JtLFxuICAgIG1hc2tUcmFuc2l0aW9uTmFtZTogbWFza1RyYW5zaXRpb25OYW1lLFxuICAgIGNsb3NlSWNvbjogY2xvc2VJY29uLFxuICAgIGdldENvbnRhaW5lcjogZ2V0Q29udGFpbmVyLFxuICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgIHJvb3RDbGFzc05hbWU6IHJvb3RDbGFzc05hbWUsXG4gICAgaWNvbnM6IGljb25zLFxuICAgIGNvdW50UmVuZGVyOiBjb3VudFJlbmRlcixcbiAgICBzaG93U3dpdGNoOiBzaG93TGVmdE9yUmlnaHRTd2l0Y2hlcyxcbiAgICBzaG93UHJvZ3Jlc3M6IHNob3dPcGVyYXRpb25zUHJvZ3Jlc3MsXG4gICAgY3VycmVudDogY3VycmVudCxcbiAgICBjb3VudDogY291bnQsXG4gICAgc2NhbGU6IHNjYWxlLFxuICAgIG1pblNjYWxlOiBtaW5TY2FsZSxcbiAgICBtYXhTY2FsZTogbWF4U2NhbGUsXG4gICAgdG9vbGJhclJlbmRlcjogdG9vbGJhclJlbmRlcixcbiAgICBvblN3aXRjaExlZnQ6IG9uU3dpdGNoTGVmdCxcbiAgICBvblN3aXRjaFJpZ2h0OiBvblN3aXRjaFJpZ2h0LFxuICAgIG9uWm9vbUluOiBvblpvb21JbixcbiAgICBvblpvb21PdXQ6IG9uWm9vbU91dCxcbiAgICBvblJvdGF0ZVJpZ2h0OiBvblJvdGF0ZVJpZ2h0LFxuICAgIG9uUm90YXRlTGVmdDogb25Sb3RhdGVMZWZ0LFxuICAgIG9uRmxpcFg6IG9uRmxpcFgsXG4gICAgb25GbGlwWTogb25GbGlwWSxcbiAgICBvbkNsb3NlOiBvbkNsb3NlLFxuICAgIHpJbmRleDogcmVzdFByb3BzLnpJbmRleCAhPT0gdW5kZWZpbmVkID8gcmVzdFByb3BzLnpJbmRleCArIDEgOiB1bmRlZmluZWRcbiAgfSkpO1xufTtcbmV4cG9ydCBkZWZhdWx0IFByZXZpZXc7IiwiaW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdG9Db25zdW1hYmxlQXJyYXlcIjtcbmltcG9ydCBfZGVmaW5lUHJvcGVydHkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5XCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDT01NT05fUFJPUFMgfSBmcm9tIFwiLi4vY29tbW9uXCI7XG4vKipcbiAqIE1lcmdlIHByb3BzIHByb3ZpZGVkIGBpdGVtc2Agb3IgY29udGV4dCBjb2xsZWN0ZWQgaW1hZ2VzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVByZXZpZXdJdGVtcyhpdGVtcykge1xuICAvLyBDb250ZXh0IGNvbGxlY3Rpb24gaW1hZ2UgZGF0YVxuICB2YXIgX1JlYWN0JHVzZVN0YXRlID0gUmVhY3QudXNlU3RhdGUoe30pLFxuICAgIF9SZWFjdCR1c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfUmVhY3QkdXNlU3RhdGUsIDIpLFxuICAgIGltYWdlcyA9IF9SZWFjdCR1c2VTdGF0ZTJbMF0sXG4gICAgc2V0SW1hZ2VzID0gX1JlYWN0JHVzZVN0YXRlMlsxXTtcbiAgdmFyIHJlZ2lzdGVySW1hZ2UgPSBSZWFjdC51c2VDYWxsYmFjayhmdW5jdGlvbiAoaWQsIGRhdGEpIHtcbiAgICBzZXRJbWFnZXMoZnVuY3Rpb24gKGltZ3MpIHtcbiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIGltZ3MpLCB7fSwgX2RlZmluZVByb3BlcnR5KHt9LCBpZCwgZGF0YSkpO1xuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICBzZXRJbWFnZXMoZnVuY3Rpb24gKGltZ3MpIHtcbiAgICAgICAgdmFyIGNsb25lSW1ncyA9IF9vYmplY3RTcHJlYWQoe30sIGltZ3MpO1xuICAgICAgICBkZWxldGUgY2xvbmVJbWdzW2lkXTtcbiAgICAgICAgcmV0dXJuIGNsb25lSW1ncztcbiAgICAgIH0pO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICAvLyBpdGVtc1xuICB2YXIgbWVyZ2VkSXRlbXMgPSBSZWFjdC51c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoaXRlbXMpIHtcbiAgICAgIHJldHVybiBpdGVtcy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpdGVtID09PSAnc3RyaW5nJykge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICAgIHNyYzogaXRlbVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGRhdGEgPSB7fTtcbiAgICAgICAgT2JqZWN0LmtleXMoaXRlbSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgICAgaWYgKFsnc3JjJ10uY29uY2F0KF90b0NvbnN1bWFibGVBcnJheShDT01NT05fUFJPUFMpKS5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgICAgICBkYXRhW2tleV0gPSBpdGVtW2tleV07XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBkYXRhOiBkYXRhXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKGltYWdlcykucmVkdWNlKGZ1bmN0aW9uICh0b3RhbCwgaWQpIHtcbiAgICAgIHZhciBfaW1hZ2VzJGlkID0gaW1hZ2VzW2lkXSxcbiAgICAgICAgY2FuUHJldmlldyA9IF9pbWFnZXMkaWQuY2FuUHJldmlldyxcbiAgICAgICAgZGF0YSA9IF9pbWFnZXMkaWQuZGF0YTtcbiAgICAgIGlmIChjYW5QcmV2aWV3KSB7XG4gICAgICAgIHRvdGFsLnB1c2goe1xuICAgICAgICAgIGRhdGE6IGRhdGEsXG4gICAgICAgICAgaWQ6IGlkXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHRvdGFsO1xuICAgIH0sIFtdKTtcbiAgfSwgW2l0ZW1zLCBpbWFnZXNdKTtcbiAgcmV0dXJuIFttZXJnZWRJdGVtcywgcmVnaXN0ZXJJbWFnZV07XG59IiwiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCBfdHlwZW9mIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2ZcIjtcbmltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1widmlzaWJsZVwiLCBcIm9uVmlzaWJsZUNoYW5nZVwiLCBcImdldENvbnRhaW5lclwiLCBcImN1cnJlbnRcIiwgXCJtb3ZhYmxlXCIsIFwibWluU2NhbGVcIiwgXCJtYXhTY2FsZVwiLCBcImNvdW50UmVuZGVyXCIsIFwiY2xvc2VJY29uXCIsIFwib25DaGFuZ2VcIiwgXCJvblRyYW5zZm9ybVwiLCBcInRvb2xiYXJSZW5kZXJcIiwgXCJpbWFnZVJlbmRlclwiXSxcbiAgX2V4Y2x1ZGVkMiA9IFtcInNyY1wiXTtcbmltcG9ydCB1c2VNZXJnZWRTdGF0ZSBmcm9tIFwicmMtdXRpbC9lcy9ob29rcy91c2VNZXJnZWRTdGF0ZVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBQcmV2aWV3R3JvdXBDb250ZXh0IH0gZnJvbSBcIi4vY29udGV4dFwiO1xuaW1wb3J0IHVzZVByZXZpZXdJdGVtcyBmcm9tIFwiLi9ob29rcy91c2VQcmV2aWV3SXRlbXNcIjtcbmltcG9ydCBQcmV2aWV3IGZyb20gXCIuL1ByZXZpZXdcIjtcbnZhciBHcm91cCA9IGZ1bmN0aW9uIEdyb3VwKF9yZWYpIHtcbiAgdmFyIF9tZXJnZWRJdGVtcyRjdXJyZW50O1xuICB2YXIgX3JlZiRwcmV2aWV3UHJlZml4Q2xzID0gX3JlZi5wcmV2aWV3UHJlZml4Q2xzLFxuICAgIHByZXZpZXdQcmVmaXhDbHMgPSBfcmVmJHByZXZpZXdQcmVmaXhDbHMgPT09IHZvaWQgMCA/ICdyYy1pbWFnZS1wcmV2aWV3JyA6IF9yZWYkcHJldmlld1ByZWZpeENscyxcbiAgICBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgX3JlZiRpY29ucyA9IF9yZWYuaWNvbnMsXG4gICAgaWNvbnMgPSBfcmVmJGljb25zID09PSB2b2lkIDAgPyB7fSA6IF9yZWYkaWNvbnMsXG4gICAgaXRlbXMgPSBfcmVmLml0ZW1zLFxuICAgIHByZXZpZXcgPSBfcmVmLnByZXZpZXcsXG4gICAgZmFsbGJhY2sgPSBfcmVmLmZhbGxiYWNrO1xuICB2YXIgX3JlZjIgPSBfdHlwZW9mKHByZXZpZXcpID09PSAnb2JqZWN0JyA/IHByZXZpZXcgOiB7fSxcbiAgICBwcmV2aWV3VmlzaWJsZSA9IF9yZWYyLnZpc2libGUsXG4gICAgb25WaXNpYmxlQ2hhbmdlID0gX3JlZjIub25WaXNpYmxlQ2hhbmdlLFxuICAgIGdldENvbnRhaW5lciA9IF9yZWYyLmdldENvbnRhaW5lcixcbiAgICBjdXJyZW50SW5kZXggPSBfcmVmMi5jdXJyZW50LFxuICAgIG1vdmFibGUgPSBfcmVmMi5tb3ZhYmxlLFxuICAgIG1pblNjYWxlID0gX3JlZjIubWluU2NhbGUsXG4gICAgbWF4U2NhbGUgPSBfcmVmMi5tYXhTY2FsZSxcbiAgICBjb3VudFJlbmRlciA9IF9yZWYyLmNvdW50UmVuZGVyLFxuICAgIGNsb3NlSWNvbiA9IF9yZWYyLmNsb3NlSWNvbixcbiAgICBvbkNoYW5nZSA9IF9yZWYyLm9uQ2hhbmdlLFxuICAgIG9uVHJhbnNmb3JtID0gX3JlZjIub25UcmFuc2Zvcm0sXG4gICAgdG9vbGJhclJlbmRlciA9IF9yZWYyLnRvb2xiYXJSZW5kZXIsXG4gICAgaW1hZ2VSZW5kZXIgPSBfcmVmMi5pbWFnZVJlbmRlcixcbiAgICBkaWFsb2dQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmMiwgX2V4Y2x1ZGVkKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PSBJdGVtcyA9PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIF91c2VQcmV2aWV3SXRlbXMgPSB1c2VQcmV2aWV3SXRlbXMoaXRlbXMpLFxuICAgIF91c2VQcmV2aWV3SXRlbXMyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVByZXZpZXdJdGVtcywgMiksXG4gICAgbWVyZ2VkSXRlbXMgPSBfdXNlUHJldmlld0l0ZW1zMlswXSxcbiAgICByZWdpc3RlciA9IF91c2VQcmV2aWV3SXRlbXMyWzFdO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gUHJldmlldyA9PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyA+Pj4gSW5kZXhcbiAgdmFyIF91c2VNZXJnZWRTdGF0ZSA9IHVzZU1lcmdlZFN0YXRlKDAsIHtcbiAgICAgIHZhbHVlOiBjdXJyZW50SW5kZXhcbiAgICB9KSxcbiAgICBfdXNlTWVyZ2VkU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZU1lcmdlZFN0YXRlLCAyKSxcbiAgICBjdXJyZW50ID0gX3VzZU1lcmdlZFN0YXRlMlswXSxcbiAgICBzZXRDdXJyZW50ID0gX3VzZU1lcmdlZFN0YXRlMlsxXTtcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKGZhbHNlKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBrZWVwT3BlbkluZGV4ID0gX3VzZVN0YXRlMlswXSxcbiAgICBzZXRLZWVwT3BlbkluZGV4ID0gX3VzZVN0YXRlMlsxXTtcblxuICAvLyA+Pj4gSW1hZ2VcbiAgdmFyIF9yZWYzID0gKChfbWVyZ2VkSXRlbXMkY3VycmVudCA9IG1lcmdlZEl0ZW1zW2N1cnJlbnRdKSA9PT0gbnVsbCB8fCBfbWVyZ2VkSXRlbXMkY3VycmVudCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX21lcmdlZEl0ZW1zJGN1cnJlbnQuZGF0YSkgfHwge30sXG4gICAgc3JjID0gX3JlZjMuc3JjLFxuICAgIGltZ0NvbW1vblByb3BzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzKF9yZWYzLCBfZXhjbHVkZWQyKTtcbiAgLy8gPj4+IFZpc2libGVcbiAgdmFyIF91c2VNZXJnZWRTdGF0ZTMgPSB1c2VNZXJnZWRTdGF0ZSghIXByZXZpZXdWaXNpYmxlLCB7XG4gICAgICB2YWx1ZTogcHJldmlld1Zpc2libGUsXG4gICAgICBvbkNoYW5nZTogZnVuY3Rpb24gb25DaGFuZ2UodmFsLCBwcmV2VmFsKSB7XG4gICAgICAgIG9uVmlzaWJsZUNoYW5nZSA9PT0gbnVsbCB8fCBvblZpc2libGVDaGFuZ2UgPT09IHZvaWQgMCB8fCBvblZpc2libGVDaGFuZ2UodmFsLCBwcmV2VmFsLCBjdXJyZW50KTtcbiAgICAgIH1cbiAgICB9KSxcbiAgICBfdXNlTWVyZ2VkU3RhdGU0ID0gX3NsaWNlZFRvQXJyYXkoX3VzZU1lcmdlZFN0YXRlMywgMiksXG4gICAgaXNTaG93UHJldmlldyA9IF91c2VNZXJnZWRTdGF0ZTRbMF0sXG4gICAgc2V0U2hvd1ByZXZpZXcgPSBfdXNlTWVyZ2VkU3RhdGU0WzFdO1xuXG4gIC8vID4+PiBQb3NpdGlvblxuICB2YXIgX3VzZVN0YXRlMyA9IHVzZVN0YXRlKG51bGwpLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBtb3VzZVBvc2l0aW9uID0gX3VzZVN0YXRlNFswXSxcbiAgICBzZXRNb3VzZVBvc2l0aW9uID0gX3VzZVN0YXRlNFsxXTtcbiAgdmFyIG9uUHJldmlld0Zyb21JbWFnZSA9IFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uIChpZCwgbW91c2VYLCBtb3VzZVkpIHtcbiAgICB2YXIgaW5kZXggPSBtZXJnZWRJdGVtcy5maW5kSW5kZXgoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgIHJldHVybiBpdGVtLmlkID09PSBpZDtcbiAgICB9KTtcbiAgICBzZXRTaG93UHJldmlldyh0cnVlKTtcbiAgICBzZXRNb3VzZVBvc2l0aW9uKHtcbiAgICAgIHg6IG1vdXNlWCxcbiAgICAgIHk6IG1vdXNlWVxuICAgIH0pO1xuICAgIHNldEN1cnJlbnQoaW5kZXggPCAwID8gMCA6IGluZGV4KTtcbiAgICBzZXRLZWVwT3BlbkluZGV4KHRydWUpO1xuICB9LCBbbWVyZ2VkSXRlbXNdKTtcblxuICAvLyBSZXNldCBjdXJyZW50IHdoZW4gcmVvcGVuXG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKGlzU2hvd1ByZXZpZXcpIHtcbiAgICAgIGlmICgha2VlcE9wZW5JbmRleCkge1xuICAgICAgICBzZXRDdXJyZW50KDApO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRLZWVwT3BlbkluZGV4KGZhbHNlKTtcbiAgICB9XG4gIH0sIFtpc1Nob3dQcmV2aWV3XSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT0gRXZlbnRzID09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBvbkludGVybmFsQ2hhbmdlID0gZnVuY3Rpb24gb25JbnRlcm5hbENoYW5nZShuZXh0LCBwcmV2KSB7XG4gICAgc2V0Q3VycmVudChuZXh0KTtcbiAgICBvbkNoYW5nZSA9PT0gbnVsbCB8fCBvbkNoYW5nZSA9PT0gdm9pZCAwIHx8IG9uQ2hhbmdlKG5leHQsIHByZXYpO1xuICB9O1xuICB2YXIgb25QcmV2aWV3Q2xvc2UgPSBmdW5jdGlvbiBvblByZXZpZXdDbG9zZSgpIHtcbiAgICBzZXRTaG93UHJldmlldyhmYWxzZSk7XG4gICAgc2V0TW91c2VQb3NpdGlvbihudWxsKTtcbiAgfTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IENvbnRleHQgPT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIHByZXZpZXdHcm91cENvbnRleHQgPSBSZWFjdC51c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4ge1xuICAgICAgcmVnaXN0ZXI6IHJlZ2lzdGVyLFxuICAgICAgb25QcmV2aWV3OiBvblByZXZpZXdGcm9tSW1hZ2VcbiAgICB9O1xuICB9LCBbcmVnaXN0ZXIsIG9uUHJldmlld0Zyb21JbWFnZV0pO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09IFJlbmRlciA9PT09PT09PT09PT09PT09PT09PT09PT09PVxuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUHJldmlld0dyb3VwQ29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiBwcmV2aWV3R3JvdXBDb250ZXh0XG4gIH0sIGNoaWxkcmVuLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChQcmV2aWV3LCBfZXh0ZW5kcyh7XG4gICAgXCJhcmlhLWhpZGRlblwiOiAhaXNTaG93UHJldmlldyxcbiAgICBtb3ZhYmxlOiBtb3ZhYmxlLFxuICAgIHZpc2libGU6IGlzU2hvd1ByZXZpZXcsXG4gICAgcHJlZml4Q2xzOiBwcmV2aWV3UHJlZml4Q2xzLFxuICAgIGNsb3NlSWNvbjogY2xvc2VJY29uLFxuICAgIG9uQ2xvc2U6IG9uUHJldmlld0Nsb3NlLFxuICAgIG1vdXNlUG9zaXRpb246IG1vdXNlUG9zaXRpb24sXG4gICAgaW1nQ29tbW9uUHJvcHM6IGltZ0NvbW1vblByb3BzLFxuICAgIHNyYzogc3JjLFxuICAgIGZhbGxiYWNrOiBmYWxsYmFjayxcbiAgICBpY29uczogaWNvbnMsXG4gICAgbWluU2NhbGU6IG1pblNjYWxlLFxuICAgIG1heFNjYWxlOiBtYXhTY2FsZSxcbiAgICBnZXRDb250YWluZXI6IGdldENvbnRhaW5lcixcbiAgICBjdXJyZW50OiBjdXJyZW50LFxuICAgIGNvdW50OiBtZXJnZWRJdGVtcy5sZW5ndGgsXG4gICAgY291bnRSZW5kZXI6IGNvdW50UmVuZGVyLFxuICAgIG9uVHJhbnNmb3JtOiBvblRyYW5zZm9ybSxcbiAgICB0b29sYmFyUmVuZGVyOiB0b29sYmFyUmVuZGVyLFxuICAgIGltYWdlUmVuZGVyOiBpbWFnZVJlbmRlcixcbiAgICBvbkNoYW5nZTogb25JbnRlcm5hbENoYW5nZVxuICB9LCBkaWFsb2dQcm9wcykpKTtcbn07XG5leHBvcnQgZGVmYXVsdCBHcm91cDsiLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX2RlZmluZVByb3BlcnR5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9kZWZpbmVQcm9wZXJ0eVwiO1xuaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgX3R5cGVvZiBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdHlwZW9mXCI7XG5pbXBvcnQgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllc1wiO1xudmFyIF9leGNsdWRlZCA9IFtcInNyY1wiLCBcImFsdFwiLCBcIm9uUHJldmlld0Nsb3NlXCIsIFwicHJlZml4Q2xzXCIsIFwicHJldmlld1ByZWZpeENsc1wiLCBcInBsYWNlaG9sZGVyXCIsIFwiZmFsbGJhY2tcIiwgXCJ3aWR0aFwiLCBcImhlaWdodFwiLCBcInN0eWxlXCIsIFwicHJldmlld1wiLCBcImNsYXNzTmFtZVwiLCBcIm9uQ2xpY2tcIiwgXCJvbkVycm9yXCIsIFwid3JhcHBlckNsYXNzTmFtZVwiLCBcIndyYXBwZXJTdHlsZVwiLCBcInJvb3RDbGFzc05hbWVcIl0sXG4gIF9leGNsdWRlZDIgPSBbXCJzcmNcIiwgXCJ2aXNpYmxlXCIsIFwib25WaXNpYmxlQ2hhbmdlXCIsIFwiZ2V0Q29udGFpbmVyXCIsIFwibWFza1wiLCBcIm1hc2tDbGFzc05hbWVcIiwgXCJtb3ZhYmxlXCIsIFwiaWNvbnNcIiwgXCJzY2FsZVN0ZXBcIiwgXCJtaW5TY2FsZVwiLCBcIm1heFNjYWxlXCIsIFwiaW1hZ2VSZW5kZXJcIiwgXCJ0b29sYmFyUmVuZGVyXCJdO1xuaW1wb3J0IGNuIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IHsgZ2V0T2Zmc2V0IH0gZnJvbSBcInJjLXV0aWwvZXMvRG9tL2Nzc1wiO1xuaW1wb3J0IHVzZU1lcmdlZFN0YXRlIGZyb20gXCJyYy11dGlsL2VzL2hvb2tzL3VzZU1lcmdlZFN0YXRlXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0LCB1c2VNZW1vLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENPTU1PTl9QUk9QUyB9IGZyb20gXCIuL2NvbW1vblwiO1xuaW1wb3J0IHsgUHJldmlld0dyb3VwQ29udGV4dCB9IGZyb20gXCIuL2NvbnRleHRcIjtcbmltcG9ydCB1c2VSZWdpc3RlckltYWdlIGZyb20gXCIuL2hvb2tzL3VzZVJlZ2lzdGVySW1hZ2VcIjtcbmltcG9ydCB1c2VTdGF0dXMgZnJvbSBcIi4vaG9va3MvdXNlU3RhdHVzXCI7XG5pbXBvcnQgUHJldmlldyBmcm9tIFwiLi9QcmV2aWV3XCI7XG5pbXBvcnQgUHJldmlld0dyb3VwIGZyb20gXCIuL1ByZXZpZXdHcm91cFwiO1xudmFyIEltYWdlSW50ZXJuYWwgPSBmdW5jdGlvbiBJbWFnZUludGVybmFsKHByb3BzKSB7XG4gIHZhciBpbWdTcmMgPSBwcm9wcy5zcmMsXG4gICAgYWx0ID0gcHJvcHMuYWx0LFxuICAgIG9uSW5pdGlhbFByZXZpZXdDbG9zZSA9IHByb3BzLm9uUHJldmlld0Nsb3NlLFxuICAgIF9wcm9wcyRwcmVmaXhDbHMgPSBwcm9wcy5wcmVmaXhDbHMsXG4gICAgcHJlZml4Q2xzID0gX3Byb3BzJHByZWZpeENscyA9PT0gdm9pZCAwID8gJ3JjLWltYWdlJyA6IF9wcm9wcyRwcmVmaXhDbHMsXG4gICAgX3Byb3BzJHByZXZpZXdQcmVmaXhDID0gcHJvcHMucHJldmlld1ByZWZpeENscyxcbiAgICBwcmV2aWV3UHJlZml4Q2xzID0gX3Byb3BzJHByZXZpZXdQcmVmaXhDID09PSB2b2lkIDAgPyBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXByZXZpZXdcIikgOiBfcHJvcHMkcHJldmlld1ByZWZpeEMsXG4gICAgcGxhY2Vob2xkZXIgPSBwcm9wcy5wbGFjZWhvbGRlcixcbiAgICBmYWxsYmFjayA9IHByb3BzLmZhbGxiYWNrLFxuICAgIHdpZHRoID0gcHJvcHMud2lkdGgsXG4gICAgaGVpZ2h0ID0gcHJvcHMuaGVpZ2h0LFxuICAgIHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgX3Byb3BzJHByZXZpZXcgPSBwcm9wcy5wcmV2aWV3LFxuICAgIHByZXZpZXcgPSBfcHJvcHMkcHJldmlldyA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRwcmV2aWV3LFxuICAgIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICBvbkNsaWNrID0gcHJvcHMub25DbGljayxcbiAgICBvbkVycm9yID0gcHJvcHMub25FcnJvcixcbiAgICB3cmFwcGVyQ2xhc3NOYW1lID0gcHJvcHMud3JhcHBlckNsYXNzTmFtZSxcbiAgICB3cmFwcGVyU3R5bGUgPSBwcm9wcy53cmFwcGVyU3R5bGUsXG4gICAgcm9vdENsYXNzTmFtZSA9IHByb3BzLnJvb3RDbGFzc05hbWUsXG4gICAgb3RoZXJQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgdmFyIGlzQ3VzdG9tUGxhY2Vob2xkZXIgPSBwbGFjZWhvbGRlciAmJiBwbGFjZWhvbGRlciAhPT0gdHJ1ZTtcbiAgdmFyIF9yZWYgPSBfdHlwZW9mKHByZXZpZXcpID09PSAnb2JqZWN0JyA/IHByZXZpZXcgOiB7fSxcbiAgICBwcmV2aWV3U3JjID0gX3JlZi5zcmMsXG4gICAgX3JlZiR2aXNpYmxlID0gX3JlZi52aXNpYmxlLFxuICAgIHByZXZpZXdWaXNpYmxlID0gX3JlZiR2aXNpYmxlID09PSB2b2lkIDAgPyB1bmRlZmluZWQgOiBfcmVmJHZpc2libGUsXG4gICAgX3JlZiRvblZpc2libGVDaGFuZ2UgPSBfcmVmLm9uVmlzaWJsZUNoYW5nZSxcbiAgICBvblByZXZpZXdWaXNpYmxlQ2hhbmdlID0gX3JlZiRvblZpc2libGVDaGFuZ2UgPT09IHZvaWQgMCA/IG9uSW5pdGlhbFByZXZpZXdDbG9zZSA6IF9yZWYkb25WaXNpYmxlQ2hhbmdlLFxuICAgIF9yZWYkZ2V0Q29udGFpbmVyID0gX3JlZi5nZXRDb250YWluZXIsXG4gICAgZ2V0UHJldmlld0NvbnRhaW5lciA9IF9yZWYkZ2V0Q29udGFpbmVyID09PSB2b2lkIDAgPyB1bmRlZmluZWQgOiBfcmVmJGdldENvbnRhaW5lcixcbiAgICBwcmV2aWV3TWFzayA9IF9yZWYubWFzayxcbiAgICBtYXNrQ2xhc3NOYW1lID0gX3JlZi5tYXNrQ2xhc3NOYW1lLFxuICAgIG1vdmFibGUgPSBfcmVmLm1vdmFibGUsXG4gICAgaWNvbnMgPSBfcmVmLmljb25zLFxuICAgIHNjYWxlU3RlcCA9IF9yZWYuc2NhbGVTdGVwLFxuICAgIG1pblNjYWxlID0gX3JlZi5taW5TY2FsZSxcbiAgICBtYXhTY2FsZSA9IF9yZWYubWF4U2NhbGUsXG4gICAgaW1hZ2VSZW5kZXIgPSBfcmVmLmltYWdlUmVuZGVyLFxuICAgIHRvb2xiYXJSZW5kZXIgPSBfcmVmLnRvb2xiYXJSZW5kZXIsXG4gICAgZGlhbG9nUHJvcHMgPSBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMoX3JlZiwgX2V4Y2x1ZGVkMik7XG4gIHZhciBzcmMgPSBwcmV2aWV3U3JjICE9PSBudWxsICYmIHByZXZpZXdTcmMgIT09IHZvaWQgMCA/IHByZXZpZXdTcmMgOiBpbWdTcmM7XG4gIHZhciBfdXNlTWVyZ2VkU3RhdGUgPSB1c2VNZXJnZWRTdGF0ZSghIXByZXZpZXdWaXNpYmxlLCB7XG4gICAgICB2YWx1ZTogcHJldmlld1Zpc2libGUsXG4gICAgICBvbkNoYW5nZTogb25QcmV2aWV3VmlzaWJsZUNoYW5nZVxuICAgIH0pLFxuICAgIF91c2VNZXJnZWRTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlTWVyZ2VkU3RhdGUsIDIpLFxuICAgIGlzU2hvd1ByZXZpZXcgPSBfdXNlTWVyZ2VkU3RhdGUyWzBdLFxuICAgIHNldFNob3dQcmV2aWV3ID0gX3VzZU1lcmdlZFN0YXRlMlsxXTtcbiAgdmFyIF91c2VTdGF0dXMgPSB1c2VTdGF0dXMoe1xuICAgICAgc3JjOiBpbWdTcmMsXG4gICAgICBpc0N1c3RvbVBsYWNlaG9sZGVyOiBpc0N1c3RvbVBsYWNlaG9sZGVyLFxuICAgICAgZmFsbGJhY2s6IGZhbGxiYWNrXG4gICAgfSksXG4gICAgX3VzZVN0YXR1czIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdHVzLCAzKSxcbiAgICBnZXRJbWdSZWYgPSBfdXNlU3RhdHVzMlswXSxcbiAgICBzcmNBbmRPbmxvYWQgPSBfdXNlU3RhdHVzMlsxXSxcbiAgICBzdGF0dXMgPSBfdXNlU3RhdHVzMlsyXTtcbiAgdmFyIF91c2VTdGF0ZSA9IHVzZVN0YXRlKG51bGwpLFxuICAgIF91c2VTdGF0ZTIgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUsIDIpLFxuICAgIG1vdXNlUG9zaXRpb24gPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldE1vdXNlUG9zaXRpb24gPSBfdXNlU3RhdGUyWzFdO1xuICB2YXIgZ3JvdXBDb250ZXh0ID0gdXNlQ29udGV4dChQcmV2aWV3R3JvdXBDb250ZXh0KTtcbiAgdmFyIGNhblByZXZpZXcgPSAhIXByZXZpZXc7XG4gIHZhciBvblByZXZpZXdDbG9zZSA9IGZ1bmN0aW9uIG9uUHJldmlld0Nsb3NlKCkge1xuICAgIHNldFNob3dQcmV2aWV3KGZhbHNlKTtcbiAgICBzZXRNb3VzZVBvc2l0aW9uKG51bGwpO1xuICB9O1xuICB2YXIgd3JhcHBlckNsYXNzID0gY24ocHJlZml4Q2xzLCB3cmFwcGVyQ2xhc3NOYW1lLCByb290Q2xhc3NOYW1lLCBfZGVmaW5lUHJvcGVydHkoe30sIFwiXCIuY29uY2F0KHByZWZpeENscywgXCItZXJyb3JcIiksIHN0YXR1cyA9PT0gJ2Vycm9yJykpO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gSW1hZ2VQcm9wcyA9PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciBpbWdDb21tb25Qcm9wcyA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHZhciBvYmogPSB7fTtcbiAgICBDT01NT05fUFJPUFMuZm9yRWFjaChmdW5jdGlvbiAocHJvcCkge1xuICAgICAgaWYgKHByb3BzW3Byb3BdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgb2JqW3Byb3BdID0gcHJvcHNbcHJvcF07XG4gICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIG9iajtcbiAgfSwgQ09NTU9OX1BST1BTLm1hcChmdW5jdGlvbiAocHJvcCkge1xuICAgIHJldHVybiBwcm9wc1twcm9wXTtcbiAgfSkpO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09IFJlZ2lzdGVyID09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHZhciByZWdpc3RlckRhdGEgPSB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBpbWdDb21tb25Qcm9wcyksIHt9LCB7XG4gICAgICBzcmM6IHNyY1xuICAgIH0pO1xuICB9LCBbc3JjLCBpbWdDb21tb25Qcm9wc10pO1xuICB2YXIgaW1hZ2VJZCA9IHVzZVJlZ2lzdGVySW1hZ2UoY2FuUHJldmlldywgcmVnaXN0ZXJEYXRhKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PSBQcmV2aWV3ID09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICB2YXIgb25QcmV2aWV3ID0gZnVuY3Rpb24gb25QcmV2aWV3KGUpIHtcbiAgICB2YXIgX2dldE9mZnNldCA9IGdldE9mZnNldChlLnRhcmdldCksXG4gICAgICBsZWZ0ID0gX2dldE9mZnNldC5sZWZ0LFxuICAgICAgdG9wID0gX2dldE9mZnNldC50b3A7XG4gICAgaWYgKGdyb3VwQ29udGV4dCkge1xuICAgICAgZ3JvdXBDb250ZXh0Lm9uUHJldmlldyhpbWFnZUlkLCBsZWZ0LCB0b3ApO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRNb3VzZVBvc2l0aW9uKHtcbiAgICAgICAgeDogbGVmdCxcbiAgICAgICAgeTogdG9wXG4gICAgICB9KTtcbiAgICAgIHNldFNob3dQcmV2aWV3KHRydWUpO1xuICAgIH1cbiAgICBvbkNsaWNrID09PSBudWxsIHx8IG9uQ2xpY2sgPT09IHZvaWQgMCB8fCBvbkNsaWNrKGUpO1xuICB9O1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PSBSZW5kZXIgPT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX2V4dGVuZHMoe30sIG90aGVyUHJvcHMsIHtcbiAgICBjbGFzc05hbWU6IHdyYXBwZXJDbGFzcyxcbiAgICBvbkNsaWNrOiBjYW5QcmV2aWV3ID8gb25QcmV2aWV3IDogb25DbGljayxcbiAgICBzdHlsZTogX29iamVjdFNwcmVhZCh7XG4gICAgICB3aWR0aDogd2lkdGgsXG4gICAgICBoZWlnaHQ6IGhlaWdodFxuICAgIH0sIHdyYXBwZXJTdHlsZSlcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiaW1nXCIsIF9leHRlbmRzKHt9LCBpbWdDb21tb25Qcm9wcywge1xuICAgIGNsYXNzTmFtZTogY24oXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1pbWdcIiksIF9kZWZpbmVQcm9wZXJ0eSh7fSwgXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1pbWctcGxhY2Vob2xkZXJcIiksIHBsYWNlaG9sZGVyID09PSB0cnVlKSwgY2xhc3NOYW1lKSxcbiAgICBzdHlsZTogX29iamVjdFNwcmVhZCh7XG4gICAgICBoZWlnaHQ6IGhlaWdodFxuICAgIH0sIHN0eWxlKSxcbiAgICByZWY6IGdldEltZ1JlZlxuICB9LCBzcmNBbmRPbmxvYWQsIHtcbiAgICB3aWR0aDogd2lkdGgsXG4gICAgaGVpZ2h0OiBoZWlnaHQsXG4gICAgb25FcnJvcjogb25FcnJvclxuICB9KSksIHN0YXR1cyA9PT0gJ2xvYWRpbmcnICYmIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBcImFyaWEtaGlkZGVuXCI6IFwidHJ1ZVwiLFxuICAgIGNsYXNzTmFtZTogXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1wbGFjZWhvbGRlclwiKVxuICB9LCBwbGFjZWhvbGRlciksIHByZXZpZXdNYXNrICYmIGNhblByZXZpZXcgJiYgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwge1xuICAgIGNsYXNzTmFtZTogY24oXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1tYXNrXCIpLCBtYXNrQ2xhc3NOYW1lKSxcbiAgICBzdHlsZToge1xuICAgICAgZGlzcGxheTogKHN0eWxlID09PSBudWxsIHx8IHN0eWxlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzdHlsZS5kaXNwbGF5KSA9PT0gJ25vbmUnID8gJ25vbmUnIDogdW5kZWZpbmVkXG4gICAgfVxuICB9LCBwcmV2aWV3TWFzaykpLCAhZ3JvdXBDb250ZXh0ICYmIGNhblByZXZpZXcgJiYgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUHJldmlldywgX2V4dGVuZHMoe1xuICAgIFwiYXJpYS1oaWRkZW5cIjogIWlzU2hvd1ByZXZpZXcsXG4gICAgdmlzaWJsZTogaXNTaG93UHJldmlldyxcbiAgICBwcmVmaXhDbHM6IHByZXZpZXdQcmVmaXhDbHMsXG4gICAgb25DbG9zZTogb25QcmV2aWV3Q2xvc2UsXG4gICAgbW91c2VQb3NpdGlvbjogbW91c2VQb3NpdGlvbixcbiAgICBzcmM6IHNyYyxcbiAgICBhbHQ6IGFsdCxcbiAgICBmYWxsYmFjazogZmFsbGJhY2ssXG4gICAgZ2V0Q29udGFpbmVyOiBnZXRQcmV2aWV3Q29udGFpbmVyLFxuICAgIGljb25zOiBpY29ucyxcbiAgICBtb3ZhYmxlOiBtb3ZhYmxlLFxuICAgIHNjYWxlU3RlcDogc2NhbGVTdGVwLFxuICAgIG1pblNjYWxlOiBtaW5TY2FsZSxcbiAgICBtYXhTY2FsZTogbWF4U2NhbGUsXG4gICAgcm9vdENsYXNzTmFtZTogcm9vdENsYXNzTmFtZSxcbiAgICBpbWFnZVJlbmRlcjogaW1hZ2VSZW5kZXIsXG4gICAgaW1nQ29tbW9uUHJvcHM6IGltZ0NvbW1vblByb3BzLFxuICAgIHRvb2xiYXJSZW5kZXI6IHRvb2xiYXJSZW5kZXJcbiAgfSwgZGlhbG9nUHJvcHMpKSk7XG59O1xuSW1hZ2VJbnRlcm5hbC5QcmV2aWV3R3JvdXAgPSBQcmV2aWV3R3JvdXA7XG5JbWFnZUludGVybmFsLmRpc3BsYXlOYW1lID0gJ0ltYWdlJztcbmV4cG9ydCBkZWZhdWx0IEltYWdlSW50ZXJuYWw7IiwiaW1wb3J0IEltYWdlIGZyb20gXCIuL0ltYWdlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9JbWFnZVwiO1xuZXhwb3J0IGRlZmF1bHQgSW1hZ2U7IiwiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgUm90YXRlTGVmdE91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcImRlZnNcIiwgXCJhdHRyc1wiOiB7fSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInN0eWxlXCIsIFwiYXR0cnNcIjoge30gfV0gfSwgeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk02NzIgNDE4SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NDE0YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDUyOGMxNy43IDAgMzItMTQuMyAzMi0zMlY0NTBjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00NCA0MDJIMTg4VjQ5NGg0NDB2MzI2elwiIH0gfSwgeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk04MTkuMyAzMjguNWMtNzguOC0xMDAuNy0xOTYtMTUzLjYtMzE0LjYtMTU0LjJsLS4yLTY0YzAtNi41LTcuNi0xMC4xLTEyLjYtNi4xbC0xMjggMTAxYy00IDMuMS0zLjkgOS4xIDAgMTIuM0w0OTIgMzE4LjZjNS4xIDQgMTIuNy40IDEyLjYtNi4xdi02My45YzEyLjkuMSAyNS45LjkgMzguOCAyLjUgNDIuMSA1LjIgODIuMSAxOC4yIDExOSAzOC43IDM4LjEgMjEuMiA3MS4yIDQ5LjcgOTguNCA4NC4zIDI3LjEgMzQuNyA0Ni43IDczLjcgNTguMSAxMTUuOGEzMjUuOTUgMzI1Ljk1IDAgMDE2LjUgMTQwLjloNzQuOWMxNC44LTEwMy42LTExLjMtMjEzLTgxLTMwMi4zelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwicm90YXRlLWxlZnRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IFJvdGF0ZUxlZnRPdXRsaW5lZDtcbiIsImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJvdGF0ZUxlZnRPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9Sb3RhdGVMZWZ0T3V0bGluZWRcIjtcbmltcG9ydCBBbnRkSWNvbiBmcm9tIFwiLi4vY29tcG9uZW50cy9BbnRkSWNvblwiO1xudmFyIFJvdGF0ZUxlZnRPdXRsaW5lZCA9IGZ1bmN0aW9uIFJvdGF0ZUxlZnRPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX2V4dGVuZHMoe30sIHByb3BzLCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogUm90YXRlTGVmdE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBSb3RhdGVMZWZ0T3V0bGluZWQuZGlzcGxheU5hbWUgPSAnUm90YXRlTGVmdE91dGxpbmVkJztcbn1cbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKFJvdGF0ZUxlZnRPdXRsaW5lZCk7IiwiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgUm90YXRlUmlnaHRPdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJkZWZzXCIsIFwiYXR0cnNcIjoge30sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJzdHlsZVwiLCBcImF0dHJzXCI6IHt9IH1dIH0sIHsgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNNDgwLjUgMjUxLjJjMTMtMS42IDI1LjktMi40IDM4LjgtMi41djYzLjljMCA2LjUgNy41IDEwLjEgMTIuNiA2LjFMNjYwIDIxNy42YzQtMy4yIDQtOS4yIDAtMTIuM2wtMTI4LTEwMWMtNS4xLTQtMTIuNi0uNC0xMi42IDYuMWwtLjIgNjRjLTExOC42LjUtMjM1LjggNTMuNC0zMTQuNiAxNTQuMkEzOTkuNzUgMzk5Ljc1IDAgMDAxMjMuNSA2MzFoNzQuOWMtLjktNS4zLTEuNy0xMC43LTIuNC0xNi4xLTUuMS00Mi4xLTIuMS04NC4xIDguOS0xMjQuOCAxMS40LTQyLjIgMzEtODEuMSA1OC4xLTExNS44IDI3LjItMzQuNyA2MC4zLTYzLjIgOTguNC04NC4zIDM3LTIwLjYgNzYuOS0zMy42IDExOS4xLTM4Ljh6XCIgfSB9LCB7IFwidGFnXCI6IFwicGF0aFwiLCBcImF0dHJzXCI6IHsgXCJkXCI6IFwiTTg4MCA0MThIMzUyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY0MTRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTI4YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjQ1MGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQ0IDQwMkgzOTZWNDk0aDQ0MHYzMjZ6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJyb3RhdGUtcmlnaHRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IFJvdGF0ZVJpZ2h0T3V0bGluZWQ7XG4iLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSb3RhdGVSaWdodE91dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1JvdGF0ZVJpZ2h0T3V0bGluZWRcIjtcbmltcG9ydCBBbnRkSWNvbiBmcm9tIFwiLi4vY29tcG9uZW50cy9BbnRkSWNvblwiO1xudmFyIFJvdGF0ZVJpZ2h0T3V0bGluZWQgPSBmdW5jdGlvbiBSb3RhdGVSaWdodE91dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBSb3RhdGVSaWdodE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBSb3RhdGVSaWdodE91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ1JvdGF0ZVJpZ2h0T3V0bGluZWQnO1xufVxuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoUm90YXRlUmlnaHRPdXRsaW5lZCk7IiwiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgU3dhcE91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk04NDcuOSA1OTJIMTUyYy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDYwNS4yTDYxMi45IDg1MWMtNC4xIDUuMi0uNCAxMyA2LjMgMTNoNzIuNWM0LjkgMCA5LjUtMi4yIDEyLjYtNi4xbDE2OC44LTIxNC4xYzE2LjUtMjEgMS42LTUxLjgtMjUuMi01MS44ek04NzIgMzU2SDI2Ni44bDE0NC4zLTE4M2M0LjEtNS4yLjQtMTMtNi4zLTEzaC03Mi41Yy00LjkgMC05LjUgMi4yLTEyLjYgNi4xTDE1MC45IDM4MC4yYy0xNi41IDIxLTEuNiA1MS44IDI1LjEgNTEuOGg2OTZjNC40IDAgOC0zLjYgOC04di02MGMwLTQuNC0zLjYtOC04LTh6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJzd2FwXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBTd2FwT3V0bGluZWQ7XG4iLCJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBTd2FwT3V0bGluZWRTdmcgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vU3dhcE91dGxpbmVkXCI7XG5pbXBvcnQgQW50ZEljb24gZnJvbSBcIi4uL2NvbXBvbmVudHMvQW50ZEljb25cIjtcbnZhciBTd2FwT3V0bGluZWQgPSBmdW5jdGlvbiBTd2FwT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9leHRlbmRzKHt9LCBwcm9wcywge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IFN3YXBPdXRsaW5lZFN2Z1xuICB9KSk7XG59O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgU3dhcE91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ1N3YXBPdXRsaW5lZCc7XG59XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihTd2FwT3V0bGluZWQpOyIsIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFpvb21Jbk91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk02MzcgNDQzSDUxOVYzMDljMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4djEzNEgzMjVjLTQuNCAwLTggMy42LTggOHY2MGMwIDQuNCAzLjYgOCA4IDhoMTE4djEzNGMwIDQuNCAzLjYgOCA4IDhoNjBjNC40IDAgOC0zLjYgOC04VjUxOWgxMThjNC40IDAgOC0zLjYgOC04di02MGMwLTQuNC0zLjYtOC04LTh6bTI4NCA0MjRMNzc1IDcyMWMxMjIuMS0xNDguOSAxMTMuNi0zNjkuNS0yNi01MDktMTQ4LTE0OC4xLTM4OC40LTE0OC4xLTUzNyAwLTE0OC4xIDE0OC42LTE0OC4xIDM4OSAwIDUzNyAxMzkuNSAxMzkuNiAzNjAuMSAxNDguMSA1MDkgMjZsMTQ2IDE0NmMzLjIgMi44IDguMyAyLjggMTEgMGw0My00M2MyLjgtMi43IDIuOC03LjggMC0xMXpNNjk2IDY5NmMtMTE4LjggMTE4LjctMzExLjIgMTE4LjctNDMwIDAtMTE4LjctMTE4LjgtMTE4LjctMzExLjIgMC00MzAgMTE4LjgtMTE4LjcgMzExLjItMTE4LjcgNDMwIDAgMTE4LjcgMTE4LjggMTE4LjcgMzExLjIgMCA0MzB6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJ6b29tLWluXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBab29tSW5PdXRsaW5lZDtcbiIsImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFpvb21Jbk91dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1pvb21Jbk91dGxpbmVkXCI7XG5pbXBvcnQgQW50ZEljb24gZnJvbSBcIi4uL2NvbXBvbmVudHMvQW50ZEljb25cIjtcbnZhciBab29tSW5PdXRsaW5lZCA9IGZ1bmN0aW9uIFpvb21Jbk91dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBab29tSW5PdXRsaW5lZFN2Z1xuICB9KSk7XG59O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgWm9vbUluT3V0bGluZWQuZGlzcGxheU5hbWUgPSAnWm9vbUluT3V0bGluZWQnO1xufVxuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoWm9vbUluT3V0bGluZWQpOyIsIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFpvb21PdXRPdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNNjM3IDQ0M0gzMjVjLTQuNCAwLTggMy42LTggOHY2MGMwIDQuNCAzLjYgOCA4IDhoMzEyYzQuNCAwIDgtMy42IDgtOHYtNjBjMC00LjQtMy42LTgtOC04em0yODQgNDI0TDc3NSA3MjFjMTIyLjEtMTQ4LjkgMTEzLjYtMzY5LjUtMjYtNTA5LTE0OC0xNDguMS0zODguNC0xNDguMS01MzcgMC0xNDguMSAxNDguNi0xNDguMSAzODkgMCA1MzcgMTM5LjUgMTM5LjYgMzYwLjEgMTQ4LjEgNTA5IDI2bDE0NiAxNDZjMy4yIDIuOCA4LjMgMi44IDExIDBsNDMtNDNjMi44LTIuNyAyLjgtNy44IDAtMTF6TTY5NiA2OTZjLTExOC44IDExOC43LTMxMS4yIDExOC43LTQzMCAwLTExOC43LTExOC44LTExOC43LTMxMS4yIDAtNDMwIDExOC44LTExOC43IDMxMS4yLTExOC43IDQzMCAwIDExOC43IDExOC44IDExOC43IDMxMS4yIDAgNDMwelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiem9vbS1vdXRcIiwgXCJ0aGVtZVwiOiBcIm91dGxpbmVkXCIgfTtcbmV4cG9ydCBkZWZhdWx0IFpvb21PdXRPdXRsaW5lZDtcbiIsImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vZXh0ZW5kc1wiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFpvb21PdXRPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9ab29tT3V0T3V0bGluZWRcIjtcbmltcG9ydCBBbnRkSWNvbiBmcm9tIFwiLi4vY29tcG9uZW50cy9BbnRkSWNvblwiO1xudmFyIFpvb21PdXRPdXRsaW5lZCA9IGZ1bmN0aW9uIFpvb21PdXRPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX2V4dGVuZHMoe30sIHByb3BzLCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogWm9vbU91dE91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBab29tT3V0T3V0bGluZWQuZGlzcGxheU5hbWUgPSAnWm9vbU91dE91dGxpbmVkJztcbn1cbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKFpvb21PdXRPdXRsaW5lZCk7IiwiaW1wb3J0IHsgdW5pdCB9IGZyb20gJ0BhbnQtZGVzaWduL2Nzc2luanMnO1xuaW1wb3J0IHsgVGlueUNvbG9yIH0gZnJvbSAnQGN0cmwvdGlueWNvbG9yJztcbmltcG9ydCB7IGdlbk1vZGFsTWFza1N0eWxlIH0gZnJvbSAnLi4vLi4vbW9kYWwvc3R5bGUnO1xuaW1wb3J0IHsgdGV4dEVsbGlwc2lzIH0gZnJvbSAnLi4vLi4vc3R5bGUnO1xuaW1wb3J0IHsgaW5pdEZhZGVNb3Rpb24sIGluaXRab29tTW90aW9uIH0gZnJvbSAnLi4vLi4vc3R5bGUvbW90aW9uJztcbmltcG9ydCB7IGdlblN0eWxlSG9va3MsIG1lcmdlVG9rZW4gfSBmcm9tICcuLi8uLi90aGVtZS9pbnRlcm5hbCc7XG5leHBvcnQgY29uc3QgZ2VuQm94U3R5bGUgPSBwb3NpdGlvbiA9PiAoe1xuICBwb3NpdGlvbjogcG9zaXRpb24gfHwgJ2Fic29sdXRlJyxcbiAgaW5zZXQ6IDBcbn0pO1xuZXhwb3J0IGNvbnN0IGdlbkltYWdlTWFza1N0eWxlID0gdG9rZW4gPT4ge1xuICBjb25zdCB7XG4gICAgaWNvbkNscyxcbiAgICBtb3Rpb25EdXJhdGlvblNsb3csXG4gICAgcGFkZGluZ1hYUyxcbiAgICBtYXJnaW5YWFMsXG4gICAgcHJlZml4Q2xzLFxuICAgIGNvbG9yVGV4dExpZ2h0U29saWRcbiAgfSA9IHRva2VuO1xuICByZXR1cm4ge1xuICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgIGluc2V0OiAwLFxuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgY29sb3I6IGNvbG9yVGV4dExpZ2h0U29saWQsXG4gICAgYmFja2dyb3VuZDogbmV3IFRpbnlDb2xvcignIzAwMCcpLnNldEFscGhhKDAuNSkudG9SZ2JTdHJpbmcoKSxcbiAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICBvcGFjaXR5OiAwLFxuICAgIHRyYW5zaXRpb246IGBvcGFjaXR5ICR7bW90aW9uRHVyYXRpb25TbG93fWAsXG4gICAgW2AuJHtwcmVmaXhDbHN9LW1hc2staW5mb2BdOiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRleHRFbGxpcHNpcyksIHtcbiAgICAgIHBhZGRpbmc6IGAwICR7dW5pdChwYWRkaW5nWFhTKX1gLFxuICAgICAgW2ljb25DbHNdOiB7XG4gICAgICAgIG1hcmdpbklubGluZUVuZDogbWFyZ2luWFhTLFxuICAgICAgICBzdmc6IHtcbiAgICAgICAgICB2ZXJ0aWNhbEFsaWduOiAnYmFzZWxpbmUnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KVxuICB9O1xufTtcbmV4cG9ydCBjb25zdCBnZW5QcmV2aWV3T3BlcmF0aW9uc1N0eWxlID0gdG9rZW4gPT4ge1xuICBjb25zdCB7XG4gICAgcHJldmlld0NscyxcbiAgICBtb2RhbE1hc2tCZyxcbiAgICBwYWRkaW5nU00sXG4gICAgbWFyZ2luWEwsXG4gICAgbWFyZ2luLFxuICAgIHBhZGRpbmdMRyxcbiAgICBwcmV2aWV3T3BlcmF0aW9uQ29sb3JEaXNhYmxlZCxcbiAgICBwcmV2aWV3T3BlcmF0aW9uSG92ZXJDb2xvcixcbiAgICBtb3Rpb25EdXJhdGlvblNsb3csXG4gICAgaWNvbkNscyxcbiAgICBjb2xvclRleHRMaWdodFNvbGlkXG4gIH0gPSB0b2tlbjtcbiAgY29uc3Qgb3BlcmF0aW9uQmcgPSBuZXcgVGlueUNvbG9yKG1vZGFsTWFza0JnKS5zZXRBbHBoYSgwLjEpO1xuICBjb25zdCBvcGVyYXRpb25CZ0hvdmVyID0gb3BlcmF0aW9uQmcuY2xvbmUoKS5zZXRBbHBoYSgwLjIpO1xuICByZXR1cm4ge1xuICAgIFtgJHtwcmV2aWV3Q2xzfS1mb290ZXJgXToge1xuICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICBib3R0b206IG1hcmdpblhMLFxuICAgICAgbGVmdDoge1xuICAgICAgICBfc2tpcF9jaGVja186IHRydWUsXG4gICAgICAgIHZhbHVlOiAwXG4gICAgICB9LFxuICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICBjb2xvcjogdG9rZW4ucHJldmlld09wZXJhdGlvbkNvbG9yXG4gICAgfSxcbiAgICBbYCR7cHJldmlld0Nsc30tcHJvZ3Jlc3NgXToge1xuICAgICAgbWFyZ2luQm90dG9tOiBtYXJnaW5cbiAgICB9LFxuICAgIFtgJHtwcmV2aWV3Q2xzfS1jbG9zZWBdOiB7XG4gICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgIHRvcDogbWFyZ2luWEwsXG4gICAgICByaWdodDoge1xuICAgICAgICBfc2tpcF9jaGVja186IHRydWUsXG4gICAgICAgIHZhbHVlOiBtYXJnaW5YTFxuICAgICAgfSxcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGNvbG9yOiBjb2xvclRleHRMaWdodFNvbGlkLFxuICAgICAgYmFja2dyb3VuZENvbG9yOiBvcGVyYXRpb25CZy50b1JnYlN0cmluZygpLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgIHBhZGRpbmc6IHBhZGRpbmdTTSxcbiAgICAgIG91dGxpbmU6IDAsXG4gICAgICBib3JkZXI6IDAsXG4gICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgIHRyYW5zaXRpb246IGBhbGwgJHttb3Rpb25EdXJhdGlvblNsb3d9YCxcbiAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IG9wZXJhdGlvbkJnSG92ZXIudG9SZ2JTdHJpbmcoKVxuICAgICAgfSxcbiAgICAgIFtgJiA+ICR7aWNvbkNsc31gXToge1xuICAgICAgICBmb250U2l6ZTogdG9rZW4ucHJldmlld09wZXJhdGlvblNpemVcbiAgICAgIH1cbiAgICB9LFxuICAgIFtgJHtwcmV2aWV3Q2xzfS1vcGVyYXRpb25zYF06IHtcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgcGFkZGluZzogYDAgJHt1bml0KHBhZGRpbmdMRyl9YCxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogb3BlcmF0aW9uQmcudG9SZ2JTdHJpbmcoKSxcbiAgICAgIGJvcmRlclJhZGl1czogMTAwLFxuICAgICAgJyYtb3BlcmF0aW9uJzoge1xuICAgICAgICBtYXJnaW5JbmxpbmVTdGFydDogcGFkZGluZ1NNLFxuICAgICAgICBwYWRkaW5nOiBwYWRkaW5nU00sXG4gICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICB0cmFuc2l0aW9uOiBgYWxsICR7bW90aW9uRHVyYXRpb25TbG93fWAsXG4gICAgICAgIHVzZXJTZWxlY3Q6ICdub25lJyxcbiAgICAgICAgW2AmOm5vdCgke3ByZXZpZXdDbHN9LW9wZXJhdGlvbnMtb3BlcmF0aW9uLWRpc2FibGVkKTpob3ZlciA+ICR7aWNvbkNsc31gXToge1xuICAgICAgICAgIGNvbG9yOiBwcmV2aWV3T3BlcmF0aW9uSG92ZXJDb2xvclxuICAgICAgICB9LFxuICAgICAgICAnJi1kaXNhYmxlZCc6IHtcbiAgICAgICAgICBjb2xvcjogcHJldmlld09wZXJhdGlvbkNvbG9yRGlzYWJsZWQsXG4gICAgICAgICAgY3Vyc29yOiAnbm90LWFsbG93ZWQnXG4gICAgICAgIH0sXG4gICAgICAgICcmOmZpcnN0LW9mLXR5cGUnOiB7XG4gICAgICAgICAgbWFyZ2luSW5saW5lU3RhcnQ6IDBcbiAgICAgICAgfSxcbiAgICAgICAgW2AmID4gJHtpY29uQ2xzfWBdOiB7XG4gICAgICAgICAgZm9udFNpemU6IHRva2VuLnByZXZpZXdPcGVyYXRpb25TaXplXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH07XG59O1xuZXhwb3J0IGNvbnN0IGdlblByZXZpZXdTd2l0Y2hTdHlsZSA9IHRva2VuID0+IHtcbiAgY29uc3Qge1xuICAgIG1vZGFsTWFza0JnLFxuICAgIGljb25DbHMsXG4gICAgcHJldmlld09wZXJhdGlvbkNvbG9yRGlzYWJsZWQsXG4gICAgcHJldmlld0NscyxcbiAgICB6SW5kZXhQb3B1cCxcbiAgICBtb3Rpb25EdXJhdGlvblNsb3dcbiAgfSA9IHRva2VuO1xuICBjb25zdCBvcGVyYXRpb25CZyA9IG5ldyBUaW55Q29sb3IobW9kYWxNYXNrQmcpLnNldEFscGhhKDAuMSk7XG4gIGNvbnN0IG9wZXJhdGlvbkJnSG92ZXIgPSBvcGVyYXRpb25CZy5jbG9uZSgpLnNldEFscGhhKDAuMik7XG4gIHJldHVybiB7XG4gICAgW2Ake3ByZXZpZXdDbHN9LXN3aXRjaC1sZWZ0LCAke3ByZXZpZXdDbHN9LXN3aXRjaC1yaWdodGBdOiB7XG4gICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgIGluc2V0QmxvY2tTdGFydDogJzUwJScsXG4gICAgICB6SW5kZXg6IHRva2VuLmNhbGMoekluZGV4UG9wdXApLmFkZCgxKS5lcXVhbCh7XG4gICAgICAgIHVuaXQ6IGZhbHNlXG4gICAgICB9KSxcbiAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgd2lkdGg6IHRva2VuLmltYWdlUHJldmlld1N3aXRjaFNpemUsXG4gICAgICBoZWlnaHQ6IHRva2VuLmltYWdlUHJldmlld1N3aXRjaFNpemUsXG4gICAgICBtYXJnaW5Ub3A6IHRva2VuLmNhbGModG9rZW4uaW1hZ2VQcmV2aWV3U3dpdGNoU2l6ZSkubXVsKC0xKS5kaXYoMikuZXF1YWwoKSxcbiAgICAgIGNvbG9yOiB0b2tlbi5wcmV2aWV3T3BlcmF0aW9uQ29sb3IsXG4gICAgICBiYWNrZ3JvdW5kOiBvcGVyYXRpb25CZy50b1JnYlN0cmluZygpLFxuICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgIHRyYW5zZm9ybTogYHRyYW5zbGF0ZVkoLTUwJSlgLFxuICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICB0cmFuc2l0aW9uOiBgYWxsICR7bW90aW9uRHVyYXRpb25TbG93fWAsXG4gICAgICB1c2VyU2VsZWN0OiAnbm9uZScsXG4gICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgYmFja2dyb3VuZDogb3BlcmF0aW9uQmdIb3Zlci50b1JnYlN0cmluZygpXG4gICAgICB9LFxuICAgICAgW2AmLWRpc2FibGVkYF06IHtcbiAgICAgICAgJyYsICY6aG92ZXInOiB7XG4gICAgICAgICAgY29sb3I6IHByZXZpZXdPcGVyYXRpb25Db2xvckRpc2FibGVkLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgY3Vyc29yOiAnbm90LWFsbG93ZWQnLFxuICAgICAgICAgIFtgPiAke2ljb25DbHN9YF06IHtcbiAgICAgICAgICAgIGN1cnNvcjogJ25vdC1hbGxvd2VkJ1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIFtgPiAke2ljb25DbHN9YF06IHtcbiAgICAgICAgZm9udFNpemU6IHRva2VuLnByZXZpZXdPcGVyYXRpb25TaXplXG4gICAgICB9XG4gICAgfSxcbiAgICBbYCR7cHJldmlld0Nsc30tc3dpdGNoLWxlZnRgXToge1xuICAgICAgaW5zZXRJbmxpbmVTdGFydDogdG9rZW4ubWFyZ2luU01cbiAgICB9LFxuICAgIFtgJHtwcmV2aWV3Q2xzfS1zd2l0Y2gtcmlnaHRgXToge1xuICAgICAgaW5zZXRJbmxpbmVFbmQ6IHRva2VuLm1hcmdpblNNXG4gICAgfVxuICB9O1xufTtcbmV4cG9ydCBjb25zdCBnZW5JbWFnZVByZXZpZXdTdHlsZSA9IHRva2VuID0+IHtcbiAgY29uc3Qge1xuICAgIG1vdGlvbkVhc2VPdXQsXG4gICAgcHJldmlld0NscyxcbiAgICBtb3Rpb25EdXJhdGlvblNsb3csXG4gICAgY29tcG9uZW50Q2xzXG4gIH0gPSB0b2tlbjtcbiAgcmV0dXJuIFt7XG4gICAgW2Ake2NvbXBvbmVudENsc30tcHJldmlldy1yb290YF06IHtcbiAgICAgIFtwcmV2aWV3Q2xzXToge1xuICAgICAgICBoZWlnaHQ6ICcxMDAlJyxcbiAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgICAgICAgcG9pbnRlckV2ZW50czogJ25vbmUnXG4gICAgICB9LFxuICAgICAgW2Ake3ByZXZpZXdDbHN9LWJvZHlgXTogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBnZW5Cb3hTdHlsZSgpKSwge1xuICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbidcbiAgICAgIH0pLFxuICAgICAgW2Ake3ByZXZpZXdDbHN9LWltZ2BdOiB7XG4gICAgICAgIG1heFdpZHRoOiAnMTAwJScsXG4gICAgICAgIG1heEhlaWdodDogJzcwJScsXG4gICAgICAgIHZlcnRpY2FsQWxpZ246ICdtaWRkbGUnLFxuICAgICAgICB0cmFuc2Zvcm06ICdzY2FsZTNkKDEsIDEsIDEpJyxcbiAgICAgICAgY3Vyc29yOiAnZ3JhYicsXG4gICAgICAgIHRyYW5zaXRpb246IGB0cmFuc2Zvcm0gJHttb3Rpb25EdXJhdGlvblNsb3d9ICR7bW90aW9uRWFzZU91dH0gMHNgLFxuICAgICAgICB1c2VyU2VsZWN0OiAnbm9uZScsXG4gICAgICAgICcmLXdyYXBwZXInOiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGdlbkJveFN0eWxlKCkpLCB7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYHRyYW5zZm9ybSAke21vdGlvbkR1cmF0aW9uU2xvd30gJHttb3Rpb25FYXNlT3V0fSAwc2AsXG4gICAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2FudC1kZXNpZ24vYW50LWRlc2lnbi9pc3N1ZXMvMzk5MTNcbiAgICAgICAgICAvLyBUYWlsd2luZENTUyB3aWxsIHJlc2V0IGltZyBkZWZhdWx0IHN0eWxlLlxuICAgICAgICAgIC8vIExldCdzIHNldCBiYWNrLlxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgJyYgPiAqJzoge1xuICAgICAgICAgICAgcG9pbnRlckV2ZW50czogJ2F1dG8nXG4gICAgICAgICAgfSxcbiAgICAgICAgICAnJjo6YmVmb3JlJzoge1xuICAgICAgICAgICAgZGlzcGxheTogJ2lubGluZS1ibG9jaycsXG4gICAgICAgICAgICB3aWR0aDogMSxcbiAgICAgICAgICAgIGhlaWdodDogJzUwJScsXG4gICAgICAgICAgICBtYXJnaW5JbmxpbmVFbmQ6IC0xLFxuICAgICAgICAgICAgY29udGVudDogJ1wiXCInXG4gICAgICAgICAgfVxuICAgICAgICB9KVxuICAgICAgfSxcbiAgICAgIFtgJHtwcmV2aWV3Q2xzfS1tb3ZpbmdgXToge1xuICAgICAgICBbYCR7cHJldmlld0Nsc30tcHJldmlldy1pbWdgXToge1xuICAgICAgICAgIGN1cnNvcjogJ2dyYWJiaW5nJyxcbiAgICAgICAgICAnJi13cmFwcGVyJzoge1xuICAgICAgICAgICAgdHJhbnNpdGlvbkR1cmF0aW9uOiAnMHMnXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9LFxuICAvLyBPdmVycmlkZVxuICB7XG4gICAgW2Ake2NvbXBvbmVudENsc30tcHJldmlldy1yb290YF06IHtcbiAgICAgIFtgJHtwcmV2aWV3Q2xzfS13cmFwYF06IHtcbiAgICAgICAgekluZGV4OiB0b2tlbi56SW5kZXhQb3B1cFxuICAgICAgfVxuICAgIH1cbiAgfSxcbiAgLy8gUHJldmlldyBvcGVyYXRpb25zICYgc3dpdGNoXG4gIHtcbiAgICBbYCR7Y29tcG9uZW50Q2xzfS1wcmV2aWV3LW9wZXJhdGlvbnMtd3JhcHBlcmBdOiB7XG4gICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgIHpJbmRleDogdG9rZW4uY2FsYyh0b2tlbi56SW5kZXhQb3B1cCkuYWRkKDEpLmVxdWFsKHtcbiAgICAgICAgdW5pdDogZmFsc2VcbiAgICAgIH0pXG4gICAgfSxcbiAgICAnJic6IFtnZW5QcmV2aWV3T3BlcmF0aW9uc1N0eWxlKHRva2VuKSwgZ2VuUHJldmlld1N3aXRjaFN0eWxlKHRva2VuKV1cbiAgfV07XG59O1xuY29uc3QgZ2VuSW1hZ2VTdHlsZSA9IHRva2VuID0+IHtcbiAgY29uc3Qge1xuICAgIGNvbXBvbmVudENsc1xuICB9ID0gdG9rZW47XG4gIHJldHVybiB7XG4gICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09IGltYWdlID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgIFtjb21wb25lbnRDbHNdOiB7XG4gICAgICBwb3NpdGlvbjogJ3JlbGF0aXZlJyxcbiAgICAgIGRpc3BsYXk6ICdpbmxpbmUtYmxvY2snLFxuICAgICAgW2Ake2NvbXBvbmVudENsc30taW1nYF06IHtcbiAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgaGVpZ2h0OiAnYXV0bycsXG4gICAgICAgIHZlcnRpY2FsQWxpZ246ICdtaWRkbGUnXG4gICAgICB9LFxuICAgICAgW2Ake2NvbXBvbmVudENsc30taW1nLXBsYWNlaG9sZGVyYF06IHtcbiAgICAgICAgYmFja2dyb3VuZENvbG9yOiB0b2tlbi5jb2xvckJnQ29udGFpbmVyRGlzYWJsZWQsXG4gICAgICAgIGJhY2tncm91bmRJbWFnZTogXCJ1cmwoJ2RhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU1UWWlJR2hsYVdkb2REMGlNVFlpSUhacFpYZENiM2c5SWpBZ01DQXhOaUF4TmlJZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWo0OGNHRjBhQ0JrUFNKTk1UUXVOU0F5TGpWb0xURXpRUzQxTGpVZ01DQXdJREFnTVNBemRqRXdZUzQxTGpVZ01DQXdJREFnTGpVdU5XZ3hNMkV1TlM0MUlEQWdNQ0F3SUM0MUxTNDFWak5oTGpVdU5TQXdJREFnTUMwdU5TMHVOWHBOTlM0eU9ERWdOQzQzTldFeElERWdNQ0F3SURFZ01DQXlJREVnTVNBd0lEQWdNU0F3TFRKNmJUZ3VNRE1nTmk0NE0yRXVNVEkzTGpFeU55QXdJREFnTVMwdU1EZ3hMakF6U0RJdU56WTVZUzR4TWpVdU1USTFJREFnTUNBeExTNHdPVFl0TGpJd04yd3lMalkyTVMwekxqRTFObUV1TVRJMkxqRXlOaUF3SURBZ01TQXVNVGMzTFM0d01UWnNMakF4Tmk0d01UWk1OeTR3T0NBeE1DNHdPV3d5TGpRM0xUSXVPVE5oTGpFeU5pNHhNallnTUNBd0lERWdMakUzTnkwdU1ERTJiQzR3TVRVdU1ERTJJRE11TlRnNElEUXVNalEwWVM0eE1qY3VNVEkzSURBZ01DQXhMUzR3TWk0eE56VjZJaUJtYVd4c1BTSWpPRU00UXpoRElpQm1hV3hzTFhKMWJHVTlJbTV2Ym5wbGNtOGlMejQ4TDNOMlp6ND0nKVwiLFxuICAgICAgICBiYWNrZ3JvdW5kUmVwZWF0OiAnbm8tcmVwZWF0JyxcbiAgICAgICAgYmFja2dyb3VuZFBvc2l0aW9uOiAnY2VudGVyIGNlbnRlcicsXG4gICAgICAgIGJhY2tncm91bmRTaXplOiAnMzAlJ1xuICAgICAgfSxcbiAgICAgIFtgJHtjb21wb25lbnRDbHN9LW1hc2tgXTogT2JqZWN0LmFzc2lnbih7fSwgZ2VuSW1hZ2VNYXNrU3R5bGUodG9rZW4pKSxcbiAgICAgIFtgJHtjb21wb25lbnRDbHN9LW1hc2s6aG92ZXJgXToge1xuICAgICAgICBvcGFjaXR5OiAxXG4gICAgICB9LFxuICAgICAgW2Ake2NvbXBvbmVudENsc30tcGxhY2Vob2xkZXJgXTogT2JqZWN0LmFzc2lnbih7fSwgZ2VuQm94U3R5bGUoKSlcbiAgICB9XG4gIH07XG59O1xuY29uc3QgZ2VuUHJldmlld01vdGlvbiA9IHRva2VuID0+IHtcbiAgY29uc3Qge1xuICAgIHByZXZpZXdDbHNcbiAgfSA9IHRva2VuO1xuICByZXR1cm4ge1xuICAgIFtgJHtwcmV2aWV3Q2xzfS1yb290YF06IGluaXRab29tTW90aW9uKHRva2VuLCAnem9vbScpLFxuICAgIFtgJmBdOiBpbml0RmFkZU1vdGlvbih0b2tlbiwgdHJ1ZSlcbiAgfTtcbn07XG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gRXhwb3J0ID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuZXhwb3J0IGNvbnN0IHByZXBhcmVDb21wb25lbnRUb2tlbiA9IHRva2VuID0+ICh7XG4gIHpJbmRleFBvcHVwOiB0b2tlbi56SW5kZXhQb3B1cEJhc2UgKyA4MCxcbiAgcHJldmlld09wZXJhdGlvbkNvbG9yOiBuZXcgVGlueUNvbG9yKHRva2VuLmNvbG9yVGV4dExpZ2h0U29saWQpLnNldEFscGhhKDAuNjUpLnRvUmdiU3RyaW5nKCksXG4gIHByZXZpZXdPcGVyYXRpb25Ib3ZlckNvbG9yOiBuZXcgVGlueUNvbG9yKHRva2VuLmNvbG9yVGV4dExpZ2h0U29saWQpLnNldEFscGhhKDAuODUpLnRvUmdiU3RyaW5nKCksXG4gIHByZXZpZXdPcGVyYXRpb25Db2xvckRpc2FibGVkOiBuZXcgVGlueUNvbG9yKHRva2VuLmNvbG9yVGV4dExpZ2h0U29saWQpLnNldEFscGhhKDAuMjUpLnRvUmdiU3RyaW5nKCksXG4gIHByZXZpZXdPcGVyYXRpb25TaXplOiB0b2tlbi5mb250U2l6ZUljb24gKiAxLjUgLy8gRklYTUU6IGZvbnRTaXplSWNvbkxHXG59KTtcbmV4cG9ydCBkZWZhdWx0IGdlblN0eWxlSG9va3MoJ0ltYWdlJywgdG9rZW4gPT4ge1xuICBjb25zdCBwcmV2aWV3Q2xzID0gYCR7dG9rZW4uY29tcG9uZW50Q2xzfS1wcmV2aWV3YDtcbiAgY29uc3QgaW1hZ2VUb2tlbiA9IG1lcmdlVG9rZW4odG9rZW4sIHtcbiAgICBwcmV2aWV3Q2xzLFxuICAgIG1vZGFsTWFza0JnOiBuZXcgVGlueUNvbG9yKCcjMDAwJykuc2V0QWxwaGEoMC40NSkudG9SZ2JTdHJpbmcoKSxcbiAgICAvLyBGSVhNRTogU2hhcmVkIFRva2VuXG4gICAgaW1hZ2VQcmV2aWV3U3dpdGNoU2l6ZTogdG9rZW4uY29udHJvbEhlaWdodExHXG4gIH0pO1xuICByZXR1cm4gW2dlbkltYWdlU3R5bGUoaW1hZ2VUb2tlbiksIGdlbkltYWdlUHJldmlld1N0eWxlKGltYWdlVG9rZW4pLCBnZW5Nb2RhbE1hc2tTdHlsZShtZXJnZVRva2VuKGltYWdlVG9rZW4sIHtcbiAgICBjb21wb25lbnRDbHM6IHByZXZpZXdDbHNcbiAgfSkpLCBnZW5QcmV2aWV3TW90aW9uKGltYWdlVG9rZW4pXTtcbn0sIHByZXBhcmVDb21wb25lbnRUb2tlbik7IiwiXCJ1c2UgY2xpZW50XCI7XG5cbnZhciBfX3Jlc3QgPSB0aGlzICYmIHRoaXMuX19yZXN0IHx8IGZ1bmN0aW9uIChzLCBlKSB7XG4gIHZhciB0ID0ge307XG4gIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSAmJiBlLmluZGV4T2YocCkgPCAwKSB0W3BdID0gc1twXTtcbiAgaWYgKHMgIT0gbnVsbCAmJiB0eXBlb2YgT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyA9PT0gXCJmdW5jdGlvblwiKSBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XG4gICAgaWYgKGUuaW5kZXhPZihwW2ldKSA8IDAgJiYgT2JqZWN0LnByb3RvdHlwZS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKHMsIHBbaV0pKSB0W3BbaV1dID0gc1twW2ldXTtcbiAgfVxuICByZXR1cm4gdDtcbn07XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQ2xvc2VPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvQ2xvc2VPdXRsaW5lZFwiO1xuaW1wb3J0IExlZnRPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvTGVmdE91dGxpbmVkXCI7XG5pbXBvcnQgUmlnaHRPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUmlnaHRPdXRsaW5lZFwiO1xuaW1wb3J0IFJvdGF0ZUxlZnRPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUm90YXRlTGVmdE91dGxpbmVkXCI7XG5pbXBvcnQgUm90YXRlUmlnaHRPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvUm90YXRlUmlnaHRPdXRsaW5lZFwiO1xuaW1wb3J0IFN3YXBPdXRsaW5lZCBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvU3dhcE91dGxpbmVkXCI7XG5pbXBvcnQgWm9vbUluT3V0bGluZWQgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zL2VzL2ljb25zL1pvb21Jbk91dGxpbmVkXCI7XG5pbXBvcnQgWm9vbU91dE91dGxpbmVkIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy9lcy9pY29ucy9ab29tT3V0T3V0bGluZWRcIjtcbmltcG9ydCBjbGFzc05hbWVzIGZyb20gJ2NsYXNzbmFtZXMnO1xuaW1wb3J0IFJjSW1hZ2UgZnJvbSAncmMtaW1hZ2UnO1xuaW1wb3J0IHsgdXNlWkluZGV4IH0gZnJvbSAnLi4vX3V0aWwvaG9va3MvdXNlWkluZGV4JztcbmltcG9ydCB7IGdldFRyYW5zaXRpb25OYW1lIH0gZnJvbSAnLi4vX3V0aWwvbW90aW9uJztcbmltcG9ydCB7IENvbmZpZ0NvbnRleHQgfSBmcm9tICcuLi9jb25maWctcHJvdmlkZXInO1xuaW1wb3J0IHVzZUNTU1ZhckNscyBmcm9tICcuLi9jb25maWctcHJvdmlkZXIvaG9va3MvdXNlQ1NTVmFyQ2xzJztcbmltcG9ydCB1c2VTdHlsZSBmcm9tICcuL3N0eWxlJztcbmV4cG9ydCBjb25zdCBpY29ucyA9IHtcbiAgcm90YXRlTGVmdDogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUm90YXRlTGVmdE91dGxpbmVkLCBudWxsKSxcbiAgcm90YXRlUmlnaHQ6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJvdGF0ZVJpZ2h0T3V0bGluZWQsIG51bGwpLFxuICB6b29tSW46IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFpvb21Jbk91dGxpbmVkLCBudWxsKSxcbiAgem9vbU91dDogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoWm9vbU91dE91dGxpbmVkLCBudWxsKSxcbiAgY2xvc2U6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KENsb3NlT3V0bGluZWQsIG51bGwpLFxuICBsZWZ0OiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChMZWZ0T3V0bGluZWQsIG51bGwpLFxuICByaWdodDogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUmlnaHRPdXRsaW5lZCwgbnVsbCksXG4gIGZsaXBYOiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChTd2FwT3V0bGluZWQsIG51bGwpLFxuICBmbGlwWTogLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoU3dhcE91dGxpbmVkLCB7XG4gICAgcm90YXRlOiA5MFxuICB9KVxufTtcbmNvbnN0IEludGVybmFsUHJldmlld0dyb3VwID0gX2EgPT4ge1xuICB2YXIge1xuICAgICAgcHJldmlld1ByZWZpeENsczogY3VzdG9taXplUHJlZml4Q2xzLFxuICAgICAgcHJldmlld1xuICAgIH0gPSBfYSxcbiAgICBvdGhlclByb3BzID0gX19yZXN0KF9hLCBbXCJwcmV2aWV3UHJlZml4Q2xzXCIsIFwicHJldmlld1wiXSk7XG4gIGNvbnN0IHtcbiAgICBnZXRQcmVmaXhDbHNcbiAgfSA9IFJlYWN0LnVzZUNvbnRleHQoQ29uZmlnQ29udGV4dCk7XG4gIGNvbnN0IHByZWZpeENscyA9IGdldFByZWZpeENscygnaW1hZ2UnLCBjdXN0b21pemVQcmVmaXhDbHMpO1xuICBjb25zdCBwcmV2aWV3UHJlZml4Q2xzID0gYCR7cHJlZml4Q2xzfS1wcmV2aWV3YDtcbiAgY29uc3Qgcm9vdFByZWZpeENscyA9IGdldFByZWZpeENscygpO1xuICBjb25zdCByb290Q2xzID0gdXNlQ1NTVmFyQ2xzKHByZWZpeENscyk7XG4gIGNvbnN0IFt3cmFwQ1NTVmFyLCBoYXNoSWQsIGNzc1ZhckNsc10gPSB1c2VTdHlsZShwcmVmaXhDbHMsIHJvb3RDbHMpO1xuICBjb25zdCBbekluZGV4XSA9IHVzZVpJbmRleCgnSW1hZ2VQcmV2aWV3JywgdHlwZW9mIHByZXZpZXcgPT09ICdvYmplY3QnID8gcHJldmlldy56SW5kZXggOiB1bmRlZmluZWQpO1xuICBjb25zdCBtZXJnZWRQcmV2aWV3ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgdmFyIF9hO1xuICAgIGlmIChwcmV2aWV3ID09PSBmYWxzZSkge1xuICAgICAgcmV0dXJuIHByZXZpZXc7XG4gICAgfVxuICAgIGNvbnN0IF9wcmV2aWV3ID0gdHlwZW9mIHByZXZpZXcgPT09ICdvYmplY3QnID8gcHJldmlldyA6IHt9O1xuICAgIGNvbnN0IG1lcmdlZFJvb3RDbGFzc05hbWUgPSBjbGFzc05hbWVzKGhhc2hJZCwgY3NzVmFyQ2xzLCByb290Q2xzLCAoX2EgPSBfcHJldmlldy5yb290Q2xhc3NOYW1lKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAnJyk7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgX3ByZXZpZXcpLCB7XG4gICAgICB0cmFuc2l0aW9uTmFtZTogZ2V0VHJhbnNpdGlvbk5hbWUocm9vdFByZWZpeENscywgJ3pvb20nLCBfcHJldmlldy50cmFuc2l0aW9uTmFtZSksXG4gICAgICBtYXNrVHJhbnNpdGlvbk5hbWU6IGdldFRyYW5zaXRpb25OYW1lKHJvb3RQcmVmaXhDbHMsICdmYWRlJywgX3ByZXZpZXcubWFza1RyYW5zaXRpb25OYW1lKSxcbiAgICAgIHJvb3RDbGFzc05hbWU6IG1lcmdlZFJvb3RDbGFzc05hbWUsXG4gICAgICB6SW5kZXhcbiAgICB9KTtcbiAgfSwgW3ByZXZpZXddKTtcbiAgcmV0dXJuIHdyYXBDU1NWYXIoIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJjSW1hZ2UuUHJldmlld0dyb3VwLCBPYmplY3QuYXNzaWduKHtcbiAgICBwcmV2aWV3OiBtZXJnZWRQcmV2aWV3LFxuICAgIHByZXZpZXdQcmVmaXhDbHM6IHByZXZpZXdQcmVmaXhDbHMsXG4gICAgaWNvbnM6IGljb25zXG4gIH0sIG90aGVyUHJvcHMpKSk7XG59O1xuZXhwb3J0IGRlZmF1bHQgSW50ZXJuYWxQcmV2aWV3R3JvdXA7IiwiXCJ1c2UgY2xpZW50XCI7XG5cbnZhciBfX3Jlc3QgPSB0aGlzICYmIHRoaXMuX19yZXN0IHx8IGZ1bmN0aW9uIChzLCBlKSB7XG4gIHZhciB0ID0ge307XG4gIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSAmJiBlLmluZGV4T2YocCkgPCAwKSB0W3BdID0gc1twXTtcbiAgaWYgKHMgIT0gbnVsbCAmJiB0eXBlb2YgT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyA9PT0gXCJmdW5jdGlvblwiKSBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XG4gICAgaWYgKGUuaW5kZXhPZihwW2ldKSA8IDAgJiYgT2JqZWN0LnByb3RvdHlwZS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKHMsIHBbaV0pKSB0W3BbaV1dID0gc1twW2ldXTtcbiAgfVxuICByZXR1cm4gdDtcbn07XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgRXllT3V0bGluZWQgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zL2VzL2ljb25zL0V5ZU91dGxpbmVkXCI7XG5pbXBvcnQgY2xhc3NOYW1lcyBmcm9tICdjbGFzc25hbWVzJztcbmltcG9ydCBSY0ltYWdlIGZyb20gJ3JjLWltYWdlJztcbmltcG9ydCB7IHVzZVpJbmRleCB9IGZyb20gJy4uL191dGlsL2hvb2tzL3VzZVpJbmRleCc7XG5pbXBvcnQgeyBnZXRUcmFuc2l0aW9uTmFtZSB9IGZyb20gJy4uL191dGlsL21vdGlvbic7XG5pbXBvcnQgeyBDb25maWdDb250ZXh0IH0gZnJvbSAnLi4vY29uZmlnLXByb3ZpZGVyJztcbmltcG9ydCB1c2VDU1NWYXJDbHMgZnJvbSAnLi4vY29uZmlnLXByb3ZpZGVyL2hvb2tzL3VzZUNTU1ZhckNscyc7XG5pbXBvcnQgZGVmYXVsdExvY2FsZSBmcm9tICcuLi9sb2NhbGUvZW5fVVMnO1xuaW1wb3J0IFByZXZpZXdHcm91cCwgeyBpY29ucyB9IGZyb20gJy4vUHJldmlld0dyb3VwJztcbmltcG9ydCB1c2VTdHlsZSBmcm9tICcuL3N0eWxlJztcbmNvbnN0IEltYWdlID0gcHJvcHMgPT4ge1xuICB2YXIgX2E7XG4gIGNvbnN0IHtcbiAgICAgIHByZWZpeENsczogY3VzdG9taXplUHJlZml4Q2xzLFxuICAgICAgcHJldmlldyxcbiAgICAgIGNsYXNzTmFtZSxcbiAgICAgIHJvb3RDbGFzc05hbWUsXG4gICAgICBzdHlsZVxuICAgIH0gPSBwcm9wcyxcbiAgICBvdGhlclByb3BzID0gX19yZXN0KHByb3BzLCBbXCJwcmVmaXhDbHNcIiwgXCJwcmV2aWV3XCIsIFwiY2xhc3NOYW1lXCIsIFwicm9vdENsYXNzTmFtZVwiLCBcInN0eWxlXCJdKTtcbiAgY29uc3Qge1xuICAgIGdldFByZWZpeENscyxcbiAgICBsb2NhbGU6IGNvbnRleHRMb2NhbGUgPSBkZWZhdWx0TG9jYWxlLFxuICAgIGdldFBvcHVwQ29udGFpbmVyOiBnZXRDb250ZXh0UG9wdXBDb250YWluZXIsXG4gICAgaW1hZ2VcbiAgfSA9IFJlYWN0LnVzZUNvbnRleHQoQ29uZmlnQ29udGV4dCk7XG4gIGNvbnN0IHByZWZpeENscyA9IGdldFByZWZpeENscygnaW1hZ2UnLCBjdXN0b21pemVQcmVmaXhDbHMpO1xuICBjb25zdCByb290UHJlZml4Q2xzID0gZ2V0UHJlZml4Q2xzKCk7XG4gIGNvbnN0IGltYWdlTG9jYWxlID0gY29udGV4dExvY2FsZS5JbWFnZSB8fCBkZWZhdWx0TG9jYWxlLkltYWdlO1xuICAvLyBTdHlsZVxuICBjb25zdCByb290Q2xzID0gdXNlQ1NTVmFyQ2xzKHByZWZpeENscyk7XG4gIGNvbnN0IFt3cmFwQ1NTVmFyLCBoYXNoSWQsIGNzc1ZhckNsc10gPSB1c2VTdHlsZShwcmVmaXhDbHMsIHJvb3RDbHMpO1xuICBjb25zdCBtZXJnZWRSb290Q2xhc3NOYW1lID0gY2xhc3NOYW1lcyhyb290Q2xhc3NOYW1lLCBoYXNoSWQsIGNzc1ZhckNscywgcm9vdENscyk7XG4gIGNvbnN0IG1lcmdlZENsYXNzTmFtZSA9IGNsYXNzTmFtZXMoY2xhc3NOYW1lLCBoYXNoSWQsIGltYWdlID09PSBudWxsIHx8IGltYWdlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBpbWFnZS5jbGFzc05hbWUpO1xuICBjb25zdCBbekluZGV4XSA9IHVzZVpJbmRleCgnSW1hZ2VQcmV2aWV3JywgdHlwZW9mIHByZXZpZXcgPT09ICdvYmplY3QnID8gcHJldmlldy56SW5kZXggOiB1bmRlZmluZWQpO1xuICBjb25zdCBtZXJnZWRQcmV2aWV3ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgdmFyIF9hO1xuICAgIGlmIChwcmV2aWV3ID09PSBmYWxzZSkge1xuICAgICAgcmV0dXJuIHByZXZpZXc7XG4gICAgfVxuICAgIGNvbnN0IF9wcmV2aWV3ID0gdHlwZW9mIHByZXZpZXcgPT09ICdvYmplY3QnID8gcHJldmlldyA6IHt9O1xuICAgIGNvbnN0IHtcbiAgICAgICAgZ2V0Q29udGFpbmVyLFxuICAgICAgICBjbG9zZUljb25cbiAgICAgIH0gPSBfcHJldmlldyxcbiAgICAgIHJlc3RQcmV2aWV3UHJvcHMgPSBfX3Jlc3QoX3ByZXZpZXcsIFtcImdldENvbnRhaW5lclwiLCBcImNsb3NlSWNvblwiXSk7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7XG4gICAgICBtYXNrOiAoIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBgJHtwcmVmaXhDbHN9LW1hc2staW5mb2BcbiAgICAgIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEV5ZU91dGxpbmVkLCBudWxsKSwgaW1hZ2VMb2NhbGUgPT09IG51bGwgfHwgaW1hZ2VMb2NhbGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGltYWdlTG9jYWxlLnByZXZpZXcpKSxcbiAgICAgIGljb25zXG4gICAgfSwgcmVzdFByZXZpZXdQcm9wcyksIHtcbiAgICAgIGdldENvbnRhaW5lcjogZ2V0Q29udGFpbmVyICE9PSBudWxsICYmIGdldENvbnRhaW5lciAhPT0gdm9pZCAwID8gZ2V0Q29udGFpbmVyIDogZ2V0Q29udGV4dFBvcHVwQ29udGFpbmVyLFxuICAgICAgdHJhbnNpdGlvbk5hbWU6IGdldFRyYW5zaXRpb25OYW1lKHJvb3RQcmVmaXhDbHMsICd6b29tJywgX3ByZXZpZXcudHJhbnNpdGlvbk5hbWUpLFxuICAgICAgbWFza1RyYW5zaXRpb25OYW1lOiBnZXRUcmFuc2l0aW9uTmFtZShyb290UHJlZml4Q2xzLCAnZmFkZScsIF9wcmV2aWV3Lm1hc2tUcmFuc2l0aW9uTmFtZSksXG4gICAgICB6SW5kZXgsXG4gICAgICBjbG9zZUljb246IGNsb3NlSWNvbiAhPT0gbnVsbCAmJiBjbG9zZUljb24gIT09IHZvaWQgMCA/IGNsb3NlSWNvbiA6IChfYSA9IGltYWdlID09PSBudWxsIHx8IGltYWdlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBpbWFnZS5wcmV2aWV3KSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2xvc2VJY29uXG4gICAgfSk7XG4gIH0sIFtwcmV2aWV3LCBpbWFnZUxvY2FsZSwgKF9hID0gaW1hZ2UgPT09IG51bGwgfHwgaW1hZ2UgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGltYWdlLnByZXZpZXcpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jbG9zZUljb25dKTtcbiAgY29uc3QgbWVyZ2VkU3R5bGUgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGltYWdlID09PSBudWxsIHx8IGltYWdlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBpbWFnZS5zdHlsZSksIHN0eWxlKTtcbiAgcmV0dXJuIHdyYXBDU1NWYXIoIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJjSW1hZ2UsIE9iamVjdC5hc3NpZ24oe1xuICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgIHByZXZpZXc6IG1lcmdlZFByZXZpZXcsXG4gICAgcm9vdENsYXNzTmFtZTogbWVyZ2VkUm9vdENsYXNzTmFtZSxcbiAgICBjbGFzc05hbWU6IG1lcmdlZENsYXNzTmFtZSxcbiAgICBzdHlsZTogbWVyZ2VkU3R5bGVcbiAgfSwgb3RoZXJQcm9wcykpKTtcbn07XG5JbWFnZS5QcmV2aWV3R3JvdXAgPSBQcmV2aWV3R3JvdXA7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBJbWFnZS5kaXNwbGF5TmFtZSA9ICdJbWFnZSc7XG59XG5leHBvcnQgZGVmYXVsdCBJbWFnZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///11499
`)},1208:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(87462);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5717);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(93771);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var EyeOutlined = function EyeOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
if (false) {}
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTIwOC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQTBEO0FBQzFEO0FBQ0E7O0FBRStCO0FBQ3VDO0FBQ3hCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsdUZBQVEsR0FBRztBQUMvRDtBQUNBLFVBQVUseUZBQWM7QUFDeEIsR0FBRztBQUNIO0FBQ0EsSUFBSSxLQUFxQyxFQUFFLEVBRTFDO0FBQ0QsbUVBQTRCLDZDQUFnQixhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvYW50ZC9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRXllT3V0bGluZWQuanM/YWRhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFeWVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gXCIuLi9jb21wb25lbnRzL0FudGRJY29uXCI7XG52YXIgRXllT3V0bGluZWQgPSBmdW5jdGlvbiBFeWVPdXRsaW5lZChwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChBbnRkSWNvbiwgX2V4dGVuZHMoe30sIHByb3BzLCB7XG4gICAgcmVmOiByZWYsXG4gICAgaWNvbjogRXllT3V0bGluZWRTdmdcbiAgfSkpO1xufTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIEV5ZU91dGxpbmVkLmRpc3BsYXlOYW1lID0gJ0V5ZU91dGxpbmVkJztcbn1cbmV4cG9ydCBkZWZhdWx0IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKEV5ZU91dGxpbmVkKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///1208
`)},64019:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Z: function() { return /* binding */ addEventListenerWrap; }
/* harmony export */ });
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(73935);

function addEventListenerWrap(target, eventType, cb, option) {
  /* eslint camelcase: 2 */
  var callback = react_dom__WEBPACK_IMPORTED_MODULE_0__.unstable_batchedUpdates ? function run(e) {
    react_dom__WEBPACK_IMPORTED_MODULE_0__.unstable_batchedUpdates(cb, e);
  } : cb;
  if (target !== null && target !== void 0 && target.addEventListener) {
    target.addEventListener(eventType, callback, option);
  }
  return {
    remove: function remove() {
      if (target !== null && target !== void 0 && target.removeEventListener) {
        target.removeEventListener(eventType, callback, option);
      }
    }
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjQwMTkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsaUJBQWlCLDhEQUFnQztBQUNqRCxJQUFJLDhEQUFnQztBQUNwQyxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL3JjLXV0aWwvZXMvRG9tL2FkZEV2ZW50TGlzdGVuZXIuanM/OTU5NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGFkZEV2ZW50TGlzdGVuZXJXcmFwKHRhcmdldCwgZXZlbnRUeXBlLCBjYiwgb3B0aW9uKSB7XG4gIC8qIGVzbGludCBjYW1lbGNhc2U6IDIgKi9cbiAgdmFyIGNhbGxiYWNrID0gUmVhY3RET00udW5zdGFibGVfYmF0Y2hlZFVwZGF0ZXMgPyBmdW5jdGlvbiBydW4oZSkge1xuICAgIFJlYWN0RE9NLnVuc3RhYmxlX2JhdGNoZWRVcGRhdGVzKGNiLCBlKTtcbiAgfSA6IGNiO1xuICBpZiAodGFyZ2V0ICE9PSBudWxsICYmIHRhcmdldCAhPT0gdm9pZCAwICYmIHRhcmdldC5hZGRFdmVudExpc3RlbmVyKSB7XG4gICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoZXZlbnRUeXBlLCBjYWxsYmFjaywgb3B0aW9uKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIHJlbW92ZTogZnVuY3Rpb24gcmVtb3ZlKCkge1xuICAgICAgaWYgKHRhcmdldCAhPT0gbnVsbCAmJiB0YXJnZXQgIT09IHZvaWQgMCAmJiB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcikge1xuICAgICAgICB0YXJnZXQucmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudFR5cGUsIGNhbGxiYWNrLCBvcHRpb24pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///64019
`)},27678:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   g1: function() { return /* binding */ getClientSize; },
/* harmony export */   os: function() { return /* binding */ getOffset; }
/* harmony export */ });
/* unused harmony exports get, set, getOuterWidth, getOuterHeight, getDocSize, getScroll */
/* eslint-disable no-nested-ternary */
var PIXEL_PATTERN = /margin|padding|width|height|max|min|offset/;
var removePixel = {
  left: true,
  top: true
};
var floatMap = {
  cssFloat: 1,
  styleFloat: 1,
  float: 1
};
function getComputedStyle(node) {
  return node.nodeType === 1 ? node.ownerDocument.defaultView.getComputedStyle(node, null) : {};
}
function getStyleValue(node, type, value) {
  type = type.toLowerCase();
  if (value === 'auto') {
    if (type === 'height') {
      return node.offsetHeight;
    }
    if (type === 'width') {
      return node.offsetWidth;
    }
  }
  if (!(type in removePixel)) {
    removePixel[type] = PIXEL_PATTERN.test(type);
  }
  return removePixel[type] ? parseFloat(value) || 0 : value;
}
function get(node, name) {
  var length = arguments.length;
  var style = getComputedStyle(node);
  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;
  return length === 1 ? style : getStyleValue(node, name, style[name] || node.style[name]);
}
function set(node, name, value) {
  var length = arguments.length;
  name = floatMap[name] ? 'cssFloat' in node.style ? 'cssFloat' : 'styleFloat' : name;
  if (length === 3) {
    if (typeof value === 'number' && PIXEL_PATTERN.test(name)) {
      value = "".concat(value, "px");
    }
    node.style[name] = value; // Number
    return value;
  }
  for (var x in name) {
    if (name.hasOwnProperty(x)) {
      set(node, x, name[x]);
    }
  }
  return getComputedStyle(node);
}
function getOuterWidth(el) {
  if (el === document.body) {
    return document.documentElement.clientWidth;
  }
  return el.offsetWidth;
}
function getOuterHeight(el) {
  if (el === document.body) {
    return window.innerHeight || document.documentElement.clientHeight;
  }
  return el.offsetHeight;
}
function getDocSize() {
  var width = Math.max(document.documentElement.scrollWidth, document.body.scrollWidth);
  var height = Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
  return {
    width: width,
    height: height
  };
}
function getClientSize() {
  var width = document.documentElement.clientWidth;
  var height = window.innerHeight || document.documentElement.clientHeight;
  return {
    width: width,
    height: height
  };
}
function getScroll() {
  return {
    scrollLeft: Math.max(document.documentElement.scrollLeft, document.body.scrollLeft),
    scrollTop: Math.max(document.documentElement.scrollTop, document.body.scrollTop)
  };
}
function getOffset(node) {
  var box = node.getBoundingClientRect();
  var docElem = document.documentElement;

  // < ie8 \u4E0D\u652F\u6301 win.pageXOffset, \u5219\u4F7F\u7528 docElem.scrollLeft
  return {
    left: box.left + (window.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || document.body.clientLeft || 0),
    top: box.top + (window.pageYOffset || docElem.scrollTop) - (docElem.clientTop || document.body.clientTop || 0)
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27678
`)}}]);
