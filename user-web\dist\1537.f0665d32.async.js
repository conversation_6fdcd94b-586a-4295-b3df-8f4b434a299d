"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1537],{65573:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29905);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(85893);


var FallbackComponent = function FallbackComponent() {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(antd__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .ZP, {
    status: "403",
    title: "Kh\\xF4ng th\\u1EC3 truy c\\u1EADp",
    subTitle: "Xin l\\u1ED7i, b\\u1EA1n kh\\xF4ng c\\xF3 quy\\u1EC1n xem n\\u1ED9i dung n\\xE0y."
    //   extra={
    //     <Button type="primary" href="/">
    //       Back Home
    //     </Button>
    //   }
  });
};
/* harmony default export */ __webpack_exports__.Z = (FallbackComponent);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjU1NzMuanMiLCJtYXBwaW5ncyI6Ijs7QUFBOEI7QUFBQTtBQUU5QixJQUFNRyxpQkFBaUIsR0FBRyxTQUFwQkEsaUJBQWlCQSxDQUFBLEVBQVM7RUFDOUIsb0JBQ0VELHNEQUFBLENBQUNGLHNEQUFNO0lBQ0xJLE1BQU0sRUFBQyxLQUFLO0lBQ1pDLEtBQUssRUFBQyxpQ0FBb0I7SUFDMUJDLFFBQVEsRUFBQztJQUNUO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7RUFBQSxDQUNELENBQUM7QUFFTixDQUFDO0FBRUQsc0RBQWVILGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vc3JjL2NvbXBvbmVudHMvRmFsbGJhY2tDb250ZW50L2luZGV4LnRzeD9mZGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlc3VsdCB9IGZyb20gJ2FudGQnO1xyXG5cclxuY29uc3QgRmFsbGJhY2tDb21wb25lbnQgPSAoKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxSZXN1bHRcclxuICAgICAgc3RhdHVzPVwiNDAzXCJcclxuICAgICAgdGl0bGU9XCJLaMO0bmcgdGjhu4MgdHJ1eSBj4bqtcFwiXHJcbiAgICAgIHN1YlRpdGxlPVwiWGluIGzhu5dpLCBi4bqhbiBraMO0bmcgY8OzIHF1eeG7gW4geGVtIG7hu5lpIGR1bmcgbsOgeS5cIlxyXG4gICAgICAvLyAgIGV4dHJhPXtcclxuICAgICAgLy8gICAgIDxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBocmVmPVwiL1wiPlxyXG4gICAgICAvLyAgICAgICBCYWNrIEhvbWVcclxuICAgICAgLy8gICAgIDwvQnV0dG9uPlxyXG4gICAgICAvLyAgIH1cclxuICAgIC8+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEZhbGxiYWNrQ29tcG9uZW50O1xyXG4iXSwibmFtZXMiOlsiUmVzdWx0IiwianN4IiwiX2pzeCIsIkZhbGxiYWNrQ29tcG9uZW50Iiwic3RhdHVzIiwidGl0bGUiLCJzdWJUaXRsZSJdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///65573
`)},78872:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Timekeeping_Approval; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectWithoutProperties.js
var objectWithoutProperties = __webpack_require__(13769);
var objectWithoutProperties_default = /*#__PURE__*/__webpack_require__.n(objectWithoutProperties);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/FallbackContent/index.tsx
var FallbackContent = __webpack_require__(65573);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createClass.js
var createClass = __webpack_require__(72004);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/classCallCheck.js
var classCallCheck = __webpack_require__(12444);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
;// CONCATENATED MODULE: ./src/services/attendance-v2/overtime-approval.ts







var OvertimeApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function OvertimeApproval() {
  _classCallCheck(this, OvertimeApproval);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "iot_user_in_workshift", void 0);
  // Link
  _defineProperty(this, "request_date", void 0);
  // Datetime
  _defineProperty(this, "approval_date", void 0);
  // Datetime
  _defineProperty(this, "approver_id", void 0);
  // Link
  _defineProperty(this, "approver_first_name", void 0);
  // First name of the approver
  _defineProperty(this, "approver_last_name", void 0);
  // Last name of the approver
  _defineProperty(this, "approval_status", void 0);
  // Approved|Processing|Deny|Canceled
  _defineProperty(this, "request_comment", void 0);
  // Data
  _defineProperty(this, "reply_comment", void 0);
  // Data
  _defineProperty(this, "customer_id", void 0);
  // Link
  _defineProperty(this, "overtime_date", void 0);
  // Date
  _defineProperty(this, "overtime_type", void 0);
  // before|after
  _defineProperty(this, "start_time", void 0);
  // Time
  _defineProperty(this, "end_time", void 0);
  // Time
  _defineProperty(this, "cancel_reason", void 0);
  // Data
  _defineProperty(this, "canceled_date", void 0);
  // Datetime
  _defineProperty(this, "reason_for_change", void 0);
  // Data
  _defineProperty(this, "change_date", void 0);
  // Datetime
  _defineProperty(this, "modified_by_user", void 0);
  // Link
  _defineProperty(this, "creator_first_name", void 0);
  // First name of the creator
  _defineProperty(this, "creator_last_name", void 0);
} // Last name of the creator
)));
var getOvertimeApprovalList = /*#__PURE__*/function () {
  var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)('api/v2/attendance/overtime-approval'), {
            method: 'GET',
            params: (0,utils/* getParamsReqList */.vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getOvertimeApprovalList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getOvertimeApprovalDetail = /*#__PURE__*/function () {
  var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(name) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)("api/v2/attendance/overtime-approval/".concat(name)), {
            method: 'GET'
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getOvertimeApprovalDetail(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var createOvertimeApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/attendance/overtime-approval'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function createOvertimeApproval(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
var updateOvertimeApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref4 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(name, data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return request(generateAPIPath("api/v2/attendance/overtime-approval/".concat(name)), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateOvertimeApproval(_x4, _x5) {
    return _ref4.apply(this, arguments);
  };
}()));
var cancelOvertimeApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name, cancelReason) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath("api/v2/attendance/overtime-approval/".concat(name, "/cancel")), {
            method: 'PUT',
            data: {
              cancel_reason: cancelReason
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function cancelOvertimeApproval(_x6, _x7) {
    return _ref5.apply(this, arguments);
  };
}()));
var approveOvertimeApproval = /*#__PURE__*/function () {
  var _ref6 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6(name, approvalStatus, replyComment) {
    var res;
    return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umi_production_exports.request)((0,utils/* generateAPIPath */.rH)("api/v2/attendance/overtime-approval/".concat(name, "/approve")), {
            method: 'PUT',
            data: {
              approval_status: approvalStatus,
              reply_comment: replyComment
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function approveOvertimeApproval(_x8, _x9, _x10) {
    return _ref6.apply(this, arguments);
  };
}();
var changeApprovalStatus = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref7 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee7(name, newStatus, reasonForChange) {
    var res;
    return _regeneratorRuntime().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return request(generateAPIPath("api/v2/attendance/overtime-approval/".concat(name, "/change-status")), {
            method: 'PUT',
            data: {
              new_status: newStatus,
              change_reason: reasonForChange
            }
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function changeApprovalStatus(_x11, _x12, _x13) {
    return _ref7.apply(this, arguments);
  };
}()));
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(55287);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/date-picker/index.js + 78 modules
var date_picker = __webpack_require__(47676);
// EXTERNAL MODULE: ./node_modules/antd/es/tag/index.js + 5 modules
var tag = __webpack_require__(66309);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/Timekeeping/Approval/Components/ApprovalModalDetail.tsx










var ApprovalDetailModal = function ApprovalDetailModal(_ref) {
  var visible = _ref.visible,
    onClose = _ref.onClose,
    onReload = _ref.onReload,
    approvalDetail = _ref.approvalDetail;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var handleApprove = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!approvalDetail) {
              _context.next = 11;
              break;
            }
            _context.prev = 1;
            _context.next = 4;
            return approveOvertimeApproval(approvalDetail.name, 'Approved', form.getFieldValue('reply_comment'));
          case 4:
            message/* default */.ZP.success('Th\xE0nh c\xF4ng');
            onClose();
            onReload();
            _context.next = 11;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](1);
          case 11:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 9]]);
    }));
    return function handleApprove() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleDeny = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!approvalDetail) {
              _context2.next = 11;
              break;
            }
            _context2.prev = 1;
            _context2.next = 4;
            return approveOvertimeApproval(approvalDetail.name, 'Deny', form.getFieldValue('reply_comment'));
          case 4:
            message/* default */.ZP.success('Th\xE0nh c\xF4ng');
            onClose();
            onReload();
            _context2.next = 11;
            break;
          case 9:
            _context2.prev = 9;
            _context2.t0 = _context2["catch"](1);
          case 11:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 9]]);
    }));
    return function handleDeny() {
      return _ref3.apply(this, arguments);
    };
  }();
  var approvalStatusMap = {
    Approved: '\u0110\xE3 ph\xEA duy\u1EC7t',
    Deny: 'T\u1EEB ch\u1ED1i',
    Canceled: '\u0110\xE3 h\u1EE7y',
    Processing: '\u0110ang x\u1EED l\xFD'
  };
  var statusColorMap = {
    Approved: 'green',
    Deny: 'red',
    Canceled: 'gray',
    Processing: 'blue'
  };
  (0,react.useEffect)(function () {
    if (visible && approvalDetail) {
      form.setFieldsValue({
        name: approvalDetail.name,
        request_date: dayjs_min_default()(approvalDetail.request_date),
        overtime_date: dayjs_min_default()(approvalDetail.overtime_date),
        overtime_type: approvalDetail.overtime_type === 'before' ? formatMessage({
          id: 'common.before'
        }) : formatMessage({
          id: 'common.after'
        }),
        start_time: approvalDetail.start_time,
        end_time: approvalDetail.end_time,
        request_comment: approvalDetail.request_comment,
        approval_status: approvalStatusMap[approvalDetail.approval_status],
        reply_comment: approvalDetail.reply_comment,
        creator_name: "".concat(approvalDetail.creator_first_name, " ").concat(approvalDetail.creator_last_name),
        approver_name: "".concat(approvalDetail.approver_first_name, " ").concat(approvalDetail.approver_last_name)
      });
    } else {
      form.resetFields();
    }
  }, [visible, approvalDetail]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
    title: formatMessage({
      id: 'common.detail'
    }),
    open: visible,
    onCancel: onClose,
    footer: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "primary",
      danger: true,
      onClick: handleDeny,
      children: formatMessage({
        id: 'common.deny'
      })
    }, "deny"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      type: "primary",
      onClick: handleApprove,
      children: formatMessage({
        id: 'common.approve'
      })
    }, "approve")],
    children: approvalDetail ? /*#__PURE__*/(0,jsx_runtime.jsxs)(es_form/* default */.Z, {
      form: form,
      layout: "vertical",
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.request_date'
            }),
            name: "request_date",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
              showTime: true,
              format: "YYYY-MM-DD HH:mm",
              disabled: true,
              style: {
                width: '100%'
              }
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.overtime_date'
            }),
            name: "overtime_date",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
              showTime: true,
              format: "YYYY-MM-DD",
              disabled: true,
              style: {
                width: '100%'
              }
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.overtime_type'
            }),
            name: "overtime_type",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.approval_status'
            }),
            name: "approval_status",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
              color: statusColorMap[approvalDetail.approval_status],
              children: approvalStatusMap[approvalDetail.approval_status]
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.start_time'
            }),
            name: "start_time",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.end_time'
            }),
            name: "end_time",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        })]
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.request_comment'
            }),
            name: "request_comment",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {
              rows: 3,
              readOnly: true
            })
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.reply_comment'
            }),
            name: "reply_comment",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {
              rows: 3
            })
          })
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.creator_name'
            }),
            name: "creator_name",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
            label: formatMessage({
              id: 'common.approver_name'
            }),
            name: "approver_name",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {
              readOnly: true
            })
          })
        })]
      })]
    }) : /*#__PURE__*/(0,jsx_runtime.jsx)("p", {
      children: "Loading..."
    })
  });
};
/* harmony default export */ var ApprovalModalDetail = (ApprovalDetailModal);
;// CONCATENATED MODULE: ./src/pages/MyUser/Timekeeping/Approval/index.tsx




var _excluded = ["type", "defaultRender"],
  _excluded2 = ["type", "defaultRender"];















var Approval = function Approval() {
  var _initialState$current, _initialState$current2;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var tableRef = (0,react.useRef)();
  var _useModel = (0,_umi_production_exports.useModel)('@@initialState'),
    initialState = _useModel.initialState;
  var customer_name = initialState === null || initialState === void 0 || (_initialState$current = initialState.currentUser) === null || _initialState$current === void 0 ? void 0 : _initialState$current.customer_id;
  if (!customer_name) return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
  var access = (0,_umi_production_exports.useAccess)();
  var user_id = initialState === null || initialState === void 0 || (_initialState$current2 = initialState.currentUser) === null || _initialState$current2 === void 0 ? void 0 : _initialState$current2.user_id;
  var reloadTable = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _tableRef$current;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            (_tableRef$current = tableRef.current) === null || _tableRef$current === void 0 || _tableRef$current.reload();
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function reloadTable() {
      return _ref.apply(this, arguments);
    };
  }();
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isModalVisible = _useState2[0],
    setIsModalVisible = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    approvalDetail = _useState4[0],
    setApprovalDetail = _useState4[1];
  var handleViewDetail = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(name) {
      var detail;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return getOvertimeApprovalDetail(name);
          case 3:
            detail = _context2.sent;
            setApprovalDetail(detail);
            setIsModalVisible(true);
            _context2.next = 11;
            break;
          case 8:
            _context2.prev = 8;
            _context2.t0 = _context2["catch"](0);
            message/* default */.ZP.error('Failed to fetch details');
          case 11:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 8]]);
    }));
    return function handleViewDetail(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var canRead = access.canAccessPageTimeKeepingManagement();
  var canUpdate = access.canUpdateInTimeKeepingManagement();
  var canCreate = access.canCreateInTimeKeepingManagement();
  var columns = [{
    title: formatMessage({
      id: 'common.detail'
    }),
    dataIndex: 'name',
    render: function render(_, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
        onClick: function onClick() {
          return handleViewDetail(entity.name);
        }
      });
    },
    fixed: 'left',
    hideInSearch: true
  }, {
    title: formatMessage({
      id: 'common.request_user'
    }),
    dataIndex: 'request_user',
    render: function render(dom, entity, index, action, schema) {
      return "".concat(entity.creator_first_name, " ").concat(entity.creator_last_name);
    },
    valueType: 'select',
    fieldProps: {
      showSearch: true
    },
    request: function () {
      var _request = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
        var res, data;
        return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return (0,customerUser/* getCustomerUserList */.J9)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              });
            case 2:
              res = _context3.sent;
              data = res.data.map(function (item) {
                return {
                  label: item.first_name + ' ' + item.last_name,
                  value: item.name
                };
              });
              return _context3.abrupt("return", data);
            case 5:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }));
      function request() {
        return _request.apply(this, arguments);
      }
      return request;
    }()
  }, {
    title: formatMessage({
      id: 'common.request_date'
    }),
    dataIndex: 'request_date',
    // valueType: 'fromNow',
    renderFormItem: function renderFormItem(schema, _ref3, form, action) {
      var type = _ref3.type,
        defaultRender = _ref3.defaultRender,
        rest = objectWithoutProperties_default()(_ref3, _excluded);
      return /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {});
    },
    render: function render(dom, entity, index, action, schema) {
      return dayjs_min_default().utc(entity.request_date).format('DD-MM-YYYY HH:mm:ss');
    }
  }, {
    title: formatMessage({
      id: 'common.approval_date'
    }),
    dataIndex: 'approval_date',
    // valueType: 'fromNow',
    render: function render(dom, entity, index, action, schema) {
      if (!entity.approval_date) return '-';
      return dayjs_min_default().utc(entity.approval_date).format('DD-MM-YYYY HH:mm:ss');
    },
    renderFormItem: function renderFormItem(schema, _ref4, form, action) {
      var type = _ref4.type,
        defaultRender = _ref4.defaultRender,
        rest = objectWithoutProperties_default()(_ref4, _excluded2);
      return /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {});
    }
  }, {
    title: formatMessage({
      id: 'common.approval_status'
    }),
    dataIndex: 'approval_status',
    render: function render(dom, entity) {
      var color;
      switch (entity.approval_status) {
        case 'Deny':
          color = 'red';
          break;
        case 'Processing':
          color = 'blue';
          break;
        case 'Approved':
          color = 'green';
          break;
        default:
          color = 'default';
      }
      return /*#__PURE__*/(0,jsx_runtime.jsx)(tag/* default */.Z, {
        color: color,
        children: entity.approval_status === 'Approved' ? '\u0110\xE3 ph\xEA duy\u1EC7t' : entity.approval_status === 'Processing' ? '\u0110ang x\u1EED l\xFD' : entity.approval_status === 'Deny' ? 'T\u1EEB ch\u1ED1i' : entity.approval_status === 'Canceled' ? '\u0110\xE3 h\u1EE7y' : ''
      });
    },
    valueType: 'select',
    valueEnum: {
      Deny: {
        text: 'T\u1EEB ch\u1ED1i'
      },
      Processing: {
        text: '\u0110ang x\u1EED l\xFD'
      },
      Approved: {
        text: '\u0110\xE3 ph\xEA duy\u1EC7t'
      }
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(_umi_production_exports.Access, {
    accessible: canRead,
    fallback: /*#__PURE__*/(0,jsx_runtime.jsx)(FallbackContent/* default */.Z, {}),
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      size: "small",
      actionRef: tableRef,
      rowKey: "name",
      request: ( /*#__PURE__*/function () {
        var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(params, sort, filter) {
          var order_by, current, pageSize, email, request_date, approval_date, approval_status, request_user, formatDate, result;
          return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                console.log('filter: ', params, filter);
                order_by = 'request_date desc';
                current = params.current, pageSize = params.pageSize, email = params.email, request_date = params.request_date, approval_date = params.approval_date, approval_status = params.approval_status, request_user = params.request_user;
                _context4.prev = 3;
                formatDate = 'YYYY-MM-DD HH:mm:ss';
                _context4.next = 7;
                return getOvertimeApprovalList({
                  page: current ? current : 0 + 1,
                  size: pageSize,
                  filters: JSON.stringify([['iot_workshift_overtime_approval', 'approver_id', '=', user_id], request_user && ['iot_workshift_overtime_approval', 'creator_id', '=', request_user], approval_status && ['iot_workshift_overtime_approval', 'approval_status', '=', approval_status], request_date && ['iot_workshift_overtime_approval', 'request_date', '>=', dayjs_min_default()(request_date).startOf('day').format(formatDate)], request_date && ['iot_workshift_overtime_approval', 'request_date', '<=', dayjs_min_default()(request_date).endOf('day').format(formatDate)], approval_date && ['iot_workshift_overtime_approval', 'approval_date', '>=', dayjs_min_default()(approval_date).startOf('day').format(formatDate)], approval_date && ['iot_workshift_overtime_approval', 'approval_date', '<=', dayjs_min_default()(approval_date).endOf('day').format(formatDate)]].filter(Boolean)),
                  order_by: order_by
                });
              case 7:
                result = _context4.sent;
                return _context4.abrupt("return", {
                  data: result.data,
                  total: result.pagination.totalElements
                });
              case 11:
                _context4.prev = 11;
                _context4.t0 = _context4["catch"](3);
                console.log(_context4.t0);
                return _context4.abrupt("return", {});
              case 15:
              case "end":
                return _context4.stop();
            }
          }, _callee4, null, [[3, 11]]);
        }));
        return function (_x2, _x3, _x4) {
          return _ref5.apply(this, arguments);
        };
      }()),
      bordered: true,
      columns: columns,
      search: {
        labelWidth: 'auto'
      },
      pagination: {
        defaultPageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['20', '50', '100']
      }
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(ApprovalModalDetail, {
      visible: isModalVisible,
      onClose: function onClose() {
        return setIsModalVisible(false);
      },
      onReload: reloadTable,
      approvalDetail: approvalDetail
    })]
  });
};
/* harmony default export */ var Timekeeping_Approval = (Approval);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///78872
`)}}]);
