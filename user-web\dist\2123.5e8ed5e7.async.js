"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2123],{75508:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(26859);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(14726);
/* harmony import */ var nanoid__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(53416);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);
















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var getFileNameFromUrl = function getFileNameFromUrl(url) {
  // Split the URL by '/' and get the last part
  if (typeof url !== 'string') {
    var _url$toString;
    return url === null || url === void 0 || (_url$toString = url.toString) === null || _url$toString === void 0 ? void 0 : _url$toString.call(url);
  }
  var parts = url.split('/');
  var fileName = parts[parts.length - 1];

  // If there's a query string, remove it
  fileName = fileName.split('?')[0];

  // If there's a fragment, remove it
  fileName = fileName.split('#')[0];
  return fileName.split('.')[0];
};
var FormUploadFiles = function FormUploadFiles(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isReadonly = _ref.isReadonly,
    onValueChange = _ref.onValueChange,
    maxSize = _ref.maxSize,
    _ref$showUploadButton = _ref.showUploadButton,
    showUploadButton = _ref$showUploadButton === void 0 ? true : _ref$showUploadButton;
  // const [previewOpen, setPreviewOpen] = useState(false);
  // const [previewImage, setPreviewImage] = useState('');
  // const [previewTitle, setPreviewTitle] = useState('');

  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState, 2),
    imageList = _useState2[0],
    setImageList = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_3___default()(_useState3, 2),
    fileList = _useState4[0],
    setFileList = _useState4[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_11__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        // name: \`File \${(index + 1).toString()}\`,
        name: getFileNameFromUrl(url),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  // const handlePreview = async (file: UploadFile) => {
  //   if (!file.url && !file.preview) {
  //     file.preview = await getBase64(file.originFileObj as RcFile);
  //   }

  //   setPreviewImage(file.url || (file.preview as string));
  //   setPreviewOpen(true);
  //   setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  // };

  var handleChange = /*#__PURE__*/function () {
    var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(_ref2) {
      var newFileList, uploadListRes, newFileListRes, arrFileUrl, fileUrls;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            newFileList = _ref2.fileList;
            _context2.next = 3;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(item) {
                var _item$lastModified, res;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      if (!item.url) {
                        _context.next = 2;
                        break;
                      }
                      return _context.abrupt("return", {
                        url: item.url.split('file_url=').at(-1),
                        uid: item.uid,
                        status: 'done',
                        name: item.name
                      });
                    case 2:
                      _context.prev = 2;
                      _context.next = 5;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_5__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_4__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 5:
                      res = _context.sent;
                      return _context.abrupt("return", {
                        url: res.data.message.file_url,
                        name: getFileNameFromUrl(res.data.message.file_url),
                        uid: (0,nanoid__WEBPACK_IMPORTED_MODULE_12__/* .nanoid */ .x0)(),
                        status: 'done'
                      });
                    case 9:
                      _context.prev = 9;
                      _context.t0 = _context["catch"](2);
                      antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                        content: "upload file kh\\xF4ng th\\xE0nh c\\xF4ng"
                      });
                      return _context.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                        status: 'error'
                      }));
                    case 13:
                    case "end":
                      return _context.stop();
                  }
                }, _callee, null, [[2, 9]]);
              }));
              return function (_x2) {
                return _ref4.apply(this, arguments);
              };
            }()));
          case 3:
            uploadListRes = _context2.sent;
            // for display
            newFileListRes = uploadListRes.map(function (item) {
              return item.status === 'fulfilled' ? item.value : null;
            }).filter(function (item) {
              return item !== null;
            }); // update img path
            arrFileUrl = newFileListRes.map(function (item) {
              return item.status === 'done' ? item.url : null;
            }).filter(function (item) {
              return item !== null;
            });
            fileUrls = arrFileUrl.join(',');
            console.log("fileUrls: ", fileUrls);

            // for value

            //
            _context2.next = 10;
            return Promise.all([onValueChange === null || onValueChange === void 0 ? void 0 : onValueChange(fileUrls)]);
          case 10:
            setFileList(function () {
              return newFileListRes.map(function (item) {
                var _getListFileUrlFromSt;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                  url: ((_getListFileUrlFromSt = (0,_services_utils__WEBPACK_IMPORTED_MODULE_6__/* .getListFileUrlFromStringV2 */ .JJ)({
                    arrUrlString: item.url
                  })) === null || _getListFileUrlFromSt === void 0 ? void 0 : _getListFileUrlFromSt[0]) || ''
                });
              });
            });
            setImageList(fileUrls);
            form === null || form === void 0 || form.setFieldValue(formItemName, fileUrls);
          case 13:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function handleChange(_x) {
      return _ref3.apply(this, arguments);
    };
  }();
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_7__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      style: {
        display: 'none'
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      label: label,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z
      // listType="text"
      , {
        fileList: fileList
        // onPreview={handlePreview}
        ,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        disabled: isReadonly,
        beforeUpload: function beforeUpload(file) {
          if (maxSize) {
            var isLt5M = file.size / 1024 / 1024 <= maxSize;
            if (!isLt5M) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error(formatMessage({
                id: 'common.upload-error-file-big'
              }) + " ".concat(maxSize, "MB"));
              // alert('Image must smaller than 5MB!');
              return antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z.LIST_IGNORE;
            }
          }
          return new Promise(function (resolve, reject) {
            // check the file size - you can specify the file size you'd like here:
            if (maxSize) {
              var _isLt5M = file.size / 1024 / 1024 <= maxSize;
              if (!_isLt5M) {
                antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error(formatMessage({
                  id: 'common.upload-error-file-big'
                }) + " ".concat(maxSize, "MB"));
                // alert('Image must smaller than 5MB!');
                reject(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z.LIST_IGNORE);
                return;
              }
            }
            resolve(true);
          });
        },
        children: showUploadButton && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .ZP, {
          disabled: isReadonly,
          icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {}),
          children: formatMessage({
            id: 'common.upload'
          })
        })
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadFiles);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///75508
`)},43370:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ components_ViewDetail; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/UploadFIles/index.tsx
var UploadFIles = __webpack_require__(75508);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/hooks/useUpdateDeliveryNote.ts
var useUpdateDeliveryNote = __webpack_require__(16380);
// EXTERNAL MODULE: ./src/services/stock/deliveryNote.ts
var deliveryNote = __webpack_require__(14329);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PrinterOutlined.js + 1 modules
var PrinterOutlined = __webpack_require__(30019);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DatePicker/index.js + 5 modules
var DatePicker = __webpack_require__(50335);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/antd/es/form/hooks/useForm.js + 2 modules
var useForm = __webpack_require__(88942);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ExportHistory/components/ExportReceiptDetail.tsx
















var ExportReceiptDetail = function ExportReceiptDetail(_ref) {
  var name = _ref.name,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var actionRef = (0,react.useRef)();
  var _useForm = (0,useForm/* default */.Z)(),
    _useForm2 = slicedToArray_default()(_useForm, 1),
    form = _useForm2[0];
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    items = _useState2[0],
    setItems = _useState2[1];
  var _useState3 = (0,react.useState)(undefined),
    _useState4 = slicedToArray_default()(_useState3, 2),
    fileList = _useState4[0],
    setFileList = _useState4[1];
  var _useRequest = (0,_umi_production_exports.useRequest)(deliveryNote/* getDeliveryNoteDetail */.r7, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        setFileList(data.file_path);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    refresh();
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.quantity"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.rate)
      });
    },
    hideInTable: !canReadInventoryValue
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.export-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.amount)
      });
    },
    hideInTable: !canReadInventoryValue
  }];
  (0,react.useEffect)(function () {
    run({
      name: name
    });
  }, [name]);
  (0,react.useEffect)(function () {
    var _data$items;
    form.resetFields();
    form.setFieldsValue(data);
    form.setFieldValue('warehouse_label', data === null || data === void 0 || (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items.at(0)) === null || _data$items === void 0 ? void 0 : _data$items.warehouse_label);
    form.setFieldValue('user', "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name));
    setItems((data === null || data === void 0 ? void 0 : data.items) || []);
  }, [data]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useUpdateDeliveryNot = (0,useUpdateDeliveryNote/* useUpdateDeliveryNote */.h)(),
    update = _useUpdateDeliveryNot.run,
    updating = _useUpdateDeliveryNot.loading;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(modal/* default */.Z, {
    open: isModalOpen,
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'warehouse-management.export-history.detail'
    }),
    onCancel: function onCancel() {
      setIsModalOpen(false);
      handleReload();
    },
    footer: [],
    width: 1000,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
      submitter: false,
      disabled: true,
      form: form,
      layout: "vertical",
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.export-history.id'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'name',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.export-history.date'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'posting_date',
        width: "md",
        fieldProps: {
          format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.export-history.customer'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'customer_label',
        width: 'md'
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.description'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'description',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.assigned_to'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'user',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.export-history.warehouse_label'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'warehouse_label',
        width: "md"
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        marginLeft: 5,
        maxWidth: 500
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 10,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
            spinning: updating,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadFIles/* default */.Z, {
              maxSize: 10,
              isReadonly: false,
              initialImages: fileList,
              formItemName: 'file_path',
              label: formatMessage({
                id: 'common.form.document'
              }),
              fileLimit: 20,
              onValueChange: ( /*#__PURE__*/function () {
                var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
                  return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                    while (1) switch (_context.prev = _context.next) {
                      case 0:
                        _context.next = 2;
                        return update({
                          name: name,
                          file_path: value
                        });
                      case 2:
                      case "end":
                        return _context.stop();
                    }
                  }, _callee);
                }));
                return function (_x) {
                  return _ref2.apply(this, arguments);
                };
              }())
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {}),
            onClick: function onClick() {
              return (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=export&id=".concat(data === null || data === void 0 ? void 0 : data.name));
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: 'common.print_receipt'
            })
          }, 'download')
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      columns: columns,
      cardBordered: true,
      size: "small",
      dataSource: items,
      rowKey: 'name',
      search: false
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        offset: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.export-history.total_quantity"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: (0,utils/* formatNumeral */.GW)(data === null || data === void 0 ? void 0 : data.total_qty)
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [0, 12],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        offset: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.export-history.total_price"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: (0,utils/* formatMoneyNumeral */.yp)(data === null || data === void 0 ? void 0 : data.net_total)
      })]
    })]
  });
};
/* harmony default export */ var components_ExportReceiptDetail = (ExportReceiptDetail);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/hooks/useUpdatePurchaseReceipt.ts
var useUpdatePurchaseReceipt = __webpack_require__(85566);
// EXTERNAL MODULE: ./src/services/stock/purchaseReceipt.ts
var purchaseReceipt = __webpack_require__(33326);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/ImportHistory/components/ImportReceiptDetail.tsx
















var ImportReceiptDetail = function ImportReceiptDetail(_ref) {
  var name = _ref.name,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var actionRef = (0,react.useRef)();
  var _useForm = (0,useForm/* default */.Z)(),
    _useForm2 = slicedToArray_default()(_useForm, 1),
    form = _useForm2[0];
  var _useState = (0,react.useState)(undefined),
    _useState2 = slicedToArray_default()(_useState, 2),
    fileList = _useState2[0],
    setFileList = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    items = _useState4[0],
    setItems = _useState4[1];
  var _useRequest = (0,_umi_production_exports.useRequest)(purchaseReceipt/* getImportReceiptDetail */.af, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        setFileList(data.file_path);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    refresh();
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.quantity"
    }),
    dataIndex: 'received_qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.received_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.rate)
      });
    },
    hideInTable: !canReadInventoryValue
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.import-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.amount)
      });
    },
    hideInTable: !canReadInventoryValue
  }];
  (0,react.useEffect)(function () {
    run({
      name: name
    });
  }, [name]);
  (0,react.useEffect)(function () {
    var _data$items;
    form.resetFields();
    form.setFieldsValue(data);
    form.setFieldValue('warehouse_label', data === null || data === void 0 || (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items.at(0)) === null || _data$items === void 0 ? void 0 : _data$items.warehouse_label);
    form.setFieldValue('user', "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name));
    setItems((data === null || data === void 0 ? void 0 : data.items) || []);
  }, [data]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useUpdatePurchaseRec = (0,useUpdatePurchaseReceipt/* useUpdatePurchaseReceipt */.V)(),
    update = _useUpdatePurchaseRec.run,
    updating = _useUpdatePurchaseRec.loading;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(modal/* default */.Z, {
    open: isModalOpen,
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'warehouse-management.import-history.detail'
    }),
    onCancel: function onCancel() {
      setIsModalOpen === null || setIsModalOpen === void 0 || setIsModalOpen(false);
      handleReload();
    },
    footer: [],
    width: 1000,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
      submitter: false,
      disabled: true,
      form: form,
      layout: "vertical",
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.import-history.id'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'name',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.import-history.date'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'posting_date',
        width: "md",
        fieldProps: {
          format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.import-history.supplier'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'supplier_label',
        width: 'md'
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.description'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'description',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.assigned_to'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'user',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.import-history.warehouse_label'
        }),
        colProps: {
          sm: 24,
          md: 8
        },
        name: 'warehouse_label',
        width: "md"
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      style: {
        marginLeft: 5,
        maxWidth: 500
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 10,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
            spinning: updating,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadFIles/* default */.Z, {
              maxSize: 10,
              isReadonly: false,
              initialImages: fileList,
              formItemName: 'file_path',
              label: formatMessage({
                id: 'common.form.document'
              }),
              fileLimit: 20,
              onValueChange: ( /*#__PURE__*/function () {
                var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(value) {
                  return regeneratorRuntime_default()().wrap(function _callee$(_context) {
                    while (1) switch (_context.prev = _context.next) {
                      case 0:
                        _context.next = 2;
                        return update({
                          name: name,
                          file_path: value
                        });
                      case 2:
                      case "end":
                        return _context.stop();
                    }
                  }, _callee);
                }));
                return function (_x) {
                  return _ref2.apply(this, arguments);
                };
              }())
            })
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {}),
            onClick: function onClick() {
              return (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=import&id=".concat(data === null || data === void 0 ? void 0 : data.name));
            },
            style: {
              marginTop: 0
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: 'common.print_receipt'
            })
          }, 'download')
        })]
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      columns: columns,
      cardBordered: true,
      size: "small",
      dataSource: items,
      rowKey: 'name',
      search: false
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        offset: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.import-history.total_quantity"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: (0,utils/* formatNumeral */.GW)(data === null || data === void 0 ? void 0 : data.total_qty)
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: [0, 12],
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        offset: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: "warehouse-management.import-history.total_price"
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: (0,utils/* formatMoneyNumeral */.yp)(data === null || data === void 0 ? void 0 : data.net_total)
      })]
    })]
  });
};
/* harmony default export */ var components_ImportReceiptDetail = (ImportReceiptDetail);
// EXTERNAL MODULE: ./src/pages/WarehouseManagementV3/Inventory/ReconciliationHistory/components/ReconciliationDetail.tsx
var ReconciliationDetail = __webpack_require__(95365);
// EXTERNAL MODULE: ./src/services/stock/stockEntry.ts
var stockEntry = __webpack_require__(1631);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/StockEntryHistory/components/StockEntryDetail.tsx












var StockEntryDetail = function StockEntryDetail(_ref) {
  var name = _ref.name,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen;
  var actionRef = (0,react.useRef)();
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    items = _useState2[0],
    setItems = _useState2[1];
  var _useState3 = (0,react.useState)(undefined),
    _useState4 = slicedToArray_default()(_useState3, 2),
    fileList = _useState4[0],
    setFileList = _useState4[1];
  var _useState5 = (0,react.useState)(),
    _useState6 = slicedToArray_default()(_useState5, 2),
    type = _useState6[0],
    setType = _useState6[1];
  var _useRequest = (0,_umi_production_exports.useRequest)(stockEntry/* getStockEntryNoteDetail */.T2, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    refresh();
  };
  var access = (0,_umi_production_exports.useAccess)();
  var canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.quantity"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatNumeral */.GW)(entity.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.rate"
    }),
    dataIndex: 'rate',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.valuation_rate)
      });
    },
    hideInTable: !canReadInventoryValue
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "warehouse-management.stock-entry-history.amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        children: (0,utils/* formatMoneyNumeral */.yp)(entity.amount)
      });
    },
    hideInTable: !canReadInventoryValue
  }];
  (0,react.useEffect)(function () {
    run({
      name: name
    });
  }, [name]);
  (0,react.useEffect)(function () {
    form.resetFields();
    form.setFieldsValue(data);
    // console.log('Data is ', data);
    // form.setFieldValue('warehouse_label', data?.items?.at(0)?.warehouse_label);
    form.setFieldValue('user', "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name));
    form.setFieldsValue({
      s_warehouse_label: data === null || data === void 0 ? void 0 : data.items[0].s_warehouse_label,
      t_warehouse_label: data === null || data === void 0 ? void 0 : data.items[0].t_warehouse_label
    });
    setType(data === null || data === void 0 ? void 0 : data.purpose);
    setItems((data === null || data === void 0 ? void 0 : data.items) || []);
  }, [data]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(modal/* default */.Z, {
    open: isModalOpen,
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: 'warehouse-management.stock-entry-history.detail'
    }),
    onCancel: function onCancel() {
      setIsModalOpen(false);
      handleReload();
    },
    footer: [],
    width: 1000,
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(ProForm/* ProForm */.A, {
      submitter: false,
      disabled: true,
      form: form,
      layout: "vertical",
      grid: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.stock-entry-history.id'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 'name',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(DatePicker/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.stock-entry-history.date'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 'posting_date',
        width: "md",
        fieldProps: {
          format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
        }
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.stock-entry-history.purpose'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 'purpose',
        width: 'md'
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.assigned_to'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 'user',
        width: "md"
      }), type === 'Material Receipt' ? '' : /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.from-warehouse-name'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 's_warehouse_label',
        width: "md"
      }), type === 'Material Issue' ? '' : /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'warehouse-management.to-warehouse-name'
        }),
        colProps: {
          sm: 24,
          md: 6
        },
        name: 't_warehouse_label',
        width: "md"
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
        label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
          id: 'common.description'
        }),
        colProps: {
          sm: 24,
          md: 12
        },
        name: 'description',
        width: "xl"
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      style: {
        marginLeft: 5,
        maxWidth: 500
      },
      children: [' ', /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
        gutter: 16,
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 10,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(UploadFIles/* default */.Z, {
            maxSize: 10,
            isReadonly: true,
            initialImages: data === null || data === void 0 ? void 0 : data.file_path,
            formItemName: 'file_path',
            label: formatMessage({
              id: 'common.form.document'
            }),
            fileLimit: 20
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          span: 12,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
            icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PrinterOutlined/* default */.Z, {}),
            onClick: function onClick() {
              var receiptType = '';
              switch (type) {
                case 'Material Issue':
                  receiptType = 'materialIssue';
                  break;
                case 'Material Receipt':
                  receiptType = 'materialReceipt';
                  break;
                case 'Material Transfer':
                  receiptType = 'materialTransfer';
                  break;
                default:
                  receiptType = 'notHandledType';
                  break;
              }
              (0,utils/* openInNewTab */.YQ)("/warehouse-management-v3/to-pdf?type=".concat(receiptType, "&id=").concat(data === null || data === void 0 ? void 0 : data.name));
            },
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
              id: 'common.print_receipt'
            })
          }, 'download')
        })]
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)("br", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      columns: columns,
      cardBordered: true,
      size: "small",
      dataSource: items,
      rowKey: 'name',
      search: false
    })]
  });
};
/* harmony default export */ var components_StockEntryDetail = (StockEntryDetail);
;// CONCATENATED MODULE: ./src/pages/WarehouseManagementV3/Inventory/InventoryListTable/components/ViewDetail.tsx











var RenderModal = function RenderModal(_ref) {
  var voucherType = _ref.voucherType,
    voucherNo = _ref.voucherNo,
    isOpen = _ref.isOpen,
    setIsOpen = _ref.setIsOpen;
  var commonProps = {
    key: voucherNo,
    name: voucherNo,
    isModalOpen: isOpen,
    setIsModalOpen: setIsOpen
  };
  if (voucherType === 'Purchase Receipt') {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ImportReceiptDetail, objectSpread2_default()({}, commonProps));
  } else if (voucherType === 'Delivery Note') {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(components_ExportReceiptDetail, objectSpread2_default()({}, commonProps));
  } else if (voucherType === 'Stock Reconciliation') {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(ReconciliationDetail/* default */.Z, objectSpread2_default()({}, commonProps));
  } else {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(components_StockEntryDetail, objectSpread2_default()({}, commonProps));
  }
};
var ViewDetail = function ViewDetail(_ref2) {
  var voucherNo = _ref2.voucherNo,
    voucherType = _ref2.voucherType;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpenModel = _useState2[0],
    setIsOpenModal = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = slicedToArray_default()(_useState3, 2),
    modalId = _useState4[0],
    setModalId = _useState4[1];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        setModalId(voucherNo);
        setIsOpenModal(true);
      },
      type: "link",
      children: voucherNo
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(RenderModal, {
      voucherType: voucherType,
      voucherNo: voucherNo,
      name: voucherNo,
      isOpen: isOpenModel && modalId === voucherNo,
      setIsOpen: setIsOpenModal
    })]
  });
};
/* harmony default export */ var components_ViewDetail = (ViewDetail);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///43370
`)},95365:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(86604);
/* harmony import */ var _services_stock_stockReconciliation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(47161);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(30019);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(34994);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(5966);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(50335);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(90672);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(4894);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(85576);
/* harmony import */ var antd_es_form_Form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(88942);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(67294);
/* harmony import */ var _components_ReconciliationVoucherEnhanced_stores_stockReconciliationStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(92997);
/* harmony import */ var _components_StockActionButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(858);
/* harmony import */ var _components_StockActionButton_voucherActions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(24234);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(85893);

















var ReconciliationDetail = function ReconciliationDetail(_ref) {
  var name = _ref.name,
    isModalOpen = _ref.isModalOpen,
    setIsModalOpen = _ref.setIsModalOpen,
    _onSuccess = _ref.onSuccess;
  var actionRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)();
  var store = (0,_components_ReconciliationVoucherEnhanced_stores_stockReconciliationStore__WEBPACK_IMPORTED_MODULE_7__/* .useStockReconciliationStore */ .k)();
  var _useForm = (0,antd_es_form_Form__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z)(),
    _useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useForm, 1),
    form = _useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState, 2),
    items = _useState2[0],
    setItems = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState3, 2),
    selectedActionComponent = _useState4[0],
    setSelectedActionComponent = _useState4[1];
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.useRequest)(_services_stock_stockReconciliation__WEBPACK_IMPORTED_MODULE_3__/* .getStockReconciliationDetail */ .qF, {
      manual: true,
      onSuccess: function onSuccess(data) {
        // Set data to store for ActionButton to use
        store.setSavedVoucherData(data);
      },
      onError: function onError(error) {
        console.log('error', error.message);
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var handleReload = function handleReload() {
    var _actionRef$current;
    (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.reload();
    refresh();
  };
  var access = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.useAccess)();
  var canReadInventoryValue = access.canReadCategoryInventoryFieldLevelManagement();
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "warehouse-management.import-history.item_id"
    }),
    dataIndex: 'item_name',
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "warehouse-management.import-history.item_label"
    }),
    dataIndex: 'item_label',
    width: 20
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.old_quantity"
    }),
    dataIndex: 'current_qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .formatNumeral */ .GW)(entity.current_qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.reconciled_quantity"
    }),
    dataIndex: 'qty',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .formatNumeral */ .GW)(entity.qty)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.old_rate"
    }),
    dataIndex: 'current_valuation_rate',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .formatMoneyNumeral */ .yp)(entity.current_valuation_rate)
      });
    },
    hideInTable: !canReadInventoryValue
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.reconciled_rate"
    }),
    dataIndex: 'valuation_rate',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .formatMoneyNumeral */ .yp)(entity.valuation_rate)
      });
    },
    hideInTable: !canReadInventoryValue
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_name',
    width: 20
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
      id: "common.reconciled_amount"
    }),
    dataIndex: 'amount',
    width: 20,
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .formatMoneyNumeral */ .yp)(entity.amount)
      });
    },
    hideInTable: !canReadInventoryValue
  }];
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (isModalOpen && name) {
      run({
        name: name
      });
    }
  }, [name, isModalOpen]);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (data) {
      var _data$items;
      form.resetFields();
      form.setFieldsValue(data);
      form.setFieldValue('warehouse_label', data === null || data === void 0 || (_data$items = data.items) === null || _data$items === void 0 || (_data$items = _data$items.at(0)) === null || _data$items === void 0 ? void 0 : _data$items.warehouse_label);
      form.setFieldValue('user', "".concat(data === null || data === void 0 ? void 0 : data.user_first_name, " ").concat(data === null || data === void 0 ? void 0 : data.user_last_name));
      setItems((data === null || data === void 0 ? void 0 : data.items) || []);
    }
  }, [data]);

  // Handler for action button selection
  var handleActionSelect = function handleActionSelect(Component, initialData) {
    setIsModalOpen(false); // Close current modal
    setSelectedActionComponent( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(Component, {
      onSuccess: function onSuccess() {
        _onSuccess === null || _onSuccess === void 0 || _onSuccess();
        setSelectedActionComponent(null); // Clear after success
      },
      onClose: function onClose() {
        return setSelectedActionComponent(null);
      },
      initialData: initialData,
      autoOpen: true
    }));
  };

  // Render action buttons in the footer
  var renderActionButtons = function renderActionButtons() {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)("div", {
      style: {
        paddingLeft: '22rem',
        paddingRight: '22rem',
        display: 'flex',
        justifyContent: 'space-between',
        width: '100%',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(antd__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .ZP, {
        icon: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {}),
        onClick: function onClick() {
          return (0,_services_utils__WEBPACK_IMPORTED_MODULE_4__/* .openInNewTab */ .YQ)("/warehouse-management-v3/to-pdf?type=reconciliation&id=".concat(data === null || data === void 0 ? void 0 : data.name));
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
          id: 'common.print_receipt'
        })
      }, 'download'), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_StockActionButton__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
        voucherData: data,
        actions: _components_StockActionButton_voucherActions__WEBPACK_IMPORTED_MODULE_9__/* .voucherActionConfigs */ .A['Stock Reconciliation'].map(function (action) {
          return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, action), {}, {
            onSelect: function onSelect() {
              return handleActionSelect(action.createComponent, action.mapData(data));
            }
          });
        }),
        onActionSuccess: _onSuccess,
        closeCurrentModal: function closeCurrentModal() {
          console.log('Closing current modal in ReconciliationDetail');
          setIsModalOpen(false);
        }
      })]
    });
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z, {
      open: isModalOpen,
      title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
        id: 'warehouse-management.import-history.detail'
      }),
      onCancel: function onCancel() {
        setIsModalOpen(false);
        handleReload();
      },
      footer: renderActionButtons(),
      width: 1000,
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_15__/* .ProForm */ .A, {
        submitter: false,
        disabled: true,
        form: form,
        layout: "vertical",
        grid: true,
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
          label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
            id: 'warehouse-management.import-history.id'
          }),
          colProps: {
            sm: 24,
            md: 8
          },
          name: 'name',
          width: "md"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {
          label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
            id: 'warehouse-management.import-history.date'
          }),
          colProps: {
            sm: 24,
            md: 8
          },
          name: 'posting_date',
          width: "md",
          fieldProps: {
            format: _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_2__/* .DEFAULT_DATE_FORMAT_WITHOUT_TIME */ .ug
          }
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
          label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
            id: 'common.assigned_to'
          }),
          colProps: {
            sm: 24,
            md: 8
          },
          name: 'user',
          width: "md"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
          label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
            id: 'warehouse-management.import-history.warehouse_label'
          }),
          colProps: {
            sm: 24,
            md: 8
          },
          name: 'set_warehouse_label',
          width: "md"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
          label: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_5__.FormattedMessage, {
            id: 'common.description'
          }),
          colProps: {
            sm: 24,
            md: 8
          },
          name: 'description',
          width: "md"
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        columns: columns,
        cardBordered: true,
        size: "small",
        dataSource: items,
        rowKey: 'name',
        search: false,
        loading: loading
      })]
    }), selectedActionComponent]
  });
};
/* harmony default export */ __webpack_exports__.Z = (ReconciliationDetail);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///95365
`)},16380:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ useUpdateDeliveryNote; }
/* harmony export */ });
/* harmony import */ var _services_stock_deliveryNote__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(14329);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(31418);



function useUpdateDeliveryNote() {
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useRequest)(_services_stock_deliveryNote__WEBPACK_IMPORTED_MODULE_0__/* .updateDeliveryNote */ .U4, {
    manual: true,
    onSuccess: function onSuccess(data, params) {
      message.success(formatMessage({
        id: 'common.success'
      }));
    },
    onError: function onError(error) {
      message.error(error.message);
    }
  });
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///16380
`)},85566:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   V: function() { return /* binding */ useUpdatePurchaseReceipt; }
/* harmony export */ });
/* harmony import */ var _services_stock_purchaseReceipt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(33326);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(31418);



function useUpdatePurchaseReceipt() {
  var _App$useApp = antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_1__.useRequest)(_services_stock_purchaseReceipt__WEBPACK_IMPORTED_MODULE_0__/* .updatePurchaseReceipt */ .cV, {
    manual: true,
    onSuccess: function onSuccess(data, params) {
      message.success(formatMessage({
        id: 'common.success'
      }));
    },
    onError: function onError(error) {
      message.error(error.message);
    }
  });
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85566
`)},23079:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Dr: function() { return /* binding */ updateCustomerV3; },
/* harmony export */   G5: function() { return /* binding */ getCustomerInventoryVouchers; },
/* harmony export */   O4: function() { return /* binding */ getCustomerTotalPaymentReport; },
/* harmony export */   Qg: function() { return /* binding */ createCustomerV3; },
/* harmony export */   Xi: function() { return /* binding */ deleteCustomerV3; },
/* harmony export */   iZ: function() { return /* binding */ getCustomerTotalPaymentDetailReport; },
/* harmony export */   jJ: function() { return /* binding */ getCustomerDetailItemReport; },
/* harmony export */   o1: function() { return /* binding */ getCustomerV3; },
/* harmony export */   y$: function() { return /* binding */ getDetailsCustomerV3; }
/* harmony export */ });
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(467);





var getCustomerV3 = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/customer'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCustomerV3(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createCustomerV3 = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/customer'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createCustomerV3(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var updateCustomerV3 = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/customer'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function updateCustomerV3(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteCustomerV3 = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/customer'), {
            method: 'PUT',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({}, data), {}, {
              is_deleted: 1
            })
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteCustomerV3(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var getDetailsCustomerV3 = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee5(params) {
    var res, data;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return getCustomerV3({
            page: 1,
            size: 1,
            filters: [['Customer', 'name', '=', params.name]]
          });
        case 2:
          res = _context5.sent;
          data = res.data[0];
          if (data) {
            _context5.next = 6;
            break;
          }
          throw new Error('Not found');
        case 6:
          return _context5.abrupt("return", {
            data: data
          });
        case 7:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getDetailsCustomerV3(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var getCustomerInventoryVouchers = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee6(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/customer/inventory-vouchers'), {
            params: params
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function getCustomerInventoryVouchers(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCustomerTotalPaymentReport = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/stock-v3/payment/report/customer/total'), {
            params: params
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCustomerTotalPaymentReport(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var getCustomerTotalPaymentDetailReport = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee8(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/stock-v3/payment/report/customer/detail'), {
            params: params
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function getCustomerTotalPaymentDetailReport(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var getCustomerDetailItemReport = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_2___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().mark(function _callee9(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_1___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_3__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_4__/* .generateAPIPath */ .rH)('api/v2/stock-v3/payment/report/customer/detail/item'), {
            params: params
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", {
            data: res.result
          });
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getCustomerDetailItemReport(_x9) {
    return _ref9.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///23079
`)}}]);
