"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2454],{97321:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ components_CheckCard; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/avatar/index.js + 4 modules
var es_avatar = __webpack_require__(7134);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-card/es/components/Actions/index.js + 1 modules
var Actions = __webpack_require__(80171);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/omit.js/es/index.js
var es = __webpack_require__(97435);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/CheckCard/Group.js




var _excluded = ["prefixCls", "className", "style", "options", "loading", "multiple", "bordered", "onChange"];








var CardLoading = function CardLoading(_ref) {
  var prefixCls = _ref.prefixCls;
  var loadingBlockClass = "".concat(prefixCls, "-loading-block");
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "".concat(prefixCls, "-loading-content"),
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: {
        xs: 8,
        sm: 8,
        md: 8,
        lg: 12
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 22,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      })
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 8,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 14,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 6,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 16,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 13,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 9,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
      gutter: 8,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 4,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 3,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        span: 14,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: loadingBlockClass
        })
      })]
    })]
  });
};
var CheckCardGroupConnext = /*#__PURE__*/(0,react.createContext)(null);
var CheckCardGroup = function CheckCardGroup(props) {
  var customizePrefixCls = props.prefixCls,
    className = props.className,
    style = props.style,
    _props$options = props.options,
    options = _props$options === void 0 ? [] : _props$options,
    _props$loading = props.loading,
    loading = _props$loading === void 0 ? false : _props$loading,
    _props$multiple = props.multiple,
    multiple = _props$multiple === void 0 ? false : _props$multiple,
    _props$bordered = props.bordered,
    bordered = _props$bordered === void 0 ? true : _props$bordered,
    onChange = props.onChange,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var antdContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext);
  var getOptions = (0,react.useCallback)(function () {
    return options === null || options === void 0 ? void 0 : options.map(function (option) {
      if (typeof option === 'string') {
        return {
          title: option,
          value: option
        };
      }
      return option;
    });
  }, [options]);
  var prefixCls = antdContext.getPrefixCls('pro-checkcard', customizePrefixCls);
  var groupPrefixCls = "".concat(prefixCls, "-group");
  var domProps = (0,es/* default */.Z)(restProps, ['children', 'defaultValue', 'value', 'disabled', 'size']);
  var _useMountMergeState = (0,useMergedState/* default */.Z)(props.defaultValue, {
      value: props.value,
      onChange: props.onChange
    }),
    _useMountMergeState2 = (0,slicedToArray/* default */.Z)(_useMountMergeState, 2),
    stateValue = _useMountMergeState2[0],
    setStateValue = _useMountMergeState2[1];
  var registerValueMap = (0,react.useRef)(new Map());
  var registerValue = function registerValue(value) {
    var _registerValueMap$cur;
    (_registerValueMap$cur = registerValueMap.current) === null || _registerValueMap$cur === void 0 || _registerValueMap$cur.set(value, true);
  };
  var cancelValue = function cancelValue(value) {
    var _registerValueMap$cur2;
    (_registerValueMap$cur2 = registerValueMap.current) === null || _registerValueMap$cur2 === void 0 || _registerValueMap$cur2.delete(value);
  };
  var toggleOption = function toggleOption(option) {
    if (!multiple) {
      var changeValue;
      changeValue = stateValue;
      // \u5355\u9009\u6A21\u5F0F
      if (changeValue === option.value) {
        changeValue = undefined;
      } else {
        changeValue = option.value;
      }
      setStateValue === null || setStateValue === void 0 || setStateValue(changeValue);
    }
    if (multiple) {
      var _changeValue2;
      var _changeValue = [];
      var stateValues = stateValue;
      var hasOption = stateValues === null || stateValues === void 0 ? void 0 : stateValues.includes(option.value);
      _changeValue = (0,toConsumableArray/* default */.Z)(stateValues || []);
      if (!hasOption) {
        _changeValue.push(option.value);
      }
      if (hasOption) {
        _changeValue = _changeValue.filter(function (itemValue) {
          return itemValue !== option.value;
        });
      }
      var newOptions = getOptions();
      var newValue = (_changeValue2 = _changeValue) === null || _changeValue2 === void 0 || (_changeValue2 = _changeValue2.filter(function (val) {
        return registerValueMap.current.has(val);
      })) === null || _changeValue2 === void 0 ? void 0 : _changeValue2.sort(function (a, b) {
        var indexA = newOptions.findIndex(function (opt) {
          return opt.value === a;
        });
        var indexB = newOptions.findIndex(function (opt) {
          return opt.value === b;
        });
        return indexA - indexB;
      });
      setStateValue(newValue);
    }
  };
  var children = (0,react.useMemo)(function () {
    if (loading) {
      return new Array(options.length || react.Children.toArray(props.children).length || 1).fill(0)
      // eslint-disable-next-line react/no-array-index-key
      .map(function (_, index) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(components_CheckCard, {
          loading: true
        }, index);
      });
    }
    if (options && options.length > 0) {
      var optionValue = stateValue;
      return getOptions().map(function (option) {
        var _option$size;
        return /*#__PURE__*/(0,jsx_runtime.jsx)(components_CheckCard, {
          disabled: option.disabled,
          size: (_option$size = option.size) !== null && _option$size !== void 0 ? _option$size : props.size,
          value: option.value,
          checked: multiple ? optionValue === null || optionValue === void 0 ? void 0 : optionValue.includes(option.value) : optionValue === option.value,
          onChange: option.onChange,
          title: option.title,
          avatar: option.avatar,
          description: option.description,
          cover: option.cover
        }, option.value.toString());
      });
    }
    return props.children;
  }, [getOptions, loading, multiple, options, props.children, props.size, stateValue]);
  var classString = classnames_default()(groupPrefixCls, className);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(CheckCardGroupConnext.Provider, {
    value: {
      toggleOption: toggleOption,
      bordered: bordered,
      value: stateValue,
      disabled: props.disabled,
      size: props.size,
      loading: props.loading,
      multiple: props.multiple,
      // https://github.com/ant-design/ant-design/issues/16376
      registerValue: registerValue,
      cancelValue: cancelValue
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      className: classString,
      style: style
    }, domProps), {}, {
      children: children
    }))
  });
};
/* harmony default export */ var Group = (CheckCardGroup);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/CheckCard/style.js




var proCheckCardActive = function proCheckCardActive(token) {
  return {
    backgroundColor: token.colorPrimaryBg,
    borderColor: token.colorPrimary
  };
};
var proCheckCardDisabled = function proCheckCardDisabled(token) {
  return (0,defineProperty/* default */.Z)({
    backgroundColor: token.colorBgContainerDisabled,
    borderColor: token.colorBorder,
    cursor: 'not-allowed'
  }, token.componentCls, {
    '&-description': {
      color: token.colorTextDisabled
    },
    '&-title': {
      color: token.colorTextDisabled
    },
    '&-avatar': {
      opacity: '0.25'
    }
  });
};
var cardLoading = new cssinjs_es/* Keyframes */.E4('card-loading', {
  '0%': {
    backgroundPosition: '0 50%'
  },
  '50%': {
    backgroundPosition: '100% 50%'
  },
  '100%': {
    backgroundPosition: '0 50%'
  }
});
var genProStyle = function genProStyle(token) {
  var _token$componentCls;
  return (0,defineProperty/* default */.Z)({}, token.componentCls, (_token$componentCls = {
    position: 'relative',
    display: 'inline-block',
    width: '320px',
    marginInlineEnd: '16px',
    marginBlockEnd: '16px',
    color: token.colorText,
    fontSize: token.fontSize,
    lineHeight: token.lineHeight,
    verticalAlign: 'top',
    backgroundColor: token.colorBgContainer,
    borderRadius: token.borderRadius,
    overflow: 'auto',
    cursor: 'pointer',
    transition: "all 0.3s",
    '&:last-child': {
      marginInlineEnd: 0
    },
    '& + &': {
      marginInlineStart: '0 !important'
    },
    '&-bordered': {
      border: "".concat(token.lineWidth, "px solid ").concat(token.colorBorder)
    },
    '&-group': {
      display: 'inline-block'
    }
  }, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_token$componentCls, "".concat(token.componentCls, "-loading"), {
    overflow: 'hidden',
    userSelect: 'none',
    '&-content': (0,defineProperty/* default */.Z)({
      paddingInline: token.padding,
      paddingBlock: token.paddingSM,
      p: {
        marginBlock: 0,
        marginInline: 0
      }
    }, "".concat(token.componentCls, "-loading-block"), {
      height: '14px',
      marginBlock: '4px',
      background: "linear-gradient(90deg, rgba(54, 61, 64, 0.2), rgba(54, 61, 64, 0.4), rgba(54, 61, 64, 0.2))",
      animationName: cardLoading,
      animationDuration: '1.4s',
      animationTimingFunction: 'ease',
      animationIterationCount: 'infinite'
    })
  }), '&:focus', proCheckCardActive(token)), '&-checked', (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, proCheckCardActive(token)), {}, {
    '&:after': {
      position: 'absolute',
      insetBlockStart: 2,
      insetInlineEnd: 2,
      width: 0,
      height: 0,
      border: "".concat(token.borderRadius + 4, "px solid ").concat(token.colorPrimary),
      borderBlockEnd: "".concat(token.borderRadius + 4, "px  solid transparent"),
      borderInlineStart: "".concat(token.borderRadius + 4, "px  solid transparent"),
      borderStartEndRadius: "".concat(token.borderRadius, "px"),
      content: "''"
    }
  })), '&-disabled', proCheckCardDisabled(token)), '&[disabled]', proCheckCardDisabled(token)), '&-checked&-disabled', {
    '&:after': {
      position: 'absolute',
      insetBlockStart: 2,
      insetInlineEnd: 2,
      width: 0,
      height: 0,
      border: "".concat(token.borderRadius + 4, "px solid ").concat(token.colorTextDisabled),
      borderBlockEnd: "".concat(token.borderRadius + 4, "px  solid transparent"),
      borderInlineStart: "".concat(token.borderRadius + 4, "px  solid transparent"),
      borderStartEndRadius: "".concat(token.borderRadius, "px"),
      content: "''"
    }
  }), '&-lg', {
    width: 440
  }), '&-sm', {
    width: 212
  }), '&-cover', {
    paddingInline: token.paddingXXS,
    paddingBlock: token.paddingXXS,
    img: {
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      borderRadius: token.borderRadius
    }
  }), '&-content', {
    display: 'flex',
    paddingInline: token.paddingSM,
    paddingBlock: token.padding
  }), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_token$componentCls, '&-body', {
    paddingInline: token.paddingSM,
    paddingBlock: token.padding
  }), '&-avatar-header', {
    display: 'flex',
    alignItems: 'center'
  }), '&-avatar', {
    paddingInlineEnd: 8
  }), '&-detail', {
    overflow: 'hidden',
    width: '100%',
    '> div:not(:last-child)': {
      marginBlockEnd: 4
    }
  }), '&-header', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    lineHeight: token.lineHeight,
    '&-left': {
      display: 'flex',
      alignItems: 'center',
      gap: token.sizeSM
    }
  }), '&-title', {
    overflow: 'hidden',
    color: token.colorTextHeading,
    fontWeight: '500',
    fontSize: token.fontSize,
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between'
  }), '&-description', {
    color: token.colorTextSecondary
  }), "&:not(".concat(token.componentCls, "-disabled)"), {
    '&:hover': {
      borderColor: token.colorPrimary
    }
  })));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('CheckCard', function (token) {
    var proListToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proListToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-card/es/components/CheckCard/index.js




var CheckCard_excluded = ["prefixCls", "className", "avatar", "title", "description", "cover", "extra", "style"];









var CheckCard = function CheckCard(props) {
  var _useMountMergeState = (0,useMergedState/* default */.Z)(props.defaultChecked || false, {
      value: props.checked,
      onChange: props.onChange
    }),
    _useMountMergeState2 = (0,slicedToArray/* default */.Z)(_useMountMergeState, 2),
    stateChecked = _useMountMergeState2[0],
    setStateChecked = _useMountMergeState2[1];
  var checkCardGroup = (0,react.useContext)(CheckCardGroupConnext);
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var handleClick = function handleClick(e) {
    var _props$onClick, _checkCardGroup$toggl;
    props === null || props === void 0 || (_props$onClick = props.onClick) === null || _props$onClick === void 0 || _props$onClick.call(props, e);
    var newChecked = !stateChecked;
    checkCardGroup === null || checkCardGroup === void 0 || (_checkCardGroup$toggl = checkCardGroup.toggleOption) === null || _checkCardGroup$toggl === void 0 || _checkCardGroup$toggl.call(checkCardGroup, {
      value: props.value
    });
    setStateChecked === null || setStateChecked === void 0 || setStateChecked(newChecked);
  };

  // small => sm large => lg
  var getSizeCls = function getSizeCls(size) {
    if (size === 'large') return 'lg';
    if (size === 'small') return 'sm';
    return '';
  };
  (0,react.useEffect)(function () {
    var _checkCardGroup$regis;
    checkCardGroup === null || checkCardGroup === void 0 || (_checkCardGroup$regis = checkCardGroup.registerValue) === null || _checkCardGroup$regis === void 0 || _checkCardGroup$regis.call(checkCardGroup, props.value);
    return function () {
      var _checkCardGroup$cance;
      return checkCardGroup === null || checkCardGroup === void 0 || (_checkCardGroup$cance = checkCardGroup.cancelValue) === null || _checkCardGroup$cance === void 0 ? void 0 : _checkCardGroup$cance.call(checkCardGroup, props.value);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.value]);

  /**
   * \u5934\u50CF\u81EA\u5B9A\u4E49
   *
   * @param prefixCls
   * @param cover
   * @returns
   */
  var renderCover = function renderCover(prefixCls, cover) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-cover"),
      children: typeof cover === 'string' ? /*#__PURE__*/(0,jsx_runtime.jsx)("img", {
        src: cover,
        alt: "checkcard"
      }) : cover
    });
  };
  var customizePrefixCls = props.prefixCls,
    className = props.className,
    avatar = props.avatar,
    title = props.title,
    description = props.description,
    cover = props.cover,
    extra = props.extra,
    _props$style = props.style,
    style = _props$style === void 0 ? {} : _props$style,
    others = (0,objectWithoutProperties/* default */.Z)(props, CheckCard_excluded);
  var checkCardProps = (0,objectSpread2/* default */.Z)({}, others);
  var prefixCls = getPrefixCls('pro-checkcard', customizePrefixCls);
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  checkCardProps.checked = stateChecked;
  var multiple = false;
  if (checkCardGroup) {
    var _checkCardGroup$value;
    // \u53D7\u7EC4\u63A7\u5236\u6A21\u5F0F
    checkCardProps.disabled = props.disabled || checkCardGroup.disabled;
    checkCardProps.loading = props.loading || checkCardGroup.loading;
    checkCardProps.bordered = props.bordered || checkCardGroup.bordered;
    multiple = checkCardGroup.multiple;
    var isChecked = checkCardGroup.multiple ? (_checkCardGroup$value = checkCardGroup.value) === null || _checkCardGroup$value === void 0 ? void 0 : _checkCardGroup$value.includes(props.value) : checkCardGroup.value === props.value;

    // loading\u65F6check\u4E3Afalse
    checkCardProps.checked = checkCardProps.loading ? false : isChecked;
    checkCardProps.size = props.size || checkCardGroup.size;
  }
  var _checkCardProps$disab = checkCardProps.disabled,
    disabled = _checkCardProps$disab === void 0 ? false : _checkCardProps$disab,
    size = checkCardProps.size,
    cardLoading = checkCardProps.loading,
    _checkCardProps$borde = checkCardProps.bordered,
    bordered = _checkCardProps$borde === void 0 ? true : _checkCardProps$borde,
    checked = checkCardProps.checked;
  var sizeCls = getSizeCls(size);
  var classString = classnames_default()(prefixCls, className, hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-loading"), cardLoading), "".concat(prefixCls, "-").concat(sizeCls), sizeCls), "".concat(prefixCls, "-checked"), checked), "".concat(prefixCls, "-multiple"), multiple), "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-bordered"), bordered), "".concat(prefixCls, "-ghost"), props.ghost));
  var metaDom = (0,react.useMemo)(function () {
    if (cardLoading) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(CardLoading, {
        prefixCls: prefixCls || ''
      });
    }
    if (cover) {
      return renderCover(prefixCls || '', cover);
    }
    var avatarDom = avatar ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-avatar ").concat(hashId).trim(),
      children: typeof avatar === 'string' ? /*#__PURE__*/(0,jsx_runtime.jsx)(es_avatar/* default */.C, {
        size: 48,
        shape: "square",
        src: avatar
      }) : avatar
    }) : null;
    var headerDom = (title !== null && title !== void 0 ? title : extra) != null && /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: "".concat(prefixCls, "-header ").concat(hashId).trim(),
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(prefixCls, "-header-left ").concat(hashId).trim(),
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefixCls, "-title ").concat(hashId).trim(),
          children: title
        }), props.subTitle ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefixCls, "-subTitle ").concat(hashId).trim(),
          children: props.subTitle
        }) : null]
      }), extra && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
        className: "".concat(prefixCls, "-extra ").concat(hashId).trim(),
        children: extra
      })]
    });
    var descriptionDom = description ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-description ").concat(hashId).trim(),
      children: description
    }) : null;
    var metaClass = classnames_default()("".concat(prefixCls, "-content"), hashId, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-avatar-header"), avatarDom && headerDom && !descriptionDom));
    return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      className: metaClass,
      children: [avatarDom, headerDom || descriptionDom ? /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(prefixCls, "-detail ").concat(hashId).trim(),
        children: [headerDom, descriptionDom]
      }) : null]
    });
  }, [avatar, cardLoading, cover, description, extra, hashId, prefixCls, props.subTitle, title]);
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: classString,
    style: style,
    onClick: function onClick(e) {
      if (!cardLoading && !disabled) {
        handleClick(e);
      }
    },
    onMouseEnter: props.onMouseEnter,
    children: [metaDom, props.children ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: classnames_default()("".concat(prefixCls, "-body")),
      style: props.bodyStyle,
      children: props.children
    }) : null, props.actions ? /*#__PURE__*/(0,jsx_runtime.jsx)(Actions/* default */.Z, {
      actions: props.actions,
      prefixCls: prefixCls
    }) : null]
  }));
};
CheckCard.Group = Group;
/* harmony default export */ var components_CheckCard = (CheckCard);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97321
`)},64176:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Rs: function() { return /* binding */ ProList; }
});

// UNUSED EXPORTS: BaseProList, default

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/index.js + 7 modules
var es = __webpack_require__(89451);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-table/es/index.js









/* harmony default export */ var pro_table_es = (Table/* default */.Z);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/antd/es/version/index.js + 1 modules
var version = __webpack_require__(67159);
// EXTERNAL MODULE: ./node_modules/antd/es/list/index.js + 3 modules
var list = __webpack_require__(2487);
// EXTERNAL MODULE: ./node_modules/antd/es/table/hooks/useLazyKVMap.js
var useLazyKVMap = __webpack_require__(84164);
// EXTERNAL MODULE: ./node_modules/antd/es/table/hooks/usePagination.js
var usePagination = __webpack_require__(58448);
// EXTERNAL MODULE: ./node_modules/antd/es/table/hooks/useSelection.js + 1 modules
var useSelection = __webpack_require__(33275);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/rc-util/es/utils/get.js
function get(entity, path) {
  var current = entity;

  for (var i = 0; i < path.length; i += 1) {
    if (current === null || current === undefined) {
      return undefined;
    }

    current = current[path[i]];
  }

  return current;
}
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/RightOutlined.js
var asn_RightOutlined = __webpack_require__(50756);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/conversion.js
var conversion = __webpack_require__(86500);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/format-input.js
var format_input = __webpack_require__(1350);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/colors/es/generate.js

var hueStep = 2; // \u8272\u76F8\u9636\u68AF
var saturationStep = 0.16; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var saturationStep2 = 0.05; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var brightnessStep1 = 0.05; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var brightnessStep2 = 0.15; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var lightColorCount = 5; // \u6D45\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0A
var darkColorCount = 4; // \u6DF1\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0B
// \u6697\u8272\u4E3B\u9898\u989C\u8272\u6620\u5C04\u5173\u7CFB\u8868
var darkColorMap = [{
  index: 7,
  opacity: 0.15
}, {
  index: 6,
  opacity: 0.25
}, {
  index: 5,
  opacity: 0.3
}, {
  index: 5,
  opacity: 0.45
}, {
  index: 5,
  opacity: 0.65
}, {
  index: 5,
  opacity: 0.85
}, {
  index: 4,
  opacity: 0.9
}, {
  index: 3,
  opacity: 0.95
}, {
  index: 2,
  opacity: 0.97
}, {
  index: 1,
  opacity: 0.98
}];
// Wrapper function ported from TinyColor.prototype.toHsv
// Keep it here because of \`hsv.h * 360\`
function toHsv(_ref) {
  var r = _ref.r,
    g = _ref.g,
    b = _ref.b;
  var hsv = (0,conversion/* rgbToHsv */.py)(r, g, b);
  return {
    h: hsv.h * 360,
    s: hsv.s,
    v: hsv.v
  };
}

// Wrapper function ported from TinyColor.prototype.toHexString
// Keep it here because of the prefix \`#\`
function toHex(_ref2) {
  var r = _ref2.r,
    g = _ref2.g,
    b = _ref2.b;
  return "#".concat((0,conversion/* rgbToHex */.vq)(r, g, b, false));
}

// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.
// Amount in range [0, 1]
// Assume color1 & color2 has no alpha, since the following src code did so.
function mix(rgb1, rgb2, amount) {
  var p = amount / 100;
  var rgb = {
    r: (rgb2.r - rgb1.r) * p + rgb1.r,
    g: (rgb2.g - rgb1.g) * p + rgb1.g,
    b: (rgb2.b - rgb1.b) * p + rgb1.b
  };
  return rgb;
}
function getHue(hsv, i, light) {
  var hue;
  // \u6839\u636E\u8272\u76F8\u4E0D\u540C\uFF0C\u8272\u76F8\u8F6C\u5411\u4E0D\u540C
  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;
  } else {
    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return hue;
}
function getSaturation(hsv, i, light) {
  // grey color don't change saturation
  if (hsv.h === 0 && hsv.s === 0) {
    return hsv.s;
  }
  var saturation;
  if (light) {
    saturation = hsv.s - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = hsv.s + saturationStep;
  } else {
    saturation = hsv.s + saturationStep2 * i;
  }
  // \u8FB9\u754C\u503C\u4FEE\u6B63
  if (saturation > 1) {
    saturation = 1;
  }
  // \u7B2C\u4E00\u683C\u7684 s \u9650\u5236\u5728 0.06-0.1 \u4E4B\u95F4
  if (light && i === lightColorCount && saturation > 0.1) {
    saturation = 0.1;
  }
  if (saturation < 0.06) {
    saturation = 0.06;
  }
  return Number(saturation.toFixed(2));
}
function getValue(hsv, i, light) {
  var value;
  if (light) {
    value = hsv.v + brightnessStep1 * i;
  } else {
    value = hsv.v - brightnessStep2 * i;
  }
  if (value > 1) {
    value = 1;
  }
  return Number(value.toFixed(2));
}
function generate(color) {
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var patterns = [];
  var pColor = (0,format_input/* inputToRGB */.uA)(color);
  for (var i = lightColorCount; i > 0; i -= 1) {
    var hsv = toHsv(pColor);
    var colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(hsv, i, true),
      s: getSaturation(hsv, i, true),
      v: getValue(hsv, i, true)
    }));
    patterns.push(colorString);
  }
  patterns.push(toHex(pColor));
  for (var _i = 1; _i <= darkColorCount; _i += 1) {
    var _hsv = toHsv(pColor);
    var _colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(_hsv, _i),
      s: getSaturation(_hsv, _i),
      v: getValue(_hsv, _i)
    }));
    patterns.push(_colorString);
  }

  // dark theme patterns
  if (opts.theme === 'dark') {
    return darkColorMap.map(function (_ref3) {
      var index = _ref3.index,
        opacity = _ref3.opacity;
      var darkColorString = toHex(mix((0,format_input/* inputToRGB */.uA)(opts.backgroundColor || '#141414'), (0,format_input/* inputToRGB */.uA)(patterns[index]), opacity * 100));
      return darkColorString;
    });
  }
  return patterns;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/colors/es/index.js

var presetPrimaryColors = {
  red: '#F5222D',
  volcano: '#FA541C',
  orange: '#FA8C16',
  gold: '#FAAD14',
  yellow: '#FADB14',
  lime: '#A0D911',
  green: '#52C41A',
  cyan: '#13C2C2',
  blue: '#1677FF',
  geekblue: '#2F54EB',
  purple: '#722ED1',
  magenta: '#EB2F96',
  grey: '#666666'
};
var presetPalettes = {};
var presetDarkPalettes = {};
Object.keys(presetPrimaryColors).forEach(function (key) {
  presetPalettes[key] = generate(presetPrimaryColors[key]);
  presetPalettes[key].primary = presetPalettes[key][5];

  // dark presetPalettes
  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {
    theme: 'dark',
    backgroundColor: '#141414'
  });
  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];
});
var red = presetPalettes.red;
var volcano = presetPalettes.volcano;
var gold = presetPalettes.gold;
var orange = presetPalettes.orange;
var yellow = presetPalettes.yellow;
var lime = presetPalettes.lime;
var green = presetPalettes.green;
var cyan = presetPalettes.cyan;
var blue = presetPalettes.blue;
var geekblue = presetPalettes.geekblue;
var purple = presetPalettes.purple;
var magenta = presetPalettes.magenta;
var grey = presetPalettes.grey;
var gray = presetPalettes.grey;

;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/components/Context.js

var IconContext = /*#__PURE__*/(0,react.createContext)({});
/* harmony default export */ var Context = (IconContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/node_modules/rc-util/es/Dom/canUseDom.js
function canUseDom() {
  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/node_modules/rc-util/es/Dom/contains.js
function contains(root, n) {
  if (!root) {
    return false;
  }

  // Use native if support
  if (root.contains) {
    return root.contains(n);
  }

  // \`document.contains\` not support with IE11
  var node = n;
  while (node) {
    if (node === root) {
      return true;
    }
    node = node.parentNode;
  }
  return false;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/node_modules/rc-util/es/Dom/dynamicCSS.js



var APPEND_ORDER = 'data-rc-order';
var APPEND_PRIORITY = 'data-rc-priority';
var MARK_KEY = "rc-util-key";
var containerCache = new Map();
function getMark() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    mark = _ref.mark;
  if (mark) {
    return mark.startsWith('data-') ? mark : "data-".concat(mark);
  }
  return MARK_KEY;
}
function getContainer(option) {
  if (option.attachTo) {
    return option.attachTo;
  }
  var head = document.querySelector('head');
  return head || document.body;
}
function getOrder(prepend) {
  if (prepend === 'queue') {
    return 'prependQueue';
  }
  return prepend ? 'prepend' : 'append';
}

/**
 * Find style which inject by rc-util
 */
function findStyles(container) {
  return Array.from((containerCache.get(container) || container).children).filter(function (node) {
    return node.tagName === 'STYLE';
  });
}
function injectCSS(css) {
  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  if (!canUseDom()) {
    return null;
  }
  var csp = option.csp,
    prepend = option.prepend,
    _option$priority = option.priority,
    priority = _option$priority === void 0 ? 0 : _option$priority;
  var mergedOrder = getOrder(prepend);
  var isPrependQueue = mergedOrder === 'prependQueue';
  var styleNode = document.createElement('style');
  styleNode.setAttribute(APPEND_ORDER, mergedOrder);
  if (isPrependQueue && priority) {
    styleNode.setAttribute(APPEND_PRIORITY, "".concat(priority));
  }
  if (csp !== null && csp !== void 0 && csp.nonce) {
    styleNode.nonce = csp === null || csp === void 0 ? void 0 : csp.nonce;
  }
  styleNode.innerHTML = css;
  var container = getContainer(option);
  var firstChild = container.firstChild;
  if (prepend) {
    // If is queue \`prepend\`, it will prepend first style and then append rest style
    if (isPrependQueue) {
      var existStyle = (option.styles || findStyles(container)).filter(function (node) {
        // Ignore style which not injected by rc-util with prepend
        if (!['prepend', 'prependQueue'].includes(node.getAttribute(APPEND_ORDER))) {
          return false;
        }

        // Ignore style which priority less then new style
        var nodePriority = Number(node.getAttribute(APPEND_PRIORITY) || 0);
        return priority >= nodePriority;
      });
      if (existStyle.length) {
        container.insertBefore(styleNode, existStyle[existStyle.length - 1].nextSibling);
        return styleNode;
      }
    }

    // Use \`insertBefore\` as \`prepend\`
    container.insertBefore(styleNode, firstChild);
  } else {
    container.appendChild(styleNode);
  }
  return styleNode;
}
function findExistNode(key) {
  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var container = getContainer(option);
  return (option.styles || findStyles(container)).find(function (node) {
    return node.getAttribute(getMark(option)) === key;
  });
}
function removeCSS(key) {
  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var existNode = findExistNode(key, option);
  if (existNode) {
    var container = getContainer(option);
    container.removeChild(existNode);
  }
}

/**
 * qiankun will inject \`appendChild\` to insert into other
 */
function syncRealContainer(container, option) {
  var cachedRealContainer = containerCache.get(container);

  // Find real container when not cached or cached container removed
  if (!cachedRealContainer || !contains(document, cachedRealContainer)) {
    var placeholderStyle = injectCSS('', option);
    var parentNode = placeholderStyle.parentNode;
    containerCache.set(container, parentNode);
    container.removeChild(placeholderStyle);
  }
}

/**
 * manually clear container cache to avoid global cache in unit testes
 */
function clearContainerCache() {
  containerCache.clear();
}
function updateCSS(css, key) {
  var originOption = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var container = getContainer(originOption);
  var styles = findStyles(container);
  var option = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, originOption), {}, {
    styles: styles
  });

  // Sync real parent
  syncRealContainer(container, option);
  var existNode = findExistNode(key, option);
  if (existNode) {
    var _option$csp, _option$csp2;
    if ((_option$csp = option.csp) !== null && _option$csp !== void 0 && _option$csp.nonce && existNode.nonce !== ((_option$csp2 = option.csp) === null || _option$csp2 === void 0 ? void 0 : _option$csp2.nonce)) {
      var _option$csp3;
      existNode.nonce = (_option$csp3 = option.csp) === null || _option$csp3 === void 0 ? void 0 : _option$csp3.nonce;
    }
    if (existNode.innerHTML !== css) {
      existNode.innerHTML = css;
    }
    return existNode;
  }
  var newNode = injectCSS(css, option);
  newNode.setAttribute(getMark(option), key);
  return newNode;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/node_modules/rc-util/es/Dom/shadow.js
function getRoot(ele) {
  var _ele$getRootNode;
  return ele === null || ele === void 0 || (_ele$getRootNode = ele.getRootNode) === null || _ele$getRootNode === void 0 ? void 0 : _ele$getRootNode.call(ele);
}

/**
 * Check if is in shadowRoot
 */
function inShadow(ele) {
  return getRoot(ele) instanceof ShadowRoot;
}

/**
 * Return shadowRoot if possible
 */
function getShadowRoot(ele) {
  return inShadow(ele) ? getRoot(ele) : null;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/node_modules/rc-util/es/warning.js
/* eslint-disable no-console */
var warned = {};
var preWarningFns = [];

/**
 * Pre warning enable you to parse content before console.error.
 * Modify to null will prevent warning.
 */
var preMessage = function preMessage(fn) {
  preWarningFns.push(fn);
};

/**
 * Warning if condition not match.
 * @param valid Condition
 * @param message Warning message
 * @example
 * \`\`\`js
 * warning(false, 'some error'); // print some error
 * warning(true, 'some error'); // print nothing
 * warning(1 === 2, 'some error'); // print some error
 * \`\`\`
 */
function warning(valid, message) {
  if (false) { var finalMessage; }
}

/** @see Similar to {@link warning} */
function note(valid, message) {
  if (false) { var finalMessage; }
}
function resetWarned() {
  warned = {};
}
function call(method, valid, message) {
  if (!valid && !warned[message]) {
    method(false, message);
    warned[message] = true;
  }
}

/** @see Same as {@link warning}, but only warn once for the same message */
function warningOnce(valid, message) {
  call(warning, valid, message);
}

/** @see Same as {@link warning}, but only warn once for the same message */
function noteOnce(valid, message) {
  call(note, valid, message);
}
warningOnce.preMessage = preMessage;
warningOnce.resetWarned = resetWarned;
warningOnce.noteOnce = noteOnce;
/* harmony default export */ var es_warning = (warningOnce);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/utils.js








function camelCase(input) {
  return input.replace(/-(.)/g, function (match, g) {
    return g.toUpperCase();
  });
}
function utils_warning(valid, message) {
  es_warning(valid, "[@ant-design/icons] ".concat(message));
}
function isIconDefinition(target) {
  return (0,esm_typeof/* default */.Z)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0,esm_typeof/* default */.Z)(target.icon) === 'object' || typeof target.icon === 'function');
}
function normalizeAttrs() {
  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.keys(attrs).reduce(function (acc, key) {
    var val = attrs[key];
    switch (key) {
      case 'class':
        acc.className = val;
        delete acc.class;
        break;
      default:
        delete acc[key];
        acc[camelCase(key)] = val;
    }
    return acc;
  }, {});
}
function utils_generate(node, key, rootProps) {
  if (!rootProps) {
    return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)({
      key: key
    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {
      return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
    }));
  }
  return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    key: key
  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {
    return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
  }));
}
function getSecondaryColor(primaryColor) {
  // choose the second color
  return generate(primaryColor)[0];
}
function normalizeTwoToneColors(twoToneColor) {
  if (!twoToneColor) {
    return [];
  }
  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];
}

// These props make sure that the SVG behaviours like general text.
// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4
var svgBaseProps = {
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  'aria-hidden': 'true',
  focusable: 'false'
};
var iconStyles = "\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n";
var useInsertStyles = function useInsertStyles(eleRef) {
  var _useContext = (0,react.useContext)(Context),
    csp = _useContext.csp,
    prefixCls = _useContext.prefixCls;
  var mergedStyleStr = iconStyles;
  if (prefixCls) {
    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);
  }
  (0,react.useEffect)(function () {
    var ele = eleRef.current;
    var shadowRoot = getShadowRoot(ele);
    updateCSS(mergedStyleStr, '@ant-design-icons', {
      prepend: true,
      csp: csp,
      attachTo: shadowRoot
    });
  }, []);
};
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/components/IconBase.js


var _excluded = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];


var twoToneColorPalette = {
  primaryColor: '#333',
  secondaryColor: '#E6E6E6',
  calculated: false
};
function setTwoToneColors(_ref) {
  var primaryColor = _ref.primaryColor,
    secondaryColor = _ref.secondaryColor;
  twoToneColorPalette.primaryColor = primaryColor;
  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);
  twoToneColorPalette.calculated = !!secondaryColor;
}
function getTwoToneColors() {
  return (0,objectSpread2/* default */.Z)({}, twoToneColorPalette);
}
var IconBase = function IconBase(props) {
  var icon = props.icon,
    className = props.className,
    onClick = props.onClick,
    style = props.style,
    primaryColor = props.primaryColor,
    secondaryColor = props.secondaryColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var svgRef = react.useRef();
  var colors = twoToneColorPalette;
  if (primaryColor) {
    colors = {
      primaryColor: primaryColor,
      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)
    };
  }
  useInsertStyles(svgRef);
  utils_warning(isIconDefinition(icon), "icon should be icon definiton, but got ".concat(icon));
  if (!isIconDefinition(icon)) {
    return null;
  }
  var target = icon;
  if (target && typeof target.icon === 'function') {
    target = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, target), {}, {
      icon: target.icon(colors.primaryColor, colors.secondaryColor)
    });
  }
  return utils_generate(target.icon, "svg-".concat(target.name), (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: className,
    onClick: onClick,
    style: style,
    'data-icon': target.name,
    width: '1em',
    height: '1em',
    fill: 'currentColor',
    'aria-hidden': 'true'
  }, restProps), {}, {
    ref: svgRef
  }));
};
IconBase.displayName = 'IconReact';
IconBase.getTwoToneColors = getTwoToneColors;
IconBase.setTwoToneColors = setTwoToneColors;
/* harmony default export */ var components_IconBase = (IconBase);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js



function setTwoToneColor(twoToneColor) {
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return components_IconBase.setTwoToneColors({
    primaryColor: primaryColor,
    secondaryColor: secondaryColor
  });
}
function getTwoToneColor() {
  var colors = components_IconBase.getTwoToneColors();
  if (!colors.calculated) {
    return colors.primaryColor;
  }
  return [colors.primaryColor, colors.secondaryColor];
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/components/AntdIcon.js
'use client';





var AntdIcon_excluded = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];







// Initial setting
// should move it to antd main repo?
setTwoToneColor(blue.primary);

// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720

var Icon = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var className = props.className,
    icon = props.icon,
    spin = props.spin,
    rotate = props.rotate,
    tabIndex = props.tabIndex,
    onClick = props.onClick,
    twoToneColor = props.twoToneColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, AntdIcon_excluded);
  var _React$useContext = react.useContext(Context),
    _React$useContext$pre = _React$useContext.prefixCls,
    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,
    rootClassName = _React$useContext.rootClassName;
  var classString = classnames_default()(rootClassName, prefixCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-").concat(icon.name), !!icon.name), "".concat(prefixCls, "-spin"), !!spin || icon.name === 'loading'), className);
  var iconTabIndex = tabIndex;
  if (iconTabIndex === undefined && onClick) {
    iconTabIndex = -1;
  }
  var svgStyle = rotate ? {
    msTransform: "rotate(".concat(rotate, "deg)"),
    transform: "rotate(".concat(rotate, "deg)")
  } : undefined;
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.Z)({
    role: "img",
    "aria-label": icon.name
  }, restProps, {
    ref: ref,
    tabIndex: iconTabIndex,
    onClick: onClick,
    className: classString
  }), /*#__PURE__*/react.createElement(components_IconBase, {
    icon: icon,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    style: svgStyle
  }));
});
Icon.displayName = 'AntdIcon';
Icon.getTwoToneColor = getTwoToneColor;
Icon.setTwoToneColor = setTwoToneColor;
/* harmony default export */ var AntdIcon = (Icon);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/@ant-design/icons/es/icons/RightOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var RightOutlined = function RightOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_RightOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_RightOutlined = (/*#__PURE__*/react.forwardRef(RightOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-card/es/components/CheckCard/index.js + 2 modules
var CheckCard = __webpack_require__(97321);
// EXTERNAL MODULE: ./node_modules/antd/es/skeleton/index.js + 12 modules
var skeleton = __webpack_require__(99559);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/node_modules/rc-util/es/hooks/useMergedState.js
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { if (typeof Symbol === "undefined" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }


function useControlledState(defaultStateValue, option) {
  var _ref = option || {},
      defaultValue = _ref.defaultValue,
      value = _ref.value,
      onChange = _ref.onChange,
      postState = _ref.postState;

  var _React$useState = react.useState(function () {
    if (value !== undefined) {
      return value;
    }

    if (defaultValue !== undefined) {
      return typeof defaultValue === 'function' ? defaultValue() : defaultValue;
    }

    return typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;
  }),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      innerValue = _React$useState2[0],
      setInnerValue = _React$useState2[1];

  var mergedValue = value !== undefined ? value : innerValue;

  if (postState) {
    mergedValue = postState(mergedValue);
  }

  function triggerChange(newValue) {
    setInnerValue(newValue);

    if (mergedValue !== newValue && onChange) {
      onChange(newValue, mergedValue);
    }
  } // Effect of reset value to \`undefined\`


  var firstRenderRef = react.useRef(true);
  react.useEffect(function () {
    if (firstRenderRef.current) {
      firstRenderRef.current = false;
      return;
    }

    if (value === undefined) {
      setInnerValue(value);
    }
  }, [value]);
  return [mergedValue, triggerChange];
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/es/Item.js




var Item_excluded = ["title", "subTitle", "content", "itemTitleRender", "prefixCls", "actions", "item", "recordKey", "avatar", "cardProps", "description", "isEditable", "checkbox", "index", "selected", "loading", "expand", "onExpand", "expandable", "rowSupportExpand", "showActions", "showExtra", "type", "style", "className", "record", "onRow", "onItem", "itemHeaderRender", "cardActionProps", "extra"];










function renderExpandIcon(_ref) {
  var prefixCls = _ref.prefixCls,
    _ref$expandIcon = _ref.expandIcon,
    expandIcon = _ref$expandIcon === void 0 ? /*#__PURE__*/(0,jsx_runtime.jsx)(icons_RightOutlined, {}) : _ref$expandIcon,
    onExpand = _ref.onExpand,
    expanded = _ref.expanded,
    record = _ref.record,
    hashId = _ref.hashId;
  var icon = expandIcon;
  var expandClassName = "".concat(prefixCls, "-row-expand-icon");
  var onClick = function onClick(event) {
    onExpand(!expanded);
    event.stopPropagation();
  };
  if (typeof expandIcon === 'function') {
    icon = expandIcon({
      expanded: expanded,
      onExpand: onExpand,
      record: record
    });
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
    className: classnames_default()(expandClassName, hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-row-expanded"), expanded), "".concat(prefixCls, "-row-collapsed"), !expanded)),
    onClick: onClick,
    children: icon
  });
}
function ProListItem(props) {
  var _ref3, _ref4;
  var customizePrefixCls = props.prefixCls;
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var _useContext2 = (0,react.useContext)(es/* ProProvider */.L_),
    hashId = _useContext2.hashId;
  var prefixCls = getPrefixCls('pro-list', customizePrefixCls);
  var defaultClassName = "".concat(prefixCls, "-row");
  var title = props.title,
    subTitle = props.subTitle,
    content = props.content,
    itemTitleRender = props.itemTitleRender,
    restPrefixCls = props.prefixCls,
    actions = props.actions,
    item = props.item,
    recordKey = props.recordKey,
    avatar = props.avatar,
    cardProps = props.cardProps,
    description = props.description,
    isEditable = props.isEditable,
    checkbox = props.checkbox,
    index = props.index,
    selected = props.selected,
    loading = props.loading,
    propsExpand = props.expand,
    propsOnExpand = props.onExpand,
    expandableConfig = props.expandable,
    rowSupportExpand = props.rowSupportExpand,
    showActions = props.showActions,
    showExtra = props.showExtra,
    type = props.type,
    style = props.style,
    _props$className = props.className,
    propsClassName = _props$className === void 0 ? defaultClassName : _props$className,
    record = props.record,
    onRow = props.onRow,
    onItem = props.onItem,
    itemHeaderRender = props.itemHeaderRender,
    cardActionProps = props.cardActionProps,
    extra = props.extra,
    rest = (0,objectWithoutProperties/* default */.Z)(props, Item_excluded);
  var _ref2 = expandableConfig || {},
    expandedRowRender = _ref2.expandedRowRender,
    expandIcon = _ref2.expandIcon,
    expandRowByClick = _ref2.expandRowByClick,
    _ref2$indentSize = _ref2.indentSize,
    indentSize = _ref2$indentSize === void 0 ? 8 : _ref2$indentSize,
    expandedRowClassName = _ref2.expandedRowClassName;
  var _useMergedState = useControlledState(!!propsExpand, {
      value: propsExpand,
      onChange: propsOnExpand
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    expanded = _useMergedState2[0],
    onExpand = _useMergedState2[1];
  var className = classnames_default()((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(defaultClassName, "-selected"), !cardProps && selected), "".concat(defaultClassName, "-show-action-hover"), showActions === 'hover'), "".concat(defaultClassName, "-type-").concat(type), !!type), "".concat(defaultClassName, "-editable"), isEditable), "".concat(defaultClassName, "-show-extra-hover"), showExtra === 'hover'), hashId, defaultClassName);
  var extraClassName = classnames_default()(hashId, (0,defineProperty/* default */.Z)({}, "".concat(propsClassName, "-extra"), showExtra === 'hover'));
  var needExpanded = expanded || Object.values(expandableConfig || {}).length === 0;
  var expandedRowDom = expandedRowRender && expandedRowRender(record, index, indentSize, expanded);
  var extraDom = (0,react.useMemo)(function () {
    if (!actions || cardActionProps === 'actions') {
      return undefined;
    }
    return [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      onClick: function onClick(e) {
        return e.stopPropagation();
      },
      children: actions
    }, "action")];
  }, [actions, cardActionProps]);
  var actionsDom = (0,react.useMemo)(function () {
    if (!actions || !cardActionProps || cardActionProps === 'extra') {
      return undefined;
    }
    return [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(defaultClassName, "-actions ").concat(hashId).trim(),
      onClick: function onClick(e) {
        return e.stopPropagation();
      },
      children: actions
    }, "action")];
  }, [actions, cardActionProps, defaultClassName, hashId]);
  var titleDom = title || subTitle ? /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "".concat(defaultClassName, "-header-container ").concat(hashId).trim(),
    children: [title && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: classnames_default()("".concat(defaultClassName, "-title"), hashId, (0,defineProperty/* default */.Z)({}, "".concat(defaultClassName, "-title-editable"), isEditable)),
      children: title
    }), subTitle && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: classnames_default()("".concat(defaultClassName, "-subTitle"), hashId, (0,defineProperty/* default */.Z)({}, "".concat(defaultClassName, "-subTitle-editable"), isEditable)),
      children: subTitle
    })]
  }) : null;
  var metaTitle = (_ref3 = itemTitleRender && (itemTitleRender === null || itemTitleRender === void 0 ? void 0 : itemTitleRender(record, index, titleDom))) !== null && _ref3 !== void 0 ? _ref3 : titleDom;
  var metaDom = metaTitle || avatar || subTitle || description ? /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item.Meta, {
    avatar: avatar,
    title: metaTitle,
    description: description && needExpanded && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(className, "-description ").concat(hashId).trim(),
      children: description
    })
  }) : null;
  var rowClassName = classnames_default()(hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(defaultClassName, "-item-has-checkbox"), checkbox), "".concat(defaultClassName, "-item-has-avatar"), avatar), className, className));
  var cardTitleDom = (0,react.useMemo)(function () {
    if (avatar || title) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [avatar, /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          className: "".concat(getPrefixCls('list-item-meta-title'), " ").concat(hashId).trim(),
          children: title
        })]
      });
    }
    return null;
  }, [avatar, getPrefixCls, hashId, title]);
  var itemProps = onItem === null || onItem === void 0 ? void 0 : onItem(record, index);
  var defaultDom = !cardProps ? /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: classnames_default()(rowClassName, hashId, (0,defineProperty/* default */.Z)({}, propsClassName, propsClassName !== defaultClassName))
  }, rest), {}, {
    actions: extraDom,
    extra: !!extra && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: extraClassName,
      children: extra
    })
  }, onRow === null || onRow === void 0 ? void 0 : onRow(record, index)), itemProps), {}, {
    onClick: function onClick(e) {
      var _onRow, _onRow$onClick, _onItem, _onItem$onClick;
      onRow === null || onRow === void 0 || (_onRow = onRow(record, index)) === null || _onRow === void 0 || (_onRow$onClick = _onRow.onClick) === null || _onRow$onClick === void 0 || _onRow$onClick.call(_onRow, e);
      onItem === null || onItem === void 0 || (_onItem = onItem(record, index)) === null || _onItem === void 0 || (_onItem$onClick = _onItem.onClick) === null || _onItem$onClick === void 0 || _onItem$onClick.call(_onItem, e);
      if (expandRowByClick) {
        onExpand(!expanded);
      }
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(skeleton/* default */.Z, {
      avatar: true,
      title: false,
      loading: loading,
      active: true,
      children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(className, "-header ").concat(hashId).trim(),
        children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
          className: "".concat(className, "-header-option ").concat(hashId).trim(),
          children: [!!checkbox && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
            className: "".concat(className, "-checkbox ").concat(hashId).trim(),
            children: checkbox
          }), Object.values(expandableConfig || {}).length > 0 && rowSupportExpand && renderExpandIcon({
            prefixCls: prefixCls,
            hashId: hashId,
            expandIcon: expandIcon,
            onExpand: onExpand,
            expanded: expanded,
            record: record
          })]
        }), (_ref4 = itemHeaderRender && (itemHeaderRender === null || itemHeaderRender === void 0 ? void 0 : itemHeaderRender(record, index, metaDom))) !== null && _ref4 !== void 0 ? _ref4 : metaDom]
      }), needExpanded && (content || expandedRowDom) && /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(className, "-content ").concat(hashId).trim(),
        children: [content, expandedRowRender && rowSupportExpand && /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: expandedRowClassName && expandedRowClassName(record, index, indentSize),
          children: expandedRowDom
        })]
      })]
    })
  })) : /*#__PURE__*/(0,jsx_runtime.jsx)(CheckCard/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    bordered: true,
    style: {
      width: '100%'
    }
  }, cardProps), {}, {
    title: cardTitleDom,
    subTitle: subTitle,
    extra: extraDom,
    actions: actionsDom,
    bodyStyle: (0,objectSpread2/* default */.Z)({
      padding: 24
    }, cardProps.bodyStyle)
  }, itemProps), {}, {
    onClick: function onClick(e) {
      var _cardProps$onClick, _itemProps$onClick;
      cardProps === null || cardProps === void 0 || (_cardProps$onClick = cardProps.onClick) === null || _cardProps$onClick === void 0 || _cardProps$onClick.call(cardProps, e);
      itemProps === null || itemProps === void 0 || (_itemProps$onClick = itemProps.onClick) === null || _itemProps$onClick === void 0 || _itemProps$onClick.call(itemProps, e);
    },
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(skeleton/* default */.Z, {
      avatar: true,
      title: false,
      loading: loading,
      active: true,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(className, "-header ").concat(hashId).trim(),
        children: [itemTitleRender && (itemTitleRender === null || itemTitleRender === void 0 ? void 0 : itemTitleRender(record, index, titleDom)), content]
      })
    })
  }));
  if (!cardProps) {
    return defaultDom;
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: classnames_default()(hashId, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(className, "-card"), cardProps), propsClassName, propsClassName !== defaultClassName)),
    style: style,
    children: defaultDom
  });
}
/* harmony default export */ var Item = (ProListItem);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/es/constants.js
var PRO_LIST_KEYS = ['title', 'subTitle', 'avatar', 'description', 'extra', 'content', 'actions', 'type'];
var PRO_LIST_KEYS_MAP = PRO_LIST_KEYS.reduce(function (pre, next) {
  pre.set(next, true);
  return pre;
}, new Map());

// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/compareVersions/index.js
var compareVersions = __webpack_require__(1977);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/es/ListView.js




var ListView_excluded = ["dataSource", "columns", "rowKey", "showActions", "showExtra", "prefixCls", "actionRef", "itemTitleRender", "renderItem", "itemCardProps", "itemHeaderRender", "expandable", "rowSelection", "pagination", "onRow", "onItem", "rowClassName"];












function ListView(props) {
  var dataSource = props.dataSource,
    columns = props.columns,
    rowKey = props.rowKey,
    showActions = props.showActions,
    showExtra = props.showExtra,
    customizePrefixCls = props.prefixCls,
    actionRef = props.actionRef,
    itemTitleRender = props.itemTitleRender,
    _renderItem = props.renderItem,
    itemCardProps = props.itemCardProps,
    itemHeaderRender = props.itemHeaderRender,
    expandableConfig = props.expandable,
    rowSelection = props.rowSelection,
    pagination = props.pagination,
    onRow = props.onRow,
    onItem = props.onItem,
    rowClassName = props.rowClassName,
    rest = (0,objectWithoutProperties/* default */.Z)(props, ListView_excluded);
  var _useContext = (0,react.useContext)(es/* ProProvider */.L_),
    hashId = _useContext.hashId;
  var _useContext2 = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext2.getPrefixCls;
  var getRowKey = react.useMemo(function () {
    if (typeof rowKey === 'function') {
      return rowKey;
    }
    return function (record, index) {
      return record[rowKey] || index;
    };
  }, [rowKey]);
  var _useLazyKVMap = (0,useLazyKVMap/* default */.Z)(dataSource, 'children', getRowKey),
    _useLazyKVMap2 = (0,slicedToArray/* default */.Z)(_useLazyKVMap, 1),
    getRecordByKey = _useLazyKVMap2[0];
  var usePaginationArgs = [function () {}, pagination];
  // \u517C\u5BB9 5.2.0 \u4EE5\u4E0B\u7684\u7248\u672C
  if ((0,compareVersions/* compareVersions */.n)(version/* default */.Z, '5.3.0') < 0) usePaginationArgs.reverse();
  // \u5408\u5E76\u5206\u9875\u7684\u7684\u914D\u7F6E\uFF0C\u8FD9\u91CC\u662F\u4E3A\u4E86\u517C\u5BB9 antd \u7684\u5206\u9875
  var _usePagination = (0,usePagination/* default */.ZP)(dataSource.length, usePaginationArgs[0], usePaginationArgs[1]),
    _usePagination2 = (0,slicedToArray/* default */.Z)(_usePagination, 1),
    mergedPagination = _usePagination2[0];
  /** \u6839\u636E\u5206\u9875\u6765\u8FD4\u56DE\u4E0D\u540C\u7684\u6570\u636E\uFF0C\u6A21\u62DF table */
  var pageData = react.useMemo(function () {
    if (pagination === false || !mergedPagination.pageSize || dataSource.length < mergedPagination.total) {
      return dataSource;
    }
    var _mergedPagination$cur = mergedPagination.current,
      current = _mergedPagination$cur === void 0 ? 1 : _mergedPagination$cur,
      _mergedPagination$pag = mergedPagination.pageSize,
      pageSize = _mergedPagination$pag === void 0 ? 10 : _mergedPagination$pag;
    var currentPageData = dataSource.slice((current - 1) * pageSize, current * pageSize);
    return currentPageData;
  }, [dataSource, mergedPagination, pagination]);
  var prefixCls = getPrefixCls('pro-list', customizePrefixCls);

  /** \u63D0\u4F9B\u548C table \u4E00\u6837\u7684 rowSelection \u914D\u7F6E */
  var useSelectionArgs = [{
    getRowKey: getRowKey,
    getRecordByKey: getRecordByKey,
    prefixCls: prefixCls,
    data: dataSource,
    pageData: pageData,
    expandType: 'row',
    childrenColumnName: 'children',
    locale: {}
  }, rowSelection
  // \u8FD9\u4E2A API \u7528\u7684\u4E0D\u597D\uFF0C\u5148 any \u4E00\u4E0B
  ];

  // \u517C\u5BB9 5.2.0 \u4EE5\u4E0B\u7684\u7248\u672C
  if ((0,compareVersions/* compareVersions */.n)(version/* default */.Z, '5.3.0') < 0) useSelectionArgs.reverse();
  var _useSelection = useSelection/* default */.ZP.apply(void 0, useSelectionArgs),
    _useSelection2 = (0,slicedToArray/* default */.Z)(_useSelection, 2),
    selectItemRender = _useSelection2[0],
    selectedKeySet = _useSelection2[1];

  // \u63D0\u4F9B\u548C Table \u4E00\u6837\u7684 expand \u652F\u6301
  var _ref = expandableConfig || {},
    expandedRowKeys = _ref.expandedRowKeys,
    defaultExpandedRowKeys = _ref.defaultExpandedRowKeys,
    _ref$defaultExpandAll = _ref.defaultExpandAllRows,
    defaultExpandAllRows = _ref$defaultExpandAll === void 0 ? true : _ref$defaultExpandAll,
    onExpand = _ref.onExpand,
    onExpandedRowsChange = _ref.onExpandedRowsChange,
    rowExpandable = _ref.rowExpandable;

  /** \u5C55\u5F00\u6536\u8D77\u529F\u80FD\u533A\u57DF star */
  var _React$useState = react.useState(function () {
      if (defaultExpandedRowKeys) {
        return defaultExpandedRowKeys;
      }
      if (defaultExpandAllRows !== false) {
        return dataSource.map(getRowKey);
      }
      return [];
    }),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    innerExpandedKeys = _React$useState2[0],
    setInnerExpandedKeys = _React$useState2[1];
  var mergedExpandedKeys = react.useMemo(function () {
    return new Set(expandedRowKeys || innerExpandedKeys || []);
  }, [expandedRowKeys, innerExpandedKeys]);
  var onTriggerExpand = react.useCallback(function (record) {
    var key = getRowKey(record, dataSource.indexOf(record));
    var newExpandedKeys;
    var hasKey = mergedExpandedKeys.has(key);
    if (hasKey) {
      mergedExpandedKeys.delete(key);
      newExpandedKeys = (0,toConsumableArray/* default */.Z)(mergedExpandedKeys);
    } else {
      newExpandedKeys = [].concat((0,toConsumableArray/* default */.Z)(mergedExpandedKeys), [key]);
    }
    setInnerExpandedKeys(newExpandedKeys);
    if (onExpand) {
      onExpand(!hasKey, record);
    }
    if (onExpandedRowsChange) {
      onExpandedRowsChange(newExpandedKeys);
    }
  }, [getRowKey, mergedExpandedKeys, dataSource, onExpand, onExpandedRowsChange]);

  /** \u5C55\u5F00\u6536\u8D77\u529F\u80FD\u533A\u57DF end */

  /** \u8FD9\u4E2A\u662F \u9009\u62E9\u6846\u7684 render \u65B9\u6CD5 \u4E3A\u4E86\u517C\u5BB9 antd \u7684 table,\u7528\u4E86\u540C\u6837\u7684\u6E32\u67D3\u903B\u8F91 \u6240\u4EE5\u770B\u8D77\u6765\u6709\u70B9\u5947\u602A */
  var selectItemDom = selectItemRender([])[0];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(list/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, rest), {}, {
    className: classnames_default()(getPrefixCls('pro-list-container', customizePrefixCls), hashId, rest.className),
    dataSource: pageData,
    pagination: pagination && mergedPagination,
    renderItem: function renderItem(item, index) {
      var _actionRef$current;
      var listItemProps = {
        className: typeof rowClassName === 'function' ? rowClassName(item, index) : rowClassName
      };
      columns === null || columns === void 0 || columns.forEach(function (column) {
        var listKey = column.listKey,
          cardActionProps = column.cardActionProps;
        if (!PRO_LIST_KEYS_MAP.has(listKey)) {
          return;
        }
        var dataIndex = column.dataIndex || listKey || column.key;
        var rawData = Array.isArray(dataIndex) ? get(item, dataIndex) : item[dataIndex];

        /** \u5982\u679CcardActionProps \u9700\u8981\u76F4\u63A5\u4F7F\u7528\u6E90\u6570\u7EC4\uFF0C\u56E0\u4E3A action \u5FC5\u987B\u8981\u6E90\u6570\u7EC4 */
        if (cardActionProps === 'actions' && listKey === 'actions') {
          listItemProps.cardActionProps = cardActionProps;
        }
        // \u8C03\u7528protable\u7684\u5217\u914D\u7F6E\u6E32\u67D3\u6570\u636E
        var data = column.render ? column.render(rawData, item, index) : rawData;
        if (data !== '-') listItemProps[column.listKey] = data;
      });
      var checkboxDom;
      if (selectItemDom && selectItemDom.render) {
        checkboxDom = selectItemDom.render(item, item, index);
      }
      var _ref2 = ((_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 ? void 0 : _actionRef$current.isEditable((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, item), {}, {
          index: index
        }))) || {},
        isEditable = _ref2.isEditable,
        recordKey = _ref2.recordKey;
      var isChecked = selectedKeySet.has(recordKey || index);
      var defaultDom = /*#__PURE__*/(0,jsx_runtime.jsx)(Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
        cardProps: rest.grid ? (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, itemCardProps), rest.grid), {}, {
          checked: isChecked,
          onChange: /*#__PURE__*/react.isValidElement(checkboxDom) ? function (changeChecked) {
            var _checkboxDom;
            return (_checkboxDom = checkboxDom) === null || _checkboxDom === void 0 || (_checkboxDom = _checkboxDom.props) === null || _checkboxDom === void 0 ? void 0 : _checkboxDom.onChange({
              nativeEvent: {},
              changeChecked: changeChecked
            });
          } : undefined
        }) : undefined
      }, listItemProps), {}, {
        recordKey: recordKey,
        isEditable: isEditable || false,
        expandable: expandableConfig,
        expand: mergedExpandedKeys.has(getRowKey(item, index)),
        onExpand: function onExpand() {
          onTriggerExpand(item);
        },
        index: index,
        record: item,
        item: item,
        showActions: showActions,
        showExtra: showExtra,
        itemTitleRender: itemTitleRender,
        itemHeaderRender: itemHeaderRender,
        rowSupportExpand: !rowExpandable || rowExpandable && rowExpandable(item),
        selected: selectedKeySet.has(getRowKey(item, index)),
        checkbox: checkboxDom,
        onRow: onRow,
        onItem: onItem
      }), recordKey);
      if (_renderItem) {
        return _renderItem(item, index, defaultDom);
      }
      return defaultDom;
    }
  }));
}
/* harmony default export */ var es_ListView = (ListView);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/es/style/index.js




var techUiListActive = new cssinjs_es/* Keyframes */.E4('techUiListActive', {
  '0%': {
    backgroundColor: 'unset'
  },
  '30%': {
    background: '#fefbe6'
  },
  '100%': {
    backgroundColor: 'unset'
  }
});
var genProListStyle = function genProListStyle(token) {
  var _row;
  return (0,defineProperty/* default */.Z)({}, token.componentCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    backgroundColor: 'transparent'
  }, "".concat(token.proComponentsCls, "-table-alert"), {
    marginBlockEnd: '16px'
  }), '&-row', (_row = {
    borderBlockEnd: "1px solid ".concat(token.colorSplit)
  }, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_row, "".concat(token.antCls, "-list-item-meta-title"), {
    borderBlockEnd: 'none',
    margin: 0
  }), '&:last-child', (0,defineProperty/* default */.Z)({
    borderBlockEnd: 'none'
  }, "".concat(token.antCls, "-list-item"), {
    borderBlockEnd: 'none'
  })), '&:hover', (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    transition: 'background-color 0.3s'
  }, "".concat(token.antCls, "-list-item-action"), {
    display: 'block'
  }), "".concat(token.antCls, "-list-item-extra"), {
    display: 'flex'
  }), "".concat(token.componentCls, "-row-extra"), {
    display: 'block'
  }), "".concat(token.componentCls, "-row-subheader-actions"), {
    display: 'block'
  })), '&-card', (0,defineProperty/* default */.Z)({
    marginBlock: 8,
    marginInline: 0,
    paddingBlock: 0,
    paddingInline: 8,
    '&:hover': {
      backgroundColor: 'transparent'
    }
  }, "".concat(token.antCls, "-list-item-meta-title"), {
    flexShrink: 9,
    marginBlock: 0,
    marginInline: 0,
    lineHeight: '22px'
  })), "&".concat(token.componentCls, "-row-editable"), (0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-list-item"), {
    '&-meta': {
      '&-avatar,&-description,&-title': {
        paddingBlock: 6,
        paddingInline: 0,
        '&-editable': {
          paddingBlock: 0
        }
      }
    },
    '&-action': {
      display: 'block'
    }
  })), "&".concat(token.componentCls, "-row-selected"), {
    backgroundColor: token.colorPrimaryBgHover,
    '&:hover': {
      backgroundColor: token.colorPrimaryBgHover
    }
  }), "&".concat(token.componentCls, "-row-type-new"), {
    animationName: techUiListActive,
    animationDuration: '3s'
  }), "&".concat(token.componentCls, "-row-type-inline"), (0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-row-title"), {
    fontWeight: 'normal'
  })), "&".concat(token.componentCls, "-row-type-top"), {
    backgroundImage: "url('https://gw.alipayobjects.com/zos/antfincdn/DehQfMbOJb/icon.svg')",
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'left top',
    backgroundSize: '12px 12px'
  }), '&-show-action-hover', (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-list-item-action,\\n            ").concat(token.proComponentsCls, "-card-extra,\\n            ").concat(token.proComponentsCls, "-card-actions"), {
    display: 'flex'
  })), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_row, '&-show-extra-hover', (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-list-item-extra"), {
    display: 'none'
  })), '&-extra', {
    display: 'none'
  }), '&-subheader', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '44px',
    paddingInline: 24,
    paddingBlock: 0,
    color: token.colorTextSecondary,
    lineHeight: '44px',
    background: 'rgba(0, 0, 0, 0.02)',
    '&-actions': {
      display: 'none'
    },
    '&-actions *': {
      marginInlineEnd: 8,
      '&:last-child': {
        marginInlineEnd: 0
      }
    }
  }), '&-expand-icon', {
    marginInlineEnd: 8,
    display: 'flex',
    fontSize: 12,
    cursor: 'pointer',
    height: '24px',
    marginRight: 4,
    color: token.colorTextSecondary,
    '> .anticon > svg': {
      transition: '0.3s'
    }
  }), '&-expanded', {
    ' > .anticon > svg': {
      transform: 'rotate(90deg)'
    }
  }), '&-title', {
    marginInlineEnd: '16px',
    wordBreak: 'break-all',
    cursor: 'pointer',
    '&-editable': {
      paddingBlock: 8
    },
    '&:hover': {
      color: token.colorPrimary
    }
  }), '&-content', {
    position: 'relative',
    display: 'flex',
    flex: '1',
    flexDirection: 'column',
    marginBlock: 0,
    marginInline: 32
  }), '&-subTitle', {
    color: 'rgba(0, 0, 0, 0.45)',
    '&-editable': {
      paddingBlock: 8
    }
  }), '&-description', {
    marginBlockStart: '4px',
    wordBreak: 'break-all'
  }), '&-avatar', {
    display: 'flex'
  }), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)(_row, '&-header', {
    display: 'flex',
    flex: '1',
    justifyContent: 'flex-start',
    h4: {
      margin: 0,
      padding: 0
    }
  }), '&-header-container', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start'
  }), '&-header-option', {
    display: 'flex'
  }), '&-checkbox', {
    width: '16px',
    marginInlineEnd: '12px'
  }), '&-no-split', (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-row"), {
    borderBlockEnd: 'none'
  }), "".concat(token.antCls, "-list ").concat(token.antCls, "-list-item"), {
    borderBlockEnd: 'none'
  })), '&-bordered', (0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-toolbar"), {
    borderBlockEnd: "1px solid ".concat(token.colorSplit)
  })), "".concat(token.antCls, "-list-vertical"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-row"), {
    borderBlockEnd: '12px 18px 12px 24px'
  }), '&-header-title', {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center'
  }), '&-content', {
    marginBlock: 0,
    marginInline: 0
  }), '&-subTitle', {
    marginBlockStart: 8
  }), "".concat(token.antCls, "-list-item-extra"), (0,defineProperty/* default */.Z)({
    display: 'flex',
    alignItems: 'center',
    marginInlineStart: '32px'
  }, "".concat(token.componentCls, "-row-description"), {
    marginBlockStart: 16
  })), "".concat(token.antCls, "-list-bordered ").concat(token.antCls, "-list-item"), {
    paddingInline: 0
  }), "".concat(token.componentCls, "-row-show-extra-hover"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-list-item-extra "), {
    display: 'none'
  }))), "".concat(token.antCls, "-list-pagination"), {
    marginBlockStart: token.margin,
    marginBlockEnd: token.margin
  }), "".concat(token.antCls, "-list-list"), {
    '&-item': {
      cursor: 'pointer',
      paddingBlock: 12,
      paddingInline: 12
    }
  }), "".concat(token.antCls, "-list-vertical ").concat(token.proComponentsCls, "-list-row"), (0,defineProperty/* default */.Z)({
    '&-header': {
      paddingBlock: 0,
      paddingInline: 0,
      borderBlockEnd: 'none'
    }
  }, "".concat(token.antCls, "-list-item"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    width: '100%',
    paddingBlock: 12,
    paddingInlineStart: 24,
    paddingInlineEnd: 18
  }, "".concat(token.antCls, "-list-item-meta-avatar"), {
    display: 'flex',
    alignItems: 'center',
    marginInlineEnd: 8
  }), "".concat(token.antCls, "-list-item-action-split"), {
    display: 'none'
  }), "".concat(token.antCls, "-list-item-meta-title"), {
    marginBlock: 0,
    marginInline: 0
  }))))));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProList', function (token) {
    var proListToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProListStyle(proListToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-list/es/index.js



var es_excluded = ["metas", "split", "footer", "rowKey", "tooltip", "className", "options", "search", "expandable", "showActions", "showExtra", "rowSelection", "pagination", "itemLayout", "renderItem", "grid", "itemCardProps", "onRow", "onItem", "rowClassName", "locale", "itemHeaderRender", "itemTitleRender"];








// \u517C\u5BB9\u6027\u4EE3\u7801


function NoProVideProList(props) {
  var metals = props.metas,
    split = props.split,
    footer = props.footer,
    rowKey = props.rowKey,
    tooltip = props.tooltip,
    className = props.className,
    _props$options = props.options,
    options = _props$options === void 0 ? false : _props$options,
    _props$search = props.search,
    search = _props$search === void 0 ? false : _props$search,
    expandable = props.expandable,
    showActions = props.showActions,
    showExtra = props.showExtra,
    _props$rowSelection = props.rowSelection,
    propRowSelection = _props$rowSelection === void 0 ? false : _props$rowSelection,
    _props$pagination = props.pagination,
    propsPagination = _props$pagination === void 0 ? false : _props$pagination,
    itemLayout = props.itemLayout,
    renderItem = props.renderItem,
    grid = props.grid,
    itemCardProps = props.itemCardProps,
    onRow = props.onRow,
    onItem = props.onItem,
    rowClassName = props.rowClassName,
    locale = props.locale,
    itemHeaderRender = props.itemHeaderRender,
    itemTitleRender = props.itemTitleRender,
    rest = (0,objectWithoutProperties/* default */.Z)(props, es_excluded);
  var actionRef = (0,react.useRef)();
  (0,react.useImperativeHandle)(rest.actionRef, function () {
    return actionRef.current;
  }, [actionRef.current]);
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var proTableColumns = (0,react.useMemo)(function () {
    var columns = [];
    Object.keys(metals || {}).forEach(function (key) {
      var meta = metals[key] || {};
      var valueType = meta.valueType;
      if (!valueType) {
        // \u6839\u636E key \u7ED9\u4E0D\u540C\u7684 valueType
        if (key === 'avatar') {
          valueType = 'avatar';
        }
        if (key === 'actions') {
          valueType = 'option';
        }
        if (key === 'description') {
          valueType = 'textarea';
        }
      }
      columns.push((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
        listKey: key,
        dataIndex: (meta === null || meta === void 0 ? void 0 : meta.dataIndex) || key
      }, meta), {}, {
        valueType: valueType
      }));
    });
    return columns;
  }, [metals]);
  var prefixCls = getPrefixCls('pro-list', props.prefixCls);
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var listClassName = classnames_default()(prefixCls, hashId, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-no-split"), !split));
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)(pro_table_es, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    tooltip: tooltip
  }, rest), {}, {
    actionRef: actionRef,
    pagination: propsPagination,
    type: "list",
    rowSelection: propRowSelection,
    search: search,
    options: options,
    className: classnames_default()(prefixCls, className, listClassName),
    columns: proTableColumns,
    rowKey: rowKey,
    tableViewRender: function tableViewRender(_ref) {
      var columns = _ref.columns,
        size = _ref.size,
        pagination = _ref.pagination,
        rowSelection = _ref.rowSelection,
        dataSource = _ref.dataSource,
        loading = _ref.loading;
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_ListView, {
        grid: grid,
        itemCardProps: itemCardProps,
        itemTitleRender: itemTitleRender,
        prefixCls: props.prefixCls,
        columns: columns,
        renderItem: renderItem,
        actionRef: actionRef,
        dataSource: dataSource || [],
        size: size,
        footer: footer,
        split: split,
        rowKey: rowKey,
        expandable: expandable,
        rowSelection: propRowSelection === false ? undefined : rowSelection,
        showActions: showActions,
        showExtra: showExtra,
        pagination: pagination,
        itemLayout: itemLayout,
        loading: loading,
        itemHeaderRender: itemHeaderRender,
        onRow: onRow,
        onItem: onItem,
        rowClassName: rowClassName,
        locale: locale
      });
    }
  })));
}
function BaseProList(props) {
  return /*#__PURE__*/_jsx(ProConfigProvider, {
    needDeps: true,
    children: /*#__PURE__*/_jsx(NoProVideProList, _objectSpread({
      cardProps: false,
      search: false,
      toolBarRender: false
    }, props))
  });
}
function ProList(props) {
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es/* ProConfigProvider */._Y, {
    needDeps: true,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(NoProVideProList, (0,objectSpread2/* default */.Z)({}, props))
  });
}

/* harmony default export */ var pro_list_es = ((/* unused pure expression or super */ null && (ProList)));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///64176
`)},85576:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ modal; }
});

// EXTERNAL MODULE: ./node_modules/antd/es/modal/confirm.js
var modal_confirm = __webpack_require__(56080);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/destroyFns.js
var destroyFns = __webpack_require__(38657);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/Modal.js + 1 modules
var Modal = __webpack_require__(56745);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/rc-dialog/es/index.js + 8 modules
var es = __webpack_require__(31058);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/PurePanel.js
var PurePanel = __webpack_require__(8745);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/ConfirmDialog.js + 3 modules
var ConfirmDialog = __webpack_require__(32409);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/shared.js + 2 modules
var shared = __webpack_require__(4941);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/style/index.js
var style = __webpack_require__(71194);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/PurePanel.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
/* eslint-disable react/jsx-no-useless-fragment */









const PurePanel_PurePanel = props => {
  const {
      prefixCls: customizePrefixCls,
      className,
      closeIcon,
      closable,
      type,
      title,
      children,
      footer
    } = props,
    restProps = __rest(props, ["prefixCls", "className", "closeIcon", "closable", "type", "title", "children", "footer"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const rootPrefixCls = getPrefixCls();
  const prefixCls = customizePrefixCls || getPrefixCls('modal');
  const rootCls = (0,useCSSVarCls/* default */.Z)(rootPrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = (0,style/* default */.ZP)(prefixCls, rootCls);
  const confirmPrefixCls = \`\${prefixCls}-confirm\`;
  // Choose target props by confirm mark
  let additionalProps = {};
  if (type) {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : false,
      title: '',
      footer: '',
      children: ( /*#__PURE__*/react.createElement(ConfirmDialog/* ConfirmContent */.O, Object.assign({}, props, {
        prefixCls: prefixCls,
        confirmPrefixCls: confirmPrefixCls,
        rootPrefixCls: rootPrefixCls,
        content: children
      })))
    };
  } else {
    additionalProps = {
      closable: closable !== null && closable !== void 0 ? closable : true,
      title,
      footer: footer !== null && /*#__PURE__*/react.createElement(shared/* Footer */.$, Object.assign({}, props)),
      children
    };
  }
  return wrapCSSVar( /*#__PURE__*/react.createElement(es/* Panel */.s, Object.assign({
    prefixCls: prefixCls,
    className: classnames_default()(hashId, \`\${prefixCls}-pure-panel\`, type && confirmPrefixCls, type && \`\${confirmPrefixCls}-\${type}\`, className, cssVarCls, rootCls)
  }, restProps, {
    closeIcon: (0,shared/* renderCloseIcon */.b)(prefixCls, closeIcon),
    closable: closable
  }, additionalProps)));
};
/* harmony default export */ var modal_PurePanel = ((0,PurePanel/* withPureRenderTheme */.i)(PurePanel_PurePanel));
// EXTERNAL MODULE: ./node_modules/antd/es/modal/useModal/index.js + 2 modules
var useModal = __webpack_require__(94423);
;// CONCATENATED MODULE: ./node_modules/antd/es/modal/index.js
"use client";






function modalWarn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withWarn */.uW)(props));
}
const modal_Modal = Modal/* default */.Z;
modal_Modal.useModal = useModal/* default */.Z;
modal_Modal.info = function infoFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withInfo */.cw)(props));
};
modal_Modal.success = function successFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withSuccess */.vq)(props));
};
modal_Modal.error = function errorFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withError */.AQ)(props));
};
modal_Modal.warning = modalWarn;
modal_Modal.warn = modalWarn;
modal_Modal.confirm = function confirmFn(props) {
  return (0,modal_confirm/* default */.ZP)((0,modal_confirm/* withConfirm */.Au)(props));
};
modal_Modal.destroyAll = function destroyAllFn() {
  while (destroyFns/* default */.Z.length) {
    const close = destroyFns/* default */.Z.pop();
    if (close) {
      close();
    }
  }
};
modal_Modal.config = modal_confirm/* modalGlobalConfig */.ai;
modal_Modal._InternalPanelDoNotUseOrYouWillBeFired = modal_PurePanel;
if (false) {}
/* harmony default export */ var modal = (modal_Modal);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///85576
`)}}]);
