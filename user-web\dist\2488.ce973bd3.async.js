"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2488],{55287:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5717);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(89099);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY



var EyeOutlined = function EyeOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props), {}, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_EyeOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
EyeOutlined.displayName = 'EyeOutlined';
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTUyODcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFxRTtBQUNyRTtBQUNBO0FBQytCO0FBQ3VDO0FBQ3hCO0FBQzlDO0FBQ0Esc0JBQXNCLGdEQUFtQixDQUFDLHFFQUFRLEVBQUUsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHLFlBQVk7QUFDOUY7QUFDQSxVQUFVLHlGQUFjO0FBQ3hCLEdBQUc7QUFDSDtBQUNBO0FBQ0EsbUVBQTRCLDZDQUFnQixhQUFhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvRXllT3V0bGluZWQuanM/OWM2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuLy8gR0VORVJBVEUgQlkgLi9zY3JpcHRzL2dlbmVyYXRlLnRzXG4vLyBET04gTk9UIEVESVQgSVQgTUFOVUFMTFlcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBFeWVPdXRsaW5lZFN2ZyBmcm9tIFwiQGFudC1kZXNpZ24vaWNvbnMtc3ZnL2VzL2Fzbi9FeWVPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gJy4uL2NvbXBvbmVudHMvQW50ZEljb24nO1xudmFyIEV5ZU91dGxpbmVkID0gZnVuY3Rpb24gRXllT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgcHJvcHMpLCB7fSwge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IEV5ZU91dGxpbmVkU3ZnXG4gIH0pKTtcbn07XG5FeWVPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdFeWVPdXRsaW5lZCc7XG5leHBvcnQgZGVmYXVsdCAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihFeWVPdXRsaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///55287
`)},42488:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Detail_Participants; }
});

// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/crop.ts
var crop = __webpack_require__(52662);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/utils/date.ts
var date = __webpack_require__(28382);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(55287);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/components/TaskParticipantsModal.tsx













var TaskParticipantsModal = function TaskParticipantsModal(_ref) {
  var full_name = _ref.full_name,
    user_id = _ref.user_id,
    crop_id = _ref.crop_id;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isOpen = _useState2[0],
    setOpen = _useState2[1];
  var showModal = function showModal() {
    setOpen(true);
  };
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.task'
    }),
    dataIndex: 'task_label',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("a", {
        href: "/farming-management/workflow-management/detail/".concat(entity.task_id),
        target: "_blank",
        rel: "noopener noreferrer",
        children: entity.task_label
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.start_date'
    }),
    dataIndex: 'start_date',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: (0,date/* formatDateDefault */.L6)(entity.start_date)
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.end_date'
    }),
    dataIndex: 'end_date',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: (0,date/* formatDateDefault */.L6)(entity.end_date)
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.form.description'
    }),
    dataIndex: 'description'
  }
  // {
  //   title: intl.formatMessage({ id: 'common.task_child' }),
  //   dataIndex: 'todos',
  //   // render(dom, entity, index, action, schema) {
  //   //   return (

  //   //   );
  //   // },
  // },
  ];
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      size: 'small',
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(EyeOutlined/* default */.Z, {}),
        onClick: showModal
      }), /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
        children: full_name
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Chi ti\\u1EBFt s\\u1EED d\\u1EE5ng v\\u1EADt t\\u01B0 ".concat(full_name, " trong v\\u1EE5 m\\xF9a"),
      open: isOpen,
      onCancel: handleCancel,
      footer: null,
      width: 1200,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
        columns: columns,
        search: false,
        pagination: {
          pageSizeOptions: [10, 20, 50, 100],
          showSizeChanger: true
        },
        request: ( /*#__PURE__*/function () {
          var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
            var res;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  _context.next = 3;
                  return (0,crop/* getCropParticipantsTaskList */.Kw)({
                    page: params.current,
                    size: params.pageSize,
                    crop_id: crop_id,
                    customer_user_id: user_id
                  });
                case 3:
                  res = _context.sent;
                  return _context.abrupt("return", {
                    data: res.data,
                    success: true,
                    total: res.pagination.totalElements
                  });
                case 7:
                  _context.prev = 7;
                  _context.t0 = _context["catch"](0);
                  message.error("L\\u1ED7i khi k\\xE9o d\\u1EEF li\\u1EC7u: ".concat(_context.t0.message));
                  return _context.abrupt("return", {
                    success: false
                  });
                case 11:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 7]]);
          }));
          return function (_x, _x2, _x3) {
            return _ref2.apply(this, arguments);
          };
        }()),
        rowKey: 'task_id'
      })
    })]
  });
};
/* harmony default export */ var components_TaskParticipantsModal = (TaskParticipantsModal);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/components/ParticipantsStatistic.tsx








var ParticipansStatistic = function ParticipansStatistic(_ref) {
  var cropId = _ref.cropId;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.personnel'
    }),
    dataIndex: 'customer_user_id',
    // render(dom, entity, index, action, schema) {
    //   return (
    //     <TaskItemsModal
    //       category_id={entity.category_name}
    //       category_label={entity.category_label}
    //       crop_id={cropId}
    //     />
    //   );
    // },
    hideInTable: true
  }, {
    title: intl.formatMessage({
      id: 'common.fullname'
    }),
    dataIndex: 'full_name',
    render: function render(dom, entity, index, action, schema) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(components_TaskParticipantsModal, {
        full_name: entity.full_name,
        user_id: entity.customer_user_id,
        crop_id: cropId
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.email'
    }),
    dataIndex: 'email'
  }, {
    title: intl.formatMessage({
      id: 'common.total_task'
    }),
    dataIndex: 'total_assign_task'
  }, {
    title: intl.formatMessage({
      id: 'common.total_related_task'
    }),
    dataIndex: 'total_involed_task'
  }, {
    title: intl.formatMessage({
      id: 'common.total_sub_task'
    }),
    dataIndex: 'total_assign_task_child'
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    headerTitle: intl.formatMessage({
      id: 'common.personnel_statistics'
    }),
    columns: columns,
    search: false,
    pagination: {
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,crop/* getCropParticipantsStatistic */.hD)({
                page: params.current,
                size: params.pageSize,
                crop_id: cropId
              });
            case 3:
              res = _context.sent;
              return _context.abrupt("return", {
                data: res.data,
                success: true,
                total: res.pagination.totalElements
              });
            case 7:
              _context.prev = 7;
              _context.t0 = _context["catch"](0);
              message.error("Error when getting Crop Items Statistic: ".concat(_context.t0.message));
              return _context.abrupt("return", {
                success: false
              });
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 7]]);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }()),
    rowKey: 'category_name'
  });
};
/* harmony default export */ var ParticipantsStatistic = (ParticipansStatistic);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./src/services/utils.ts
var utils = __webpack_require__(467);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/defineProperty.js
var defineProperty = __webpack_require__(9783);
var defineProperty_default = /*#__PURE__*/__webpack_require__.n(defineProperty);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ModalForm/index.js
var ModalForm = __webpack_require__(37476);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/components/AddParticipant.tsx














var AddParticipant = function AddParticipant(_ref) {
  var cropId = _ref.cropId,
    onSuccess = _ref.onSuccess,
    trigger = _ref.trigger,
    open = _ref.open,
    onOpenChange = _ref.onOpenChange;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var access = (0,_umi_production_exports.useAccess)();
  var canCreateCrop = access.canCreateInSeasonalManagement();
  var intl = (0,_umi_production_exports.useIntl)();
  if (canCreateCrop) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(ModalForm/* ModalForm */.Y, {
      title: intl.formatMessage({
        id: 'common.add_participant'
      }),
      open: open,
      onOpenChange: onOpenChange,
      trigger: trigger || /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        icon: /*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}),
        type: "primary",
        children: intl.formatMessage({
          id: 'common.add_participant'
        })
      }),
      name: "seasonal_management:detail:add_participants",
      onFinish: ( /*#__PURE__*/function () {
        var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
          var _error$response, _error$response2;
          return regeneratorRuntime_default()().wrap(function _callee$(_context) {
            while (1) switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return (0,cropManager/* addParticipantInCrop */.JB)({
                  iot_crop: cropId,
                  iot_customer_user: values.iot_customer_user
                });
              case 3:
                onSuccess === null || onSuccess === void 0 || onSuccess();
                return _context.abrupt("return", true);
              case 7:
                _context.prev = 7;
                _context.t0 = _context["catch"](0);
                if ((_context.t0 === null || _context.t0 === void 0 || (_error$response = _context.t0.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 409 && (_error$response2 = _context.t0.response) !== null && _error$response2 !== void 0 && (_error$response2 = _error$response2.data) !== null && _error$response2 !== void 0 && (_error$response2 = _error$response2.exc) !== null && _error$response2 !== void 0 && _error$response2.includes('duplicate key value violates unique constraint')) {
                  message.error('Ng\u01B0\u1EDDi d\xF9ng \u0111\xE3 \u0111\xE3 t\u1ED3n t\u1EA1i');
                }
                return _context.abrupt("return", false);
              case 11:
              case "end":
                return _context.stop();
            }
          }, _callee, null, [[0, 7]]);
        }));
        return function (_x) {
          return _ref2.apply(this, arguments);
        };
      }()),
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
        rules: [{
          required: true
        }],
        label: intl.formatMessage({
          id: 'common.participant'
        }),
        name: "iot_customer_user",
        request: ( /*#__PURE__*/function () {
          var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params) {
            var listKey, paramsFilter, paramsReq, res;
            return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  listKey = ['first_name', 'last_name', 'email', 'name']; // filter like table
                  paramsFilter = params.keyWords && params.keyWords !== '' ? listKey.reduce(function (acc, item) {
                    return objectSpread2_default()(objectSpread2_default()({}, acc), {}, defineProperty_default()({}, item, params.keyWords));
                  }, {}) : {};
                  paramsReq = (0,utils/* getParamsReqTable */.wh)({
                    doc_name: constanst/* DOCTYPE_ERP */.lH.iotCustomerUser,
                    tableReqParams: {
                      filter: {},
                      sort: {},
                      params: paramsFilter
                    }
                  });
                  _context2.next = 5;
                  return (0,customerUser/* getCustomerUserList */.J9)(objectSpread2_default()(objectSpread2_default()({}, paramsReq), {}, {
                    filters: undefined,
                    or_filters: paramsReq.filters,
                    fields: listKey
                  }));
                case 5:
                  res = _context2.sent;
                  return _context2.abrupt("return", res.data.map(function (item) {
                    return {
                      label: "".concat(item.first_name, " ").concat(item.last_name, " - ").concat(item.email),
                      value: item.name
                    };
                  }));
                case 7:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          }));
          return function (_x2) {
            return _ref3.apply(this, arguments);
          };
        }()),
        showSearch: true,
        debounceTime: 200
      })
    });
  } else return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {});
};
/* harmony default export */ var components_AddParticipant = (AddParticipant);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/components/ParticipantTable.tsx















var ParticipantsTable = function ParticipantsTable(_ref) {
  var cropId = _ref.cropId;
  var tableRef = (0,react.useRef)();
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal,
    message = _App$useApp.message;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    dataSource = _useState2[0],
    setDataSource = _useState2[1];
  var reloadTable = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _tableRef$current;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            (_tableRef$current = tableRef.current) === null || _tableRef$current === void 0 || _tableRef$current.reload();
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function reloadTable() {
      return _ref2.apply(this, arguments);
    };
  }();
  var intl = (0,_umi_production_exports.useIntl)();
  var fetchData = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(params, sort, filter) {
      var paramsReq, res;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            paramsReq = (0,utils/* getParamsReqTable */.wh)({
              doc_name: constanst/* DOCTYPE_ERP */.lH.iotEmployeeInCrop,
              tableReqParams: {
                params: params,
                sort: {},
                filter: filter
              },
              concatFilter: [[constanst/* DOCTYPE_ERP */.lH.iotEmployeeInCrop, 'iot_crop', '=', cropId]]
            });
            _context2.next = 3;
            return (0,cropManager/* getParticipantsInCrop */.No)(paramsReq);
          case 3:
            res = _context2.sent;
            return _context2.abrupt("return", {
              data: res.data,
              total: res.pagination.totalElements
            });
          case 5:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function fetchData(_x, _x2, _x3) {
      return _ref3.apply(this, arguments);
    };
  }();
  var handleSort = function handleSort(data, sort) {
    if (!sort) return data;
    var sortedData = toConsumableArray_default()(data);
    Object.keys(sort).forEach(function (key) {
      sortedData.sort(function (a, b) {
        var sortKey = key;
        var aValue = a[sortKey] ? a[sortKey].toString() : '';
        var bValue = b[sortKey] ? b[sortKey].toString() : '';
        if (sort[key] === 'ascend') {
          return aValue.localeCompare(bValue);
        } else if (sort[key] === 'descend') {
          return bValue.localeCompare(aValue);
        }
        return 0;
      });
    });
    return sortedData;
  };
  var cleanSort = function cleanSort(sort) {
    return Object.keys(sort).reduce(function (acc, key) {
      if (sort[key] === 'ascend' || sort[key] === 'descend') {
        acc[key] = sort[key];
      }
      return acc;
    }, {});
  };
  var columns = [{
    dataIndex: 'name',
    title: 'ID',
    width: 200,
    ellipsis: true,
    copyable: true,
    hideInTable: true
  }, {
    title: 'Email',
    dataIndex: 'email',
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.last-name'
    }),
    dataIndex: 'last_name',
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.first-name'
    }),
    dataIndex: 'first_name',
    sorter: true
  }, {
    title: intl.formatMessage({
      id: 'common.number-phone'
    }),
    dataIndex: 'phone_number',
    sorter: true
  }, {
    width: 150,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          type: "primary",
          danger: true,
          icon: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {}),
          onClick: function onClick() {
            modal.confirm({
              title: intl.formatMessage({
                id: 'common.remove_from_participant_list'
              }),
              onOk: function () {
                var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3() {
                  return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
                    while (1) switch (_context3.prev = _context3.next) {
                      case 0:
                        _context3.prev = 0;
                        _context3.next = 3;
                        return (0,cropManager/* deleteParticipantsInCrop */.Tq)(entity === null || entity === void 0 ? void 0 : entity.name);
                      case 3:
                        message.success('X\xF3a th\xE0nh c\xF4ng');
                        reloadTable();
                        _context3.next = 10;
                        break;
                      case 7:
                        _context3.prev = 7;
                        _context3.t0 = _context3["catch"](0);
                        message.error('X\xF3a th\u1EA5t b\u1EA1i');
                      case 10:
                      case "end":
                        return _context3.stop();
                    }
                  }, _callee3, null, [[0, 7]]);
                }));
                function onOk() {
                  return _onOk.apply(this, arguments);
                }
                return onOk;
              }(),
              okButtonProps: {
                danger: true
              }
            });
          }
        })
      });
    }
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
      direction: "vertical",
      style: {
        width: '100%'
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
        size: "small",
        actionRef: tableRef,
        rowKey: "name",
        request: ( /*#__PURE__*/function () {
          var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(params, sort, filter) {
            var _yield$fetchData, data, total, cleanedSort, sortedData;
            return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
              while (1) switch (_context4.prev = _context4.next) {
                case 0:
                  _context4.prev = 0;
                  _context4.next = 3;
                  return fetchData(params, sort, filter);
                case 3:
                  _yield$fetchData = _context4.sent;
                  data = _yield$fetchData.data;
                  total = _yield$fetchData.total;
                  cleanedSort = cleanSort(sort);
                  sortedData = handleSort(data, cleanedSort);
                  setDataSource(sortedData);
                  return _context4.abrupt("return", {
                    data: sortedData,
                    total: total
                  });
                case 12:
                  _context4.prev = 12;
                  _context4.t0 = _context4["catch"](0);
                  return _context4.abrupt("return", {
                    success: false
                  });
                case 15:
                case "end":
                  return _context4.stop();
              }
            }, _callee4, null, [[0, 12]]);
          }));
          return function (_x4, _x5, _x6) {
            return _ref4.apply(this, arguments);
          };
        }()),
        bordered: true,
        columns: columns,
        dataSource: dataSource,
        search: false,
        headerTitle: intl.formatMessage({
          id: 'common.participant_list'
        }),
        toolBarRender: function toolBarRender() {
          return [/*#__PURE__*/(0,jsx_runtime.jsx)(components_AddParticipant, {
            onSuccess: function onSuccess() {
              message.success('Th\xEAm th\xE0nh c\xF4ng');
              reloadTable();
            },
            cropId: cropId
          }, "create")];
        },
        pagination: {
          defaultPageSize: 20,
          showSizeChanger: true,
          pageSizeOptions: ['20', '50', '100']
        }
      })
    })
  });
};
/* harmony default export */ var ParticipantTable = (ParticipantsTable);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/components/WorksheetStatistic.tsx







var WorksheetStatistic = function WorksheetStatistic(_ref) {
  var cropId = _ref.cropId;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'seasonalTab.laborList'
    }),
    dataIndex: 'work_type_label'
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.totalExpectedLabor'
    }),
    dataIndex: 'total_exp_quantity'
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.totalReality'
    }),
    dataIndex: 'total_quantity'
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material-management.unit",
      defaultMessage: "unknown"
    }),
    dataIndex: 'type'
  }, {
    title: intl.formatMessage({
      id: 'seasonalTab.totalCost'
    }),
    dataIndex: 'cost'
  }];
  return /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
    headerTitle: intl.formatMessage({
      id: 'seasonalTab.laborAndCostStatistics'
    }),
    columns: columns,
    search: false,
    pagination: {
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true
    },
    request: ( /*#__PURE__*/function () {
      var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(params, sorter, filter) {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              _context.next = 3;
              return (0,crop/* getCropWorksheetStatistic */.NQ)({
                page: params.current,
                size: params.pageSize,
                crop_id: cropId
              });
            case 3:
              res = _context.sent;
              return _context.abrupt("return", {
                data: res.data,
                success: true
              });
            case 7:
              _context.prev = 7;
              _context.t0 = _context["catch"](0);
              message.error("Error when getting Crop Items Statistic: ".concat(_context.t0.message));
              return _context.abrupt("return", {
                success: false
              });
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 7]]);
      }));
      return function (_x, _x2, _x3) {
        return _ref2.apply(this, arguments);
      };
    }()),
    rowKey: 'name'
  });
};
/* harmony default export */ var components_WorksheetStatistic = (WorksheetStatistic);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/SeasonalManagement/Detail/Participants/index.tsx







var Participants = function Participants(_ref) {
  var cropId = _ref.cropId;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      direction: "vertical",
      style: {
        width: '100%'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(ParticipantTable, {
        cropId: cropId
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(components_WorksheetStatistic, {
        cropId: cropId
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(ParticipantsStatistic, {
        cropId: cropId
      })]
    })
  });
};
/* harmony default export */ var Detail_Participants = (Participants);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///42488
`)}}]);
