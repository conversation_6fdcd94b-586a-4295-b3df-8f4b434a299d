"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[2864],{97679:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(86604);
/* harmony import */ var _services_fileUpload__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96876);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(51042);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(27068);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(34994);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(78367);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(85576);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);















var getBase64 = function getBase64(file) {
  return new Promise(function (resolve, reject) {
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      return resolve(reader.result);
    };
    reader.onerror = function (error) {
      return reject(error);
    };
  });
};
var FormUploadsPreviewable = function FormUploadsPreviewable(_ref) {
  var formItemName = _ref.formItemName,
    fileLimit = _ref.fileLimit,
    label = _ref.label,
    initialImages = _ref.initialImages,
    docType = _ref.docType,
    isRequired = _ref.isRequired,
    hideUploadButton = _ref.hideUploadButton,
    hideDeleteIcon = _ref.hideDeleteIcon,
    hidePreviewIcon = _ref.hidePreviewIcon,
    _ref$uploadButtonLayo = _ref.uploadButtonLayout,
    uploadButtonLayout = _ref$uploadButtonLayo === void 0 ? 'vertical' : _ref$uploadButtonLayo,
    _ref$uploadButtonIcon = _ref.uploadButtonIcon,
    uploadButtonIcon = _ref$uploadButtonIcon === void 0 ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}) : _ref$uploadButtonIcon,
    _ref$uploadButtonText = _ref.uploadButtonText,
    uploadButtonText = _ref$uploadButtonText === void 0 ? 'T\u1EA3i l\xEAn' : _ref$uploadButtonText,
    uploadButtonStyle = _ref.uploadButtonStyle,
    readOnly = _ref.readOnly;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState, 2),
    previewOpen = _useState2[0],
    setPreviewOpen = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState3, 2),
    previewImage = _useState4[0],
    setPreviewImage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(''),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState5, 2),
    previewTitle = _useState6[0],
    setPreviewTitle = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(initialImages),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState7, 2),
    imageList = _useState8[0],
    setImageList = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]),
    _useState10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4___default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  (0,_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_12__/* .useDeepCompareEffect */ .KW)(function () {
    var listImg = (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .getListFileUrlFromStringV2 */ .JJ)({
      arrUrlString: initialImages
    }).map(function (url, index) {
      return {
        name: "\\u1EA2nh ".concat((index + 1).toString()),
        url: url || '',
        uid: (-index).toString(),
        status: url ? 'done' : 'error'
      };
    });
    setFileList(listImg);
  }, [initialImages]);
  var handlePreview = /*#__PURE__*/function () {
    var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(file) {
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!(!file.url && !file.preview)) {
              _context.next = 4;
              break;
            }
            _context.next = 3;
            return getBase64(file.originFileObj);
          case 3:
            file.preview = _context.sent;
          case 4:
            setPreviewImage(file.url || file.preview);
            setPreviewOpen(true);
            setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlePreview(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleChange = /*#__PURE__*/function () {
    var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(_ref3) {
      var newFileList, oldFileList, uploadListRes, checkUploadFailed, arrFileUrl;
      return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            newFileList = _ref3.fileList;
            oldFileList = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(fileList);
            if (!readOnly) {
              _context3.next = 4;
              break;
            }
            return _context3.abrupt("return");
          case 4:
            setFileList(newFileList);
            _context3.next = 7;
            return Promise.allSettled(newFileList.map( /*#__PURE__*/function () {
              var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(item) {
                var _item$lastModified;
                return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      if (!item.url) {
                        _context2.next = 2;
                        break;
                      }
                      return _context2.abrupt("return", {
                        data: {
                          message: {
                            file_url: item.url.split('file_url=').at(-1)
                          }
                        }
                      });
                    case 2:
                      _context2.next = 4;
                      return (0,_services_fileUpload__WEBPACK_IMPORTED_MODULE_6__/* .uploadFile */ .cT)({
                        docType: docType || _common_contanst_constanst__WEBPACK_IMPORTED_MODULE_5__/* .DOCTYPE_ERP */ .lH.iotPlant,
                        docName: item.name + Math.random().toString(4) + ((_item$lastModified = item.lastModified) === null || _item$lastModified === void 0 ? void 0 : _item$lastModified.toString(4)),
                        file: item.originFileObj
                      });
                    case 4:
                      return _context2.abrupt("return", _context2.sent);
                    case 5:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }));
              return function (_x3) {
                return _ref5.apply(this, arguments);
              };
            }()));
          case 7:
            uploadListRes = _context3.sent;
            checkUploadFailed = uploadListRes.find(function (item) {
              return item.status === 'rejected';
            });
            if (checkUploadFailed) {
              antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .ZP.error({
                content: "upload \\u1EA3nh kh\\xF4ng th\\xE0nh c\\xF4ng"
              });
              setFileList(oldFileList);
            }
            arrFileUrl = uploadListRes.reduce(function (prev, item) {
              var _item$value, _item$value2;
              return item.status === 'fulfilled' && item !== null && item !== void 0 && (_item$value = item.value) !== null && _item$value !== void 0 && (_item$value = _item$value.data) !== null && _item$value !== void 0 && (_item$value = _item$value.message) !== null && _item$value !== void 0 && _item$value.file_url ? [].concat(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(prev), [item === null || item === void 0 || (_item$value2 = item.value) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.data) === null || _item$value2 === void 0 || (_item$value2 = _item$value2.message) === null || _item$value2 === void 0 ? void 0 : _item$value2.file_url]) : prev;
            }, []).filter(function (item) {
              return typeof item === 'string';
            });
            if (arrFileUrl) {
              setImageList(arrFileUrl.join(','));
              form.setFieldValue(formItemName, arrFileUrl.join(','));
            }
          case 12:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChange(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var uploadButton = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    style: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0___default()({
      display: 'flex',
      flexDirection: uploadButtonLayout === 'vertical' ? 'column' : 'row',
      alignItems: 'center',
      gap: uploadButtonLayout === 'vertical' ? '8px' : '4px'
    }, uploadButtonStyle),
    children: [uploadButtonIcon, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      children: uploadButtonText
    })]
  });
  var handleCancel = function handleCancel() {
    return setPreviewOpen(false);
  };
  var normFile = function normFile(e) {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_14__/* .ProForm */ .A.Item, {
      name: formItemName,
      initialValue: imageList,
      label: label,
      rules: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_1___default()(isRequired ? [{
        required: isRequired
      }] : []),
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
        listType: "picture-card",
        fileList: fileList,
        onPreview: handlePreview,
        maxCount: fileLimit,
        onChange: handleChange,
        multiple: true,
        showUploadList: {
          showRemoveIcon: !hideDeleteIcon,
          showPreviewIcon: !hidePreviewIcon
        },
        children: !hideUploadButton && (fileList.length >= fileLimit ? null : uploadButton)
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
      open: previewOpen,
      title: previewTitle,
      footer: null,
      onCancel: handleCancel,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("img", {
        alt: "example",
        style: {
          width: '100%'
        },
        src: previewImage
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (FormUploadsPreviewable);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///97679
`)},22864:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Create; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./src/components/Task/TaskItemUsed/ItemUsedTableCreateView.tsx + 1 modules
var ItemUsedTableCreateView = __webpack_require__(60325);
// EXTERNAL MODULE: ./src/components/Task/TaskProductionNew/ProductionTableCreateView.tsx + 1 modules
var ProductionTableCreateView = __webpack_require__(1316);
// EXTERNAL MODULE: ./src/components/Task/TaskTodo/CreateTodoTableEditer.tsx + 2 modules
var CreateTodoTableEditer = __webpack_require__(84726);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./src/services/farming-plan.ts
var farming_plan = __webpack_require__(74459);
// EXTERNAL MODULE: ./src/stores/TaskItemUsedCreateStore.tsx
var TaskItemUsedCreateStore = __webpack_require__(14682);
// EXTERNAL MODULE: ./src/stores/TaskProductionCreateStore.tsx
var TaskProductionCreateStore = __webpack_require__(55059);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/hooks/useDeepCompareEffect/index.js
var useDeepCompareEffect = __webpack_require__(27068);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./src/components/FormUploadsPreviewable/index.tsx
var FormUploadsPreviewable = __webpack_require__(97679);
// EXTERNAL MODULE: ./src/services/cropManager.ts
var cropManager = __webpack_require__(77890);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Checkbox/index.js
var Checkbox = __webpack_require__(63434);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateRangePicker/index.js
var DateRangePicker = __webpack_require__(34540);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/message/index.js
var message = __webpack_require__(45360);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/date-picker/index.js + 78 modules
var date_picker = __webpack_require__(47676);
// EXTERNAL MODULE: ./node_modules/antd/es/checkbox/index.js + 3 modules
var es_checkbox = __webpack_require__(84567);
// EXTERNAL MODULE: ./node_modules/antd/es/input-number/index.js + 16 modules
var input_number = __webpack_require__(9735);
// EXTERNAL MODULE: ./node_modules/lodash/fp.js
var fp = __webpack_require__(78230);
// EXTERNAL MODULE: ./src/pages/FarmingManagement/WorkflowManagement/TagManager/ProFormTagSelect.tsx + 2 modules
var ProFormTagSelect = __webpack_require__(97035);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/Create/DetailedInfo.tsx



















var PAGE_SIZE = 20;
var DetailedInfo = function DetailedInfo(_ref) {
  var children = _ref.children,
    onEditTagSuccess = _ref.onEditTagSuccess,
    currentPlanParam = _ref.currentPlanParam,
    _ref$onFileListChange = _ref.onFileListChange,
    onFileListChange = _ref$onFileListChange === void 0 ? function () {} : _ref$onFileListChange,
    setTodoList = _ref.setTodoList,
    setTaskItems = _ref.setTaskItems,
    setWorkTimes = _ref.setWorkTimes,
    setProductions = _ref.setProductions,
    _ref$isTemplateTask = _ref.isTemplateTask,
    isTemplateTask = _ref$isTemplateTask === void 0 ? false : _ref$isTemplateTask,
    _ref$openFromModal = _ref.openFromModal,
    openFromModal = _ref$openFromModal === void 0 ? false : _ref$openFromModal;
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    isInterval = _useState2[0],
    setIsInterval = _useState2[1];
  var _useState3 = (0,react.useState)({}),
    _useState4 = slicedToArray_default()(_useState3, 2),
    currentPlan = _useState4[0],
    setCurrentPlan = _useState4[1];
  var _useState5 = (0,react.useState)(''),
    _useState6 = slicedToArray_default()(_useState5, 2),
    selectedPlan = _useState6[0],
    setSelectedPlan = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    planStateOptions = _useState8[0],
    setPlanStateOptions = _useState8[1];
  var _useState9 = (0,react.useState)(false),
    _useState10 = slicedToArray_default()(_useState9, 2),
    loading = _useState10[0],
    setLoading = _useState10[1];
  var _useState11 = (0,react.useState)([]),
    _useState12 = slicedToArray_default()(_useState11, 2),
    fileList = _useState12[0],
    setFileList = _useState12[1];
  var _useState13 = (0,react.useState)(1),
    _useState14 = slicedToArray_default()(_useState13, 2),
    page = _useState14[0],
    setPage = _useState14[1];
  var _useState15 = (0,react.useState)(0),
    _useState16 = slicedToArray_default()(_useState15, 2),
    total = _useState16[0],
    setTotal = _useState16[1];
  var _useState17 = (0,react.useState)([]),
    _useState18 = slicedToArray_default()(_useState17, 2),
    taskOptions = _useState18[0],
    setTaskOptions = _useState18[1];
  var form = ProForm/* ProForm */.A.useFormInstance();
  var cropId = ProForm/* ProForm */.A.useWatch('crop', form);
  var _useState19 = (0,react.useState)(isTemplateTask),
    _useState20 = slicedToArray_default()(_useState19, 2),
    isTemplate = _useState20[0],
    setIsTemplate = _useState20[1];
  var _useState21 = (0,react.useState)([]),
    _useState22 = slicedToArray_default()(_useState21, 2),
    cropList = _useState22[0],
    setCropList = _useState22[1];
  var handleTaskSelect = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(taskId) {
      var filters, res, task, currentValues, todoList, taskItems, productions;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setLoading(true);
            _context.prev = 1;
            filters = [['iot_farming_plan_task', 'name', 'like', taskId]];
            _context.next = 5;
            return (0,farming_plan/* getTemplateTaskManagerList */.dQ)({
              filters: filters,
              page: 1,
              size: 1
            });
          case 5:
            res = _context.sent;
            task = res.data[0];
            currentValues = form.getFieldsValue(); // Get current form values
            form.setFieldsValue(objectSpread2_default()(objectSpread2_default()({}, currentValues), {}, {
              // Retain existing fields
              // crop: task.crop_id,
              label: task.label,
              status: task.status,
              // farming_plan_state: task.farming_plan_state,
              description: task.description,
              assigned_to: task.assigned_to,
              involved_in_users: task.involve_in_users ? task.involve_in_users.map(function (item) {
                return {
                  label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                  customer_user: item.customer_user,
                  value: item.name
                };
              }) : [],
              tag: task.tag
            }));
            todoList = task.todo_list ? task.todo_list.map(function (item) {
              return {
                name: item.name,
                label: item.label,
                status: item.status,
                description: item.description,
                is_completed: 0,
                customer_user_id: item.customer_user_id,
                customer_user_name: item.customer_user_name
              };
            }) : [];
            setTodoList(todoList);
            taskItems = task.item_list ? task.item_list.map(function (item) {
              return {
                iot_category_id: item.iot_category_id,
                item_name: item.item_name,
                label: item.label,
                uom_name: item.uom_name,
                description: item.description,
                exp_quantity: item.exp_quantity
              };
            }) : [];
            setTaskItems(taskItems);
            productions = task.prod_quantity_list ? task.prod_quantity_list.map(function (item) {
              return {
                product_id: item.product_id,
                label: item.label,
                item_name: item.item_name,
                uom_name: item.uom_name,
                description: item.description,
                exp_quantity: item.exp_quantity
              };
            }) : [];
            setProductions(productions);
            _context.next = 21;
            break;
          case 17:
            _context.prev = 17;
            _context.t0 = _context["catch"](1);
            console.log(_context.t0);
            message/* default */.ZP.error('Failed to fetch task details.');
          case 21:
            _context.prev = 21;
            setLoading(false);
            return _context.finish(21);
          case 24:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 17, 21, 24]]);
    }));
    return function handleTaskSelect(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var _useModel = (0,_umi_production_exports.useModel)("@@initialState"),
    initialState = _useModel.initialState;
  var currentUser = initialState === null || initialState === void 0 ? void 0 : initialState.currentUser;
  (0,react.useEffect)(function () {
    onFileListChange(fileList);
  }, [fileList]);
  (0,react.useEffect)(function () {
    form.setFieldValue('farming_plan', currentPlanParam);
    setSelectedPlan(currentPlanParam.name);
    form.setFieldValue('is_template', isTemplateTask);
    setIsTemplate(isTemplateTask);
  }, [currentPlanParam]);
  (0,react.useEffect)(function () {
    form.setFieldValue('assigned_to', currentUser === null || currentUser === void 0 ? void 0 : currentUser.user_id);
  }, [currentUser]);
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
        var res, today, todayState, _todayState$data, _res$data$at;
        return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.prev = 0;
              if (selectedPlan) {
                _context2.next = 3;
                break;
              }
              return _context2.abrupt("return");
            case 3:
              _context2.next = 5;
              return (0,farming_plan/* getFarmingPlanState */.jY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan]]
              });
            case 5:
              res = _context2.sent;
              setPlanStateOptions(res.data.map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
              today = new Date();
              _context2.next = 10;
              return (0,farming_plan/* getFarmingPlanState */.jY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                filters: [['iot_farming_plan_state', 'farming_plan', 'like', selectedPlan], ['iot_farming_plan_state', 'start_date', '<=', today], ['iot_farming_plan_state', 'end_date', '>=', today]]
              });
            case 10:
              todayState = _context2.sent;
              if (todayState.data.length !== 0) {
                form.setFieldValue('farming_plan_state', todayState === null || todayState === void 0 || (_todayState$data = todayState.data) === null || _todayState$data === void 0 || (_todayState$data = _todayState$data.at(0)) === null || _todayState$data === void 0 ? void 0 : _todayState$data.name);
                form.setFieldValue('start_date', dayjs_min_default()(today.toISOString()));
              } else {
                form.setFieldValue('farming_plan_state', (_res$data$at = res.data.at(0)) === null || _res$data$at === void 0 ? void 0 : _res$data$at.name);
              }
              _context2.next = 17;
              break;
            case 14:
              _context2.prev = 14;
              _context2.t0 = _context2["catch"](0);
              message/* default */.ZP.error(_context2.t0.toString());
            case 17:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[0, 14]]);
      }));
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    fetchData();
    if (currentPlanParam) {
      setCurrentPlan(currentPlanParam);
    }
  }, [selectedPlan]);
  var _useRequest = (0,_umi_production_exports.useRequest)(function (_ref4) {
      var cropId = _ref4.cropId;
      return (0,farming_plan/* getFarmingPlanList */.Qo)({
        page: 1,
        size: 1,
        filters: [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlan, 'crop', '=', cropId]]
      });
    }, {
      manual: true
    }),
    loadingFarmingPlan = _useRequest.loading,
    getFarmingPlanByCrop = _useRequest.run,
    data = _useRequest.data;
  var isDisableSelectCrop = (0,react.useMemo)(function () {
    return form.getFieldValue('crop');
  }, []);
  var handleChangeCrop = /*#__PURE__*/function () {
    var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(v) {
      var _res$;
      var res, farmingPlan;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return getFarmingPlanByCrop({
              cropId: v
            });
          case 2:
            res = _context3.sent;
            farmingPlan = res === null || res === void 0 || (_res$ = res[0]) === null || _res$ === void 0 ? void 0 : _res$.name;
            form.setFieldValue('farming_plan', farmingPlan);
            setSelectedPlan(farmingPlan);
          case 6:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handleChangeCrop(_x2) {
      return _ref5.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    handleChangeCrop(cropId);
  }, [cropId]);
  var intl = (0,_umi_production_exports.useIntl)();
  (0,react.useEffect)(function () {
    var fetchCropList = /*#__PURE__*/function () {
      var _ref6 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4() {
        var res;
        return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              if (!isTemplate) {
                _context4.next = 6;
                break;
              }
              _context4.next = 3;
              return (0,cropManager/* getTemplateCropList */.LY)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              });
            case 3:
              _context4.t0 = _context4.sent;
              _context4.next = 9;
              break;
            case 6:
              _context4.next = 8;
              return (0,cropManager/* getCropList */.TQ)({
                page: 1,
                size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY
              });
            case 8:
              _context4.t0 = _context4.sent;
            case 9:
              res = _context4.t0;
              // form.setFieldValue(
              //   'crop',
              //   uniqBy('name', res.data).map((item: any) => ({
              //     label: item.label,
              //     value: item.name,
              //   })),
              // );
              setCropList((0,fp.uniqBy)('name', res.data).map(function (item) {
                return {
                  label: item.label,
                  value: item.name
                };
              }));
            case 11:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      return function fetchCropList() {
        return _ref6.apply(this, arguments);
      };
    }();
    fetchCropList();
  }, [isTemplate]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
    spinning: loading,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
      gutter: [5, 5],
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
        md: 24,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
          title: intl.formatMessage({
            id: 'common.detail'
          }),
          children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
            gutter: [5, 5],
            children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(FormUploadsPreviewable/* default */.Z, {
                fileLimit: 20,
                label: intl.formatMessage({
                  id: 'common.image'
                }),
                formItemName: 'upload-image'
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              className: "gutter-row",
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                name: "copy_from_task",
                label: intl.formatMessage({
                  id: 'common.copy_from_task'
                }),
                showSearch: true,
                request: ( /*#__PURE__*/function () {
                  var _ref7 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee5(searchKeys) {
                    var filters, res;
                    return regeneratorRuntime_default()().wrap(function _callee5$(_context5) {
                      while (1) switch (_context5.prev = _context5.next) {
                        case 0:
                          filters = searchKeys.keyWords ? [['iot_farming_plan_task', 'label', 'like', searchKeys.keyWords]] : undefined;
                          _context5.next = 3;
                          return (0,farming_plan/* getTemplateTaskManagerList */.dQ)({
                            filters: filters,
                            page: page,
                            size: PAGE_SIZE
                          });
                        case 3:
                          res = _context5.sent;
                          return _context5.abrupt("return", res.data.map(function (item) {
                            return {
                              label: item.label,
                              value: item.name,
                              cropName: item.crop_name,
                              stateName: item.state_name
                            };
                          }));
                        case 5:
                        case "end":
                          return _context5.stop();
                      }
                    }, _callee5);
                  }));
                  return function (_x3) {
                    return _ref7.apply(this, arguments);
                  };
                }()),
                onChange: handleTaskSelect,
                fieldProps: {
                  options: taskOptions,
                  optionLabelProp: 'label',
                  optionRender: function optionRender(option) {
                    return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
                      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                        children: option.label
                      }), /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                        style: {
                          fontSize: '12px',
                          color: '#888'
                        },
                        children: "VM: ".concat(option.data.cropName, " - G\\u0110: ").concat(option.data.stateName)
                      })]
                    });
                  }
                }
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Checkbox/* default */.Z, {
                name: "is_template",
                label: intl.formatMessage({
                  id: 'common.template_task'
                }),
                fieldProps: {
                  onChange: function onChange(event) {
                    setIsTemplate(event.target.checked);
                  }
                },
                disabled: openFromModal
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.name'
                }),
                rules: [{
                  required: true
                }],
                name: "label"
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsxs)(col/* default */.Z, {
              span: 12,
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
                hidden: true,
                name: 'farming_plan'
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                name: "crop",
                label: intl.formatMessage({
                  id: isTemplate ? 'common.template-crop' : 'common.crop'
                }),
                onChange: handleChangeCrop,
                disabled: isDisableSelectCrop,
                showSearch: true
                // request={async () => {
                //   return form.getFieldValue('crop');
                // }}
                ,
                options: cropList,
                rules: [{
                  required: true
                }]
              })]
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.state'
                }),
                name: "farming_plan_state",
                options: planStateOptions,
                disabled: planStateOptions.length === 0,
                fieldProps: {
                  loading: loadingFarmingPlan
                },
                rules: [{
                  required: true
                }]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProFormTagSelect/* default */.Z, {
                onEditTagSuccess: onEditTagSuccess
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 6,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                label: intl.formatMessage({
                  id: 'common.start_date'
                }),
                rules: [{
                  required: true
                }],
                name: "start_date",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  style: {
                    width: '100%'
                  },
                  showTime: true,
                  format: 'HH:mm DD/MM/YYYY'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 6,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                label: intl.formatMessage({
                  id: 'common.end_date'
                }),
                rules: [{
                  required: true
                }],
                name: "end_date",
                children: /*#__PURE__*/(0,jsx_runtime.jsx)(date_picker["default"], {
                  style: {
                    width: '100%'
                  },
                  showTime: true,
                  format: 'HH:mm DD/MM/YYYY'
                })
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.assigned_to'
                }),
                name: "assigned_to",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee6() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee6$(_context6) {
                    while (1) switch (_context6.prev = _context6.next) {
                      case 0:
                        _context6.next = 2;
                        return (0,customerUser/* getCustomerUserList */.J9)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          fields: ['name', 'first_name', 'last_name', 'email']
                        });
                      case 2:
                        res = _context6.sent;
                        return _context6.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                            value: item.name
                          };
                        }));
                      case 4:
                      case "end":
                        return _context6.stop();
                    }
                  }, _callee6);
                }))
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.related_members'
                }),
                name: "involved_in_users",
                request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee7() {
                  var res;
                  return regeneratorRuntime_default()().wrap(function _callee7$(_context7) {
                    while (1) switch (_context7.prev = _context7.next) {
                      case 0:
                        _context7.next = 2;
                        return (0,customerUser/* getCustomerUserList */.J9)({
                          page: 1,
                          size: constanst/* DEFAULT_PAGE_SIZE_ALL */.YY,
                          fields: ['name', 'first_name', 'last_name', 'email']
                        });
                      case 2:
                        res = _context7.sent;
                        return _context7.abrupt("return", res.data.map(function (item) {
                          return {
                            label: item.first_name || item.last_name ? "".concat(item.first_name || '', " ").concat(item.last_name || '') : "".concat(item.email),
                            value: item.name
                          };
                        }));
                      case 4:
                      case "end":
                        return _context7.stop();
                    }
                  }, _callee7);
                })),
                mode: "multiple"
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 12,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                rules: [{
                  required: true
                }],
                label: intl.formatMessage({
                  id: 'common.status'
                }),
                name: "status",
                options: [{
                  label: 'L\xEAn k\u1EBF ho\u1EA1ch',
                  value: 'Plan'
                }, {
                  label: '\u0110ang x\u1EED l\xFD',
                  value: 'In progress'
                }, {
                  label: 'Ho\xE0n t\u1EA5t',
                  value: 'Done'
                }, {
                  label: 'Tr\xEC ho\xE3n',
                  value: 'Pending'
                }],
                initialValue: 'Plan'
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              span: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
                gutter: 16,
                children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                  span: 6,
                  children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                    label: intl.formatMessage({
                      id: 'common.repeat_task'
                    }),
                    name: "is_interval",
                    valuePropName: "checked",
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_checkbox/* default */.Z, {
                      value: isInterval,
                      onChange: function onChange(v) {
                        setIsInterval(v.target.checked);
                      }
                    })
                  })
                }), isInterval && /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
                  children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 4,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, {
                      label: intl.formatMessage({
                        id: 'common.each'
                      }),
                      name: "interval_value",
                      initialValue: 1,
                      rules: [{
                        required: true
                      }],
                      children: /*#__PURE__*/(0,jsx_runtime.jsx)(input_number/* default */.Z, {
                        style: {
                          width: '100%'
                        },
                        min: 1
                      })
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 6,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
                      style: {
                        width: '100%'
                      },
                      name: "interval_type",
                      label: intl.formatMessage({
                        id: 'common.time_type'
                      }),
                      options: [{
                        value: 'd',
                        label: 'Ng\xE0y'
                      }, {
                        value: 'w',
                        label: 'Tu\u1EA7n'
                      }, {
                        value: 'M',
                        label: 'Th\xE1ng'
                      }],
                      rules: [{
                        required: true
                      }]
                    })
                  }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
                    span: 8,
                    children: /*#__PURE__*/(0,jsx_runtime.jsx)(DateRangePicker/* default */.Z, {
                      style: {
                        width: '100%'
                      },
                      label: intl.formatMessage({
                        id: 'common.interval_range'
                      }),
                      width: 'lg',
                      rules: [{
                        required: true
                      }],
                      fieldProps: {
                        format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
                      },
                      name: "intervalRange"
                    })
                  })]
                })]
              })
            }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
              md: 24,
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
                label: intl.formatMessage({
                  id: 'common.note'
                }),
                name: "description"
              })
            })]
          })
        })
      })
    })
  });
};
/* harmony default export */ var Create_DetailedInfo = (DetailedInfo);
;// CONCATENATED MODULE: ./src/pages/FarmingManagement/WorkflowManagement/Create/index.tsx





















var CreateWorkflow = function CreateWorkflow(_ref) {
  var _ref$mode = _ref.mode,
    mode = _ref$mode === void 0 ? 'normal' : _ref$mode,
    onCreateSuccess = _ref.onCreateSuccess,
    open = _ref.open,
    onOpenChange = _ref.onOpenChange,
    farmingPlanStateId = _ref.farmingPlanStateId,
    planId = _ref.planId,
    defaultValue = _ref.defaultValue,
    cropId = _ref.cropId,
    isTemplateTask = _ref.isTemplateTask;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    todoList = _useState2[0],
    setTodoList = _useState2[1];
  // const [taskItems, setTaskItems] = useState<any>([]);
  // const [productions, setProductions] = useState<any>([]);

  var _useTaskItemUsedCreat = (0,TaskItemUsedCreateStore/* useTaskItemUsedCreateStore */.W)(),
    taskItems = _useTaskItemUsedCreat.taskItemUsed,
    setTaskItems = _useTaskItemUsedCreat.setTaskItemUsed;
  var _useTaskProductionCre = (0,TaskProductionCreateStore/* useTaskProductionCreateStore */.N)(),
    productions = _useTaskProductionCre.taskProduction,
    setProductions = _useTaskProductionCre.setTaskProduction;
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    workTimes = _useState4[0],
    setWorkTimes = _useState4[1];
  var _useState5 = (0,react.useState)(false),
    _useState6 = slicedToArray_default()(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var _useState7 = (0,react.useState)([]),
    _useState8 = slicedToArray_default()(_useState7, 2),
    customerUserOptions = _useState8[0],
    setCustomerUserOptions = _useState8[1];
  var _useState9 = (0,react.useState)([]),
    _useState10 = slicedToArray_default()(_useState9, 2),
    fileList = _useState10[0],
    setFileList = _useState10[1];
  var _useState11 = (0,react.useState)({}),
    _useState12 = slicedToArray_default()(_useState11, 2),
    currentPlan = _useState12[0],
    setCurrentPlan = _useState12[1];

  //set taskItems and productions when taskItemUsed and taskProduction change
  // useEffect(() => {
  //   setTaskItems(taskItemUsed);
  //   setProductions(taskProduction);
  // }, [taskItemUsed, taskProduction]);

  var onFileListChange = function onFileListChange(fileList) {
    setFileList(fileList);
  };
  var getCustomerUser = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
      var _result$data, result;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            setLoading(true);
            //call api
            _context.next = 4;
            return (0,customerUser/* customerUserListAll */.jt)();
          case 4:
            result = _context.sent;
            console.log('result', result);
            setCustomerUserOptions(result === null || result === void 0 || (_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.map(function (d) {
              return {
                value: d.name,
                label: "".concat(d.full_name, " ").concat(d.email)
              };
            }));
            _context.next = 11;
            break;
          case 9:
            _context.prev = 9;
            _context.t0 = _context["catch"](0);
          case 11:
            _context.prev = 11;
            setLoading(false);
            return _context.finish(11);
          case 14:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 9, 11, 14]]);
    }));
    return function getCustomerUser() {
      return _ref2.apply(this, arguments);
    };
  }();
  var getCurrentFarmingPlan = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
      var _farmingPlan, filters, farmingPlanState, farmingPlanId, farmingPlan;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!planId) {
              _context2.next = 6;
              break;
            }
            _context2.next = 3;
            return (0,farming_plan/* getFarmingPlan */.j1)(planId);
          case 3:
            _farmingPlan = _context2.sent;
            setCurrentPlan(_farmingPlan.data);
            return _context2.abrupt("return");
          case 6:
            if (farmingPlanStateId) {
              _context2.next = 8;
              break;
            }
            return _context2.abrupt("return");
          case 8:
            filters = [[constanst/* DOCTYPE_ERP */.lH.iotFarmingPlanState, 'name', 'like', farmingPlanStateId]];
            console.log('filters', filters);
            _context2.next = 12;
            return (0,farming_plan/* getFarmingPlanState */.jY)({
              filters: filters
            });
          case 12:
            farmingPlanState = _context2.sent;
            console.log('farming plan state is', farmingPlanState);
            farmingPlanId = farmingPlanState.data[0].farming_plan;
            _context2.next = 17;
            return (0,farming_plan/* getFarmingPlan */.j1)(farmingPlanId);
          case 17:
            farmingPlan = _context2.sent;
            setCurrentPlan(farmingPlan.data);
          case 19:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function getCurrentFarmingPlan() {
      return _ref3.apply(this, arguments);
    };
  }();
  (0,react.useEffect)(function () {
    getCustomerUser();
    getCurrentFarmingPlan();
  }, []);
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useState13 = (0,react.useState)(false),
    _useState14 = slicedToArray_default()(_useState13, 2),
    submitting = _useState14[0],
    setSubmitting = _useState14[1];
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  // const formRef = useRef<ProFormInstance<any>>();
  var onFinish = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(values) {
      var imagePath, is_interval, interval_type, interval_value, intervalRange, start_date, end_date, enable_origin_tracing, involved_in_users, requestArr, start_check, counter, task;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            console.log('start on finish');
            setSubmitting(true);
            imagePath = (values === null || values === void 0 ? void 0 : values['upload-image']) || '';
            _context3.prev = 3;
            is_interval = values.is_interval, interval_type = values.interval_type, interval_value = values.interval_value, intervalRange = values.intervalRange, start_date = values.start_date, end_date = values.end_date, enable_origin_tracing = values.enable_origin_tracing, involved_in_users = values.involved_in_users;
            requestArr = []; // Ensure intervalRange is defined and valid before mapping
            if (intervalRange && Array.isArray(intervalRange)) {
              values.intervalRange = intervalRange.map(function (d) {
                return dayjs_min_default()(d, 'DD-MM-YYYY').format('YYYY-MM-DD');
              });
            } else {
              values.intervalRange = [];
            }
            if (is_interval && interval_type && interval_value && values.intervalRange.length === 2 && dayjs_min_default()(values.intervalRange[0]).isValid() && dayjs_min_default()(values.intervalRange[1]).isValid()) {
              start_check = dayjs_min_default()(values.intervalRange[0]);
              counter = 1;
              while (start_check.isBefore(values.intervalRange[1])) {
                task = {
                  label: values.label,
                  farming_plan_state: values.farming_plan_state,
                  start_date: dayjs_min_default()(start_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss'),
                  end_date: dayjs_min_default()(end_date).add(interval_value * counter, interval_type).format('YYYY-MM-DD HH:mm:ss'),
                  description: values.description,
                  assigned_to: values.assigned_to,
                  image: imagePath,
                  status: values.status,
                  enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                  involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                    var id = typeof d === 'string' ? d : d.customer_user;
                    return {
                      customer_user: id
                    };
                  })) || [],
                  worksheet_list: workTimes.map(function (d) {
                    var _d$work_type_id = d.work_type_id,
                      work_type_id = _d$work_type_id === void 0 ? null : _d$work_type_id,
                      _d$exp_quantity = d.exp_quantity,
                      exp_quantity = _d$exp_quantity === void 0 ? 0 : _d$exp_quantity,
                      _d$quantity = d.quantity,
                      quantity = _d$quantity === void 0 ? 0 : _d$quantity,
                      _d$type = d.type,
                      type = _d$type === void 0 ? null : _d$type,
                      _d$description = d.description,
                      description = _d$description === void 0 ? null : _d$description,
                      _d$cost = d.cost,
                      cost = _d$cost === void 0 ? 0 : _d$cost;
                    return {
                      cost: cost,
                      work_type_id: work_type_id,
                      exp_quantity: exp_quantity,
                      quantity: quantity,
                      type: type,
                      description: description
                    };
                  }),
                  item_list: taskItems.map(function (d) {
                    var _d$quantity2 = d.quantity,
                      quantity = _d$quantity2 === void 0 ? 0 : _d$quantity2,
                      _d$description2 = d.description,
                      description = _d$description2 === void 0 ? null : _d$description2,
                      _d$iot_category_id = d.iot_category_id,
                      iot_category_id = _d$iot_category_id === void 0 ? null : _d$iot_category_id,
                      exp_quantity = d.exp_quantity,
                      _d$conversion_factor = d.conversion_factor,
                      conversion_factor = _d$conversion_factor === void 0 ? 1 : _d$conversion_factor,
                      _d$loss_quantity = d.loss_quantity,
                      loss_quantity = _d$loss_quantity === void 0 ? 0 : _d$loss_quantity;

                    // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                    var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                    return {
                      quantity: quantity,
                      description: description,
                      iot_category_id: iot_category_id,
                      exp_quantity: calculatedExpQuantity,
                      loss_quantity: loss_quantity
                    };
                  }),
                  todo_list: todoList.map(function (d) {
                    delete d['name'];
                    var label = d.label,
                      _d$description3 = d.description,
                      description = _d$description3 === void 0 ? null : _d$description3,
                      customer_user_id = d.customer_user_id;
                    return {
                      label: label,
                      description: description,
                      customer_user_id: customer_user_id
                    };
                  }),
                  prod_quantity_list: productions.map(function (d) {
                    var _d$quantity3 = d.quantity,
                      quantity = _d$quantity3 === void 0 ? 0 : _d$quantity3,
                      _d$description4 = d.description,
                      description = _d$description4 === void 0 ? null : _d$description4,
                      _d$product_id = d.product_id,
                      product_id = _d$product_id === void 0 ? null : _d$product_id,
                      exp_quantity = d.exp_quantity,
                      _d$conversion_factor2 = d.conversion_factor,
                      conversion_factor = _d$conversion_factor2 === void 0 ? 1 : _d$conversion_factor2,
                      _d$lost_quantity = d.lost_quantity,
                      lost_quantity = _d$lost_quantity === void 0 ? 0 : _d$lost_quantity;

                    // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                    var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                    return {
                      quantity: quantity,
                      description: description,
                      product_id: product_id,
                      exp_quantity: calculatedExpQuantity,
                      lost_quantity: lost_quantity
                    };
                  })
                };
                requestArr.push(task);
                start_check = start_check.add(interval_value, interval_type);
                counter++;
                console.log('counter is ', counter);
              }
              requestArr = requestArr.filter(function (d) {
                return d.start_date !== dayjs_min_default()(values.start_date[0]).format('YYYY-MM-DD HH:mm:ss');
              });
              requestArr.push({
                label: values.label,
                farming_plan_state: values.farming_plan_state,
                start_date: dayjs_min_default()(start_date).format('YYYY-MM-DD HH:mm:ss'),
                end_date: dayjs_min_default()(end_date).format('YYYY-MM-DD HH:mm:ss'),
                description: values.description,
                assigned_to: values.assigned_to,
                status: values.status,
                image: imagePath,
                enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                  var id = typeof d === 'string' ? d : d.customer_user;
                  return {
                    customer_user: id
                  };
                })) || [],
                worksheet_list: workTimes.map(function (d) {
                  var _d$cost2 = d.cost,
                    cost = _d$cost2 === void 0 ? 0 : _d$cost2,
                    _d$work_type_id2 = d.work_type_id,
                    work_type_id = _d$work_type_id2 === void 0 ? null : _d$work_type_id2,
                    _d$exp_quantity2 = d.exp_quantity,
                    exp_quantity = _d$exp_quantity2 === void 0 ? 0 : _d$exp_quantity2,
                    _d$quantity4 = d.quantity,
                    quantity = _d$quantity4 === void 0 ? 0 : _d$quantity4,
                    _d$type2 = d.type,
                    type = _d$type2 === void 0 ? null : _d$type2,
                    _d$description5 = d.description,
                    description = _d$description5 === void 0 ? null : _d$description5;
                  return {
                    cost: cost,
                    work_type_id: work_type_id,
                    exp_quantity: exp_quantity,
                    quantity: quantity,
                    type: type,
                    description: description
                  };
                }),
                item_list: taskItems.map(function (d) {
                  var _d$quantity5 = d.quantity,
                    quantity = _d$quantity5 === void 0 ? 0 : _d$quantity5,
                    _d$description6 = d.description,
                    description = _d$description6 === void 0 ? null : _d$description6,
                    _d$iot_category_id2 = d.iot_category_id,
                    iot_category_id = _d$iot_category_id2 === void 0 ? null : _d$iot_category_id2,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor3 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor3 === void 0 ? 1 : _d$conversion_factor3,
                    _d$loss_quantity2 = d.loss_quantity,
                    loss_quantity = _d$loss_quantity2 === void 0 ? 0 : _d$loss_quantity2;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    iot_category_id: iot_category_id,
                    exp_quantity: calculatedExpQuantity,
                    loss_quantity: loss_quantity
                  };
                }),
                todo_list: todoList.map(function (d) {
                  delete d['name'];
                  var label = d.label,
                    _d$description7 = d.description,
                    description = _d$description7 === void 0 ? null : _d$description7,
                    customer_user_id = d.customer_user_id;
                  return {
                    label: label,
                    description: description,
                    customer_user_id: customer_user_id
                  };
                }),
                prod_quantity_list: productions.map(function (d) {
                  var _d$quantity6 = d.quantity,
                    quantity = _d$quantity6 === void 0 ? 0 : _d$quantity6,
                    _d$description8 = d.description,
                    description = _d$description8 === void 0 ? null : _d$description8,
                    _d$product_id2 = d.product_id,
                    product_id = _d$product_id2 === void 0 ? null : _d$product_id2,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor4 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor4 === void 0 ? 1 : _d$conversion_factor4,
                    _d$lost_quantity2 = d.lost_quantity,
                    lost_quantity = _d$lost_quantity2 === void 0 ? 0 : _d$lost_quantity2;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    product_id: product_id,
                    exp_quantity: calculatedExpQuantity,
                    lost_quantity: lost_quantity
                  };
                })
              });
            } else {
              requestArr.push({
                label: values.label,
                farming_plan_state: values.farming_plan_state,
                start_date: dayjs_min_default()(start_date).format('YYYY-MM-DD HH:mm:ss'),
                end_date: dayjs_min_default()(end_date).format('YYYY-MM-DD HH:mm:ss'),
                description: values.description,
                assigned_to: values.assigned_to,
                status: values.status,
                image: imagePath,
                enable_origin_tracing: enable_origin_tracing ? 1 : 0,
                involve_in_users: (involved_in_users === null || involved_in_users === void 0 ? void 0 : involved_in_users.map(function (d) {
                  var id = typeof d === 'string' ? d : d.customer_user;
                  return {
                    customer_user: id
                  };
                })) || [],
                worksheet_list: workTimes.map(function (d) {
                  var _d$cost3 = d.cost,
                    cost = _d$cost3 === void 0 ? 0 : _d$cost3,
                    _d$work_type_id3 = d.work_type_id,
                    work_type_id = _d$work_type_id3 === void 0 ? null : _d$work_type_id3,
                    _d$exp_quantity3 = d.exp_quantity,
                    exp_quantity = _d$exp_quantity3 === void 0 ? 0 : _d$exp_quantity3,
                    _d$quantity7 = d.quantity,
                    quantity = _d$quantity7 === void 0 ? 0 : _d$quantity7,
                    _d$type3 = d.type,
                    type = _d$type3 === void 0 ? null : _d$type3,
                    _d$description9 = d.description,
                    description = _d$description9 === void 0 ? null : _d$description9;
                  return {
                    cost: cost,
                    work_type_id: work_type_id,
                    exp_quantity: exp_quantity,
                    quantity: quantity,
                    type: type,
                    description: description
                  };
                }),
                item_list: taskItems.map(function (d) {
                  var _d$quantity8 = d.quantity,
                    quantity = _d$quantity8 === void 0 ? 0 : _d$quantity8,
                    _d$description10 = d.description,
                    description = _d$description10 === void 0 ? null : _d$description10,
                    _d$iot_category_id3 = d.iot_category_id,
                    iot_category_id = _d$iot_category_id3 === void 0 ? null : _d$iot_category_id3,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor5 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor5 === void 0 ? 1 : _d$conversion_factor5,
                    _d$loss_quantity3 = d.loss_quantity,
                    loss_quantity = _d$loss_quantity3 === void 0 ? 0 : _d$loss_quantity3;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    iot_category_id: iot_category_id,
                    exp_quantity: calculatedExpQuantity,
                    loss_quantity: loss_quantity
                  };
                }),
                todo_list: todoList.map(function (d) {
                  delete d['name'];
                  var label = d.label,
                    _d$description11 = d.description,
                    description = _d$description11 === void 0 ? null : _d$description11,
                    customer_user_id = d.customer_user_id;
                  return {
                    label: label,
                    description: description,
                    customer_user_id: customer_user_id
                  };
                }),
                prod_quantity_list: productions.map(function (d) {
                  var _d$quantity9 = d.quantity,
                    quantity = _d$quantity9 === void 0 ? 0 : _d$quantity9,
                    _d$description12 = d.description,
                    description = _d$description12 === void 0 ? null : _d$description12,
                    _d$product_id3 = d.product_id,
                    product_id = _d$product_id3 === void 0 ? null : _d$product_id3,
                    exp_quantity = d.exp_quantity,
                    _d$conversion_factor6 = d.conversion_factor,
                    conversion_factor = _d$conversion_factor6 === void 0 ? 1 : _d$conversion_factor6,
                    _d$lost_quantity3 = d.lost_quantity,
                    lost_quantity = _d$lost_quantity3 === void 0 ? 0 : _d$lost_quantity3;

                  // T\xEDnh to\xE1n l\u1EA1i exp_quantity n\u1EBFu c\u1EA7n thi\u1EBFt
                  var calculatedExpQuantity = exp_quantity !== undefined && conversion_factor !== undefined ? exp_quantity * conversion_factor : exp_quantity;
                  return {
                    quantity: quantity,
                    description: description,
                    product_id: product_id,
                    exp_quantity: calculatedExpQuantity,
                    lost_quantity: lost_quantity
                  };
                })
              });
            }
            requestArr = requestArr.map(function (d) {
              return objectSpread2_default()(objectSpread2_default()({}, d), {}, {
                task_progress: 0,
                tag: values.tag
              });
            });
            _context3.next = 11;
            return (0,farming_plan/* createFarmingPlanTask */.qM)(requestArr);
          case 11:
            message.success({
              content: 'Created successfully'
            });
            onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
            if (onCreateSuccess) {
              onCreateSuccess === null || onCreateSuccess === void 0 || onCreateSuccess();
            } else {
              _umi_production_exports.history.push('/farming-management/workflow-management');
            }
            return _context3.abrupt("return", true);
          case 17:
            _context3.prev = 17;
            _context3.t0 = _context3["catch"](3);
            console.log('error', _context3.t0);
            return _context3.abrupt("return", false);
          case 21:
            _context3.prev = 21;
            setSubmitting(false);
            return _context3.finish(21);
          case 24:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[3, 17, 21, 24]]);
    }));
    return function onFinish(_x) {
      return _ref4.apply(this, arguments);
    };
  }();
  var _useSearchParams = (0,_umi_production_exports.useSearchParams)(),
    _useSearchParams2 = slicedToArray_default()(_useSearchParams, 2),
    searchParams = _useSearchParams2[0],
    setSearchParams = _useSearchParams2[1];
  var farmingPlanState = searchParams.get('farming_plan_state');
  var intl = (0,_umi_production_exports.useIntl)();
  /// default value for form
  (0,useDeepCompareEffect/* useDeepCompareEffect */.KW)(function () {
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
    }
  }, [defaultValue]);
  var content = /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
    onFinish: onFinish,
    submitter: false,
    initialValues: {
      farming_plan_state: farmingPlanStateId || farmingPlanState,
      start_date: dayjs_min_default()(),
      crop: cropId
    },
    form: form
    // formRef={formRef}
    ,
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
      size: 'large',
      direction: "vertical",
      style: {
        width: '100%'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Create_DetailedInfo, {
        openFromModal: mode === 'modal',
        isTemplateTask: isTemplateTask,
        currentPlanParam: currentPlan,
        onEditTagSuccess: onCreateSuccess,
        onFileListChange: onFileListChange,
        setTaskItems: setTaskItems,
        setTodoList: setTodoList,
        setWorkTimes: setWorkTimes,
        setProductions: setProductions
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(CreateTodoTableEditer/* default */.Z, {
        dataSource: todoList,
        setDataSource: setTodoList,
        customerUserOptions: customerUserOptions
      }), /*#__PURE__*/(0,jsx_runtime.jsx)(ItemUsedTableCreateView/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(ProductionTableCreateView/* default */.Z, {})]
    })
  });
  var footer = [/*#__PURE__*/(0,jsx_runtime.jsxs)(space/* default */.Z, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: function onClick() {
        if (mode === 'modal') {
          onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
          return;
        }
        _umi_production_exports.history.back();
      },
      children: intl.formatMessage({
        id: 'common.cancel'
      })
    }, 'cancel'), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      onClick: ( /*#__PURE__*/function () {
        var _ref5 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee4(event) {
          var valid;
          return regeneratorRuntime_default()().wrap(function _callee4$(_context4) {
            while (1) switch (_context4.prev = _context4.next) {
              case 0:
                console.log('submitting', event);
                _context4.prev = 1;
                _context4.next = 4;
                return form.validateFields();
              case 4:
                valid = _context4.sent;
                // Th\xEAm validateFields \u0111\u1EC3 ki\u1EC3m tra l\u1ED7i
                console.log('valid', valid);
                onFinish(form.getFieldsValue());
                // form.submit(); // Call form.submit() to trigger onFinish
                _context4.next = 12;
                break;
              case 9:
                _context4.prev = 9;
                _context4.t0 = _context4["catch"](1);
                console.error('Validation failed:', _context4.t0);
              case 12:
              case "end":
                return _context4.stop();
            }
          }, _callee4, null, [[1, 9]]);
        }));
        return function (_x2) {
          return _ref5.apply(this, arguments);
        };
      }()),
      loading: submitting,
      type: "primary",
      children: intl.formatMessage({
        id: 'common.save'
      })
    }, "save")]
  }, "footer")];
  if (mode === 'modal') return /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
    open: open,
    onCancel: function onCancel() {
      onOpenChange === null || onOpenChange === void 0 || onOpenChange(false);
    },
    confirmLoading: loading,
    width: 800,
    title: intl.formatMessage({
      id: 'common.create'
    }),
    footer: footer,
    children: content
  });
  return /*#__PURE__*/(0,jsx_runtime.jsx)(PageContainer/* PageContainer */._z, {
    fixedHeader: true
    // extra={[
    //   <Button
    //     key={'cancel'}
    //     onClick={() => {
    //       history.back();
    //     }}
    //   >
    //     H\u1EE7y
    //   </Button>,
    //   <Button key="save" type="primary">
    //     L\u01B0u
    //   </Button>,
    // ]}
    ,
    footer: footer,
    children: content
  });
};
/* harmony default export */ var Create = (CreateWorkflow);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///22864
`)}}]);
