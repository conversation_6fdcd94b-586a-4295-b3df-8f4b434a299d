"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[3913],{23984:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _services_timesheetsV2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(62872);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(42075);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(67839);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);








var AttendanceReportV2Print = function AttendanceReportV2Print(_ref) {
  var start_date = _ref.start_date,
    end_date = _ref.end_date,
    employee_id = _ref.employee_id,
    openPrint = _ref.openPrint;
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.useRequest)(_services_timesheetsV2__WEBPACK_IMPORTED_MODULE_0__/* .getAttendanceV2Report */ .j1, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var columns = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        children: index + 1
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
      id: 'common.date'
    }),
    render: function render(dom, entity, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_1__/* .formatDate */ .p6)(entity.work_date)
      });
    },
    width: 10
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
      id: 'common.day_of_week'
    }),
    dataIndex: 'day',
    width: 10,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
        id: "common.".concat(record.day_of_week)
      });
    }
  },
  // {
  //   title: <FormattedMessage id={'common.employee_id'} />,
  //   dataIndex: 'user_id',
  //   render(value, record, index) {
  //     return (
  //       <div
  //         style={{
  //           whiteSpace: 'pre-wrap',
  //         }}
  //       >
  //         {record.user_id}
  //       </div>
  //     );
  //   },
  //   width: 5,
  // },
  {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
      id: 'common.employee'
    }),
    dataIndex: 'first_name',
    width: 15,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        children: "".concat(record.first_name, " ").concat(record.last_name)
      });
    }
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_2__.FormattedMessage, {
      id: 'common.work_hour'
    }),
    dataIndex: 'work_hours',
    width: 10,
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_1__/* .formatNumeral */ .GW)(record.work_hours)
      });
    }
  }];
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; }\\n    ".concat(openPrint ? '.ant-table table { font-size: 10px; }' : '', "\\n       .ant-table-cell {padding: 2px 2px !important;}\\n    ");
  };
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    run({
      end_date: end_date,
      start_date: start_date,
      employee_id: employee_id
    });
  }, [start_date, end_date, employee_id]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z, {
          span: 24,
          style: {
            display: 'flex',
            justifyContent: 'center'
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z, {
            direction: "vertical",
            align: "center",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("b", {
              children: "B\\xC1O C\\xC1O \\u0110I\\u1EC2M DANH"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, {
              children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_1__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_1__/* .formatDate */ .p6)(end_date)]
            })]
          })
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        scroll: {
          x: 400
        },
        dataSource: data || [],
        rowKey: 'name',
        pagination: false
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (AttendanceReportV2Print);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///23984
`)},3672:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(19632);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_timesheetsV2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(62872);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(96074);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(67839);
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(92077);
/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(numeral__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(85893);











var TimesheetReportPrint = function TimesheetReportPrint(_ref) {
  var start_date = _ref.start_date,
    end_date = _ref.end_date,
    employee_id = _ref.employee_id,
    openPrint = _ref.openPrint;
  function getAllDaysBetween(startDateStr, endDateStr) {
    var startDate = new Date(startDateStr);
    var endDate = new Date(endDateStr);
    var dates = [];
    var currentDate = startDate;
    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().substring(0, 10));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  }
  var _useRequest = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.useRequest)(_services_timesheetsV2__WEBPACK_IMPORTED_MODULE_2__/* .getTimeSheetReport */ .d7, {
      manual: true,
      onError: function onError(error) {
        console.log('error', error.message);
      },
      onSuccess: function onSuccess(data, params) {
        if (openPrint) {
          setTimeout(function () {
            window.print();
          }, 1000);
        }
      }
    }),
    data = _useRequest.data,
    loading = _useRequest.loading,
    refresh = _useRequest.refresh,
    run = _useRequest.run;
  var baseColumn = [{
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_4__.FormattedMessage, {
      id: "common.index"
    }),
    dataIndex: 'index',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("div", {
        children: index + 1
      });
    },
    width: 15
  }, {
    title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_4__.FormattedMessage, {
      id: 'common.employee'
    }),
    dataIndex: 'first_name',
    render: function render(value, record, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("div", {
        children: record.first_name + ' ' + record.last_name
      });
    },
    width: 80
  }];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(baseColumn),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1___default()(_useState, 2),
    columns = _useState2[0],
    setColumns = _useState2[1];
  var getPageMargins = function getPageMargins() {
    return "@page { margin: 0 !important; }\\n       ".concat(openPrint ? '.ant-table table { font-size: 8px; }' : '', "\\n       .ant-table-cell {padding: 0px 0px !important;}\\n       .ant-table-cell .work_date {width: ").concat(openPrint ? '25px' : '50px', " !important}\\n    ");
  };
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    console.log({
      end_date: end_date,
      start_date: start_date,
      employee_id: employee_id
    });
    run({
      end_date: end_date,
      start_date: start_date,
      employee_id: employee_id
    });
    var dates = getAllDaysBetween(start_date, end_date);
    setColumns(function (prev) {
      return [].concat(baseColumn, D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0___default()(dates.map(function (date) {
        return {
          title: date.substring(8),
          width: 15,
          render: function render(dom, entity, index) {
            var _entity$work_dates$fi;
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("div", {
              className: "work_date",
              children: numeral__WEBPACK_IMPORTED_MODULE_5___default()((_entity$work_dates$fi = entity.work_dates.find(function (workdate) {
                return workdate.work_date === date;
              })) === null || _entity$work_dates$fi === void 0 ? void 0 : _entity$work_dates$fi.work_hours).format('0,0.0')
            });
          }
        };
      })), [{
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_umijs_max__WEBPACK_IMPORTED_MODULE_4__.FormattedMessage, {
          id: 'common.total'
        }),
        width: 15,
        render: function render(dome, entity, index) {
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("div", {
            children: (0,_services_utils__WEBPACK_IMPORTED_MODULE_3__/* .formatNumeral */ .GW)(entity.work_dates.reduce(function (acc, ele) {
              return acc + ele.work_hours;
            }, 0))
          });
        }
      }]);
    });
  }, [start_date, end_date, employee_id]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("style", {
      children: getPageMargins()
    }), data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
        justify: "center",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z, {
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
            justify: "center",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("b", {
              children: "B\\u1EA2NG C\\xD4NG TH\\xC1NG"
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, {
            justify: "center",
            children: ["T\\u1EEB ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_3__/* .formatDate */ .p6)(start_date), " \\u0111\\u1EBFn ", (0,_services_utils__WEBPACK_IMPORTED_MODULE_3__/* .formatDate */ .p6)(end_date)]
          })]
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)("br", {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z, {
        columns: columns,
        size: "small",
        bordered: true,
        scroll: {
          x: 400
        },
        dataSource: data || [],
        rowKey: 'name',
        pagination: false
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (TimesheetReportPrint);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///3672
`)},62872:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Kv: function() { return /* binding */ createTimeSeedTask; },
/* harmony export */   Mr: function() { return /* binding */ deleteTimesheetTask; },
/* harmony export */   OR: function() { return /* binding */ createTimeSheet; },
/* harmony export */   Qf: function() { return /* binding */ getTimeSheetTasks; },
/* harmony export */   ZS: function() { return /* binding */ getTimeSheetTaskRecords; },
/* harmony export */   d7: function() { return /* binding */ getTimeSheetReport; },
/* harmony export */   eQ: function() { return /* binding */ editTimeSheet; },
/* harmony export */   fB: function() { return /* binding */ createTimeSheetApprove; },
/* harmony export */   gr: function() { return /* binding */ getTimeSheetApproves; },
/* harmony export */   hs: function() { return /* binding */ editTimeSheetApprove; },
/* harmony export */   id: function() { return /* binding */ deleteTimesheet; },
/* harmony export */   is: function() { return /* binding */ getTimeSheets; },
/* harmony export */   j1: function() { return /* binding */ getAttendanceV2Report; },
/* harmony export */   v6: function() { return /* binding */ editTimesheet; }
/* harmony export */ });
/* unused harmony exports editTimeSheetApproveForRequest, copyTimeSheetTask */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(27484);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);




var _excluded = ["start_date", "end_date", "employee_id"];



var getTimeSheets = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTimeSheets(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createTimeSheet = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createTimeSheet(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var editTimeSheet = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function editTimeSheet(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteTimesheet = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee4(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)("api/v2/timesheet-table-v2/iot_employee_timesheet?name=".concat(id)), {
            method: 'DELETE'
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteTimesheet(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//===========================================

var getTimeSheetApproves = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee5() {
    var _ref6,
      params,
      timesheet_id,
      name,
      res,
      _args5 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _ref6 = _args5.length > 0 && _args5[0] !== undefined ? _args5[0] : {}, params = _ref6.params, timesheet_id = _ref6.timesheet_id, name = _ref6.name;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              timesheet_id: timesheet_id,
              name: name
            })
          });
        case 3:
          res = _context5.sent;
          console.log('res approval list', res);
          return _context5.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                approval_full_name: "".concat(item.approver_first_name, " ").concat(item.approver_last_name)
              });
            })
          }));
        case 6:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getTimeSheetApproves() {
    return _ref5.apply(this, arguments);
  };
}();
var createTimeSheetApprove = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, data), {}, {
              request_date: dayjs__WEBPACK_IMPORTED_MODULE_5___default()().toISOString()
            })
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function createTimeSheetApprove(_x5) {
    return _ref7.apply(this, arguments);
  };
}();
var editTimeSheetApprove = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee7(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function editTimeSheetApprove(_x6) {
    return _ref8.apply(this, arguments);
  };
}();
var editTimeSheetApproveForRequest = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref9 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee8(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/iot_timesheet_approval/request'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function editTimeSheetApproveForRequest(_x7) {
    return _ref9.apply(this, arguments);
  };
}()));
//================================================================

var getTimeSheetTasks = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee9() {
    var params,
      res,
      _args9 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          params = _args9.length > 0 && _args9[0] !== undefined ? _args9[0] : {};
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context9.sent;
          return _context9.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.timesheet_task_label
              });
            })
          }));
        case 5:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getTimeSheetTasks() {
    return _ref10.apply(this, arguments);
  };
}();
var getTimeSheetTaskRecords = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee10() {
    var params,
      res,
      _args10 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          params = _args10.length > 0 && _args10[0] !== undefined ? _args10[0] : {};
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task_record'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.task_record_label
              });
            })
          }));
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getTimeSheetTaskRecords() {
    return _ref11.apply(this, arguments);
  };
}();
var createTimeSeedTask = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee11(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function createTimeSeedTask(_x8) {
    return _ref12.apply(this, arguments);
  };
}();
var editTimesheet = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function editTimesheet(_x9) {
    return _ref13.apply(this, arguments);
  };
}();
var deleteTimesheetTask = /*#__PURE__*/function () {
  var _ref14 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee13(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'DELETE',
            params: {
              name: id
            }
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function deleteTimesheetTask(_x10) {
    return _ref14.apply(this, arguments);
  };
}();
var copyTimeSheetTask = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref15 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee14(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/copy_iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function copyTimeSheetTask(_x11) {
    return _ref15.apply(this, arguments);
  };
}()));
var getTimeSheetReport = /*#__PURE__*/function () {
  var _ref16 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee15() {
    var params,
      res,
      _args15 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          params = _args15.length > 0 && _args15[0] !== undefined ? _args15[0] : {};
          _context15.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context15.sent;
          return _context15.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item);
            })
          }));
        case 5:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getTimeSheetReport() {
    return _ref16.apply(this, arguments);
  };
}();
function getAttendanceV2Report(_x12) {
  return _getAttendanceV2Report.apply(this, arguments);
}
function _getAttendanceV2Report() {
  _getAttendanceV2Report = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee16(_ref17) {
    var start_date, end_date, employee_id, params, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          start_date = _ref17.start_date, end_date = _ref17.end_date, employee_id = _ref17.employee_id, params = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref17, _excluded);
          _context16.prev = 1;
          _context16.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report-no-group'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 10000
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              start_date: start_date,
              end_date: end_date,
              employee_id: employee_id
            })
          });
        case 4:
          result = _context16.sent;
          return _context16.abrupt("return", {
            data: result.result.data || [],
            pagination: result.result.pagination
          });
        case 8:
          _context16.prev = 8;
          _context16.t0 = _context16["catch"](1);
          return _context16.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context16.stop();
      }
    }, _callee16, null, [[1, 8]]);
  }));
  return _getAttendanceV2Report.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///62872
`)}}]);
