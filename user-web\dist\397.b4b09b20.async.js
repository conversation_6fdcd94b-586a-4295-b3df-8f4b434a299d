"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[397],{10397:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ qr_code; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/ReloadOutlined.js
var asn_ReloadOutlined = __webpack_require__(82947);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/components/AntdIcon.js + 3 modules
var AntdIcon = __webpack_require__(93771);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/ReloadOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var ReloadOutlined = function ReloadOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_ReloadOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_ReloadOutlined = (/*#__PURE__*/react.forwardRef(ReloadOutlined));
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
;// CONCATENATED MODULE: ./node_modules/qrcode.react/lib/esm/index.js
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};

// src/index.tsx


// src/third-party/qrcodegen/index.ts
/**
 * @license QR Code generator library (TypeScript)
 * Copyright (c) Project Nayuki.
 * SPDX-License-Identifier: MIT
 */
var qrcodegen;
((qrcodegen2) => {
  const _QrCode = class {
    constructor(version, errorCorrectionLevel, dataCodewords, msk) {
      this.version = version;
      this.errorCorrectionLevel = errorCorrectionLevel;
      this.modules = [];
      this.isFunction = [];
      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)
        throw new RangeError("Version value out of range");
      if (msk < -1 || msk > 7)
        throw new RangeError("Mask value out of range");
      this.size = version * 4 + 17;
      let row = [];
      for (let i = 0; i < this.size; i++)
        row.push(false);
      for (let i = 0; i < this.size; i++) {
        this.modules.push(row.slice());
        this.isFunction.push(row.slice());
      }
      this.drawFunctionPatterns();
      const allCodewords = this.addEccAndInterleave(dataCodewords);
      this.drawCodewords(allCodewords);
      if (msk == -1) {
        let minPenalty = 1e9;
        for (let i = 0; i < 8; i++) {
          this.applyMask(i);
          this.drawFormatBits(i);
          const penalty = this.getPenaltyScore();
          if (penalty < minPenalty) {
            msk = i;
            minPenalty = penalty;
          }
          this.applyMask(i);
        }
      }
      assert(0 <= msk && msk <= 7);
      this.mask = msk;
      this.applyMask(msk);
      this.drawFormatBits(msk);
      this.isFunction = [];
    }
    static encodeText(text, ecl) {
      const segs = qrcodegen2.QrSegment.makeSegments(text);
      return _QrCode.encodeSegments(segs, ecl);
    }
    static encodeBinary(data, ecl) {
      const seg = qrcodegen2.QrSegment.makeBytes(data);
      return _QrCode.encodeSegments([seg], ecl);
    }
    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {
      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)
        throw new RangeError("Invalid value");
      let version;
      let dataUsedBits;
      for (version = minVersion; ; version++) {
        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;
        const usedBits = QrSegment.getTotalBits(segs, version);
        if (usedBits <= dataCapacityBits2) {
          dataUsedBits = usedBits;
          break;
        }
        if (version >= maxVersion)
          throw new RangeError("Data too long");
      }
      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {
        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)
          ecl = newEcl;
      }
      let bb = [];
      for (const seg of segs) {
        appendBits(seg.mode.modeBits, 4, bb);
        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);
        for (const b of seg.getData())
          bb.push(b);
      }
      assert(bb.length == dataUsedBits);
      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;
      assert(bb.length <= dataCapacityBits);
      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);
      appendBits(0, (8 - bb.length % 8) % 8, bb);
      assert(bb.length % 8 == 0);
      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)
        appendBits(padByte, 8, bb);
      let dataCodewords = [];
      while (dataCodewords.length * 8 < bb.length)
        dataCodewords.push(0);
      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));
      return new _QrCode(version, ecl, dataCodewords, mask);
    }
    getModule(x, y) {
      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];
    }
    getModules() {
      return this.modules;
    }
    drawFunctionPatterns() {
      for (let i = 0; i < this.size; i++) {
        this.setFunctionModule(6, i, i % 2 == 0);
        this.setFunctionModule(i, 6, i % 2 == 0);
      }
      this.drawFinderPattern(3, 3);
      this.drawFinderPattern(this.size - 4, 3);
      this.drawFinderPattern(3, this.size - 4);
      const alignPatPos = this.getAlignmentPatternPositions();
      const numAlign = alignPatPos.length;
      for (let i = 0; i < numAlign; i++) {
        for (let j = 0; j < numAlign; j++) {
          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))
            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);
        }
      }
      this.drawFormatBits(0);
      this.drawVersion();
    }
    drawFormatBits(mask) {
      const data = this.errorCorrectionLevel.formatBits << 3 | mask;
      let rem = data;
      for (let i = 0; i < 10; i++)
        rem = rem << 1 ^ (rem >>> 9) * 1335;
      const bits = (data << 10 | rem) ^ 21522;
      assert(bits >>> 15 == 0);
      for (let i = 0; i <= 5; i++)
        this.setFunctionModule(8, i, getBit(bits, i));
      this.setFunctionModule(8, 7, getBit(bits, 6));
      this.setFunctionModule(8, 8, getBit(bits, 7));
      this.setFunctionModule(7, 8, getBit(bits, 8));
      for (let i = 9; i < 15; i++)
        this.setFunctionModule(14 - i, 8, getBit(bits, i));
      for (let i = 0; i < 8; i++)
        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));
      for (let i = 8; i < 15; i++)
        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));
      this.setFunctionModule(8, this.size - 8, true);
    }
    drawVersion() {
      if (this.version < 7)
        return;
      let rem = this.version;
      for (let i = 0; i < 12; i++)
        rem = rem << 1 ^ (rem >>> 11) * 7973;
      const bits = this.version << 12 | rem;
      assert(bits >>> 18 == 0);
      for (let i = 0; i < 18; i++) {
        const color = getBit(bits, i);
        const a = this.size - 11 + i % 3;
        const b = Math.floor(i / 3);
        this.setFunctionModule(a, b, color);
        this.setFunctionModule(b, a, color);
      }
    }
    drawFinderPattern(x, y) {
      for (let dy = -4; dy <= 4; dy++) {
        for (let dx = -4; dx <= 4; dx++) {
          const dist = Math.max(Math.abs(dx), Math.abs(dy));
          const xx = x + dx;
          const yy = y + dy;
          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)
            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);
        }
      }
    }
    drawAlignmentPattern(x, y) {
      for (let dy = -2; dy <= 2; dy++) {
        for (let dx = -2; dx <= 2; dx++)
          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);
      }
    }
    setFunctionModule(x, y, isDark) {
      this.modules[y][x] = isDark;
      this.isFunction[y][x] = true;
    }
    addEccAndInterleave(data) {
      const ver = this.version;
      const ecl = this.errorCorrectionLevel;
      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))
        throw new RangeError("Invalid argument");
      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];
      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);
      const numShortBlocks = numBlocks - rawCodewords % numBlocks;
      const shortBlockLen = Math.floor(rawCodewords / numBlocks);
      let blocks = [];
      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);
      for (let i = 0, k = 0; i < numBlocks; i++) {
        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));
        k += dat.length;
        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);
        if (i < numShortBlocks)
          dat.push(0);
        blocks.push(dat.concat(ecc));
      }
      let result = [];
      for (let i = 0; i < blocks[0].length; i++) {
        blocks.forEach((block, j) => {
          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)
            result.push(block[i]);
        });
      }
      assert(result.length == rawCodewords);
      return result;
    }
    drawCodewords(data) {
      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))
        throw new RangeError("Invalid argument");
      let i = 0;
      for (let right = this.size - 1; right >= 1; right -= 2) {
        if (right == 6)
          right = 5;
        for (let vert = 0; vert < this.size; vert++) {
          for (let j = 0; j < 2; j++) {
            const x = right - j;
            const upward = (right + 1 & 2) == 0;
            const y = upward ? this.size - 1 - vert : vert;
            if (!this.isFunction[y][x] && i < data.length * 8) {
              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));
              i++;
            }
          }
        }
      }
      assert(i == data.length * 8);
    }
    applyMask(mask) {
      if (mask < 0 || mask > 7)
        throw new RangeError("Mask value out of range");
      for (let y = 0; y < this.size; y++) {
        for (let x = 0; x < this.size; x++) {
          let invert;
          switch (mask) {
            case 0:
              invert = (x + y) % 2 == 0;
              break;
            case 1:
              invert = y % 2 == 0;
              break;
            case 2:
              invert = x % 3 == 0;
              break;
            case 3:
              invert = (x + y) % 3 == 0;
              break;
            case 4:
              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;
              break;
            case 5:
              invert = x * y % 2 + x * y % 3 == 0;
              break;
            case 6:
              invert = (x * y % 2 + x * y % 3) % 2 == 0;
              break;
            case 7:
              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;
              break;
            default:
              throw new Error("Unreachable");
          }
          if (!this.isFunction[y][x] && invert)
            this.modules[y][x] = !this.modules[y][x];
        }
      }
    }
    getPenaltyScore() {
      let result = 0;
      for (let y = 0; y < this.size; y++) {
        let runColor = false;
        let runX = 0;
        let runHistory = [0, 0, 0, 0, 0, 0, 0];
        for (let x = 0; x < this.size; x++) {
          if (this.modules[y][x] == runColor) {
            runX++;
            if (runX == 5)
              result += _QrCode.PENALTY_N1;
            else if (runX > 5)
              result++;
          } else {
            this.finderPenaltyAddHistory(runX, runHistory);
            if (!runColor)
              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;
            runColor = this.modules[y][x];
            runX = 1;
          }
        }
        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;
      }
      for (let x = 0; x < this.size; x++) {
        let runColor = false;
        let runY = 0;
        let runHistory = [0, 0, 0, 0, 0, 0, 0];
        for (let y = 0; y < this.size; y++) {
          if (this.modules[y][x] == runColor) {
            runY++;
            if (runY == 5)
              result += _QrCode.PENALTY_N1;
            else if (runY > 5)
              result++;
          } else {
            this.finderPenaltyAddHistory(runY, runHistory);
            if (!runColor)
              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;
            runColor = this.modules[y][x];
            runY = 1;
          }
        }
        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;
      }
      for (let y = 0; y < this.size - 1; y++) {
        for (let x = 0; x < this.size - 1; x++) {
          const color = this.modules[y][x];
          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])
            result += _QrCode.PENALTY_N2;
        }
      }
      let dark = 0;
      for (const row of this.modules)
        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);
      const total = this.size * this.size;
      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;
      assert(0 <= k && k <= 9);
      result += k * _QrCode.PENALTY_N4;
      assert(0 <= result && result <= 2568888);
      return result;
    }
    getAlignmentPatternPositions() {
      if (this.version == 1)
        return [];
      else {
        const numAlign = Math.floor(this.version / 7) + 2;
        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;
        let result = [6];
        for (let pos = this.size - 7; result.length < numAlign; pos -= step)
          result.splice(1, 0, pos);
        return result;
      }
    }
    static getNumRawDataModules(ver) {
      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)
        throw new RangeError("Version number out of range");
      let result = (16 * ver + 128) * ver + 64;
      if (ver >= 2) {
        const numAlign = Math.floor(ver / 7) + 2;
        result -= (25 * numAlign - 10) * numAlign - 55;
        if (ver >= 7)
          result -= 36;
      }
      assert(208 <= result && result <= 29648);
      return result;
    }
    static getNumDataCodewords(ver, ecl) {
      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
    }
    static reedSolomonComputeDivisor(degree) {
      if (degree < 1 || degree > 255)
        throw new RangeError("Degree out of range");
      let result = [];
      for (let i = 0; i < degree - 1; i++)
        result.push(0);
      result.push(1);
      let root = 1;
      for (let i = 0; i < degree; i++) {
        for (let j = 0; j < result.length; j++) {
          result[j] = _QrCode.reedSolomonMultiply(result[j], root);
          if (j + 1 < result.length)
            result[j] ^= result[j + 1];
        }
        root = _QrCode.reedSolomonMultiply(root, 2);
      }
      return result;
    }
    static reedSolomonComputeRemainder(data, divisor) {
      let result = divisor.map((_) => 0);
      for (const b of data) {
        const factor = b ^ result.shift();
        result.push(0);
        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));
      }
      return result;
    }
    static reedSolomonMultiply(x, y) {
      if (x >>> 8 != 0 || y >>> 8 != 0)
        throw new RangeError("Byte out of range");
      let z = 0;
      for (let i = 7; i >= 0; i--) {
        z = z << 1 ^ (z >>> 7) * 285;
        z ^= (y >>> i & 1) * x;
      }
      assert(z >>> 8 == 0);
      return z;
    }
    finderPenaltyCountPatterns(runHistory) {
      const n = runHistory[1];
      assert(n <= this.size * 3);
      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;
      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);
    }
    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {
      if (currentRunColor) {
        this.finderPenaltyAddHistory(currentRunLength, runHistory);
        currentRunLength = 0;
      }
      currentRunLength += this.size;
      this.finderPenaltyAddHistory(currentRunLength, runHistory);
      return this.finderPenaltyCountPatterns(runHistory);
    }
    finderPenaltyAddHistory(currentRunLength, runHistory) {
      if (runHistory[0] == 0)
        currentRunLength += this.size;
      runHistory.pop();
      runHistory.unshift(currentRunLength);
    }
  };
  let QrCode = _QrCode;
  QrCode.MIN_VERSION = 1;
  QrCode.MAX_VERSION = 40;
  QrCode.PENALTY_N1 = 3;
  QrCode.PENALTY_N2 = 3;
  QrCode.PENALTY_N3 = 40;
  QrCode.PENALTY_N4 = 10;
  QrCode.ECC_CODEWORDS_PER_BLOCK = [
    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],
    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],
    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],
    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]
  ];
  QrCode.NUM_ERROR_CORRECTION_BLOCKS = [
    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],
    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],
    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],
    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]
  ];
  qrcodegen2.QrCode = QrCode;
  function appendBits(val, len, bb) {
    if (len < 0 || len > 31 || val >>> len != 0)
      throw new RangeError("Value out of range");
    for (let i = len - 1; i >= 0; i--)
      bb.push(val >>> i & 1);
  }
  function getBit(x, i) {
    return (x >>> i & 1) != 0;
  }
  function assert(cond) {
    if (!cond)
      throw new Error("Assertion error");
  }
  const _QrSegment = class {
    constructor(mode, numChars, bitData) {
      this.mode = mode;
      this.numChars = numChars;
      this.bitData = bitData;
      if (numChars < 0)
        throw new RangeError("Invalid argument");
      this.bitData = bitData.slice();
    }
    static makeBytes(data) {
      let bb = [];
      for (const b of data)
        appendBits(b, 8, bb);
      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);
    }
    static makeNumeric(digits) {
      if (!_QrSegment.isNumeric(digits))
        throw new RangeError("String contains non-numeric characters");
      let bb = [];
      for (let i = 0; i < digits.length; ) {
        const n = Math.min(digits.length - i, 3);
        appendBits(parseInt(digits.substr(i, n), 10), n * 3 + 1, bb);
        i += n;
      }
      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);
    }
    static makeAlphanumeric(text) {
      if (!_QrSegment.isAlphanumeric(text))
        throw new RangeError("String contains unencodable characters in alphanumeric mode");
      let bb = [];
      let i;
      for (i = 0; i + 2 <= text.length; i += 2) {
        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;
        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));
        appendBits(temp, 11, bb);
      }
      if (i < text.length)
        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);
      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);
    }
    static makeSegments(text) {
      if (text == "")
        return [];
      else if (_QrSegment.isNumeric(text))
        return [_QrSegment.makeNumeric(text)];
      else if (_QrSegment.isAlphanumeric(text))
        return [_QrSegment.makeAlphanumeric(text)];
      else
        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];
    }
    static makeEci(assignVal) {
      let bb = [];
      if (assignVal < 0)
        throw new RangeError("ECI assignment value out of range");
      else if (assignVal < 1 << 7)
        appendBits(assignVal, 8, bb);
      else if (assignVal < 1 << 14) {
        appendBits(2, 2, bb);
        appendBits(assignVal, 14, bb);
      } else if (assignVal < 1e6) {
        appendBits(6, 3, bb);
        appendBits(assignVal, 21, bb);
      } else
        throw new RangeError("ECI assignment value out of range");
      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);
    }
    static isNumeric(text) {
      return _QrSegment.NUMERIC_REGEX.test(text);
    }
    static isAlphanumeric(text) {
      return _QrSegment.ALPHANUMERIC_REGEX.test(text);
    }
    getData() {
      return this.bitData.slice();
    }
    static getTotalBits(segs, version) {
      let result = 0;
      for (const seg of segs) {
        const ccbits = seg.mode.numCharCountBits(version);
        if (seg.numChars >= 1 << ccbits)
          return Infinity;
        result += 4 + ccbits + seg.bitData.length;
      }
      return result;
    }
    static toUtf8ByteArray(str) {
      str = encodeURI(str);
      let result = [];
      for (let i = 0; i < str.length; i++) {
        if (str.charAt(i) != "%")
          result.push(str.charCodeAt(i));
        else {
          result.push(parseInt(str.substr(i + 1, 2), 16));
          i += 2;
        }
      }
      return result;
    }
  };
  let QrSegment = _QrSegment;
  QrSegment.NUMERIC_REGEX = /^[0-9]*$/;
  QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;
  QrSegment.ALPHANUMERIC_CHARSET = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:";
  qrcodegen2.QrSegment = QrSegment;
})(qrcodegen || (qrcodegen = {}));
((qrcodegen2) => {
  let QrCode;
  ((QrCode2) => {
    const _Ecc = class {
      constructor(ordinal, formatBits) {
        this.ordinal = ordinal;
        this.formatBits = formatBits;
      }
    };
    let Ecc = _Ecc;
    Ecc.LOW = new _Ecc(0, 1);
    Ecc.MEDIUM = new _Ecc(1, 0);
    Ecc.QUARTILE = new _Ecc(2, 3);
    Ecc.HIGH = new _Ecc(3, 2);
    QrCode2.Ecc = Ecc;
  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));
})(qrcodegen || (qrcodegen = {}));
((qrcodegen2) => {
  let QrSegment;
  ((QrSegment2) => {
    const _Mode = class {
      constructor(modeBits, numBitsCharCount) {
        this.modeBits = modeBits;
        this.numBitsCharCount = numBitsCharCount;
      }
      numCharCountBits(ver) {
        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];
      }
    };
    let Mode = _Mode;
    Mode.NUMERIC = new _Mode(1, [10, 12, 14]);
    Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);
    Mode.BYTE = new _Mode(4, [8, 16, 16]);
    Mode.KANJI = new _Mode(8, [8, 10, 12]);
    Mode.ECI = new _Mode(7, [0, 0, 0]);
    QrSegment2.Mode = Mode;
  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));
})(qrcodegen || (qrcodegen = {}));
var qrcodegen_default = qrcodegen;

// src/index.tsx
/**
 * @license qrcode.react
 * Copyright (c) Paul O'Shannessy
 * SPDX-License-Identifier: ISC
 */
var ERROR_LEVEL_MAP = {
  L: qrcodegen_default.QrCode.Ecc.LOW,
  M: qrcodegen_default.QrCode.Ecc.MEDIUM,
  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,
  H: qrcodegen_default.QrCode.Ecc.HIGH
};
var DEFAULT_SIZE = 128;
var DEFAULT_LEVEL = "L";
var DEFAULT_BGCOLOR = "#FFFFFF";
var DEFAULT_FGCOLOR = "#000000";
var DEFAULT_INCLUDEMARGIN = false;
var MARGIN_SIZE = 4;
var DEFAULT_IMG_SCALE = 0.1;
function generatePath(modules, margin = 0) {
  const ops = [];
  modules.forEach(function(row, y) {
    let start = null;
    row.forEach(function(cell, x) {
      if (!cell && start !== null) {
        ops.push(\`M\${start + margin} \${y + margin}h\${x - start}v1H\${start + margin}z\`);
        start = null;
        return;
      }
      if (x === row.length - 1) {
        if (!cell) {
          return;
        }
        if (start === null) {
          ops.push(\`M\${x + margin},\${y + margin} h1v1H\${x + margin}z\`);
        } else {
          ops.push(\`M\${start + margin},\${y + margin} h\${x + 1 - start}v1H\${start + margin}z\`);
        }
        return;
      }
      if (cell && start === null) {
        start = x;
      }
    });
  });
  return ops.join("");
}
function excavateModules(modules, excavation) {
  return modules.slice().map((row, y) => {
    if (y < excavation.y || y >= excavation.y + excavation.h) {
      return row;
    }
    return row.map((cell, x) => {
      if (x < excavation.x || x >= excavation.x + excavation.w) {
        return cell;
      }
      return false;
    });
  });
}
function getImageSettings(cells, size, includeMargin, imageSettings) {
  if (imageSettings == null) {
    return null;
  }
  const margin = includeMargin ? MARGIN_SIZE : 0;
  const numCells = cells.length + margin * 2;
  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);
  const scale = numCells / size;
  const w = (imageSettings.width || defaultSize) * scale;
  const h = (imageSettings.height || defaultSize) * scale;
  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;
  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;
  let excavation = null;
  if (imageSettings.excavate) {
    let floorX = Math.floor(x);
    let floorY = Math.floor(y);
    let ceilW = Math.ceil(w + x - floorX);
    let ceilH = Math.ceil(h + y - floorY);
    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };
  }
  return { x, y, h, w, excavation };
}
var SUPPORTS_PATH2D = function() {
  try {
    new Path2D().addPath(new Path2D());
  } catch (e) {
    return false;
  }
  return true;
}();
function QRCodeCanvas(props) {
  const _a = props, {
    value,
    size = DEFAULT_SIZE,
    level = DEFAULT_LEVEL,
    bgColor = DEFAULT_BGCOLOR,
    fgColor = DEFAULT_FGCOLOR,
    includeMargin = DEFAULT_INCLUDEMARGIN,
    style,
    imageSettings
  } = _a, otherProps = __objRest(_a, [
    "value",
    "size",
    "level",
    "bgColor",
    "fgColor",
    "includeMargin",
    "style",
    "imageSettings"
  ]);
  const imgSrc = imageSettings == null ? void 0 : imageSettings.src;
  const _canvas = (0,react.useRef)(null);
  const _image = (0,react.useRef)(null);
  const [isImgLoaded, setIsImageLoaded] = (0,react.useState)(false);
  (0,react.useEffect)(() => {
    if (_canvas.current != null) {
      const canvas = _canvas.current;
      const ctx = canvas.getContext("2d");
      if (!ctx) {
        return;
      }
      let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();
      const margin = includeMargin ? MARGIN_SIZE : 0;
      const numCells = cells.length + margin * 2;
      const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);
      const image = _image.current;
      const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;
      if (haveImageToRender) {
        if (calculatedImageSettings.excavation != null) {
          cells = excavateModules(cells, calculatedImageSettings.excavation);
        }
      }
      const pixelRatio = window.devicePixelRatio || 1;
      canvas.height = canvas.width = size * pixelRatio;
      const scale = size / numCells * pixelRatio;
      ctx.scale(scale, scale);
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, numCells, numCells);
      ctx.fillStyle = fgColor;
      if (SUPPORTS_PATH2D) {
        ctx.fill(new Path2D(generatePath(cells, margin)));
      } else {
        cells.forEach(function(row, rdx) {
          row.forEach(function(cell, cdx) {
            if (cell) {
              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);
            }
          });
        });
      }
      if (haveImageToRender) {
        ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);
      }
    }
  });
  (0,react.useEffect)(() => {
    setIsImageLoaded(false);
  }, [imgSrc]);
  const canvasStyle = __spreadValues({ height: size, width: size }, style);
  let img = null;
  if (imgSrc != null) {
    img = /* @__PURE__ */ react.createElement("img", {
      src: imgSrc,
      key: imgSrc,
      style: { display: "none" },
      onLoad: () => {
        setIsImageLoaded(true);
      },
      ref: _image
    });
  }
  return /* @__PURE__ */ react.createElement(react.Fragment, null, /* @__PURE__ */ react.createElement("canvas", __spreadValues({
    style: canvasStyle,
    height: size,
    width: size,
    ref: _canvas
  }, otherProps)), img);
}
function QRCodeSVG(props) {
  const _a = props, {
    value,
    size = DEFAULT_SIZE,
    level = DEFAULT_LEVEL,
    bgColor = DEFAULT_BGCOLOR,
    fgColor = DEFAULT_FGCOLOR,
    includeMargin = DEFAULT_INCLUDEMARGIN,
    imageSettings
  } = _a, otherProps = __objRest(_a, [
    "value",
    "size",
    "level",
    "bgColor",
    "fgColor",
    "includeMargin",
    "imageSettings"
  ]);
  let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();
  const margin = includeMargin ? MARGIN_SIZE : 0;
  const numCells = cells.length + margin * 2;
  const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);
  let image = null;
  if (imageSettings != null && calculatedImageSettings != null) {
    if (calculatedImageSettings.excavation != null) {
      cells = excavateModules(cells, calculatedImageSettings.excavation);
    }
    image = /* @__PURE__ */ react.createElement("image", {
      xlinkHref: imageSettings.src,
      height: calculatedImageSettings.h,
      width: calculatedImageSettings.w,
      x: calculatedImageSettings.x + margin,
      y: calculatedImageSettings.y + margin,
      preserveAspectRatio: "none"
    });
  }
  const fgPath = generatePath(cells, margin);
  return /* @__PURE__ */ react.createElement("svg", __spreadValues({
    height: size,
    width: size,
    viewBox: \`0 0 \${numCells} \${numCells}\`
  }, otherProps), /* @__PURE__ */ react.createElement("path", {
    fill: bgColor,
    d: \`M0,0 h\${numCells}v\${numCells}H0z\`,
    shapeRendering: "crispEdges"
  }), /* @__PURE__ */ react.createElement("path", {
    fill: fgColor,
    d: fgPath,
    shapeRendering: "crispEdges"
  }), image);
}
var QRCode = (props) => {
  const _a = props, { renderAs } = _a, otherProps = __objRest(_a, ["renderAs"]);
  if (renderAs === "svg") {
    return /* @__PURE__ */ React.createElement(QRCodeSVG, __spreadValues({}, otherProps));
  }
  return /* @__PURE__ */ React.createElement(QRCodeCanvas, __spreadValues({}, otherProps));
};


// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/useLocale.js
var useLocale = __webpack_require__(10110);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/useToken.js
var useToken = __webpack_require__(29691);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/index.js
var dist_module = __webpack_require__(10274);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(14747);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/statistic.js
var statistic = __webpack_require__(45503);
;// CONCATENATED MODULE: ./node_modules/antd/es/qr-code/style/index.js




const genQRCodeStyle = token => {
  const {
    componentCls,
    lineWidth,
    lineType,
    colorSplit
  } = token;
  return {
    [componentCls]: Object.assign(Object.assign({}, (0,style/* resetComponent */.Wf)(token)), {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: token.paddingSM,
      backgroundColor: token.colorWhite,
      borderRadius: token.borderRadiusLG,
      border: \`\${(0,es/* unit */.bf)(lineWidth)} \${lineType} \${colorSplit}\`,
      position: 'relative',
      overflow: 'hidden',
      [\`& > \${componentCls}-mask\`]: {
        position: 'absolute',
        insetBlockStart: 0,
        insetInlineStart: 0,
        zIndex: 10,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
        height: '100%',
        color: token.colorText,
        lineHeight: token.lineHeight,
        background: token.QRCodeMaskBackgroundColor,
        textAlign: 'center',
        [\`& > \${componentCls}-expired, & > \${componentCls}-scanned\`]: {
          color: token.QRCodeTextColor
        }
      },
      '> canvas': {
        alignSelf: 'stretch',
        flex: 'auto',
        minWidth: 0
      },
      '&-icon': {
        marginBlockEnd: token.marginXS,
        fontSize: token.controlHeight
      }
    }),
    [\`\${componentCls}-borderless\`]: {
      borderColor: 'transparent'
    }
  };
};
const prepareComponentToken = token => ({
  QRCodeMaskBackgroundColor: new dist_module/* TinyColor */.C(token.colorBgContainer).setAlpha(0.96).toRgbString()
});
/* harmony default export */ var qr_code_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('QRCode', token => {
  const mergedToken = (0,statistic/* merge */.TS)(token, {
    QRCodeTextColor: token.colorText
  });
  return genQRCodeStyle(mergedToken);
}, prepareComponentToken));
;// CONCATENATED MODULE: ./node_modules/antd/es/qr-code/index.js
"use client";












const qr_code_QRCode = props => {
  const [, token] = (0,useToken/* default */.ZP)();
  const {
    value,
    type = 'canvas',
    icon = '',
    size = 160,
    iconSize = 40,
    color = token.colorText,
    errorLevel = 'M',
    status = 'active',
    bordered = true,
    onRefresh,
    style,
    className,
    rootClassName,
    prefixCls: customizePrefixCls,
    bgColor = 'transparent'
  } = props;
  const {
    getPrefixCls
  } = (0,react.useContext)(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('qrcode', customizePrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = qr_code_style(prefixCls);
  const imageSettings = {
    src: icon,
    x: undefined,
    y: undefined,
    height: iconSize,
    width: iconSize,
    excavate: true
  };
  const qrCodeProps = {
    value,
    size,
    level: errorLevel,
    bgColor,
    fgColor: color,
    style: {
      width: undefined,
      height: undefined
    },
    imageSettings: icon ? imageSettings : undefined
  };
  const [locale] = (0,useLocale/* default */.Z)('QRCode');
  if (false) {}
  if (!value) {
    return null;
  }
  const mergedCls = classnames_default()(prefixCls, className, rootClassName, hashId, cssVarCls, {
    [\`\${prefixCls}-borderless\`]: !bordered
  });
  return wrapCSSVar( /*#__PURE__*/react.createElement("div", {
    className: mergedCls,
    style: Object.assign(Object.assign({}, style), {
      width: size,
      height: size,
      backgroundColor: bgColor
    })
  }, status !== 'active' && ( /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-mask\`
  }, status === 'loading' && /*#__PURE__*/react.createElement(spin/* default */.Z, null), status === 'expired' && ( /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("p", {
    className: \`\${prefixCls}-expired\`
  }, locale === null || locale === void 0 ? void 0 : locale.expired), onRefresh && ( /*#__PURE__*/react.createElement(es_button/* default */.ZP, {
    type: "link",
    icon: /*#__PURE__*/react.createElement(icons_ReloadOutlined, null),
    onClick: onRefresh
  }, locale === null || locale === void 0 ? void 0 : locale.refresh)))), status === 'scanned' && /*#__PURE__*/react.createElement("p", {
    className: \`\${prefixCls}-scanned\`
  }, locale === null || locale === void 0 ? void 0 : locale.scanned))), type === 'canvas' ? /*#__PURE__*/react.createElement(QRCodeCanvas, Object.assign({}, qrCodeProps)) : /*#__PURE__*/react.createElement(QRCodeSVG, Object.assign({}, qrCodeProps))));
};
if (false) {}
/* harmony default export */ var qr_code = (qr_code_QRCode);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///10397
`)}}]);
