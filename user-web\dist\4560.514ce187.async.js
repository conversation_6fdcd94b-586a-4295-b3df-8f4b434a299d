"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4560],{952:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _layouts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(34994);

// \u517C\u5BB9\u4EE3\u7801-----------









//----------------------






/* harmony default export */ __webpack_exports__.ZP = (_layouts__WEBPACK_IMPORTED_MODULE_0__/* .ProForm */ .A);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTUyLmpzIiwibWFwcGluZ3MiOiI7QUFBb0M7QUFDcEM7QUFDOEI7QUFDRjtBQUNDO0FBQ0Q7QUFDRDtBQUNFO0FBQ0Q7QUFDRTtBQUNVO0FBQ3hDO0FBQ3VEO0FBQ1Q7QUFDakI7QUFDdUI7QUFDMUI7QUFDTTtBQUNoQyx1REFBZSxzREFBTyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby1mb3JtL2VzL2luZGV4LmpzPzQ4ZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJvRm9ybSB9IGZyb20gXCIuL2xheW91dHNcIjtcbi8vIOWFvOWuueS7o+eggS0tLS0tLS0tLS0tXG5pbXBvcnQgXCJhbnRkL2VzL2RyYXdlci9zdHlsZVwiO1xuaW1wb3J0IFwiYW50ZC9lcy9mb3JtL3N0eWxlXCI7XG5pbXBvcnQgXCJhbnRkL2VzL21vZGFsL3N0eWxlXCI7XG5pbXBvcnQgXCJhbnRkL2VzL3JhdGUvc3R5bGVcIjtcbmltcG9ydCBcImFudGQvZXMvcm93L3N0eWxlXCI7XG5pbXBvcnQgXCJhbnRkL2VzL3N0ZXBzL3N0eWxlXCI7XG5pbXBvcnQgXCJhbnRkL2VzL3RhYnMvc3R5bGVcIjtcbmltcG9ydCBcImFudGQvZXMvdXBsb2FkL3N0eWxlXCI7XG5pbXBvcnQgeyBHcmlkQ29udGV4dCB9IGZyb20gXCIuL2hlbHBlcnNcIjtcbi8vLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuZXhwb3J0IHsgUHJvRm9ybUNvbnRleHQgfSBmcm9tICdAYW50LWRlc2lnbi9wcm8tdXRpbHMnO1xuZXhwb3J0IHsgRmllbGRDb250ZXh0IH0gZnJvbSBcIi4vRmllbGRDb250ZXh0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb21wb25lbnRzXCI7XG5leHBvcnQgeyBGb3JtTGlzdENvbnRleHQgfSBmcm9tIFwiLi9jb21wb25lbnRzL0xpc3RcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2xheW91dHNcIjtcbmV4cG9ydCB7IEdyaWRDb250ZXh0LCBQcm9Gb3JtIH07XG5leHBvcnQgZGVmYXVsdCBQcm9Gb3JtOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///952
`)},90081:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  U: function() { return /* binding */ InlineErrorFormItem; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js
var LoadingOutlined = __webpack_require__(93005);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/popover/index.js
var popover = __webpack_require__(55241);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/rc-util/es/utils/get.js
var get = __webpack_require__(88306);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/compareVersions/openVisibleCompatible.js
var openVisibleCompatible = __webpack_require__(73177);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/InlineErrorFormItem/style.js



var genProStyle = function genProStyle(token) {
  var progressBgCls = "".concat(token.antCls, "-progress-bg");
  return (0,defineProperty/* default */.Z)({}, token.componentCls, {
    '&-multiple': {
      paddingBlockStart: 6,
      paddingBlockEnd: 12,
      paddingInline: 8
    },
    '&-progress': {
      '&-success': (0,defineProperty/* default */.Z)({}, progressBgCls, {
        backgroundColor: token.colorSuccess
      }),
      '&-error': (0,defineProperty/* default */.Z)({}, progressBgCls, {
        backgroundColor: token.colorError
      }),
      '&-warning': (0,defineProperty/* default */.Z)({}, progressBgCls, {
        backgroundColor: token.colorWarning
      })
    },
    '&-rule': {
      display: 'flex',
      alignItems: 'center',
      '&-icon': {
        '&-default': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '14px',
          height: '22px',
          '&-circle': {
            width: '6px',
            height: '6px',
            backgroundColor: token.colorTextSecondary,
            borderRadius: '4px'
          }
        },
        '&-loading': {
          color: token.colorPrimary
        },
        '&-error': {
          color: token.colorError
        },
        '&-success': {
          color: token.colorSuccess
        }
      },
      '&-text': {
        color: token.colorText
      }
    }
  });
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('InlineErrorFormItem', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/InlineErrorFormItem/index.js



var _excluded = ["rules", "name", "children", "popoverProps"],
  _excluded2 = ["errorType", "rules", "name", "popoverProps", "children"];










var FIX_INLINE_STYLE = {
  marginBlockStart: -5,
  marginBlockEnd: -5,
  marginInlineStart: 0,
  marginInlineEnd: 0
};
var InlineErrorFormItemPopover = function InlineErrorFormItemPopover(_ref) {
  var inputProps = _ref.inputProps,
    input = _ref.input,
    extra = _ref.extra,
    errorList = _ref.errorList,
    popoverProps = _ref.popoverProps;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    open = _useState2[0],
    setOpen = _useState2[1];
  var _useState3 = (0,react.useState)([]),
    _useState4 = (0,slicedToArray/* default */.Z)(_useState3, 2),
    errorStringList = _useState4[0],
    setErrorList = _useState4[1];
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls();
  var token = (0,useStyle/* useToken */.dQ)();
  var _useStyle = style_useStyle("".concat(prefixCls, "-form-item-with-help")),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  (0,react.useEffect)(function () {
    if (inputProps.validateStatus !== 'validating') {
      setErrorList(inputProps.errors);
    }
  }, [inputProps.errors, inputProps.validateStatus]);
  var popoverOpenProps = (0,openVisibleCompatible/* openVisibleCompatible */.X)(errorStringList.length < 1 ? false : open, function (changeOpen) {
    if (changeOpen === open) return;
    setOpen(changeOpen);
  });
  var loading = inputProps.validateStatus === 'validating';
  return /*#__PURE__*/(0,jsx_runtime.jsx)(popover/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    trigger: (popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.trigger) || ['click'],
    placement: (popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.placement) || 'topLeft'
  }, popoverOpenProps), {}, {
    getPopupContainer: popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.getPopupContainer,
    getTooltipContainer: popoverProps === null || popoverProps === void 0 ? void 0 : popoverProps.getTooltipContainer,
    content: wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-form-item ").concat(hashId, " ").concat(token.hashId).trim(),
      style: {
        margin: 0,
        padding: 0
      },
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
        className: "".concat(prefixCls, "-form-item-with-help ").concat(hashId, " ").concat(token.hashId).trim(),
        children: [loading ? /*#__PURE__*/(0,jsx_runtime.jsx)(LoadingOutlined/* default */.Z, {}) : null, errorList]
      })
    }))
  }, popoverProps), {}, {
    children: /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
      children: [input, extra]
    })
  }), "popover");
};
var InternalFormItemFunction = function InternalFormItemFunction(_ref2) {
  var rules = _ref2.rules,
    name = _ref2.name,
    children = _ref2.children,
    popoverProps = _ref2.popoverProps,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref2, _excluded);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    name: name,
    rules: rules,
    hasFeedback: false,
    shouldUpdate: function shouldUpdate(prev, next) {
      if (prev === next) return false;
      var shouldName = [name].flat(1);
      if (shouldName.length > 1) {
        shouldName.pop();
      }
      try {
        return JSON.stringify((0,get/* default */.Z)(prev, shouldName)) !== JSON.stringify((0,get/* default */.Z)(next, shouldName));
      } catch (error) {
        return true;
      }
    }
    // @ts-ignore
    ,
    _internalItemRender: {
      mark: 'pro_table_render',
      render: function render(inputProps, doms) {
        return /*#__PURE__*/(0,jsx_runtime.jsx)(InlineErrorFormItemPopover, (0,objectSpread2/* default */.Z)({
          inputProps: inputProps,
          popoverProps: popoverProps
        }, doms));
      }
    }
  }, rest), {}, {
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, FIX_INLINE_STYLE), rest === null || rest === void 0 ? void 0 : rest.style),
    children: children
  }));
};
var InlineErrorFormItem = function InlineErrorFormItem(props) {
  var errorType = props.errorType,
    rules = props.rules,
    name = props.name,
    popoverProps = props.popoverProps,
    children = props.children,
    rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded2);
  if (name && rules !== null && rules !== void 0 && rules.length && errorType === 'popover') {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(InternalFormItemFunction, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      name: name,
      rules: rules,
      popoverProps: popoverProps
    }, rest), {}, {
      children: children
    }));
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    rules: rules,
    shouldUpdate: name ? function (prev, next) {
      if (prev === next) return false;
      var shouldName = [name].flat(1);
      if (shouldName.length > 1) {
        shouldName.pop();
      }
      try {
        return JSON.stringify((0,get/* default */.Z)(prev, shouldName)) !== JSON.stringify((0,get/* default */.Z)(next, shouldName));
      } catch (error) {
        return true;
      }
    } : undefined
  }, rest), {}, {
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, FIX_INLINE_STYLE), rest.style),
    name: name,
    children: children
  }));
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///90081
`)},77398:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   X: function() { return /* binding */ genCopyable; }
/* harmony export */ });
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(25514);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



var isNeedTranText = function isNeedTranText(item) {
  var _item$valueType;
  if (item !== null && item !== void 0 && (_item$valueType = item.valueType) !== null && _item$valueType !== void 0 && _item$valueType.toString().startsWith('date')) {
    return true;
  }
  if ((item === null || item === void 0 ? void 0 : item.valueType) === 'select' || item !== null && item !== void 0 && item.valueEnum) {
    return true;
  }
  return false;
};
var getEllipsis = function getEllipsis(item) {
  var _item$ellipsis;
  if (((_item$ellipsis = item.ellipsis) === null || _item$ellipsis === void 0 ? void 0 : _item$ellipsis.showTitle) === false) {
    return false;
  }
  return item.ellipsis;
};

/**
 * \u751F\u6210 Copyable \u6216 Ellipsis \u7684 dom
 *
 * @param dom
 * @param item
 * @param text
 */
var genCopyable = function genCopyable(dom, item, text) {
  if (item.copyable || item.ellipsis) {
    var copyable = item.copyable && text ? {
      text: text,
      tooltips: ['', '']
    } : undefined;

    /** \u6709\u4E9B valueType \u9700\u8981\u8BBE\u7F6Ecopy\u7684\u4E3Astring */
    var needTranText = isNeedTranText(item);
    var ellipsis = getEllipsis(item) && text ? {
      tooltip:
      // \u652F\u6301\u4E00\u4E0B tooltip \u7684\u5173\u95ED
      (item === null || item === void 0 ? void 0 : item.tooltip) !== false && needTranText ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
        className: "pro-table-tooltip-text",
        children: dom
      }) : text
    } : false;
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z.Text, {
      style: {
        width: '100%',
        margin: 0,
        padding: 0
      },
      title: "",
      copyable: copyable,
      ellipsis: ellipsis,
      children: dom
    });
  }
  return dom;
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///77398
`)},2026:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   w: function() { return /* binding */ getFieldPropsOrFormItemProps; }
/* harmony export */ });
/* harmony import */ var _runFunction__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22270);


/**
 * \u56E0\u4E3A fieldProps \u652F\u6301\u4E86 function \u6240\u4EE5\u65B0\u589E\u4E86\u8FD9\u4E2A\u65B9\u6CD5
 *
 * @param fieldProps
 * @param form
 */
var getFieldPropsOrFormItemProps = function getFieldPropsOrFormItemProps(fieldProps, form, extraProps) {
  if (form === undefined) {
    return fieldProps;
  }
  return (0,_runFunction__WEBPACK_IMPORTED_MODULE_0__/* .runFunction */ .h)(fieldProps, form, extraProps);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjAyNi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQTZDOztBQUU3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLFNBQVMsa0VBQVc7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9wcm8tdXRpbHMvZXMvZ2V0RmllbGRQcm9wc09yRm9ybUl0ZW1Qcm9wcy9pbmRleC5qcz9hNjk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJ1bkZ1bmN0aW9uIH0gZnJvbSBcIi4uL3J1bkZ1bmN0aW9uXCI7XG5cbi8qKlxuICog5Zug5Li6IGZpZWxkUHJvcHMg5pSv5oyB5LqGIGZ1bmN0aW9uIOaJgOS7peaWsOWinuS6hui/meS4quaWueazlVxuICpcbiAqIEBwYXJhbSBmaWVsZFByb3BzXG4gKiBAcGFyYW0gZm9ybVxuICovXG5leHBvcnQgdmFyIGdldEZpZWxkUHJvcHNPckZvcm1JdGVtUHJvcHMgPSBmdW5jdGlvbiBnZXRGaWVsZFByb3BzT3JGb3JtSXRlbVByb3BzKGZpZWxkUHJvcHMsIGZvcm0sIGV4dHJhUHJvcHMpIHtcbiAgaWYgKGZvcm0gPT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybiBmaWVsZFByb3BzO1xuICB9XG4gIHJldHVybiBydW5GdW5jdGlvbihmaWVsZFByb3BzLCBmb3JtLCBleHRyYVByb3BzKTtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///2026
`)},86671:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CB: function() { return /* binding */ useEditableArray; },
/* harmony export */   aX: function() { return /* binding */ defaultActionRender; },
/* harmony export */   cx: function() { return /* binding */ editableRowByKey; },
/* harmony export */   sN: function() { return /* binding */ recordKeyToString; }
/* harmony export */ });
/* unused harmony exports SaveEditableAction, DeleteEditableAction */
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(74902);
/* harmony import */ var _babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(74165);
/* harmony import */ var _babel_runtime_helpers_esm_toArray__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(84506);
/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(15861);
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(97685);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(4942);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(91);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71002);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(93005);
/* harmony import */ var _ant_design_pro_provider__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(89451);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(86738);
/* harmony import */ var antd_es_table_hooks_useLazyKVMap__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(84164);
/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(21770);
/* harmony import */ var rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(88306);
/* harmony import */ var rc_util_es_utils_set__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(8880);
/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(80334);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(48171);
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(10178);
/* harmony import */ var _components_ProFormContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(41036);
/* harmony import */ var _hooks_useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(27068);
/* harmony import */ var _hooks_usePrevious__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(26369);
/* harmony import */ var _merge__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(92210);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85893);









var _excluded = ["map_row_parentKey"],
  _excluded2 = ["map_row_parentKey", "map_row_key"],
  _excluded3 = ["map_row_key"];
/* eslint-disable react-hooks/exhaustive-deps */
















/**
 * \u517C\u5BB9antd@4 \u548C antd@5 \u7684warning
 * @param messageStr
 */


var warning = function warning(messageStr) {
  // @ts-ignore
  return (antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .ZP.warn || antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .ZP.warning)(messageStr);
};
var recordKeyToString = function recordKeyToString(rowKey) {
  if (Array.isArray(rowKey)) return rowKey.join(',');
  return rowKey;
};
/**
 * \u4F7F\u7528map \u6765\u5220\u9664\u6570\u636E\uFF0C\u6027\u80FD\u4E00\u822C \u4F46\u662F\u51C6\u786E\u7387\u6BD4\u8F83\u9AD8
 *
 * @param keyProps
 * @param action
 */
function editableRowByKey(keyProps, action) {
  var _recordKeyToString;
  var getRowKey = keyProps.getRowKey,
    row = keyProps.row,
    data = keyProps.data,
    _keyProps$childrenCol = keyProps.childrenColumnName,
    childrenColumnName = _keyProps$childrenCol === void 0 ? 'children' : _keyProps$childrenCol;
  var key = (_recordKeyToString = recordKeyToString(keyProps.key)) === null || _recordKeyToString === void 0 ? void 0 : _recordKeyToString.toString();
  var kvMap = new Map();

  /**
   * \u6253\u5E73\u8FD9\u4E2A\u6570\u7EC4
   *
   * @param records
   * @param parentKey
   */
  function dig(records, map_row_parentKey, map_row_index) {
    records.forEach(function (record, index) {
      var eachIndex = (map_row_index || 0) * 10 + index;
      var recordKey = getRowKey(record, eachIndex).toString();
      // children \u53D6\u5728\u524D\u9762\u65B9\u4FBF\u62FC\u7684\u65F6\u5019\u6309\u7167\u53CD\u987A\u5E8F\u653E\u56DE\u53BB
      if (record && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(record) === 'object' && childrenColumnName in record) {
        dig(record[childrenColumnName] || [], recordKey, eachIndex);
      }
      var newRecord = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, record), {}, {
        map_row_key: recordKey,
        children: undefined,
        map_row_parentKey: map_row_parentKey
      });
      delete newRecord.children;
      if (!map_row_parentKey) {
        delete newRecord.map_row_parentKey;
      }
      kvMap.set(recordKey, newRecord);
    });
  }
  if (action === 'top') {
    kvMap.set(key, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, kvMap.get(key)), row));
  }
  dig(data);
  if (action === 'update') {
    kvMap.set(key, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, kvMap.get(key)), row));
  }
  if (action === 'delete') {
    kvMap.delete(key);
  }
  var fill = function fill(map) {
    var kvArrayMap = new Map();
    var kvSource = [];
    var fillNewRecord = function fillNewRecord() {
      var fillChildren = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      map.forEach(function (value) {
        if (value.map_row_parentKey && !value.map_row_key) {
          var map_row_parentKey = value.map_row_parentKey,
            rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z)(value, _excluded);
          if (!kvArrayMap.has(map_row_parentKey)) {
            kvArrayMap.set(map_row_parentKey, []);
          }
          if (fillChildren) {
            var _kvArrayMap$get;
            (_kvArrayMap$get = kvArrayMap.get(map_row_parentKey)) === null || _kvArrayMap$get === void 0 || _kvArrayMap$get.push(rest);
          }
        }
      });
    };
    fillNewRecord(action === 'top');
    map.forEach(function (value) {
      if (value.map_row_parentKey && value.map_row_key) {
        var _kvArrayMap$get2;
        var map_row_parentKey = value.map_row_parentKey,
          map_row_key = value.map_row_key,
          rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z)(value, _excluded2);
        if (kvArrayMap.has(map_row_key)) {
          rest[childrenColumnName] = kvArrayMap.get(map_row_key);
        }
        if (!kvArrayMap.has(map_row_parentKey)) {
          kvArrayMap.set(map_row_parentKey, []);
        }
        (_kvArrayMap$get2 = kvArrayMap.get(map_row_parentKey)) === null || _kvArrayMap$get2 === void 0 || _kvArrayMap$get2.push(rest);
      }
    });
    fillNewRecord(action === 'update');
    map.forEach(function (value) {
      if (!value.map_row_parentKey) {
        var map_row_key = value.map_row_key,
          rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z)(value, _excluded3);
        if (map_row_key && kvArrayMap.has(map_row_key)) {
          var item = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, rest), {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z)({}, childrenColumnName, kvArrayMap.get(map_row_key)));
          kvSource.push(item);
          return;
        }
        kvSource.push(rest);
      }
    });
    return kvSource;
  };
  return fill(kvMap);
}

/**
 * \u4FDD\u5B58\u6309\u94AE\u7684dom
 *
 * @param ActionRenderConfig
 */
function SaveEditableAction(_ref, ref) {
  var recordKey = _ref.recordKey,
    onSave = _ref.onSave,
    row = _ref.row,
    children = _ref.children,
    newLineConfig = _ref.newLineConfig,
    editorType = _ref.editorType,
    tableName = _ref.tableName;
  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_components_ProFormContext__WEBPACK_IMPORTED_MODULE_10__/* .ProFormContext */ .J);
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  var _useMountMergeState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(false),
    _useMountMergeState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(_useMountMergeState, 2),
    loading = _useMountMergeState2[0],
    setLoading = _useMountMergeState2[1];
  var save = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee() {
    var _context$getFieldForm, isMapEditor, namePath, fields, _recordKey, recordKeyPath, curValue, data, res;
    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          isMapEditor = editorType === 'Map'; // \u4E3A\u4E86\u517C\u5BB9\u7C7B\u578B\u4E3A array \u7684 dataIndex,\u5F53 recordKey \u662F\u4E00\u4E2A\u6570\u7EC4\u65F6\uFF0C\u7528\u4E8E\u83B7\u53D6\u8868\u5355\u503C\u7684 key \u53EA\u53D6\u7B2C\u4E00\u9879\uFF0C
          // \u4ECE\u8868\u5355\u4E2D\u83B7\u53D6\u56DE\u6765\u4E4B\u540E\uFF0C\u518D\u6839\u636E namepath \u83B7\u53D6\u5177\u4F53\u7684\u67D0\u4E2A\u5B57\u6BB5\u5E76\u8BBE\u7F6E
          namePath = [tableName, Array.isArray(recordKey) ? recordKey[0] : recordKey].map(function (key) {
            return key === null || key === void 0 ? void 0 : key.toString();
          }).flat(1).filter(Boolean);
          setLoading(true);
          _context.next = 6;
          return form.validateFields(namePath, {
            recursive: true
          });
        case 6:
          fields = (context === null || context === void 0 || (_context$getFieldForm = context.getFieldFormatValue) === null || _context$getFieldForm === void 0 ? void 0 : _context$getFieldForm.call(context, namePath)) || form.getFieldValue(namePath); // \u5904\u7406 dataIndex \u4E3A\u6570\u7EC4\u7684\u60C5\u51B5
          if (Array.isArray(recordKey) && recordKey.length > 1) {
            // \u83B7\u53D6 namepath
            _recordKey = (0,_babel_runtime_helpers_esm_toArray__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z)(recordKey), recordKeyPath = _recordKey.slice(1); // \u5C06\u76EE\u6807\u503C\u83B7\u53D6\u51FA\u6765\u5E76\u8BBE\u7F6E\u5230 fields \u5F53\u4E2D
            curValue = (0,rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z)(fields, recordKeyPath);
            (0,rc_util_es_utils_set__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(fields, recordKeyPath, curValue);
          }
          data = isMapEditor ? (0,rc_util_es_utils_set__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)({}, namePath, fields) : fields; // \u83B7\u53D6\u6570\u636E\u5E76\u4FDD\u5B58
          _context.next = 11;
          return onSave === null || onSave === void 0 ? void 0 : onSave(recordKey,
          // \u5982\u679C\u662F map \u6A21\u5F0F\uFF0Cfields \u5C31\u662F\u4E00\u4E2A\u503C\uFF0C\u6240\u4EE5\u9700\u8981set \u5230\u5BF9\u8C61\u4E2D
          // \u6570\u636E\u6A21\u5F0F fields \u662F\u4E00\u4E2A\u5BF9\u8C61\uFF0C\u6240\u4EE5\u4E0D\u9700\u8981
          (0,_merge__WEBPACK_IMPORTED_MODULE_18__/* .merge */ .T)({}, row, data), row, newLineConfig);
        case 11:
          res = _context.sent;
          setLoading(false);
          return _context.abrupt("return", res);
        case 16:
          _context.prev = 16;
          _context.t0 = _context["catch"](0);
          // eslint-disable-next-line no-console
          console.log(_context.t0);
          setLoading(false);
          throw _context.t0;
        case 21:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 16]]);
  })));

  // \u4FDD\u5B58\u6570\u636E
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle)(ref, function () {
    return {
      save: save
    };
  }, [save]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("a", {
    onClick: ( /*#__PURE__*/function () {
      var _ref3 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee2(e) {
        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              e.stopPropagation();
              e.preventDefault();
              _context2.prev = 2;
              _context2.next = 5;
              return save();
            case 5:
              _context2.next = 9;
              break;
            case 7:
              _context2.prev = 7;
              _context2.t0 = _context2["catch"](2);
            case 9:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[2, 7]]);
      }));
      return function (_x) {
        return _ref3.apply(this, arguments);
      };
    }()),
    children: [loading ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
      style: {
        marginInlineEnd: 8
      }
    }) : null, children || '\u4FDD\u5B58']
  }, "save");
}
/**
 * \u5220\u9664\u6309\u94AE dom
 *
 * @param ActionRenderConfig
 */
var DeleteEditableAction = function DeleteEditableAction(_ref4) {
  var recordKey = _ref4.recordKey,
    onDelete = _ref4.onDelete,
    row = _ref4.row,
    children = _ref4.children,
    deletePopconfirmMessage = _ref4.deletePopconfirmMessage;
  var _useMountMergeState3 = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(function () {
      return false;
    }),
    _useMountMergeState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(_useMountMergeState3, 2),
    loading = _useMountMergeState4[0],
    setLoading = _useMountMergeState4[1];
  var _onConfirm = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee3() {
    var res;
    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          setLoading(true);
          _context3.next = 4;
          return onDelete === null || onDelete === void 0 ? void 0 : onDelete(recordKey, row);
        case 4:
          res = _context3.sent;
          setLoading(false);
          return _context3.abrupt("return", res);
        case 9:
          _context3.prev = 9;
          _context3.t0 = _context3["catch"](0);
          // eslint-disable-next-line no-console
          console.log(_context3.t0);
          setLoading(false);
          return _context3.abrupt("return", null);
        case 14:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[0, 9]]);
  })));
  return children !== false ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(antd__WEBPACK_IMPORTED_MODULE_20__/* ["default"] */ .Z, {
    title: deletePopconfirmMessage,
    onConfirm: function onConfirm() {
      return _onConfirm();
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("a", {
      children: [loading ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
        style: {
          marginInlineEnd: 8
        }
      }) : null, children || '\u5220\u9664']
    })
  }, "delete") : null;
};
var CancelEditableAction = function CancelEditableAction(props) {
  var recordKey = props.recordKey,
    tableName = props.tableName,
    newLineConfig = props.newLineConfig,
    editorType = props.editorType,
    onCancel = props.onCancel,
    cancelEditable = props.cancelEditable,
    row = props.row,
    cancelText = props.cancelText;
  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_components_ProFormContext__WEBPACK_IMPORTED_MODULE_10__/* .ProFormContext */ .J);
  var form = antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Z.useFormInstance();
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("a", {
    onClick: ( /*#__PURE__*/function () {
      var _ref6 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee4(e) {
        var _context$getFieldForm2;
        var isMapEditor, namePath, fields, record, res;
        return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              e.stopPropagation();
              e.preventDefault();
              isMapEditor = editorType === 'Map';
              namePath = [tableName, recordKey].flat(1).filter(Boolean);
              fields = (context === null || context === void 0 || (_context$getFieldForm2 = context.getFieldFormatValue) === null || _context$getFieldForm2 === void 0 ? void 0 : _context$getFieldForm2.call(context, namePath)) || (form === null || form === void 0 ? void 0 : form.getFieldValue(namePath));
              record = isMapEditor ? (0,rc_util_es_utils_set__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)({}, namePath, fields) : fields;
              _context4.next = 8;
              return onCancel === null || onCancel === void 0 ? void 0 : onCancel(recordKey, record, row, newLineConfig);
            case 8:
              res = _context4.sent;
              _context4.next = 11;
              return cancelEditable(recordKey);
            case 11:
              /** \u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C\uFF0C\u4E0D\u7136\u7F16\u8F91\u7684\u884C\u4F1A\u4E22\u6389 */
              form.setFieldsValue((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z)({}, "".concat(recordKey), isMapEditor ? (0,rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z)(row, namePath) : row));
              return _context4.abrupt("return", res);
            case 13:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      return function (_x2) {
        return _ref6.apply(this, arguments);
      };
    }()),
    children: cancelText || '\u53D6\u6D88'
  }, "cancel");
};
function defaultActionRender(row, config) {
  var recordKey = config.recordKey,
    newLineConfig = config.newLineConfig,
    saveText = config.saveText,
    deleteText = config.deleteText;
  var SaveEditableActionRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.forwardRef)(SaveEditableAction);
  var saveRef = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createRef)();
  return {
    save: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(SaveEditableActionRef, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, config), {}, {
      row: row,
      ref: saveRef,
      children: saveText
    }), 'save' + recordKey),
    saveRef: saveRef,
    delete: (newLineConfig === null || newLineConfig === void 0 ? void 0 : newLineConfig.options.recordKey) !== recordKey ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(DeleteEditableAction, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, config), {}, {
      row: row,
      children: deleteText
    }), 'delete' + recordKey) : undefined,
    cancel: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(CancelEditableAction, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, config), {}, {
      row: row
    }), 'cancel' + recordKey)
  };
}

/**
 * \u4E00\u4E2A\u65B9\u4FBF\u7684hooks \u7528\u4E8E\u7EF4\u62A4\u7F16\u8F91\u7684\u72B6\u6001
 *
 * @param props
 */
function useEditableArray(props) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(undefined),
    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(_useState, 2),
    newLineRecordCache = _useState2[0],
    setNewLineRecordCache = _useState2[1];
  var resetMapRef = function resetMapRef() {
    var map = new Map();
    //\u5B58\u5728children\u65F6\u4F1A\u8986\u76D6Map\u7684key,\u5BFC\u81F4\u4F7F\u7528\u6570\u7EC4\u7D22\u5F15\u67E5\u627Ekey\u9519\u8BEF
    var loopGetKey = function loopGetKey(dataSource, parentKey) {
      dataSource === null || dataSource === void 0 || dataSource.forEach(function (record, index) {
        var _recordKeyToString2;
        var key = parentKey === undefined || parentKey === null ? index.toString() : parentKey + '_' + index.toString();
        map.set(key, recordKeyToString(props.getRowKey(record, -1)));
        map.set((_recordKeyToString2 = recordKeyToString(props.getRowKey(record, -1))) === null || _recordKeyToString2 === void 0 ? void 0 : _recordKeyToString2.toString(), key);
        if (props.childrenColumnName && record[props.childrenColumnName]) {
          loopGetKey(record[props.childrenColumnName], key);
        }
      });
    };
    loopGetKey(props.dataSource);
    return map;
  };
  var initDataSourceKeyIndexMap = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return resetMapRef();
  }, []);
  var dataSourceKeyIndexMapRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(initDataSourceKeyIndexMap);
  var newLineRecordRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(undefined);
  (0,_hooks_useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_21__/* .useDeepCompareEffectDebounce */ .Au)(function () {
    dataSourceKeyIndexMapRef.current = resetMapRef();
  }, [props.dataSource]);

  // \u8FD9\u91CC\u8FD9\u4E48\u505A\u662F\u4E3A\u4E86\u5B58\u4E0A\u6B21\u7684\u72B6\u6001\uFF0C\u4E0D\u7136\u6BCF\u6B21\u5B58\u4E00\u4E0B\u518D\u62FF
  newLineRecordRef.current = newLineRecordCache;
  var editableType = props.type || 'single';
  var _useLazyKVMap = (0,antd_es_table_hooks_useLazyKVMap__WEBPACK_IMPORTED_MODULE_22__/* ["default"] */ .Z)(props.dataSource, 'children', props.getRowKey),
    _useLazyKVMap2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(_useLazyKVMap, 1),
    getRecordByKey = _useLazyKVMap2[0];
  var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)([], {
      value: props.editableKeys,
      onChange: props.onChange ? function (keys) {
        var _props$onChange, _keys$filter, _keys$map$filter;
        props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(props, // \u8BA1\u7B97\u7F16\u8F91\u7684key
        (_keys$filter = keys === null || keys === void 0 ? void 0 : keys.filter(function (key) {
          return key !== undefined;
        })) !== null && _keys$filter !== void 0 ? _keys$filter : [], // \u8BA1\u7B97\u7F16\u8F91\u7684\u884C
        (_keys$map$filter = keys === null || keys === void 0 ? void 0 : keys.map(function (key) {
          return getRecordByKey(key);
        }).filter(function (key) {
          return key !== undefined;
        })) !== null && _keys$map$filter !== void 0 ? _keys$map$filter : []);
      } : undefined
    }),
    _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z)(_useMergedState, 2),
    editableKeys = _useMergedState2[0],
    setEditableRowKeys = _useMergedState2[1];

  /** \u4E00\u4E2A\u7528\u6765\u6807\u5FD7\u7684set \u63D0\u4F9B\u4E86\u65B9\u4FBF\u7684 api \u6765\u53BB\u91CD\u4EC0\u4E48\u7684 */
  var editableKeysSet = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var keys = editableType === 'single' ? editableKeys === null || editableKeys === void 0 ? void 0 : editableKeys.slice(0, 1) : editableKeys;
    return new Set(keys);
  }, [(editableKeys || []).join(','), editableType]);
  var editableKeysRef = (0,_hooks_usePrevious__WEBPACK_IMPORTED_MODULE_23__/* .usePrevious */ .D)(editableKeys);

  /** \u8FD9\u884C\u662F\u4E0D\u662F\u7F16\u8F91\u72B6\u6001 */
  var isEditable = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)(function (row) {
    var _props$getRowKey, _props$getRowKey$toSt, _props$getRowKey2, _props$getRowKey2$toS;
    // \u4E3A\u4E86\u517C\u5BB9\u4E00\u4E0Bname \u6A21\u5F0F\u7684 indexKey\uFF0C\u6240\u4EE5\u9700\u8981\u5224\u65AD\u4E24\u6B21\uFF0C\u4E00\u6B21\u662Findex\uFF0C\u4E00\u6B21\u662F\u6CA1\u6709 index \u7684
    var recordKeyOrIndex = (_props$getRowKey = props.getRowKey(row, row.index)) === null || _props$getRowKey === void 0 || (_props$getRowKey$toSt = _props$getRowKey.toString) === null || _props$getRowKey$toSt === void 0 ? void 0 : _props$getRowKey$toSt.call(_props$getRowKey);
    // \u8FD9\u91CC\u662F\u4E0D\u8BBE\u7F6E index \u7684\u5730\u65B9
    var recordKey = (_props$getRowKey2 = props.getRowKey(row, -1)) === null || _props$getRowKey2 === void 0 || (_props$getRowKey2$toS = _props$getRowKey2.toString) === null || _props$getRowKey2$toS === void 0 ? void 0 : _props$getRowKey2$toS.call(_props$getRowKey2);

    // \u90FD\u8F6C\u5316\u4E3A\u4E86\u5B57\u7B26\u4E32\uFF0C\u4E0D\u7136 number \u548C string
    var stringEditableKeys = editableKeys === null || editableKeys === void 0 ? void 0 : editableKeys.map(function (key) {
      return key === null || key === void 0 ? void 0 : key.toString();
    });
    var stringEditableKeysRef = (editableKeysRef === null || editableKeysRef === void 0 ? void 0 : editableKeysRef.map(function (key) {
      return key === null || key === void 0 ? void 0 : key.toString();
    })) || [];
    var preIsEditable = props.tableName && !!(stringEditableKeysRef !== null && stringEditableKeysRef !== void 0 && stringEditableKeysRef.includes(recordKey)) || !!(stringEditableKeysRef !== null && stringEditableKeysRef !== void 0 && stringEditableKeysRef.includes(recordKeyOrIndex));
    return {
      recordKey: recordKey,
      isEditable: props.tableName && (stringEditableKeys === null || stringEditableKeys === void 0 ? void 0 : stringEditableKeys.includes(recordKey)) || (stringEditableKeys === null || stringEditableKeys === void 0 ? void 0 : stringEditableKeys.includes(recordKeyOrIndex)),
      preIsEditable: preIsEditable
    };
  });

  /**
   * \u8FDB\u5165\u7F16\u8F91\u72B6\u6001
   *
   * @param recordKey
   */
  var startEditable = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)(function (recordKey) {
    // \u5982\u679C\u662F\u5355\u884C\u7684\u8BDD\uFF0C\u4E0D\u5141\u8BB8\u591A\u884C\u7F16\u8F91
    if (editableKeysSet.size > 0 && editableType === 'single' && props.onlyOneLineEditorAlertMessage !== false) {
      warning(props.onlyOneLineEditorAlertMessage || '\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C');
      return false;
    }
    editableKeysSet.add(recordKey);
    setEditableRowKeys(Array.from(editableKeysSet));
    return true;
  });

  /**
   * \u9000\u51FA\u7F16\u8F91\u72B6\u6001
   *
   * @param recordKey
   */
  var cancelEditable = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/function () {
    var _ref7 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee5(recordKey, needReTry) {
      var relayKey, key;
      return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            relayKey = recordKeyToString(recordKey).toString();
            key = dataSourceKeyIndexMapRef.current.get(relayKey);
            /** \u5982\u679C\u6CA1\u627E\u5230key\uFF0C\u8F6C\u5316\u4E00\u4E0B\u518D\u53BB\u627E */
            if (!(!editableKeysSet.has(relayKey) && key && (needReTry !== null && needReTry !== void 0 ? needReTry : true) && props.tableName)) {
              _context5.next = 5;
              break;
            }
            cancelEditable(key, false);
            return _context5.abrupt("return");
          case 5:
            /** \u5982\u679C\u8FD9\u4E2A\u662F new Line \u76F4\u63A5\u5220\u9664 */
            if (newLineRecordCache && newLineRecordCache.options.recordKey === recordKey) {
              setNewLineRecordCache(undefined);
            }
            editableKeysSet.delete(relayKey);
            editableKeysSet.delete(recordKeyToString(recordKey));
            setEditableRowKeys(Array.from(editableKeysSet));
            return _context5.abrupt("return", true);
          case 10:
          case "end":
            return _context5.stop();
        }
      }, _callee5);
    }));
    return function (_x3, _x4) {
      return _ref7.apply(this, arguments);
    };
  }());
  var propsOnValuesChange = (0,___WEBPACK_IMPORTED_MODULE_24__/* .useDebounceFn */ .D)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee6() {
    var _props$onValuesChange;
    var _len,
      rest,
      _key,
      _args6 = arguments;
    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          for (_len = _args6.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
            rest[_key] = _args6[_key];
          }
          //@ts-ignore
          (_props$onValuesChange = props.onValuesChange) === null || _props$onValuesChange === void 0 || _props$onValuesChange.call.apply(_props$onValuesChange, [props].concat(rest));
        case 2:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  })), 64);
  var onValuesChange = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)(function (value, values) {
    var _Object$keys$pop;
    if (!props.onValuesChange) {
      return;
    }
    var dataSource = props.dataSource;

    // \u8FD9\u91CC\u662F\u628A\u6B63\u5728\u7F16\u8F91\u4E2D\u7684\u6240\u6709\u8868\u5355\u6570\u636E\u90FD\u4FEE\u6539\u6389
    // \u4E0D\u7136\u4F1A\u7528 props \u91CC\u9762\u7684 dataSource\uFF0C\u6570\u636E\u53EA\u6709\u6B63\u5728\u7F16\u8F91\u4E2D\u7684
    // Object.keys(get(values, [props.tableName || ''].flat(1)) || values).forEach((recordKey) => {
    editableKeys === null || editableKeys === void 0 || editableKeys.forEach(function (eachRecordKey) {
      if ((newLineRecordCache === null || newLineRecordCache === void 0 ? void 0 : newLineRecordCache.options.recordKey) === eachRecordKey) return;
      var recordKey = eachRecordKey.toString();
      // \u5982\u679C\u6570\u636E\u5728\u8FD9\u4E2A form \u4E2D\u6CA1\u6709\u5C55\u793A\uFF0C\u4E5F\u4E0D\u663E\u793A
      var editRow = (0,rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z)(values, [props.tableName || '', recordKey].flat(1).filter(function (key) {
        return key || key === 0;
      }));
      if (!editRow) return;
      dataSource = editableRowByKey({
        data: dataSource,
        getRowKey: props.getRowKey,
        row: editRow,
        key: recordKey,
        childrenColumnName: props.childrenColumnName || 'children'
      }, 'update');
    });
    var relayValue = value;
    var recordKey = (_Object$keys$pop = Object.keys(relayValue || {}).pop()) === null || _Object$keys$pop === void 0 ? void 0 : _Object$keys$pop.toString();

    //\u4ECEform \u548C cache \u4E2D\u53D6\u5F97\u6570\u636E
    var newLineRecordData = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, newLineRecordCache === null || newLineRecordCache === void 0 ? void 0 : newLineRecordCache.defaultValue), (0,rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z)(values, [props.tableName || '', recordKey.toString()].flat(1).filter(function (key) {
      return key || key === 0;
    })));

    /** \u5982\u679C\u5DF2\u7ECF\u5728 dataSource \u4E2D\u5B58\u5728\u4E86\uFF0C\u76F4\u63A5 find */
    var editRow = dataSourceKeyIndexMapRef.current.has(recordKeyToString(recordKey)) ? dataSource.find(function (item, index) {
      var _props$getRowKey3;
      var key = (_props$getRowKey3 = props.getRowKey(item, index)) === null || _props$getRowKey3 === void 0 ? void 0 : _props$getRowKey3.toString();
      return key === recordKey;
    }) : newLineRecordData;
    propsOnValuesChange.run(editRow || newLineRecordData, dataSource);
  });
  var saveRefsMap = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(new Map());
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    // \u786E\u4FDD\u53EA\u4FDD\u7559\u7F16\u8F91\u72B6\u6001\u7684\uFF0C\u5176\u5B83\u7684\u90FD\u5220\u9664\u6389
    saveRefsMap.current.forEach(function (ref, key) {
      if (!editableKeysSet.has(key)) {
        saveRefsMap.current.delete(key);
      }
    });
  }, [saveRefsMap, editableKeysSet]);
  /**
   * \u4FDD\u5B58\u7F16\u8F91\u884C
   *
   * @param recordKey
   * @param needReTry
   */
  var saveEditable = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/function () {
    var _ref9 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee7(recordKey, needReTry) {
      var relayKey, key, saveRef, _saveRef$current;
      return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee7$(_context7) {
        while (1) switch (_context7.prev = _context7.next) {
          case 0:
            relayKey = recordKeyToString(recordKey);
            key = dataSourceKeyIndexMapRef.current.get(recordKey.toString());
            /** \u5982\u679C\u6CA1\u627E\u5230key\uFF0C\u8F6C\u5316\u4E00\u4E0B\u518D\u53BB\u627E */
            if (!(!editableKeysSet.has(relayKey) && key && (needReTry !== null && needReTry !== void 0 ? needReTry : true) && props.tableName)) {
              _context7.next = 6;
              break;
            }
            _context7.next = 5;
            return saveEditable(key, false);
          case 5:
            return _context7.abrupt("return", _context7.sent);
          case 6:
            saveRef = saveRefsMap.current.get(relayKey) || saveRefsMap.current.get(relayKey.toString());
            _context7.prev = 7;
            _context7.next = 10;
            return saveRef === null || saveRef === void 0 || (_saveRef$current = saveRef.current) === null || _saveRef$current === void 0 ? void 0 : _saveRef$current.save();
          case 10:
            _context7.next = 15;
            break;
          case 12:
            _context7.prev = 12;
            _context7.t0 = _context7["catch"](7);
            return _context7.abrupt("return", false);
          case 15:
            editableKeysSet.delete(relayKey);
            editableKeysSet.delete(relayKey.toString());
            setEditableRowKeys(Array.from(editableKeysSet));
            return _context7.abrupt("return", true);
          case 19:
          case "end":
            return _context7.stop();
        }
      }, _callee7, null, [[7, 12]]);
    }));
    return function (_x5, _x6) {
      return _ref9.apply(this, arguments);
    };
  }());

  /**
   * \u540C\u65F6\u53EA\u80FD\u652F\u6301\u4E00\u884C,\u53D6\u6D88\u4E4B\u540E\u6570\u636E\u6D88\u606F\uFF0C\u4E0D\u4F1A\u89E6\u53D1 dataSource
   *
   * @param row
   * @param options
   * @name \u589E\u52A0\u65B0\u7684\u884C
   */
  var addEditRecord = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)(function (row, options) {
    if (options !== null && options !== void 0 && options.parentKey && !dataSourceKeyIndexMapRef.current.has(recordKeyToString(options === null || options === void 0 ? void 0 : options.parentKey).toString())) {
      console.warn("can't find record by key", options === null || options === void 0 ? void 0 : options.parentKey);
      return false;
    }
    // \u6682\u65F6\u4E0D\u652F\u6301\u591A\u884C\u65B0\u589E
    if (newLineRecordRef.current && props.onlyAddOneLineAlertMessage !== false) {
      warning(props.onlyAddOneLineAlertMessage || '\u53EA\u80FD\u65B0\u589E\u4E00\u884C');
      return false;
    }
    // \u5982\u679C\u662F\u5355\u884C\u7684\u8BDD\uFF0C\u4E0D\u5141\u8BB8\u591A\u884C\u7F16\u8F91
    if (editableKeysSet.size > 0 && editableType === 'single' && props.onlyOneLineEditorAlertMessage !== false) {
      warning(props.onlyOneLineEditorAlertMessage || '\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C');
      return false;
    }
    // \u9632\u6B62\u591A\u6B21\u6E32\u67D3
    var recordKey = props.getRowKey(row, -1);
    if (!recordKey && recordKey !== 0) {
      (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_2__/* .noteOnce */ .ET)(!!recordKey, '\u8BF7\u8BBE\u7F6E recordCreatorProps.record \u5E76\u8FD4\u56DE\u4E00\u4E2A\u552F\u4E00\u7684key  \\n  https://procomponents.ant.design/components/editable-table#editable-%E6%96%B0%E5%BB%BA%E8%A1%8C');
      throw new Error('\u8BF7\u8BBE\u7F6E recordCreatorProps.record \u5E76\u8FD4\u56DE\u4E00\u4E2A\u552F\u4E00\u7684key');
    }
    editableKeysSet.add(recordKey);
    setEditableRowKeys(Array.from(editableKeysSet));

    // \u5982\u679C\u662FdataSource \u65B0\u589E\u6A21\u5F0F\u7684\u8BDD\uFF0C\u53D6\u6D88\u518D\u5F00\u59CB\u7F16\u8F91\uFF0C
    // \u8FD9\u6837\u5C31\u53EF\u4EE5\u628A\u65B0\u589E\u5230 dataSource\u7684\u6570\u636E\u8FDB\u5165\u7F16\u8F91\u6A21\u5F0F\u4E86
    // [a,b,cache] => [a,b,c]
    if ((options === null || options === void 0 ? void 0 : options.newRecordType) === 'dataSource' || props.tableName) {
      var _recordKeyToString3;
      var actionProps = {
        data: props.dataSource,
        getRowKey: props.getRowKey,
        row: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, row), {}, {
          map_row_parentKey: options !== null && options !== void 0 && options.parentKey ? (_recordKeyToString3 = recordKeyToString(options === null || options === void 0 ? void 0 : options.parentKey)) === null || _recordKeyToString3 === void 0 ? void 0 : _recordKeyToString3.toString() : undefined
        }),
        key: recordKey,
        childrenColumnName: props.childrenColumnName || 'children'
      };
      props.setDataSource(editableRowByKey(actionProps, (options === null || options === void 0 ? void 0 : options.position) === 'top' ? 'top' : 'update'));
    } else {
      setNewLineRecordCache({
        defaultValue: row,
        options: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, options), {}, {
          recordKey: recordKey
        })
      });
    }
    return true;
  });

  // Internationalization
  var intl = (0,_ant_design_pro_provider__WEBPACK_IMPORTED_MODULE_25__/* .useIntl */ .YB)();
  var saveText = (props === null || props === void 0 ? void 0 : props.saveText) || intl.getMessage('editableTable.action.save', '\u4FDD\u5B58');
  var deleteText = (props === null || props === void 0 ? void 0 : props.deleteText) || intl.getMessage('editableTable.action.delete', '\u5220\u9664');
  var cancelText = (props === null || props === void 0 ? void 0 : props.cancelText) || intl.getMessage('editableTable.action.cancel', '\u53D6\u6D88');
  var actionSaveRef = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/function () {
    var _ref10 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee8(recordKey, editRow, originRow, newLine) {
      var _props$onSave, _recordKeyToString4, _options$parentKey;
      var res, _ref11, options, actionProps;
      return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee8$(_context8) {
        while (1) switch (_context8.prev = _context8.next) {
          case 0:
            _context8.next = 2;
            return props === null || props === void 0 || (_props$onSave = props.onSave) === null || _props$onSave === void 0 ? void 0 : _props$onSave.call(props, recordKey, editRow, originRow, newLine);
          case 2:
            res = _context8.sent;
            _context8.next = 5;
            return cancelEditable(recordKey);
          case 5:
            _ref11 = newLine || newLineRecordRef.current || {}, options = _ref11.options;
            if (!(!(options !== null && options !== void 0 && options.parentKey) && (options === null || options === void 0 ? void 0 : options.recordKey) === recordKey)) {
              _context8.next = 9;
              break;
            }
            if ((options === null || options === void 0 ? void 0 : options.position) === 'top') {
              props.setDataSource([editRow].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_26__/* ["default"] */ .Z)(props.dataSource)));
            } else {
              props.setDataSource([].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_26__/* ["default"] */ .Z)(props.dataSource), [editRow]));
            }
            return _context8.abrupt("return", res);
          case 9:
            actionProps = {
              data: props.dataSource,
              getRowKey: props.getRowKey,
              row: options ? (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z)({}, editRow), {}, {
                map_row_parentKey: (_recordKeyToString4 = recordKeyToString((_options$parentKey = options === null || options === void 0 ? void 0 : options.parentKey) !== null && _options$parentKey !== void 0 ? _options$parentKey : '')) === null || _recordKeyToString4 === void 0 ? void 0 : _recordKeyToString4.toString()
              }) : editRow,
              key: recordKey,
              childrenColumnName: props.childrenColumnName || 'children'
            };
            props.setDataSource(editableRowByKey(actionProps, (options === null || options === void 0 ? void 0 : options.position) === 'top' ? 'top' : 'update'));
            _context8.next = 13;
            return cancelEditable(recordKey);
          case 13:
            return _context8.abrupt("return", res);
          case 14:
          case "end":
            return _context8.stop();
        }
      }, _callee8);
    }));
    return function (_x7, _x8, _x9, _x10) {
      return _ref10.apply(this, arguments);
    };
  }());
  var actionDeleteRef = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/function () {
    var _ref12 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee9(recordKey, editRow) {
      var _props$onDelete;
      var actionProps, res;
      return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee9$(_context9) {
        while (1) switch (_context9.prev = _context9.next) {
          case 0:
            actionProps = {
              data: props.dataSource,
              getRowKey: props.getRowKey,
              row: editRow,
              key: recordKey,
              childrenColumnName: props.childrenColumnName || 'children'
            };
            _context9.next = 3;
            return props === null || props === void 0 || (_props$onDelete = props.onDelete) === null || _props$onDelete === void 0 ? void 0 : _props$onDelete.call(props, recordKey, editRow);
          case 3:
            res = _context9.sent;
            _context9.next = 6;
            return cancelEditable(recordKey, false);
          case 6:
            props.setDataSource(editableRowByKey(actionProps, 'delete'));
            return _context9.abrupt("return", res);
          case 8:
          case "end":
            return _context9.stop();
        }
      }, _callee9);
    }));
    return function (_x11, _x12) {
      return _ref12.apply(this, arguments);
    };
  }());
  var actionCancelRef = (0,___WEBPACK_IMPORTED_MODULE_13__/* .useRefFunction */ .J)( /*#__PURE__*/function () {
    var _ref13 = (0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().mark(function _callee10(recordKey, editRow, originRow, newLine) {
      var _props$onCancel;
      var res;
      return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z)().wrap(function _callee10$(_context10) {
        while (1) switch (_context10.prev = _context10.next) {
          case 0:
            _context10.next = 2;
            return props === null || props === void 0 || (_props$onCancel = props.onCancel) === null || _props$onCancel === void 0 ? void 0 : _props$onCancel.call(props, recordKey, editRow, originRow, newLine);
          case 2:
            res = _context10.sent;
            return _context10.abrupt("return", res);
          case 4:
          case "end":
            return _context10.stop();
        }
      }, _callee10);
    }));
    return function (_x13, _x14, _x15, _x16) {
      return _ref13.apply(this, arguments);
    };
  }());
  var actionRender = function actionRender(row) {
    var key = props.getRowKey(row, row.index);
    var config = {
      saveText: saveText,
      cancelText: cancelText,
      deleteText: deleteText,
      addEditRecord: addEditRecord,
      recordKey: key,
      cancelEditable: cancelEditable,
      index: row.index,
      tableName: props.tableName,
      newLineConfig: newLineRecordCache,
      onCancel: actionCancelRef,
      onDelete: actionDeleteRef,
      onSave: actionSaveRef,
      editableKeys: editableKeys,
      setEditableRowKeys: setEditableRowKeys,
      deletePopconfirmMessage: props.deletePopconfirmMessage || "".concat(intl.getMessage('deleteThisLine', '\u5220\u9664\u6B64\u9879'), "?")
    };
    var renderResult = defaultActionRender(row, config);
    // \u7F13\u5B58\u4E00\u4E0BsaveRef
    if (props.tableName) {
      saveRefsMap.current.set(dataSourceKeyIndexMapRef.current.get(recordKeyToString(key)) || recordKeyToString(key), renderResult.saveRef);
    } else {
      saveRefsMap.current.set(recordKeyToString(key), renderResult.saveRef);
    }
    if (props.actionRender) return props.actionRender(row, config, {
      save: renderResult.save,
      delete: renderResult.delete,
      cancel: renderResult.cancel
    });
    return [renderResult.save, renderResult.delete, renderResult.cancel];
  };
  return {
    editableKeys: editableKeys,
    setEditableRowKeys: setEditableRowKeys,
    isEditable: isEditable,
    actionRender: actionRender,
    startEditable: startEditable,
    cancelEditable: cancelEditable,
    addEditRecord: addEditRecord,
    saveEditable: saveEditable,
    newLineRecord: newLineRecordCache,
    preEditableKeys: editableKeysRef,
    onValuesChange: onValuesChange,
    getRealIndex: props.getRealIndex
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///86671
`)},93005:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(87462);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _ant_design_icons_svg_es_asn_LoadingOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15294);
/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(62914);

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var LoadingOutlined = function LoadingOutlined(props, ref) {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, props, {
    ref: ref,
    icon: _ant_design_icons_svg_es_asn_LoadingOutlined__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z
  }));
};
if (false) {}
/* harmony default export */ __webpack_exports__.Z = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(LoadingOutlined));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTMwMDUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUEwRDtBQUMxRDtBQUNBOztBQUUrQjtBQUMrQztBQUNoQztBQUM5QztBQUNBLHNCQUFzQixnREFBbUIsQ0FBQyxxRUFBUSxFQUFFLHVGQUFRLEdBQUc7QUFDL0Q7QUFDQSxVQUFVLDZGQUFrQjtBQUM1QixHQUFHO0FBQ0g7QUFDQSxJQUFJLEtBQXFDLEVBQUUsRUFFMUM7QUFDRCxtRUFBNEIsNkNBQWdCLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby11dGlscy9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvaWNvbnMvTG9hZGluZ091dGxpbmVkLmpzP2UxYTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG4vLyBHRU5FUkFURSBCWSAuL3NjcmlwdHMvZ2VuZXJhdGUudHNcbi8vIERPTiBOT1QgRURJVCBJVCBNQU5VQUxMWVxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTG9hZGluZ091dGxpbmVkU3ZnIGZyb20gXCJAYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL0xvYWRpbmdPdXRsaW5lZFwiO1xuaW1wb3J0IEFudGRJY29uIGZyb20gXCIuLi9jb21wb25lbnRzL0FudGRJY29uXCI7XG52YXIgTG9hZGluZ091dGxpbmVkID0gZnVuY3Rpb24gTG9hZGluZ091dGxpbmVkKHByb3BzLCByZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEFudGRJY29uLCBfZXh0ZW5kcyh7fSwgcHJvcHMsIHtcbiAgICByZWY6IHJlZixcbiAgICBpY29uOiBMb2FkaW5nT3V0bGluZWRTdmdcbiAgfSkpO1xufTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIExvYWRpbmdPdXRsaW5lZC5kaXNwbGF5TmFtZSA9ICdMb2FkaW5nT3V0bGluZWQnO1xufVxuZXhwb3J0IGRlZmF1bHQgLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoTG9hZGluZ091dGxpbmVkKTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///93005
`)},86738:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ popconfirm; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/ExclamationCircleFilled.js
var ExclamationCircleFilled = __webpack_require__(29950);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/rc-util/es/KeyCode.js
var KeyCode = __webpack_require__(15105);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(98423);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/reactNode.js
var reactNode = __webpack_require__(96159);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/popover/index.js
var popover = __webpack_require__(55241);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/ActionButton.js
var ActionButton = __webpack_require__(86743);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/getRenderPropValue.js
var getRenderPropValue = __webpack_require__(81643);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/button/buttonHelpers.js
var buttonHelpers = __webpack_require__(33671);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/useLocale.js
var useLocale = __webpack_require__(10110);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/en_US.js + 1 modules
var en_US = __webpack_require__(24457);
// EXTERNAL MODULE: ./node_modules/antd/es/popover/PurePanel.js
var PurePanel = __webpack_require__(66330);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
;// CONCATENATED MODULE: ./node_modules/antd/es/popconfirm/style/index.js

// =============================== Base ===============================
const genBaseStyle = token => {
  const {
    componentCls,
    iconCls,
    antCls,
    zIndexPopup,
    colorText,
    colorWarning,
    marginXXS,
    marginXS,
    fontSize,
    fontWeightStrong,
    colorTextHeading
  } = token;
  return {
    [componentCls]: {
      zIndex: zIndexPopup,
      [\`&\${antCls}-popover\`]: {
        fontSize
      },
      [\`\${componentCls}-message\`]: {
        marginBottom: marginXS,
        display: 'flex',
        flexWrap: 'nowrap',
        alignItems: 'start',
        [\`> \${componentCls}-message-icon \${iconCls}\`]: {
          color: colorWarning,
          fontSize,
          lineHeight: 1,
          marginInlineEnd: marginXS
        },
        [\`\${componentCls}-title\`]: {
          fontWeight: fontWeightStrong,
          color: colorTextHeading,
          '&:only-child': {
            fontWeight: 'normal'
          }
        },
        [\`\${componentCls}-description\`]: {
          marginTop: marginXXS,
          color: colorText
        }
      },
      [\`\${componentCls}-buttons\`]: {
        textAlign: 'end',
        whiteSpace: 'nowrap',
        button: {
          marginInlineStart: marginXS
        }
      }
    }
  };
};
// ============================== Export ==============================
const prepareComponentToken = token => {
  const {
    zIndexPopupBase
  } = token;
  return {
    zIndexPopup: zIndexPopupBase + 60
  };
};
/* harmony default export */ var popconfirm_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('Popconfirm', token => genBaseStyle(token), prepareComponentToken, {
  resetStyle: false
}));
;// CONCATENATED MODULE: ./node_modules/antd/es/popconfirm/PurePanel.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};












const Overlay = props => {
  const {
    prefixCls,
    okButtonProps,
    cancelButtonProps,
    title,
    description,
    cancelText,
    okText,
    okType = 'primary',
    icon = /*#__PURE__*/react.createElement(ExclamationCircleFilled/* default */.Z, null),
    showCancel = true,
    close,
    onConfirm,
    onCancel,
    onPopupClick
  } = props;
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const [contextLocale] = (0,useLocale/* default */.Z)('Popconfirm', en_US/* default */.Z.Popconfirm);
  const theTitle = (0,getRenderPropValue/* getRenderPropValue */.Z)(title);
  const theDescription = (0,getRenderPropValue/* getRenderPropValue */.Z)(description);
  return /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-inner-content\`,
    onClick: onPopupClick
  }, /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-message\`
  }, icon && /*#__PURE__*/react.createElement("span", {
    className: \`\${prefixCls}-message-icon\`
  }, icon), /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-message-text\`
  }, theTitle && /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(\`\${prefixCls}-title\`)
  }, theTitle), theDescription && /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-description\`
  }, theDescription))), /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-buttons\`
  }, showCancel && ( /*#__PURE__*/react.createElement(es_button/* default */.ZP, Object.assign({
    onClick: onCancel,
    size: "small"
  }, cancelButtonProps), cancelText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.cancelText))), /*#__PURE__*/react.createElement(ActionButton/* default */.Z, {
    buttonProps: Object.assign(Object.assign({
      size: 'small'
    }, (0,buttonHelpers/* convertLegacyProps */.nx)(okType)), okButtonProps),
    actionFn: onConfirm,
    close: close,
    prefixCls: getPrefixCls('btn'),
    quitOnNullishReturnValue: true,
    emitEvent: true
  }, okText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.okText))));
};
const PurePanel_PurePanel = props => {
  const {
      prefixCls: customizePrefixCls,
      placement,
      className,
      style
    } = props,
    restProps = __rest(props, ["prefixCls", "placement", "className", "style"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);
  const [wrapCSSVar] = popconfirm_style(prefixCls);
  return wrapCSSVar( /*#__PURE__*/react.createElement(PurePanel/* default */.ZP, {
    placement: placement,
    className: classnames_default()(prefixCls, className),
    style: style,
    content: /*#__PURE__*/react.createElement(Overlay, Object.assign({
      prefixCls: prefixCls
    }, restProps))
  }));
};
/* harmony default export */ var popconfirm_PurePanel = (PurePanel_PurePanel);
;// CONCATENATED MODULE: ./node_modules/antd/es/popconfirm/index.js
"use client";

var popconfirm_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};











const Popconfirm = /*#__PURE__*/react.forwardRef((props, ref) => {
  var _a, _b;
  const {
      prefixCls: customizePrefixCls,
      placement = 'top',
      trigger = 'click',
      okType = 'primary',
      icon = /*#__PURE__*/react.createElement(ExclamationCircleFilled/* default */.Z, null),
      children,
      overlayClassName,
      onOpenChange,
      onVisibleChange
    } = props,
    restProps = popconfirm_rest(props, ["prefixCls", "placement", "trigger", "okType", "icon", "children", "overlayClassName", "onOpenChange", "onVisibleChange"]);
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const [open, setOpen] = (0,useMergedState/* default */.Z)(false, {
    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,
    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible
  });
  const settingOpen = (value, e) => {
    setOpen(value, true);
    onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(value);
    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);
  };
  const close = e => {
    settingOpen(false, e);
  };
  const onConfirm = e => {
    var _a;
    return (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(undefined, e);
  };
  const onCancel = e => {
    var _a;
    settingOpen(false, e);
    (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(undefined, e);
  };
  const onKeyDown = e => {
    if (e.keyCode === KeyCode/* default */.Z.ESC && open) {
      settingOpen(false, e);
    }
  };
  const onInternalOpenChange = value => {
    const {
      disabled = false
    } = props;
    if (disabled) {
      return;
    }
    settingOpen(value);
  };
  const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);
  const overlayClassNames = classnames_default()(prefixCls, overlayClassName);
  const [wrapCSSVar] = popconfirm_style(prefixCls);
  return wrapCSSVar( /*#__PURE__*/react.createElement(popover/* default */.Z, Object.assign({}, (0,omit/* default */.Z)(restProps, ['title']), {
    trigger: trigger,
    placement: placement,
    onOpenChange: onInternalOpenChange,
    open: open,
    ref: ref,
    overlayClassName: overlayClassNames,
    content: /*#__PURE__*/react.createElement(Overlay, Object.assign({
      okType: okType,
      icon: icon
    }, props, {
      prefixCls: prefixCls,
      close: close,
      onConfirm: onConfirm,
      onCancel: onCancel
    })),
    "data-popover-inject": true
  }), (0,reactNode/* cloneElement */.Tm)(children, {
    onKeyDown: e => {
      var _a, _b;
      if ( /*#__PURE__*/react.isValidElement(children)) {
        (_b = children === null || children === void 0 ? void 0 : (_a = children.props).onKeyDown) === null || _b === void 0 ? void 0 : _b.call(_a, e);
      }
      onKeyDown(e);
    }
  })));
});
// We don't care debug panel
/* istanbul ignore next */
Popconfirm._InternalPanelDoNotUseOrYouWillBeFired = popconfirm_PurePanel;
if (false) {}
/* harmony default export */ var popconfirm = (Popconfirm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///86738
`)}}]);
