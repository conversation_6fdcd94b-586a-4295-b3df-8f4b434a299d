"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4645],{4645:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5574);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(51042);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(83863);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(14726);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(85576);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(45360);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(15746);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(96365);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(67294);
/* harmony import */ var _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(98792);
/* harmony import */ var _services_customerUser__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(40063);
/* harmony import */ var _services_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);
/* harmony import */ var _ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(64317);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7837);
/* harmony import */ var antd_es_input_TextArea__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(70006);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(85893);






var Item = antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z.Item;









var Option = antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z.Option;
var CreateCustomerUserForm = function CreateCustomerUserForm(_ref) {
  var refreshFnc = _ref.refreshFnc,
    customer_id = _ref.customer_id,
    _ref$buttonType = _ref.buttonType,
    buttonType = _ref$buttonType === void 0 ? 'primary' : _ref$buttonType;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z.useForm(),
    _Form$useForm2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState5, 2),
    stateOption = _useState6[0],
    setStateOption = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2___default()(_useState7, 2),
    wardOption = _useState8[0],
    setWardOption = _useState8[1];
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  var handleChangeCity = function handleChangeCity(value) {
    if (value) {
      var new_state = Object.keys(_helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[value]['quan-huyen']).map(function (key) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Option, {
          value: key,
          children: _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[value]['quan-huyen'][key].name_with_type
        }, key);
      });
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
      setStateOption(new_state);
    } else {
      form.setFieldValue('district', null);
      form.setFieldValue('ward', null);
    }
  };
  var handleChangeState = function handleChangeState(value) {
    if (value) {
      var city = form.getFieldValue('province');
      if (city) {
        var new_ward = Object.keys(_helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[city]['quan-huyen'][value]['xa-phuong']).map(function (key) {
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Option, {
            value: key,
            children: _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[city]['quan-huyen'][value]['xa-phuong'][key].name_with_type
          }, key);
        });
        form.setFieldValue('ward', null);
        setWardOption(new_ward);
      }
    } else {
      form.setFieldValue('ward', null);
    }
  };
  var _useIntl = (0,_umijs_max__WEBPACK_IMPORTED_MODULE_8__.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .ZP, {
      type: buttonType,
      onClick: showModal,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .Z, {}), ' ', formatMessage({
        id: 'common.add_new_user'
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .Z, {
      title: formatMessage({
        id: 'common.add_new_user'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      width: 800,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(value) {
            var _vietnam_location$pro, email, phone, province, district, ward, address, description, first_name, last_name, password, iot_dynamic_role, province_str, district_str, ward_str;
            return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.prev = 0;
                  email = value.email, phone = value.phone, province = value.province, district = value.district, ward = value.ward, address = value.address, description = value.description, first_name = value.first_name, last_name = value.last_name, password = value.password, iot_dynamic_role = value.iot_dynamic_role;
                  ward_str = '';
                  province_str = ((_vietnam_location$pro = _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[province]) === null || _vietnam_location$pro === void 0 ? void 0 : _vietnam_location$pro.name_with_type) || null;
                  if (district) district_str = _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[province]['quan-huyen'][district]['name_with_type'];
                  if (ward) ward_str = _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[province]['quan-huyen'][district]['xa-phuong'][ward]['name_with_type'];
                  province = province_str;
                  district = district_str;
                  ward = ward_str;

                  // await generalCreate('iot_customer_user', {
                  //     data: {
                  //         first_name,
                  //         last_name,
                  //         email,
                  //         phone,
                  //         province,
                  //         district,
                  //         ward,
                  //         address,
                  //         description,
                  //         customer_id: customer_id
                  //     }
                  // });
                  console.log('value is', value);
                  _context.next = 12;
                  return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_6__/* .createCustomerUser */ .y_)({
                    first_name: first_name,
                    last_name: last_name,
                    email: email,
                    phone_number: phone,
                    province: province,
                    district: district,
                    ward: ward,
                    address: address,
                    description: description,
                    customer_id: customer_id,
                    password: password,
                    iot_dynamic_role: iot_dynamic_role,
                    is_deactivated: 0
                  });
                case 12:
                  hideModal();
                  if (!refreshFnc) {
                    _context.next = 16;
                    break;
                  }
                  _context.next = 16;
                  return refreshFnc();
                case 16:
                  antd__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .ZP.success('Success!');
                  _context.next = 21;
                  break;
                case 19:
                  _context.prev = 19;
                  _context.t0 = _context["catch"](0);
                case 21:
                  _context.prev = 21;
                  setLoading(false);
                  return _context.finish(21);
                case 24:
                case "end":
                  return _context.stop();
              }
            }, _callee, null, [[0, 19, 21, 24]]);
          }));
          return function (_x) {
            return _ref2.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(antd__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .Z, {
          gutter: 16,
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: formatMessage({
                id: 'common.last_name'
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "last_name",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: formatMessage({
                id: 'common.first_name'
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "first_name",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Email",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }, {
                type: 'email',
                message: 'Vui l\xF2ng nh\u1EADp \u0111\xFAng \u0111\u1ECBnh d\u1EA1ng email'
              }],
              name: "email",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Phone",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "phone",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: formatMessage({
                id: 'common.password'
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "password",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z.Password, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Province",
              labelCol: {
                span: 24
              },
              name: "province",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                onChange: handleChangeCity,
                filterOption: function filterOption(input, option) {
                  return (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(option.children).includes((0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(input));
                },
                children: Object.keys(_helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__).map(function (key) {
                  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Option, {
                    value: key,
                    children: _helpers_tree_json__WEBPACK_IMPORTED_MODULE_5__[key].name
                  }, key);
                })
              })
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "District",
              labelCol: {
                span: 24
              },
              name: "district",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                onChange: handleChangeState,
                filterOption: function filterOption(input, option) {
                  return (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(option.children).includes((0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(input));
                },
                children: stateOption
              })
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Ward",
              labelCol: {
                span: 24
              },
              name: "ward",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Z, {
                allowClear: true,
                showSearch: true,
                style: {
                  width: '100%'
                },
                filterOption: function filterOption(input, option) {
                  return (0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(option.children).includes((0,_services_utils__WEBPACK_IMPORTED_MODULE_7__/* .toLowerCaseNonAccentVietnamese */ .HO)(input));
                },
                children: wardOption
              })
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Address",
              labelCol: {
                span: 24
              },
              name: "address",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .Z, {})
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 6,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: formatMessage({
                id: 'common.role'
              }),
              required: true,
              rules: [{
                required: true,
                message: 'Vui l\xF2ng ch\u1ECDn vai tr\xF2'
              }],
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ant_design_pro_components__WEBPACK_IMPORTED_MODULE_18__/* ["default"] */ .Z, {
                name: 'iot_dynamic_role',
                showSearch: true,
                rules: [{
                  required: true,
                  message: 'Vui l\xF2ng ch\u1ECDn vai tr\xF2'
                }],
                request: ( /*#__PURE__*/function () {
                  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(option) {
                    var roleList;
                    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
                      while (1) switch (_context2.prev = _context2.next) {
                        case 0:
                          _context2.next = 2;
                          return (0,_services_customerUser__WEBPACK_IMPORTED_MODULE_6__/* .getDynamicRole */ .w)();
                        case 2:
                          roleList = _context2.sent;
                          console.log('roleList', roleList);
                          return _context2.abrupt("return", roleList.map(function (item) {
                            return {
                              label: item.label,
                              value: item.name
                            };
                          }));
                        case 5:
                        case "end":
                          return _context2.stop();
                      }
                    }, _callee2);
                  }));
                  return function (_x2) {
                    return _ref3.apply(this, arguments);
                  };
                }())
              })
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(Item, {
              label: "Description",
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(antd_es_input_TextArea__WEBPACK_IMPORTED_MODULE_19__/* ["default"] */ .Z, {
                rows: 5,
                placeholder: "maxLength is 100",
                maxLength: 100
              })
            })
          })]
        })
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__.Z = (CreateCustomerUserForm);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///4645
`)},40063:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J9: function() { return /* binding */ getCustomerUserList; },
/* harmony export */   Lf: function() { return /* binding */ listDynamicRoleAllSection; },
/* harmony export */   cb: function() { return /* binding */ updateCustomerUser; },
/* harmony export */   f6: function() { return /* binding */ createDynamicRole; },
/* harmony export */   fh: function() { return /* binding */ updateDynamicRole; },
/* harmony export */   jt: function() { return /* binding */ customerUserListAll; },
/* harmony export */   rX: function() { return /* binding */ removeDynamicRole; },
/* harmony export */   w: function() { return /* binding */ getDynamicRole; },
/* harmony export */   y_: function() { return /* binding */ createCustomerUser; }
/* harmony export */ });
/* unused harmony exports IIotDynamicRole, getCustomerUserIndividualList, deleteCustomerUser, deleteCustomerUserCredential */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _sscript__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(39750);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(467);








var IIotDynamicRole = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotDynamicRole() {
  _classCallCheck(this, IIotDynamicRole);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "label", void 0);
  // Data
  _defineProperty(this, "role", void 0);
  // Data
  _defineProperty(this, "iot_customer", void 0);
  // Link
  _defineProperty(this, "sections", void 0);
} // Data
)));
var createCustomerUser = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/register/customer-user-with-role'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function createCustomerUser(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getCustomerUserList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getCustomerUserList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCustomerUserIndividualList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user/individual'), {
            params: getParamsReqList(params)
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCustomerUserIndividualList(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));
function customerUserListAll() {
  return _customerUserListAll.apply(this, arguments);
}

//update customer user
function _customerUserListAll() {
  _customerUserListAll = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/customerUser/user"), {
            method: 'GET',
            params: {
              fields: ['*']
            }
          });
        case 3:
          result = _context7.sent;
          return _context7.abrupt("return", result.result);
        case 7:
          _context7.prev = 7;
          _context7.t0 = _context7["catch"](0);
          console.log(_context7.t0);
          throw _context7.t0;
        case 11:
        case "end":
          return _context7.stop();
      }
    }, _callee7, null, [[0, 7]]);
  }));
  return _customerUserListAll.apply(this, arguments);
}
var updateCustomerUser = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)('api/v2/customerUser/user'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function updateCustomerUser(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//delete customer user
var deleteCustomerUser = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function deleteCustomerUser(_x5) {
    return _ref5.apply(this, arguments);
  };
}()));

//delete customer user credential
var deleteCustomerUserCredential = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6(name) {
    var res;
    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return request(generateAPIPath('api/v2/customerUser/user-credential'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function deleteCustomerUserCredential(_x6) {
    return _ref6.apply(this, arguments);
  };
}()));
/**\r
 *\r
 * DYNAMIC ROLE APIs\r
 */
function listDynamicRoleAllSection() {
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function _listDynamicRoleAllSection() {
  _listDynamicRoleAllSection = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole/listAllSection"), {
            method: 'GET'
          });
        case 3:
          result = _context8.sent;
          return _context8.abrupt("return", result.result);
        case 7:
          _context8.prev = 7;
          _context8.t0 = _context8["catch"](0);
          console.log(_context8.t0);
          throw _context8.t0;
        case 11:
        case "end":
          return _context8.stop();
      }
    }, _callee8, null, [[0, 7]]);
  }));
  return _listDynamicRoleAllSection.apply(this, arguments);
}
function getDynamicRole() {
  return _getDynamicRole.apply(this, arguments);
}
function _getDynamicRole() {
  _getDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9() {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'GET',
            params: {
              page: 1,
              size: 100
            }
          });
        case 3:
          result = _context9.sent;
          return _context9.abrupt("return", result.result.data);
        case 7:
          _context9.prev = 7;
          _context9.t0 = _context9["catch"](0);
          console.log(_context9.t0);
          throw _context9.t0;
        case 11:
        case "end":
          return _context9.stop();
      }
    }, _callee9, null, [[0, 7]]);
  }));
  return _getDynamicRole.apply(this, arguments);
}
function createDynamicRole(_x7) {
  return _createDynamicRole.apply(this, arguments);
}
function _createDynamicRole() {
  _createDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          _context10.prev = 0;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'POST',
            data: data
          });
        case 3:
          result = _context10.sent;
          return _context10.abrupt("return", result.result);
        case 7:
          _context10.prev = 7;
          _context10.t0 = _context10["catch"](0);
          console.log(_context10.t0);
          throw _context10.t0;
        case 11:
        case "end":
          return _context10.stop();
      }
    }, _callee10, null, [[0, 7]]);
  }));
  return _createDynamicRole.apply(this, arguments);
}
function updateDynamicRole(_x8) {
  return _updateDynamicRole.apply(this, arguments);
}
function _updateDynamicRole() {
  _updateDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(data) {
    var result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.prev = 0;
          _context11.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_7__/* .generateAPIPath */ .rH)("api/v2/dynamicRole"), {
            method: 'PUT',
            data: data
          });
        case 3:
          result = _context11.sent;
          return _context11.abrupt("return", result.result);
        case 7:
          _context11.prev = 7;
          _context11.t0 = _context11["catch"](0);
          console.log(_context11.t0);
          throw _context11.t0;
        case 11:
        case "end":
          return _context11.stop();
      }
    }, _callee11, null, [[0, 7]]);
  }));
  return _updateDynamicRole.apply(this, arguments);
}
function removeDynamicRole(_x9) {
  return _removeDynamicRole.apply(this, arguments);
}
function _removeDynamicRole() {
  _removeDynamicRole = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var name, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.prev = 0;
          name = data.name ? data.name : '';
          _context12.next = 4;
          return (0,_sscript__WEBPACK_IMPORTED_MODULE_6__/* .generalDelete */ .ID)('iot_dynamic_role', name);
        case 4:
          result = _context12.sent;
          return _context12.abrupt("return", result);
        case 8:
          _context12.prev = 8;
          _context12.t0 = _context12["catch"](0);
          throw _context12.t0;
        case 11:
        case "end":
          return _context12.stop();
      }
    }, _callee12, null, [[0, 8]]);
  }));
  return _removeDynamicRole.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///40063
`)}}]);
