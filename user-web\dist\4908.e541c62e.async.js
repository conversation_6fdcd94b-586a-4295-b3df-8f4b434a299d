(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[4908],{9105:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ EditOrReadOnlyContext; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);

var EditOrReadOnlyContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({
  mode: 'edit'
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTEwNS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQTBCO0FBQ25CLHlDQUF5QyxnREFBbUI7QUFDbkU7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vZXMvQmFzZUZvcm0vRWRpdE9yUmVhZE9ubHlDb250ZXh0LmpzP2U1YmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgRWRpdE9yUmVhZE9ubHlDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe1xuICBtb2RlOiAnZWRpdCdcbn0pOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///9105
`)},66758:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   z: function() { return /* binding */ FieldContext; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);

var FieldContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({});

/* harmony default export */ __webpack_exports__.Z = (FieldContext);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjY3NTguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUEwQjtBQUMxQixnQ0FBZ0MsZ0RBQW1CLEdBQUc7QUFDOUI7QUFDeEIsc0RBQWUsWUFBWSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby1mb3JtL2VzL0ZpZWxkQ29udGV4dC5qcz9hNTNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgRmllbGRDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe30pO1xuZXhwb3J0IHsgRmllbGRDb250ZXh0IH07XG5leHBvcnQgZGVmYXVsdCBGaWVsZENvbnRleHQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///66758
`)},4499:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ FormItem; }
});

// UNUSED EXPORTS: FormItemProvide

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/hooks/useRefFunction/index.js
var useRefFunction = __webpack_require__(48171);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/hooks/useDeepCompareMemo/index.js
var useDeepCompareMemo = __webpack_require__(74138);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/omitUndefined/index.js
var omitUndefined = __webpack_require__(51812);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/isDropdownValueType/index.js
var isDropdownValueType = function isDropdownValueType(valueType) {
  var isDropdown = false;
  if (typeof valueType === 'string' && valueType.startsWith('date') && !valueType.endsWith('Range') || valueType === 'select' || valueType === 'time') {
    isDropdown = true;
  }
  return isDropdown;
};
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/omit.js/es/index.js
var es = __webpack_require__(97435);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/dateArrayFormatter/index.js
var dateArrayFormatter = __webpack_require__(86190);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/conversionMomentValue/index.js
var conversionMomentValue = __webpack_require__(23312);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/components/FilterDropdown/index.js + 3 modules
var FilterDropdown = __webpack_require__(1336);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/components/FieldLabel/index.js + 3 modules
var FieldLabel = __webpack_require__(98912);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/BaseForm/LightWrapper/style.js



var genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-collapse-label"), {
    paddingInline: 1,
    paddingBlock: 1
  }), "".concat(token.componentCls, "-container"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item"), {
    marginBlockEnd: 0
  }));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('LightWrapper', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/BaseForm/LightWrapper/index.js





var _excluded = ["label", "size", "disabled", "onChange", "className", "style", "children", "valuePropName", "placeholder", "labelFormatter", "bordered", "footerRender", "allowClear", "otherFieldProps", "valueType", "placement"];






var LightWrapper = function LightWrapper(props) {
  var label = props.label,
    size = props.size,
    disabled = props.disabled,
    propsOnChange = props.onChange,
    className = props.className,
    style = props.style,
    children = props.children,
    valuePropName = props.valuePropName,
    placeholder = props.placeholder,
    labelFormatter = props.labelFormatter,
    bordered = props.bordered,
    footerRender = props.footerRender,
    allowClear = props.allowClear,
    otherFieldProps = props.otherFieldProps,
    valueType = props.valueType,
    placement = props.placement,
    rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls('pro-field-light-wrapper');
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var _useState = (0,react.useState)(props[valuePropName]),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    tempValue = _useState2[0],
    setTempValue = _useState2[1];
  var _useMountMergeState = (0,useMergedState/* default */.Z)(false),
    _useMountMergeState2 = (0,slicedToArray/* default */.Z)(_useMountMergeState, 2),
    open = _useMountMergeState2[0],
    setOpen = _useMountMergeState2[1];
  var onChange = function onChange() {
    var _otherFieldProps$onCh;
    for (var _len = arguments.length, restParams = new Array(_len), _key = 0; _key < _len; _key++) {
      restParams[_key] = arguments[_key];
    }
    otherFieldProps === null || otherFieldProps === void 0 || (_otherFieldProps$onCh = otherFieldProps.onChange) === null || _otherFieldProps$onCh === void 0 || _otherFieldProps$onCh.call.apply(_otherFieldProps$onCh, [otherFieldProps].concat(restParams));
    propsOnChange === null || propsOnChange === void 0 || propsOnChange.apply(void 0, restParams);
  };
  var labelValue = props[valuePropName];

  /** DataRange\u7684\u8F6C\u5316\uFF0Cdayjs \u7684 toString \u6709\u70B9\u4E0D\u597D\u7528 */
  var labelValueText = (0,react.useMemo)(function () {
    var _valueType$toLowerCas;
    if (!labelValue) return labelValue;
    if (valueType !== null && valueType !== void 0 && (_valueType$toLowerCas = valueType.toLowerCase()) !== null && _valueType$toLowerCas !== void 0 && _valueType$toLowerCas.endsWith('range') && valueType !== 'digitRange' && !labelFormatter) {
      return (0,dateArrayFormatter/* dateArrayFormatter */.c)(labelValue, conversionMomentValue/* dateFormatterMap */.Cl[valueType] || 'YYYY-MM-DD');
    }
    if (Array.isArray(labelValue)) return labelValue.map(function (item) {
      if ((0,esm_typeof/* default */.Z)(item) === 'object' && item.label && item.value) {
        return item.label;
      }
      return item;
    });
    return labelValue;
  }, [labelValue, valueType, labelFormatter]);
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)(FilterDropdown/* FilterDropdown */.M, {
    disabled: disabled,
    open: open,
    onOpenChange: setOpen,
    placement: placement,
    label: /*#__PURE__*/(0,jsx_runtime.jsx)(FieldLabel/* FieldLabel */.Q, {
      ellipsis: true,
      size: size,
      onClear: function onClear() {
        onChange === null || onChange === void 0 || onChange();
        setTempValue('');
      },
      bordered: bordered,
      style: style,
      className: className,
      label: label,
      placeholder: placeholder,
      value: labelValueText,
      disabled: disabled,
      formatter: labelFormatter,
      allowClear: allowClear
    }),
    footer: {
      onClear: function onClear() {
        return setTempValue('');
      },
      onConfirm: function onConfirm() {
        onChange === null || onChange === void 0 || onChange(tempValue);
        setOpen(false);
      }
    },
    footerRender: footerRender,
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: classnames_default()("".concat(prefixCls, "-container"), hashId, className),
      style: style,
      children: /*#__PURE__*/react.cloneElement(children, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, rest), {}, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, valuePropName, tempValue), "onChange", function onChange(e) {
        setTempValue(e !== null && e !== void 0 && e.target ? e.target.value : e);
      }), children.props))
    })
  }));
};

// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/FieldContext.js
var FieldContext = __webpack_require__(66758);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/List/index.js + 6 modules
var List = __webpack_require__(55895);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/FormItem/index.js



var FormItem_excluded = ["children", "onChange", "onBlur", "ignoreFormItem", "valuePropName"],
  _excluded2 = ["children", "addonAfter", "addonBefore", "valuePropName", "addonWarpStyle", "convertValue"],
  _excluded3 = ["valueType", "transform", "dataFormat", "ignoreFormItem", "lightProps", "children"];











var FormItemProvide = /*#__PURE__*/react.createContext({});

/**
 * \u628Avalue\u6254\u7ED9 fieldProps\uFF0C\u65B9\u4FBF\u7ED9\u81EA\u5B9A\u4E49\u7528
 *
 * @param param0
 * @returns
 */
var WithValueFomFiledProps = function WithValueFomFiledProps(formFieldProps) {
  var _filedChildren$type, _filedChildren$props9;
  var filedChildren = formFieldProps.children,
    onChange = formFieldProps.onChange,
    onBlur = formFieldProps.onBlur,
    ignoreFormItem = formFieldProps.ignoreFormItem,
    _formFieldProps$value = formFieldProps.valuePropName,
    valuePropName = _formFieldProps$value === void 0 ? 'value' : _formFieldProps$value,
    restProps = (0,objectWithoutProperties/* default */.Z)(formFieldProps, FormItem_excluded);
  var isProFormComponent =
  // @ts-ignore
  (filedChildren === null || filedChildren === void 0 || (_filedChildren$type = filedChildren.type) === null || _filedChildren$type === void 0 ? void 0 : _filedChildren$type.displayName) !== 'ProFormComponent';
  var isValidElementForFiledChildren = ! /*#__PURE__*/react.isValidElement(filedChildren);
  var onChangeMemo = (0,useRefFunction/* useRefFunction */.J)(function () {
    var _filedChildren$props, _filedChildren$props$, _filedChildren$props2, _filedChildren$props3;
    for (var _len = arguments.length, restParams = new Array(_len), _key = 0; _key < _len; _key++) {
      restParams[_key] = arguments[_key];
    }
    onChange === null || onChange === void 0 || onChange.apply(void 0, restParams);
    if (isProFormComponent) return;
    if (isValidElementForFiledChildren) return undefined;
    filedChildren === null || filedChildren === void 0 || (_filedChildren$props = filedChildren.props) === null || _filedChildren$props === void 0 || (_filedChildren$props$ = _filedChildren$props.onChange) === null || _filedChildren$props$ === void 0 || _filedChildren$props$.call.apply(_filedChildren$props$, [_filedChildren$props].concat(restParams));
    filedChildren === null || filedChildren === void 0 || (_filedChildren$props2 = filedChildren.props) === null || _filedChildren$props2 === void 0 || (_filedChildren$props2 = _filedChildren$props2.fieldProps) === null || _filedChildren$props2 === void 0 || (_filedChildren$props3 = _filedChildren$props2.onChange) === null || _filedChildren$props3 === void 0 || _filedChildren$props3.call.apply(_filedChildren$props3, [_filedChildren$props2].concat(restParams));
  });
  var onBlurMemo = (0,useRefFunction/* useRefFunction */.J)(function () {
    var _filedChildren$props4, _filedChildren$props5, _filedChildren$props6, _filedChildren$props7;
    if (isProFormComponent) return;
    if (isValidElementForFiledChildren) return;
    for (var _len2 = arguments.length, restParams = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      restParams[_key2] = arguments[_key2];
    }
    onBlur === null || onBlur === void 0 || onBlur.apply(void 0, restParams);
    filedChildren === null || filedChildren === void 0 || (_filedChildren$props4 = filedChildren.props) === null || _filedChildren$props4 === void 0 || (_filedChildren$props5 = _filedChildren$props4.onBlur) === null || _filedChildren$props5 === void 0 || _filedChildren$props5.call.apply(_filedChildren$props5, [_filedChildren$props4].concat(restParams));
    filedChildren === null || filedChildren === void 0 || (_filedChildren$props6 = filedChildren.props) === null || _filedChildren$props6 === void 0 || (_filedChildren$props6 = _filedChildren$props6.fieldProps) === null || _filedChildren$props6 === void 0 || (_filedChildren$props7 = _filedChildren$props6.onBlur) === null || _filedChildren$props7 === void 0 || _filedChildren$props7.call.apply(_filedChildren$props7, [_filedChildren$props6].concat(restParams));
  });
  var omitOnBlurAndOnChangeProps = (0,useDeepCompareMemo/* default */.Z)(function () {
    var _filedChildren$props8;
    return (0,es/* default */.Z)(
    // @ts-ignore
    (filedChildren === null || filedChildren === void 0 || (_filedChildren$props8 = filedChildren.props) === null || _filedChildren$props8 === void 0 ? void 0 : _filedChildren$props8.fieldProps) || {}, ['onBlur', 'onChange']);
  }, [(0,es/* default */.Z)(
  // @ts-ignore
  (filedChildren === null || filedChildren === void 0 || (_filedChildren$props9 = filedChildren.props) === null || _filedChildren$props9 === void 0 ? void 0 : _filedChildren$props9.fieldProps) || {}, ['onBlur', 'onChange'])]);
  var propsValuePropName = formFieldProps[valuePropName];
  var fieldProps = (0,react.useMemo)(function () {
    if (isProFormComponent) return undefined;
    if (isValidElementForFiledChildren) return undefined;
    return (0,omitUndefined/* omitUndefined */.Y)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,defineProperty/* default */.Z)({
      id: restProps.id
    }, valuePropName, propsValuePropName), omitOnBlurAndOnChangeProps), {}, {
      onBlur: onBlurMemo,
      // \u8FD9\u4E2A onChange \u662F Form.Item \u6DFB\u52A0\u4E0A\u7684\uFF0C
      // \u8981\u901A\u8FC7 fieldProps \u900F\u4F20\u7ED9 ProField \u8C03\u7528
      onChange: onChangeMemo
    }));
  }, [propsValuePropName, omitOnBlurAndOnChangeProps, onBlurMemo, onChangeMemo, restProps.id, valuePropName]);
  var finalChange = (0,react.useMemo)(function () {
    if (fieldProps) return undefined;
    if (! /*#__PURE__*/react.isValidElement(filedChildren)) return undefined;
    return function () {
      var _filedChildren$props10, _filedChildren$props11;
      for (var _len3 = arguments.length, restParams = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        restParams[_key3] = arguments[_key3];
      }
      onChange === null || onChange === void 0 || onChange.apply(void 0, restParams);
      filedChildren === null || filedChildren === void 0 || (_filedChildren$props10 = filedChildren.props) === null || _filedChildren$props10 === void 0 || (_filedChildren$props11 = _filedChildren$props10.onChange) === null || _filedChildren$props11 === void 0 || _filedChildren$props11.call.apply(_filedChildren$props11, [_filedChildren$props10].concat(restParams));
    };
  }, [fieldProps, filedChildren, onChange]);
  if (! /*#__PURE__*/react.isValidElement(filedChildren)) return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: filedChildren
  });
  return /*#__PURE__*/react.cloneElement(filedChildren, (0,omitUndefined/* omitUndefined */.Y)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, restProps), {}, (0,defineProperty/* default */.Z)({}, valuePropName, formFieldProps[valuePropName]), filedChildren.props), {}, {
    onChange: finalChange,
    fieldProps: fieldProps
  })));
};
/**
 * \u652F\u6301\u4E86\u4E00\u4E0B\u524D\u7F6E dom \u548C\u540E\u7F6E\u7684 dom \u540C\u65F6\u5305\u4E00\u4E2Aprovide
 *
 * @param WarpFormItemProps
 * @returns
 */
var WarpFormItem = function WarpFormItem(_ref) {
  var children = _ref.children,
    addonAfter = _ref.addonAfter,
    addonBefore = _ref.addonBefore,
    valuePropName = _ref.valuePropName,
    addonWarpStyle = _ref.addonWarpStyle,
    convertValue = _ref.convertValue,
    props = (0,objectWithoutProperties/* default */.Z)(_ref, _excluded2);
  var formDom = (0,react.useMemo)(function () {
    var getValuePropsFunc = function getValuePropsFunc(value) {
      var _convertValue;
      var newValue = (_convertValue = convertValue === null || convertValue === void 0 ? void 0 : convertValue(value, props.name)) !== null && _convertValue !== void 0 ? _convertValue : value;
      if (props.getValueProps) return props.getValueProps(newValue);
      return (0,defineProperty/* default */.Z)({}, valuePropName || 'value', newValue);
    };
    if (!convertValue && !props.getValueProps) {
      getValuePropsFunc = undefined;
    }
    if (!addonAfter && !addonBefore) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
        valuePropName: valuePropName,
        getValueProps: getValuePropsFunc,
        children: children
      }));
    }
    return /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
      valuePropName: valuePropName
      // @ts-ignore
      ,
      _internalItemRender: {
        mark: 'pro_table_render',
        render: function render(inputProps, doms) {
          return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
            children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
              style: (0,objectSpread2/* default */.Z)({
                display: 'flex',
                alignItems: 'center',
                flexWrap: 'wrap'
              }, addonWarpStyle),
              children: [addonBefore ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                style: {
                  marginInlineEnd: 8
                },
                children: addonBefore
              }) : null, doms.input, addonAfter ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
                style: {
                  marginInlineStart: 8
                },
                children: addonAfter
              }) : null]
            }), doms.extra, doms.errorList]
          });
        }
      }
    }, props), {}, {
      getValueProps: getValuePropsFunc,
      children: children
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [addonAfter, addonBefore, children, convertValue === null || convertValue === void 0 ? void 0 : convertValue.toString(), props]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(FormItemProvide.Provider, {
    value: {
      name: props.name,
      label: props.label
    },
    children: formDom
  });
};
var ProFormItem = function ProFormItem(props) {
  var _ConfigProvider$useCo, _rest$name2, _rest$name3, _rest$name4;
  /** \u4ECE context \u4E2D\u62FF\u5230\u7684\u503C */
  var _ref3 = (config_provider/* default */.ZP === null || config_provider/* default */.ZP === void 0 || (_ConfigProvider$useCo = config_provider/* default */.ZP.useConfig) === null || _ConfigProvider$useCo === void 0 ? void 0 : _ConfigProvider$useCo.call(config_provider/* default */.ZP)) || {
      componentSize: 'middle'
    },
    componentSize = _ref3.componentSize;
  var size = componentSize;
  var valueType = props.valueType,
    transform = props.transform,
    dataFormat = props.dataFormat,
    ignoreFormItem = props.ignoreFormItem,
    lightProps = props.lightProps,
    unusedChildren = props.children,
    rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded3);
  var formListField = (0,react.useContext)(List/* FormListContext */.J);

  // ProFromList \u7684 filed\uFF0C\u91CC\u9762\u6709name\u548Ckey
  /** \u4ECE context \u4E2D\u62FF\u5230\u7684\u503C */
  var name = (0,react.useMemo)(function () {
    if (props.name === undefined) return props.name;
    if (formListField.name !== undefined) {
      return [formListField.name, props.name].flat(1);
    }
    return props.name;
  }, [formListField.name, props.name]);

  /** \u4ECE context \u4E2D\u62FF\u5230\u7684\u503C */
  var _React$useContext = react.useContext(FieldContext/* default */.Z),
    setFieldValueType = _React$useContext.setFieldValueType,
    formItemProps = _React$useContext.formItemProps;
  (0,react.useEffect)(function () {
    // \u5982\u679C setFieldValueType \u548C props.name \u4E0D\u5B58\u5728\u4E0D\u5B58\u5165
    if (!setFieldValueType || !props.name) {
      return;
    }
    // Field.type === 'ProField' \u65F6 props \u91CC\u9762\u662F\u6709 valueType \u7684\uFF0C\u6240\u4EE5\u8981\u8BBE\u7F6E\u4E00\u4E0B
    // \u5199\u4E00\u4E2A ts \u6BD4\u8F83\u9EBB\u70E6\uFF0C\u7528 any \u9876\u4E00\u4E0B
    setFieldValueType([formListField.listName, props.name].flat(1).filter(function (itemName) {
      return itemName !== undefined;
    }), {
      valueType: valueType || 'text',
      dateFormat: dataFormat,
      transform: transform
    });
  }, [formListField.listName, name, dataFormat, props.name, setFieldValueType, transform, valueType]);
  var isDropdown = /*#__PURE__*/react.isValidElement(props.children) && isDropdownValueType(valueType || props.children.props.valueType);
  var noLightFormItem = (0,react.useMemo)(function () {
    if (!(lightProps !== null && lightProps !== void 0 && lightProps.light) || lightProps !== null && lightProps !== void 0 && lightProps.customLightMode || isDropdown) {
      return true;
    }
    return false;
  }, [lightProps === null || lightProps === void 0 ? void 0 : lightProps.customLightMode, isDropdown, lightProps === null || lightProps === void 0 ? void 0 : lightProps.light]);

  // formItem \u652F\u6301function\uFF0C\u5982\u679C\u662Ffunction \u6211\u5C31\u76F4\u63A5\u4E0D\u7BA1\u4E86
  if (typeof props.children === 'function') {
    var _rest$name;
    return /*#__PURE__*/(0,react.createElement)(WarpFormItem, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, rest), {}, {
      name: name,
      key: rest.proFormFieldKey || ((_rest$name = rest.name) === null || _rest$name === void 0 ? void 0 : _rest$name.toString())
    }), props.children);
  }
  var children = /*#__PURE__*/(0,jsx_runtime.jsx)(WithValueFomFiledProps, {
    valuePropName: props.valuePropName,
    children: props.children
  }, rest.proFormFieldKey || ((_rest$name2 = rest.name) === null || _rest$name2 === void 0 ? void 0 : _rest$name2.toString()));
  var lightDom = noLightFormItem ? children : /*#__PURE__*/(0,react.createElement)(LightWrapper, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, lightProps), {}, {
    key: rest.proFormFieldKey || ((_rest$name3 = rest.name) === null || _rest$name3 === void 0 ? void 0 : _rest$name3.toString()),
    size: size
  }), children);
  // \u8FD9\u91CC\u63A7\u5236\u662F\u5426\u9700\u8981 LightWrapper\uFF0C\u4E3A\u4E86\u63D0\u5347\u4E00\u70B9\u70B9\u6027\u80FD
  if (ignoreFormItem) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: lightDom
    });
  }
  return /*#__PURE__*/(0,jsx_runtime.jsx)(WarpFormItem, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, formItemProps), rest), {}, {
    name: name,
    isListField: formListField.name !== undefined,
    children: lightDom
  }), rest.proFormFieldKey || ((_rest$name4 = rest.name) === null || _rest$name4 === void 0 ? void 0 : _rest$name4.toString()));
};

/* harmony default export */ var FormItem = (ProFormItem);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///4499
`)},55895:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  J: function() { return /* binding */ FormListContext; },
  u: function() { return /* binding */ ProFormList; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/CopyOutlined.js
var asn_CopyOutlined = __webpack_require__(48820);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/components/AntdIcon.js + 6 modules
var AntdIcon = __webpack_require__(46976);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/icons/CopyOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var CopyOutlined = function CopyOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_CopyOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_CopyOutlined = (/*#__PURE__*/react.forwardRef(CopyOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/DeleteOutlined.js
var asn_DeleteOutlined = __webpack_require__(47046);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/icons/DeleteOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DeleteOutlined = function DeleteOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_DeleteOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_DeleteOutlined = (/*#__PURE__*/react.forwardRef(DeleteOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/index.js + 7 modules
var es = __webpack_require__(89451);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/components/ProFormContext/index.js
var ProFormContext = __webpack_require__(41036);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/helpers/grid.js
var helpers_grid = __webpack_require__(2514);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(74165);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(15861);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/PlusOutlined.js
var asn_PlusOutlined = __webpack_require__(42110);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/icons/PlusOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PlusOutlined = function PlusOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_PlusOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_PlusOutlined = (/*#__PURE__*/react.forwardRef(PlusOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/nanoid/index.js
var nanoid = __webpack_require__(75661);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/runFunction/index.js
var runFunction = __webpack_require__(22270);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/omit.js/es/index.js
var omit_js_es = __webpack_require__(97435);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/BaseForm/EditOrReadOnlyContext.js
var EditOrReadOnlyContext = __webpack_require__(9105);
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(83062);
// EXTERNAL MODULE: ./node_modules/antd/es/spin/index.js + 2 modules
var spin = __webpack_require__(75081);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Children/toArray.js
var toArray = __webpack_require__(50344);
// EXTERNAL MODULE: ./node_modules/rc-util/es/utils/set.js
var set = __webpack_require__(8880);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/List/ListItem.js





var _excluded = ["creatorButtonProps", "deleteIconProps", "copyIconProps", "itemContainerRender", "itemRender", "alwaysShowItemLabel", "prefixCls", "creatorRecord", "action", "actionGuard", "children", "actionRender", "fields", "meta", "field", "index", "formInstance", "originName", "containerClassName", "containerStyle", "min", "max", "count"];











/** Antd \u81EA\u5E26\u7684toArray \u4E0D\u652F\u6301\u65B9\u6CD5\uFF0C\u6240\u4EE5\u9700\u8981\u81EA\u5DF1\u641E\u4E00\u4E2A */
var listToArray = function listToArray(children) {
  if (Array.isArray(children)) {
    return children;
  }
  if (typeof children === 'function') {
    return [children];
  }
  return (0,toArray/* default */.Z)(children);
};
var ProFormListItem = function ProFormListItem(props) {
  var _formInstance$getFiel2;
  var creatorButtonProps = props.creatorButtonProps,
    deleteIconProps = props.deleteIconProps,
    copyIconProps = props.copyIconProps,
    itemContainerRender = props.itemContainerRender,
    itemRender = props.itemRender,
    alwaysShowItemLabel = props.alwaysShowItemLabel,
    prefixCls = props.prefixCls,
    creatorRecord = props.creatorRecord,
    action = props.action,
    actionGuard = props.actionGuard,
    children = props.children,
    actionRender = props.actionRender,
    fields = props.fields,
    meta = props.meta,
    field = props.field,
    index = props.index,
    formInstance = props.formInstance,
    originName = props.originName,
    containerClassName = props.containerClassName,
    containerStyle = props.containerStyle,
    min = props.min,
    max = props.max,
    count = props.count,
    rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var _useContext = (0,react.useContext)(es/* ProProvider */.L_),
    hashId = _useContext.hashId;
  var listContext = (0,react.useContext)(FormListContext);
  var unmountedRef = (0,react.useRef)(false);
  var _useContext2 = (0,react.useContext)(EditOrReadOnlyContext/* EditOrReadOnlyContext */.A),
    mode = _useContext2.mode;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    loadingRemove = _useState2[0],
    setLoadingRemove = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = (0,slicedToArray/* default */.Z)(_useState3, 2),
    loadingCopy = _useState4[0],
    setLoadingCopy = _useState4[1];
  (0,react.useEffect)(function () {
    return function () {
      unmountedRef.current = true;
    };
  }, []);
  var getCurrentRowData = function getCurrentRowData() {
    return formInstance.getFieldValue([listContext.listName, originName, index === null || index === void 0 ? void 0 : index.toString()].flat(1).filter(function (item) {
      return item !== null && item !== undefined;
    }));
  };
  var formListAction = {
    getCurrentRowData: getCurrentRowData,
    setCurrentRowData: function setCurrentRowData(data) {
      var _formInstance$getFiel;
      var oldTableDate = (formInstance === null || formInstance === void 0 || (_formInstance$getFiel = formInstance.getFieldsValue) === null || _formInstance$getFiel === void 0 ? void 0 : _formInstance$getFiel.call(formInstance)) || {};
      var rowKeyName = [listContext.listName, originName, index === null || index === void 0 ? void 0 : index.toString()].flat(1).filter(function (item) {
        return item !== null && item !== undefined;
      });
      var updateValues = (0,set/* default */.Z)(oldTableDate, rowKeyName, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, getCurrentRowData()), data || {}));
      return formInstance.setFieldsValue(updateValues);
    }
  };
  var childrenArray = listToArray(children).map(function (childrenItem) {
    if (typeof childrenItem === 'function') {
      return childrenItem === null || childrenItem === void 0 ? void 0 : childrenItem(field, index, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, action), formListAction), count);
    }
    return childrenItem;
  }).map(function (childrenItem, itemIndex) {
    if ( /*#__PURE__*/react.isValidElement(childrenItem)) {
      var _childrenItem$props;
      return /*#__PURE__*/react.cloneElement(childrenItem, (0,objectSpread2/* default */.Z)({
        key: childrenItem.key || (childrenItem === null || childrenItem === void 0 || (_childrenItem$props = childrenItem.props) === null || _childrenItem$props === void 0 ? void 0 : _childrenItem$props.name) || itemIndex
      }, (childrenItem === null || childrenItem === void 0 ? void 0 : childrenItem.props) || {}));
    }
    return childrenItem;
  });
  var copyIcon = (0,react.useMemo)(function () {
    if (mode === 'read') return null;
    /** \u590D\u5236\u6309\u94AE\u7684\u914D\u7F6E */
    if (copyIconProps === false || max === count) return null;
    var _ref = copyIconProps,
      _ref$Icon = _ref.Icon,
      Icon = _ref$Icon === void 0 ? icons_CopyOutlined : _ref$Icon,
      tooltipText = _ref.tooltipText;
    return /*#__PURE__*/(0,jsx_runtime.jsx)(tooltip/* default */.Z, {
      title: tooltipText,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
        spinning: loadingCopy,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Icon, {
          className: "".concat(prefixCls, "-action-icon action-copy ").concat(hashId).trim(),
          onClick: /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee() {
            var row;
            return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  setLoadingCopy(true);
                  row = formInstance === null || formInstance === void 0 ? void 0 : formInstance.getFieldValue([listContext.listName, originName, field.name].filter(function (item) {
                    return item !== undefined;
                  }).flat(1));
                  _context.next = 4;
                  return action.add(row);
                case 4:
                  setLoadingCopy(false);
                case 5:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }))
        })
      })
    }, "copy");
  }, [copyIconProps, max, count, loadingCopy, prefixCls, hashId, formInstance, listContext.listName, field.name, originName, action]);
  var deleteIcon = (0,react.useMemo)(function () {
    if (mode === 'read') return null;
    if (deleteIconProps === false || min === count) return null;
    var _ref3 = deleteIconProps,
      _ref3$Icon = _ref3.Icon,
      Icon = _ref3$Icon === void 0 ? icons_DeleteOutlined : _ref3$Icon,
      tooltipText = _ref3.tooltipText;
    return /*#__PURE__*/(0,jsx_runtime.jsx)(tooltip/* default */.Z, {
      title: tooltipText,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(spin/* default */.Z, {
        spinning: loadingRemove,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(Icon, {
          className: "".concat(prefixCls, "-action-icon action-remove ").concat(hashId).trim(),
          onClick: /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee2() {
            return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  setLoadingRemove(true);
                  _context2.next = 3;
                  return action.remove(field.name);
                case 3:
                  if (!unmountedRef.current) {
                    setLoadingRemove(false);
                  }
                case 4:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          }))
        })
      })
    }, "delete");
  }, [deleteIconProps, min, count, loadingRemove, prefixCls, hashId, action, field.name]);
  var defaultActionDom = (0,react.useMemo)(function () {
    return [copyIcon, deleteIcon].filter(function (item) {
      return item !== null && item !== undefined;
    });
  }, [copyIcon, deleteIcon]);
  var actions = (actionRender === null || actionRender === void 0 ? void 0 : actionRender(field, action, defaultActionDom, count)) || defaultActionDom;
  var dom = actions.length > 0 && mode !== 'read' ? /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: "".concat(prefixCls, "-action ").concat(hashId).trim(),
    children: actions
  }) : null;
  var options = {
    name: rest.name,
    field: field,
    index: index,
    record: formInstance === null || formInstance === void 0 || (_formInstance$getFiel2 = formInstance.getFieldValue) === null || _formInstance$getFiel2 === void 0 ? void 0 : _formInstance$getFiel2.call(formInstance, [listContext.listName, originName, field.name].filter(function (item) {
      return item !== undefined;
    }).flat(1)),
    fields: fields,
    operation: action,
    meta: meta
  };
  var _useGridHelpers = (0,helpers_grid/* useGridHelpers */.zx)(),
    grid = _useGridHelpers.grid;
  var itemContainer = (itemContainerRender === null || itemContainerRender === void 0 ? void 0 : itemContainerRender(childrenArray, options)) || childrenArray;
  var contentDom = (itemRender === null || itemRender === void 0 ? void 0 : itemRender({
    listDom: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-container ").concat(containerClassName || '', " ").concat(hashId || '').trim(),
      style: (0,objectSpread2/* default */.Z)({
        width: grid ? '100%' : undefined
      }, containerStyle),
      children: itemContainer
    }),
    action: dom
  }, options)) || /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    className: "".concat(prefixCls, "-item ").concat(hashId, " \\n      ").concat(alwaysShowItemLabel === undefined && "".concat(prefixCls, "-item-default"), "\\n      ").concat(alwaysShowItemLabel ? "".concat(prefixCls, "-item-show-label") : ''),
    style: {
      display: 'flex',
      alignItems: 'flex-end'
    },
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: "".concat(prefixCls, "-container ").concat(containerClassName || '', " ").concat(hashId).trim(),
      style: (0,objectSpread2/* default */.Z)({
        width: grid ? '100%' : undefined
      }, containerStyle),
      children: itemContainer
    }), dom]
  });
  return /*#__PURE__*/(0,jsx_runtime.jsx)(FormListContext.Provider, {
    value: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, field), {}, {
      listName: [listContext.listName, originName, field.name].filter(function (item) {
        return item !== undefined;
      }).flat(1)
    }),
    children: contentDom
  });
};

;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/List/ListContainer.js
















var ProFormListContainer = function ProFormListContainer(props) {
  var intl = (0,es/* useIntl */.YB)();
  var creatorButtonProps = props.creatorButtonProps,
    prefixCls = props.prefixCls,
    children = props.children,
    creatorRecord = props.creatorRecord,
    action = props.action,
    fields = props.fields,
    actionGuard = props.actionGuard,
    max = props.max,
    fieldExtraRender = props.fieldExtraRender,
    meta = props.meta,
    containerClassName = props.containerClassName,
    containerStyle = props.containerStyle,
    onAfterAdd = props.onAfterAdd,
    onAfterRemove = props.onAfterRemove;
  var _useContext = (0,react.useContext)(es/* ProProvider */.L_),
    hashId = _useContext.hashId;
  var fieldKeyMap = (0,react.useRef)(new Map());
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var uuidFields = (0,react.useMemo)(function () {
    return fields.map(function (field) {
      var _fieldKeyMap$current, _fieldKeyMap$current3;
      if (!((_fieldKeyMap$current = fieldKeyMap.current) !== null && _fieldKeyMap$current !== void 0 && _fieldKeyMap$current.has(field.key.toString()))) {
        var _fieldKeyMap$current2;
        (_fieldKeyMap$current2 = fieldKeyMap.current) === null || _fieldKeyMap$current2 === void 0 || _fieldKeyMap$current2.set(field.key.toString(), (0,nanoid/* nanoid */.x)());
      }
      var uuid = (_fieldKeyMap$current3 = fieldKeyMap.current) === null || _fieldKeyMap$current3 === void 0 ? void 0 : _fieldKeyMap$current3.get(field.key.toString());
      return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, field), {}, {
        uuid: uuid
      });
    });
  }, [fields]);

  /**
   * \u6839\u636E\u884C\u4E3A\u5B88\u536B\u5305\u88C5action\u51FD\u6570
   */
  var wrapperAction = (0,react.useMemo)(function () {
    var wrapAction = (0,objectSpread2/* default */.Z)({}, action);
    var count = uuidFields.length;
    if (actionGuard !== null && actionGuard !== void 0 && actionGuard.beforeAddRow) {
      wrapAction.add = /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee() {
        var _len,
          rest,
          _key,
          success,
          res,
          _args = arguments;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              for (_len = _args.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
                rest[_key] = _args[_key];
              }
              _context.next = 3;
              return actionGuard.beforeAddRow.apply(actionGuard, rest.concat([count]));
            case 3:
              success = _context.sent;
              if (!success) {
                _context.next = 8;
                break;
              }
              res = action.add.apply(action, rest);
              onAfterAdd === null || onAfterAdd === void 0 || onAfterAdd.apply(void 0, rest.concat([count + 1]));
              return _context.abrupt("return", res);
            case 8:
              return _context.abrupt("return", false);
            case 9:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
    } else {
      wrapAction.add = /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee2() {
        var _len2,
          rest,
          _key2,
          res,
          _args2 = arguments;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              for (_len2 = _args2.length, rest = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
                rest[_key2] = _args2[_key2];
              }
              res = action.add.apply(action, rest);
              onAfterAdd === null || onAfterAdd === void 0 || onAfterAdd.apply(void 0, rest.concat([count + 1]));
              return _context2.abrupt("return", res);
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
    }
    if (actionGuard !== null && actionGuard !== void 0 && actionGuard.beforeRemoveRow) {
      wrapAction.remove = /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee3() {
        var _len3,
          rest,
          _key3,
          success,
          res,
          _args3 = arguments;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              for (_len3 = _args3.length, rest = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
                rest[_key3] = _args3[_key3];
              }
              _context3.next = 3;
              return actionGuard.beforeRemoveRow.apply(actionGuard, rest.concat([count]));
            case 3:
              success = _context3.sent;
              if (!success) {
                _context3.next = 8;
                break;
              }
              res = action.remove.apply(action, rest);
              onAfterRemove === null || onAfterRemove === void 0 || onAfterRemove.apply(void 0, rest.concat([count - 1]));
              return _context3.abrupt("return", res);
            case 8:
              return _context3.abrupt("return", false);
            case 9:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }));
    } else {
      wrapAction.remove = /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee4() {
        var _len4,
          rest,
          _key4,
          res,
          _args4 = arguments;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              for (_len4 = _args4.length, rest = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
                rest[_key4] = _args4[_key4];
              }
              res = action.remove.apply(action, rest);
              onAfterRemove === null || onAfterRemove === void 0 || onAfterRemove.apply(void 0, rest.concat([count - 1]));
              return _context4.abrupt("return", res);
            case 4:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
    }
    return wrapAction;
  }, [action, actionGuard === null || actionGuard === void 0 ? void 0 : actionGuard.beforeAddRow, actionGuard === null || actionGuard === void 0 ? void 0 : actionGuard.beforeRemoveRow, onAfterAdd, onAfterRemove, uuidFields.length]);
  var creatorButton = (0,react.useMemo)(function () {
    if (creatorButtonProps === false || uuidFields.length === max) return null;
    var _ref5 = creatorButtonProps || {},
      _ref5$position = _ref5.position,
      position = _ref5$position === void 0 ? 'bottom' : _ref5$position,
      _ref5$creatorButtonTe = _ref5.creatorButtonText,
      creatorButtonText = _ref5$creatorButtonTe === void 0 ? intl.getMessage('editableTable.action.add', '\u6DFB\u52A0\u4E00\u884C\u6570\u636E') : _ref5$creatorButtonTe;
    return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
      className: "".concat(prefixCls, "-creator-button-").concat(position, " ").concat(hashId || '').trim(),
      type: "dashed",
      loading: loading,
      block: true,
      icon: /*#__PURE__*/(0,jsx_runtime.jsx)(icons_PlusOutlined, {})
    }, (0,omit_js_es/* default */.Z)(creatorButtonProps || {}, ['position', 'creatorButtonText'])), {}, {
      onClick: /*#__PURE__*/(0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee5() {
        var index;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              setLoading(true);
              // \u5982\u679C\u4E0D\u662F\u4ECE\u9876\u90E8\u5F00\u59CB\u6DFB\u52A0\uFF0C\u5219\u63D2\u5165\u7684\u7D22\u5F15\u4E3A\u5F53\u524D\u884C\u6570
              index = uuidFields.length; // \u5982\u679C\u662F\u9876\u90E8\uFF0C\u52A0\u5230\u7B2C\u4E00\u4E2A\uFF0C\u5982\u679C\u4E0D\u662F\uFF0C\u4E3A\u7A7A\u5C31\u662F\u6700\u540E\u4E00\u4E2A
              if (position === 'top') index = 0;
              _context5.next = 5;
              return wrapperAction.add((0,runFunction/* runFunction */.h)(creatorRecord) || {}, index);
            case 5:
              setLoading(false);
            case 6:
            case "end":
              return _context5.stop();
          }
        }, _callee5);
      })),
      children: creatorButtonText
    }));
  }, [creatorButtonProps, uuidFields.length, max, intl, prefixCls, hashId, loading, wrapperAction, creatorRecord]);
  var readOnlyContext = (0,react.useContext)(EditOrReadOnlyContext/* EditOrReadOnlyContext */.A);
  var defaultStyle = (0,objectSpread2/* default */.Z)({
    width: 'max-content',
    maxWidth: '100%',
    minWidth: '100%'
  }, containerStyle);
  var itemList = (0,react.useMemo)(function () {
    return uuidFields.map(function (field, index) {
      return /*#__PURE__*/(0,react.createElement)(ProFormListItem, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, props), {}, {
        key: field.uuid,
        field: field,
        index: index,
        action: wrapperAction,
        count: uuidFields.length
      }), children);
    });
  }, [children, props, uuidFields, wrapperAction]);
  if (readOnlyContext.mode === 'read' || props.readonly === true) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
      children: itemList
    });
  }
  return /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
    style: defaultStyle,
    className: containerClassName,
    children: [creatorButtonProps !== false && (creatorButtonProps === null || creatorButtonProps === void 0 ? void 0 : creatorButtonProps.position) === 'top' && creatorButton, itemList, fieldExtraRender && fieldExtraRender(wrapperAction, meta), creatorButtonProps !== false && (creatorButtonProps === null || creatorButtonProps === void 0 ? void 0 : creatorButtonProps.position) !== 'top' && creatorButton]
  });
};

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/List/style.js



var genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-pro"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form:not(").concat(token.antCls, "-form-horizontal)"), (0,defineProperty/* default */.Z)({}, token.componentCls, (0,defineProperty/* default */.Z)({}, "&-item:not(".concat(token.componentCls, "-item-show-label)"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item-label"), {
    display: 'none'
  }))))), token.componentCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    maxWidth: '100%',
    '&-item': {
      '&&-show-label': (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item-label"), {
        display: 'inline-block'
      }),
      '&&-default:first-child': {
        'div:first-of-type': (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item-label"), {
          display: 'inline-block'
        }))
      },
      '&&-default:not(:first-child)': {
        'div:first-of-type': (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-form-item-label"), {
          display: 'none'
        }))
      }
    },
    '&-action': {
      display: 'flex',
      height: '32px',
      marginBlockEnd: token.marginLG,
      lineHeight: '32px'
    },
    '&-action-icon': {
      marginInlineStart: 8,
      cursor: 'pointer',
      transition: 'color 0.3s ease-in-out',
      '&:hover': {
        color: token.colorPrimaryTextHover
      }
    }
  }, "".concat(token.proComponentsCls, "-card ").concat(token.proComponentsCls, "-card-extra"), (0,defineProperty/* default */.Z)({}, token.componentCls, {
    '&-action': {
      marginBlockEnd: 0
    }
  })), '&-creator-button-top', {
    marginBlockEnd: 24
  }));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('ProFormList', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/es/components/List/index.js



var List_excluded = ["actionRender", "creatorButtonProps", "label", "alwaysShowItemLabel", "tooltip", "creatorRecord", "itemRender", "rules", "itemContainerRender", "fieldExtraRender", "copyIconProps", "children", "deleteIconProps", "actionRef", "style", "prefixCls", "actionGuard", "min", "max", "colProps", "wrapperCol", "rowProps", "onAfterAdd", "onAfterRemove", "isValidateList", "emptyListMessage", "className", "containerClassName", "containerStyle", "readonly"];












var FormListContext = /*#__PURE__*/react.createContext({});
function ProFormList(props) {
  var actionRefs = (0,react.useRef)();
  var context = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext);
  var listContext = (0,react.useContext)(FormListContext);
  var baseClassName = context.getPrefixCls('pro-form-list');
  // Internationalization
  var intl = (0,es/* useIntl */.YB)();
  var actionRender = props.actionRender,
    creatorButtonProps = props.creatorButtonProps,
    label = props.label,
    alwaysShowItemLabel = props.alwaysShowItemLabel,
    tooltip = props.tooltip,
    creatorRecord = props.creatorRecord,
    itemRender = props.itemRender,
    rules = props.rules,
    itemContainerRender = props.itemContainerRender,
    fieldExtraRender = props.fieldExtraRender,
    _props$copyIconProps = props.copyIconProps,
    copyIconProps = _props$copyIconProps === void 0 ? {
      Icon: icons_CopyOutlined,
      tooltipText: intl.getMessage('copyThisLine', '\u590D\u5236\u6B64\u9879')
    } : _props$copyIconProps,
    _children = props.children,
    _props$deleteIconProp = props.deleteIconProps,
    deleteIconProps = _props$deleteIconProp === void 0 ? {
      Icon: icons_DeleteOutlined,
      tooltipText: intl.getMessage('deleteThisLine', '\u5220\u9664\u6B64\u9879')
    } : _props$deleteIconProp,
    actionRef = props.actionRef,
    style = props.style,
    prefixCls = props.prefixCls,
    actionGuard = props.actionGuard,
    min = props.min,
    max = props.max,
    colProps = props.colProps,
    wrapperCol = props.wrapperCol,
    rowProps = props.rowProps,
    _onAfterAdd = props.onAfterAdd,
    _onAfterRemove = props.onAfterRemove,
    _props$isValidateList = props.isValidateList,
    isValidateList = _props$isValidateList === void 0 ? false : _props$isValidateList,
    _props$emptyListMessa = props.emptyListMessage,
    emptyListMessage = _props$emptyListMessa === void 0 ? '\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A' : _props$emptyListMessa,
    className = props.className,
    containerClassName = props.containerClassName,
    containerStyle = props.containerStyle,
    readonly = props.readonly,
    rest = (0,objectWithoutProperties/* default */.Z)(props, List_excluded);
  var _useGridHelpers = (0,helpers_grid/* useGridHelpers */.zx)({
      colProps: colProps,
      rowProps: rowProps
    }),
    ColWrapper = _useGridHelpers.ColWrapper,
    RowWrapper = _useGridHelpers.RowWrapper;
  var proFormContext = (0,react.useContext)(ProFormContext/* ProFormContext */.J);

  // \u5904\u7406 list \u7684\u5D4C\u5957
  var name = (0,react.useMemo)(function () {
    if (listContext.name === undefined) {
      return [rest.name].flat(1);
    }
    return [listContext.name, rest.name].flat(1);
  }, [listContext.name, rest.name]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  (0,react.useImperativeHandle)(actionRef, function () {
    return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, actionRefs.current), {}, {
      get: function get(index) {
        return proFormContext.formRef.current.getFieldValue([].concat((0,toConsumableArray/* default */.Z)(name), [index]));
      },
      getList: function getList() {
        return proFormContext.formRef.current.getFieldValue((0,toConsumableArray/* default */.Z)(name));
      }
    });
  }, [name, proFormContext.formRef]);
  (0,react.useEffect)(function () {
    (0,warning/* noteOnce */.ET)(!!proFormContext.formRef, "ProFormList \\u5FC5\\u987B\\u8981\\u653E\\u5230 ProForm \\u4E2D,\\u5426\\u5219\\u4F1A\\u9020\\u6210\\u884C\\u4E3A\\u5F02\\u5E38\\u3002");
    (0,warning/* noteOnce */.ET)(!!proFormContext.formRef, "Proformlist must be placed in ProForm, otherwise it will cause abnormal behavior.");
  }, [proFormContext.formRef]);
  var _useStyle = style_useStyle(baseClassName),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  if (!proFormContext.formRef) return null;
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)(ColWrapper, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
      className: classnames_default()(baseClassName, hashId),
      style: style,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.Item, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
        label: label,
        prefixCls: prefixCls,
        tooltip: tooltip,
        style: style,
        required: rules === null || rules === void 0 ? void 0 : rules.some(function (rule) {
          return rule.required;
        }),
        wrapperCol: wrapperCol,
        className: className
      }, rest), {}, {
        name: isValidateList ? name : undefined,
        rules: isValidateList ? [{
          validator: function validator(rule, value) {
            if (!value || value.length === 0) {
              return Promise.reject(new Error(emptyListMessage));
            }
            return Promise.resolve();
          },
          required: true
        }] : undefined,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.List, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
          rules: rules
        }, rest), {}, {
          name: name,
          children: function children(fields, action, meta) {
            // \u5C06 action \u66B4\u9732\u7ED9\u5916\u90E8
            actionRefs.current = action;
            return /*#__PURE__*/(0,jsx_runtime.jsxs)(RowWrapper, {
              children: [/*#__PURE__*/(0,jsx_runtime.jsx)(ProFormListContainer, {
                name: name,
                readonly: !!readonly,
                originName: rest.name,
                copyIconProps: copyIconProps,
                deleteIconProps: deleteIconProps,
                formInstance: proFormContext.formRef.current,
                prefixCls: baseClassName,
                meta: meta,
                fields: fields,
                itemContainerRender: itemContainerRender,
                itemRender: itemRender,
                fieldExtraRender: fieldExtraRender,
                creatorButtonProps: creatorButtonProps,
                creatorRecord: creatorRecord,
                actionRender: actionRender,
                action: action,
                actionGuard: actionGuard,
                alwaysShowItemLabel: alwaysShowItemLabel,
                min: min,
                max: max,
                count: fields.length,
                onAfterAdd: function onAfterAdd(defaultValue, insertIndex, count) {
                  if (isValidateList) {
                    proFormContext.formRef.current.validateFields([name]);
                  }
                  _onAfterAdd === null || _onAfterAdd === void 0 || _onAfterAdd(defaultValue, insertIndex, count);
                },
                onAfterRemove: function onAfterRemove(index, count) {
                  if (isValidateList) {
                    if (count === 0) {
                      proFormContext.formRef.current.validateFields([name]);
                    }
                  }
                  _onAfterRemove === null || _onAfterRemove === void 0 || _onAfterRemove(index, count);
                },
                containerClassName: containerClassName,
                containerStyle: containerStyle,
                children: _children
              }), /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z.ErrorList, {
                errors: meta.errors
              })]
            });
          }
        }))
      }))
    })
  }));
}
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///55895
`)},2514:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   _p: function() { return /* binding */ GridContext; },
/* harmony export */   zx: function() { return /* binding */ useGridHelpers; }
/* harmony export */ });
/* unused harmony export gridHelpers */
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71002);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(71230);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(15746);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



var _excluded = ["children", "Wrapper"],
  _excluded2 = ["children", "Wrapper"];



var GridContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({
  grid: false,
  colProps: undefined,
  rowProps: undefined
});
var gridHelpers = function gridHelpers(_ref) {
  var grid = _ref.grid,
    rowProps = _ref.rowProps,
    colProps = _ref.colProps;
  return {
    grid: !!grid,
    RowWrapper: function RowWrapper() {
      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        children = _ref2.children,
        Wrapper = _ref2.Wrapper,
        props = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref2, _excluded);
      if (!grid) {
        return Wrapper ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Wrapper, {
          children: children
        }) : children;
      }
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
        gutter: 8
      }, rowProps), props), {}, {
        children: children
      }));
    },
    ColWrapper: function ColWrapper() {
      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        children = _ref3.children,
        Wrapper = _ref3.Wrapper,
        rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref3, _excluded2);
      var props = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
        var originProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({}, colProps), rest);

        /**
         * \`xs\` takes precedence over \`span\`
         * avoid \`span\` doesn't work
         */
        if (typeof originProps.span === 'undefined' && typeof originProps.xs === 'undefined') {
          originProps.xs = 24;
        }
        return originProps;
      }, [rest]);
      if (!grid) {
        return Wrapper ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Wrapper, {
          children: children
        }) : children;
      }
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({}, props), {}, {
        children: children
      }));
    }
  };
};
var useGridHelpers = function useGridHelpers(props) {
  var config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    {
      if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(props) === 'object') {
        return props;
      }
      return {
        grid: props
      };
    }
  }, [props]);
  var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GridContext),
    grid = _useContext.grid,
    colProps = _useContext.colProps;
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return gridHelpers({
      grid: !!(grid || config.grid),
      rowProps: config === null || config === void 0 ? void 0 : config.rowProps,
      colProps: (config === null || config === void 0 ? void 0 : config.colProps) || colProps,
      Wrapper: config === null || config === void 0 ? void 0 : config.Wrapper
    });
  },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [config === null || config === void 0 ? void 0 : config.Wrapper, config.grid, grid,
  // eslint-disable-next-line react-hooks/exhaustive-deps
  JSON.stringify([colProps, config === null || config === void 0 ? void 0 : config.colProps, config === null || config === void 0 ? void 0 : config.rowProps])]);
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///2514
`)},46976:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ AntdIcon; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/conversion.js
var conversion = __webpack_require__(86500);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/format-input.js
var format_input = __webpack_require__(1350);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/colors/es/generate.js

var hueStep = 2; // \u8272\u76F8\u9636\u68AF
var saturationStep = 0.16; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var saturationStep2 = 0.05; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var brightnessStep1 = 0.05; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var brightnessStep2 = 0.15; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var lightColorCount = 5; // \u6D45\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0A
var darkColorCount = 4; // \u6DF1\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0B
// \u6697\u8272\u4E3B\u9898\u989C\u8272\u6620\u5C04\u5173\u7CFB\u8868
var darkColorMap = [{
  index: 7,
  opacity: 0.15
}, {
  index: 6,
  opacity: 0.25
}, {
  index: 5,
  opacity: 0.3
}, {
  index: 5,
  opacity: 0.45
}, {
  index: 5,
  opacity: 0.65
}, {
  index: 5,
  opacity: 0.85
}, {
  index: 4,
  opacity: 0.9
}, {
  index: 3,
  opacity: 0.95
}, {
  index: 2,
  opacity: 0.97
}, {
  index: 1,
  opacity: 0.98
}];
// Wrapper function ported from TinyColor.prototype.toHsv
// Keep it here because of \`hsv.h * 360\`
function toHsv(_ref) {
  var r = _ref.r,
    g = _ref.g,
    b = _ref.b;
  var hsv = (0,conversion/* rgbToHsv */.py)(r, g, b);
  return {
    h: hsv.h * 360,
    s: hsv.s,
    v: hsv.v
  };
}

// Wrapper function ported from TinyColor.prototype.toHexString
// Keep it here because of the prefix \`#\`
function toHex(_ref2) {
  var r = _ref2.r,
    g = _ref2.g,
    b = _ref2.b;
  return "#".concat((0,conversion/* rgbToHex */.vq)(r, g, b, false));
}

// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.
// Amount in range [0, 1]
// Assume color1 & color2 has no alpha, since the following src code did so.
function mix(rgb1, rgb2, amount) {
  var p = amount / 100;
  var rgb = {
    r: (rgb2.r - rgb1.r) * p + rgb1.r,
    g: (rgb2.g - rgb1.g) * p + rgb1.g,
    b: (rgb2.b - rgb1.b) * p + rgb1.b
  };
  return rgb;
}
function getHue(hsv, i, light) {
  var hue;
  // \u6839\u636E\u8272\u76F8\u4E0D\u540C\uFF0C\u8272\u76F8\u8F6C\u5411\u4E0D\u540C
  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;
  } else {
    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return hue;
}
function getSaturation(hsv, i, light) {
  // grey color don't change saturation
  if (hsv.h === 0 && hsv.s === 0) {
    return hsv.s;
  }
  var saturation;
  if (light) {
    saturation = hsv.s - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = hsv.s + saturationStep;
  } else {
    saturation = hsv.s + saturationStep2 * i;
  }
  // \u8FB9\u754C\u503C\u4FEE\u6B63
  if (saturation > 1) {
    saturation = 1;
  }
  // \u7B2C\u4E00\u683C\u7684 s \u9650\u5236\u5728 0.06-0.1 \u4E4B\u95F4
  if (light && i === lightColorCount && saturation > 0.1) {
    saturation = 0.1;
  }
  if (saturation < 0.06) {
    saturation = 0.06;
  }
  return Number(saturation.toFixed(2));
}
function getValue(hsv, i, light) {
  var value;
  if (light) {
    value = hsv.v + brightnessStep1 * i;
  } else {
    value = hsv.v - brightnessStep2 * i;
  }
  if (value > 1) {
    value = 1;
  }
  return Number(value.toFixed(2));
}
function generate(color) {
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var patterns = [];
  var pColor = (0,format_input/* inputToRGB */.uA)(color);
  for (var i = lightColorCount; i > 0; i -= 1) {
    var hsv = toHsv(pColor);
    var colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(hsv, i, true),
      s: getSaturation(hsv, i, true),
      v: getValue(hsv, i, true)
    }));
    patterns.push(colorString);
  }
  patterns.push(toHex(pColor));
  for (var _i = 1; _i <= darkColorCount; _i += 1) {
    var _hsv = toHsv(pColor);
    var _colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(_hsv, _i),
      s: getSaturation(_hsv, _i),
      v: getValue(_hsv, _i)
    }));
    patterns.push(_colorString);
  }

  // dark theme patterns
  if (opts.theme === 'dark') {
    return darkColorMap.map(function (_ref3) {
      var index = _ref3.index,
        opacity = _ref3.opacity;
      var darkColorString = toHex(mix((0,format_input/* inputToRGB */.uA)(opts.backgroundColor || '#141414'), (0,format_input/* inputToRGB */.uA)(patterns[index]), opacity * 100));
      return darkColorString;
    });
  }
  return patterns;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/colors/es/index.js

var presetPrimaryColors = {
  red: '#F5222D',
  volcano: '#FA541C',
  orange: '#FA8C16',
  gold: '#FAAD14',
  yellow: '#FADB14',
  lime: '#A0D911',
  green: '#52C41A',
  cyan: '#13C2C2',
  blue: '#1677FF',
  geekblue: '#2F54EB',
  purple: '#722ED1',
  magenta: '#EB2F96',
  grey: '#666666'
};
var presetPalettes = {};
var presetDarkPalettes = {};
Object.keys(presetPrimaryColors).forEach(function (key) {
  presetPalettes[key] = generate(presetPrimaryColors[key]);
  presetPalettes[key].primary = presetPalettes[key][5];

  // dark presetPalettes
  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {
    theme: 'dark',
    backgroundColor: '#141414'
  });
  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];
});
var red = presetPalettes.red;
var volcano = presetPalettes.volcano;
var gold = presetPalettes.gold;
var orange = presetPalettes.orange;
var yellow = presetPalettes.yellow;
var lime = presetPalettes.lime;
var green = presetPalettes.green;
var cyan = presetPalettes.cyan;
var blue = presetPalettes.blue;
var geekblue = presetPalettes.geekblue;
var purple = presetPalettes.purple;
var magenta = presetPalettes.magenta;
var grey = presetPalettes.grey;
var gray = presetPalettes.grey;

;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/components/Context.js

var IconContext = /*#__PURE__*/(0,react.createContext)({});
/* harmony default export */ var Context = (IconContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/dynamicCSS.js
var dynamicCSS = __webpack_require__(44958);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/shadow.js
var shadow = __webpack_require__(27571);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/utils.js








function camelCase(input) {
  return input.replace(/-(.)/g, function (match, g) {
    return g.toUpperCase();
  });
}
function utils_warning(valid, message) {
  (0,warning/* default */.ZP)(valid, "[@ant-design/icons] ".concat(message));
}
function isIconDefinition(target) {
  return (0,esm_typeof/* default */.Z)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0,esm_typeof/* default */.Z)(target.icon) === 'object' || typeof target.icon === 'function');
}
function normalizeAttrs() {
  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.keys(attrs).reduce(function (acc, key) {
    var val = attrs[key];
    switch (key) {
      case 'class':
        acc.className = val;
        delete acc.class;
        break;
      default:
        delete acc[key];
        acc[camelCase(key)] = val;
    }
    return acc;
  }, {});
}
function utils_generate(node, key, rootProps) {
  if (!rootProps) {
    return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)({
      key: key
    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {
      return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
    }));
  }
  return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    key: key
  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {
    return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
  }));
}
function getSecondaryColor(primaryColor) {
  // choose the second color
  return generate(primaryColor)[0];
}
function normalizeTwoToneColors(twoToneColor) {
  if (!twoToneColor) {
    return [];
  }
  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];
}

// These props make sure that the SVG behaviours like general text.
// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4
var svgBaseProps = {
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  'aria-hidden': 'true',
  focusable: 'false'
};
var iconStyles = "\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n";
var useInsertStyles = function useInsertStyles(eleRef) {
  var _useContext = (0,react.useContext)(Context),
    csp = _useContext.csp,
    prefixCls = _useContext.prefixCls;
  var mergedStyleStr = iconStyles;
  if (prefixCls) {
    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);
  }
  (0,react.useEffect)(function () {
    var ele = eleRef.current;
    var shadowRoot = (0,shadow/* getShadowRoot */.A)(ele);
    (0,dynamicCSS/* updateCSS */.hq)(mergedStyleStr, '@ant-design-icons', {
      prepend: true,
      csp: csp,
      attachTo: shadowRoot
    });
  }, []);
};
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/components/IconBase.js


var _excluded = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];


var twoToneColorPalette = {
  primaryColor: '#333',
  secondaryColor: '#E6E6E6',
  calculated: false
};
function setTwoToneColors(_ref) {
  var primaryColor = _ref.primaryColor,
    secondaryColor = _ref.secondaryColor;
  twoToneColorPalette.primaryColor = primaryColor;
  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);
  twoToneColorPalette.calculated = !!secondaryColor;
}
function getTwoToneColors() {
  return (0,objectSpread2/* default */.Z)({}, twoToneColorPalette);
}
var IconBase = function IconBase(props) {
  var icon = props.icon,
    className = props.className,
    onClick = props.onClick,
    style = props.style,
    primaryColor = props.primaryColor,
    secondaryColor = props.secondaryColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var svgRef = react.useRef();
  var colors = twoToneColorPalette;
  if (primaryColor) {
    colors = {
      primaryColor: primaryColor,
      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)
    };
  }
  useInsertStyles(svgRef);
  utils_warning(isIconDefinition(icon), "icon should be icon definiton, but got ".concat(icon));
  if (!isIconDefinition(icon)) {
    return null;
  }
  var target = icon;
  if (target && typeof target.icon === 'function') {
    target = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, target), {}, {
      icon: target.icon(colors.primaryColor, colors.secondaryColor)
    });
  }
  return utils_generate(target.icon, "svg-".concat(target.name), (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: className,
    onClick: onClick,
    style: style,
    'data-icon': target.name,
    width: '1em',
    height: '1em',
    fill: 'currentColor',
    'aria-hidden': 'true'
  }, restProps), {}, {
    ref: svgRef
  }));
};
IconBase.displayName = 'IconReact';
IconBase.getTwoToneColors = getTwoToneColors;
IconBase.setTwoToneColors = setTwoToneColors;
/* harmony default export */ var components_IconBase = (IconBase);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js



function setTwoToneColor(twoToneColor) {
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return components_IconBase.setTwoToneColors({
    primaryColor: primaryColor,
    secondaryColor: secondaryColor
  });
}
function getTwoToneColor() {
  var colors = components_IconBase.getTwoToneColors();
  if (!colors.calculated) {
    return colors.primaryColor;
  }
  return [colors.primaryColor, colors.secondaryColor];
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-form/node_modules/@ant-design/icons/es/components/AntdIcon.js
'use client';





var AntdIcon_excluded = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];







// Initial setting
// should move it to antd main repo?
setTwoToneColor(blue.primary);

// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720

var Icon = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var className = props.className,
    icon = props.icon,
    spin = props.spin,
    rotate = props.rotate,
    tabIndex = props.tabIndex,
    onClick = props.onClick,
    twoToneColor = props.twoToneColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, AntdIcon_excluded);
  var _React$useContext = react.useContext(Context),
    _React$useContext$pre = _React$useContext.prefixCls,
    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,
    rootClassName = _React$useContext.rootClassName;
  var classString = classnames_default()(rootClassName, prefixCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-").concat(icon.name), !!icon.name), "".concat(prefixCls, "-spin"), !!spin || icon.name === 'loading'), className);
  var iconTabIndex = tabIndex;
  if (iconTabIndex === undefined && onClick) {
    iconTabIndex = -1;
  }
  var svgStyle = rotate ? {
    msTransform: "rotate(".concat(rotate, "deg)"),
    transform: "rotate(".concat(rotate, "deg)")
  } : undefined;
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.Z)({
    role: "img",
    "aria-label": icon.name
  }, restProps, {
    ref: ref,
    tabIndex: iconTabIndex,
    onClick: onClick,
    className: classString
  }), /*#__PURE__*/react.createElement(components_IconBase, {
    icon: icon,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    style: svgStyle
  }));
});
Icon.displayName = 'AntdIcon';
Icon.getTwoToneColor = getTwoToneColor;
Icon.setTwoToneColor = setTwoToneColor;
/* harmony default export */ var AntdIcon = (Icon);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDY5NzYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlFO0FBQ2pFLGlCQUFpQjtBQUNqQiwyQkFBMkI7QUFDM0IsNEJBQTRCO0FBQzVCLDRCQUE0QjtBQUM1Qiw0QkFBNEI7QUFDNUIseUJBQXlCO0FBQ3pCLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLCtCQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0JBQVE7QUFDNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDZTtBQUNmO0FBQ0E7QUFDQSxlQUFlLG1DQUFVO0FBQ3pCLGdDQUFnQyxPQUFPO0FBQ3ZDO0FBQ0EsNEJBQTRCLG1DQUFVO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsc0JBQXNCO0FBQ3pDO0FBQ0EsNkJBQTZCLG1DQUFVO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsbUNBQVUscUNBQXFDLG1DQUFVO0FBQy9GO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxDOztBQ2xLa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFFBQVE7QUFDaEM7O0FBRUE7QUFDQSw0QkFBNEIsUUFBUTtBQUNwQztBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQzFDc0M7QUFDdEMsK0JBQStCLHVCQUFhLEdBQUc7QUFDL0MsNENBQWUsV0FBVyxFOzs7Ozs7Ozs7Ozs7QUNGMkM7QUFDYjtBQUNPO0FBQ1Q7QUFDQTtBQUNoQjtBQUNlO0FBQ047QUFDL0M7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ08sU0FBUyxhQUFPO0FBQ3ZCLEVBQUUsMkJBQUk7QUFDTjtBQUNPO0FBQ1AsU0FBUyw2QkFBTyxpR0FBaUcsNkJBQU87QUFDeEg7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxJQUFJO0FBQ1A7QUFDTyxTQUFTLGNBQVE7QUFDeEI7QUFDQSx3QkFBd0IsbUJBQW1CLFdBQVcsZ0NBQWE7QUFDbkU7QUFDQSxLQUFLO0FBQ0wsYUFBYSxjQUFRO0FBQ3JCLEtBQUs7QUFDTDtBQUNBLHNCQUFzQixtQkFBbUIsV0FBVyxnQ0FBYSxDQUFDLGdDQUFhO0FBQy9FO0FBQ0EsR0FBRztBQUNILFdBQVcsY0FBUTtBQUNuQixHQUFHO0FBQ0g7QUFDTztBQUNQO0FBQ0EsU0FBUyxRQUFhO0FBQ3RCO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDhCQUE4QiwwQkFBMEIsbUJBQW1CLHVCQUF1QixtQkFBbUIsdUJBQXVCLHlCQUF5Qiw2QkFBNkIsdUNBQXVDLHdDQUF3Qyx1Q0FBdUMsR0FBRyxrQkFBa0IsbUJBQW1CLEdBQUcsa0JBQWtCLDBCQUEwQixHQUFHLHNCQUFzQixrQkFBa0IsR0FBRyw0QkFBNEIsbUJBQW1CLEdBQUcsd0JBQXdCLG9CQUFvQixHQUFHLDJDQUEyQywwQkFBMEIsd0RBQXdELGdEQUFnRCxHQUFHLHNDQUFzQyxVQUFVLHdDQUF3QyxnQ0FBZ0MsS0FBSyxHQUFHLDhCQUE4QixVQUFVLHdDQUF3QyxnQ0FBZ0MsS0FBSyxHQUFHO0FBQ3Q4QjtBQUNQLG9CQUFvQixvQkFBVSxDQUFDLE9BQVc7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxtQkFBUztBQUNYO0FBQ0EscUJBQXFCLCtCQUFhO0FBQ2xDLElBQUksZ0NBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNILEU7O0FDdkYwRjtBQUNyQjtBQUNyRTtBQUMrQjtBQUNvRTtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsaUJBQWlCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLFNBQVMsZ0NBQWEsR0FBRztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDBDQUF3QjtBQUN4QyxlQUFlLFlBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsaUJBQWlCO0FBQ3pEO0FBQ0E7QUFDQSxFQUFFLGVBQWU7QUFDakIsRUFBRSxhQUFPLENBQUMsZ0JBQWdCO0FBQzFCLE9BQU8sZ0JBQWdCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxnQ0FBYSxDQUFDLGdDQUFhLEdBQUcsYUFBYTtBQUN4RDtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsY0FBUSwwQ0FBMEMsZ0NBQWEsQ0FBQyxnQ0FBYTtBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxnQkFBZ0I7QUFDbkI7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBZSxRQUFRLEU7O0FDL0QrQztBQUNuQztBQUNlO0FBQzNDO0FBQ1AsOEJBQThCLHNCQUFzQjtBQUNwRCw2QkFBNkIsZ0NBQWM7QUFDM0M7QUFDQTtBQUNBLFNBQVMsbUJBQVM7QUFDbEI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNPO0FBQ1AsZUFBZSxtQkFBUztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEM7O0FDbkJBOztBQUUwRDtBQUNZO0FBQ0U7QUFDa0I7QUFDMUYsSUFBSSxpQkFBUztBQUNrQjtBQUNLO0FBQ007QUFDVjtBQUNHO0FBQ3NDO0FBQ3ZCO0FBQ2xEO0FBQ0E7QUFDQSxlQUFlLENBQUMsSUFBSTs7QUFFcEI7O0FBRUEsd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiwwQ0FBd0IsUUFBUSxpQkFBUztBQUN6RCwwQkFBMEIsZ0JBQWdCLENBQUMsT0FBTztBQUNsRDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQVUsMkJBQTJCLGlDQUFlLENBQUMsaUNBQWUsR0FBRztBQUMzRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiw4QkFBOEIsc0JBQXNCO0FBQ3BELDZCQUE2QixnQ0FBYztBQUMzQztBQUNBO0FBQ0Esc0JBQXNCLG1CQUFtQixTQUFTLDhCQUFRO0FBQzFEO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLGdCQUFnQixtQkFBbUIsQ0FBQyxtQkFBUztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDQSx1QkFBdUIsZUFBZTtBQUN0Qyx1QkFBdUIsZUFBZTtBQUN0Qyw2Q0FBZSxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2NvbG9ycy9lcy9nZW5lcmF0ZS5qcz8wNWVhIiwid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby1mb3JtL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9jb2xvcnMvZXMvaW5kZXguanM/OWI2YyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9wcm8tZm9ybS9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvY29tcG9uZW50cy9Db250ZXh0LmpzPzE1NTIiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zL2VzL3V0aWxzLmpzPzQ5NzQiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zL2VzL2NvbXBvbmVudHMvSWNvbkJhc2UuanM/N2YwMyIsIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9wcm8tZm9ybS9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvY29tcG9uZW50cy90d29Ub25lUHJpbWFyeUNvbG9yLmpzPzJiMzgiLCJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLWZvcm0vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zL2VzL2NvbXBvbmVudHMvQW50ZEljb24uanM/MDM1MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnB1dFRvUkdCLCByZ2JUb0hleCwgcmdiVG9Ic3YgfSBmcm9tICdAY3RybC90aW55Y29sb3InO1xudmFyIGh1ZVN0ZXAgPSAyOyAvLyDoibLnm7jpmLbmoq9cbnZhciBzYXR1cmF0aW9uU3RlcCA9IDAuMTY7IC8vIOmlseWSjOW6pumYtuair++8jOa1heiJsumDqOWIhlxudmFyIHNhdHVyYXRpb25TdGVwMiA9IDAuMDU7IC8vIOmlseWSjOW6pumYtuair++8jOa3seiJsumDqOWIhlxudmFyIGJyaWdodG5lc3NTdGVwMSA9IDAuMDU7IC8vIOS6ruW6pumYtuair++8jOa1heiJsumDqOWIhlxudmFyIGJyaWdodG5lc3NTdGVwMiA9IDAuMTU7IC8vIOS6ruW6pumYtuair++8jOa3seiJsumDqOWIhlxudmFyIGxpZ2h0Q29sb3JDb3VudCA9IDU7IC8vIOa1heiJsuaVsOmHj++8jOS4u+iJsuS4ilxudmFyIGRhcmtDb2xvckNvdW50ID0gNDsgLy8g5rex6Imy5pWw6YeP77yM5Li76Imy5LiLXG4vLyDmmpfoibLkuLvpopjpopzoibLmmKDlsITlhbPns7vooahcbnZhciBkYXJrQ29sb3JNYXAgPSBbe1xuICBpbmRleDogNyxcbiAgb3BhY2l0eTogMC4xNVxufSwge1xuICBpbmRleDogNixcbiAgb3BhY2l0eTogMC4yNVxufSwge1xuICBpbmRleDogNSxcbiAgb3BhY2l0eTogMC4zXG59LCB7XG4gIGluZGV4OiA1LFxuICBvcGFjaXR5OiAwLjQ1XG59LCB7XG4gIGluZGV4OiA1LFxuICBvcGFjaXR5OiAwLjY1XG59LCB7XG4gIGluZGV4OiA1LFxuICBvcGFjaXR5OiAwLjg1XG59LCB7XG4gIGluZGV4OiA0LFxuICBvcGFjaXR5OiAwLjlcbn0sIHtcbiAgaW5kZXg6IDMsXG4gIG9wYWNpdHk6IDAuOTVcbn0sIHtcbiAgaW5kZXg6IDIsXG4gIG9wYWNpdHk6IDAuOTdcbn0sIHtcbiAgaW5kZXg6IDEsXG4gIG9wYWNpdHk6IDAuOThcbn1dO1xuLy8gV3JhcHBlciBmdW5jdGlvbiBwb3J0ZWQgZnJvbSBUaW55Q29sb3IucHJvdG90eXBlLnRvSHN2XG4vLyBLZWVwIGl0IGhlcmUgYmVjYXVzZSBvZiBgaHN2LmggKiAzNjBgXG5mdW5jdGlvbiB0b0hzdihfcmVmKSB7XG4gIHZhciByID0gX3JlZi5yLFxuICAgIGcgPSBfcmVmLmcsXG4gICAgYiA9IF9yZWYuYjtcbiAgdmFyIGhzdiA9IHJnYlRvSHN2KHIsIGcsIGIpO1xuICByZXR1cm4ge1xuICAgIGg6IGhzdi5oICogMzYwLFxuICAgIHM6IGhzdi5zLFxuICAgIHY6IGhzdi52XG4gIH07XG59XG5cbi8vIFdyYXBwZXIgZnVuY3Rpb24gcG9ydGVkIGZyb20gVGlueUNvbG9yLnByb3RvdHlwZS50b0hleFN0cmluZ1xuLy8gS2VlcCBpdCBoZXJlIGJlY2F1c2Ugb2YgdGhlIHByZWZpeCBgI2BcbmZ1bmN0aW9uIHRvSGV4KF9yZWYyKSB7XG4gIHZhciByID0gX3JlZjIucixcbiAgICBnID0gX3JlZjIuZyxcbiAgICBiID0gX3JlZjIuYjtcbiAgcmV0dXJuIFwiI1wiLmNvbmNhdChyZ2JUb0hleChyLCBnLCBiLCBmYWxzZSkpO1xufVxuXG4vLyBXcmFwcGVyIGZ1bmN0aW9uIHBvcnRlZCBmcm9tIFRpbnlDb2xvci5wcm90b3R5cGUubWl4LCBub3QgdHJlZXNoYWthYmxlLlxuLy8gQW1vdW50IGluIHJhbmdlIFswLCAxXVxuLy8gQXNzdW1lIGNvbG9yMSAmIGNvbG9yMiBoYXMgbm8gYWxwaGEsIHNpbmNlIHRoZSBmb2xsb3dpbmcgc3JjIGNvZGUgZGlkIHNvLlxuZnVuY3Rpb24gbWl4KHJnYjEsIHJnYjIsIGFtb3VudCkge1xuICB2YXIgcCA9IGFtb3VudCAvIDEwMDtcbiAgdmFyIHJnYiA9IHtcbiAgICByOiAocmdiMi5yIC0gcmdiMS5yKSAqIHAgKyByZ2IxLnIsXG4gICAgZzogKHJnYjIuZyAtIHJnYjEuZykgKiBwICsgcmdiMS5nLFxuICAgIGI6IChyZ2IyLmIgLSByZ2IxLmIpICogcCArIHJnYjEuYlxuICB9O1xuICByZXR1cm4gcmdiO1xufVxuZnVuY3Rpb24gZ2V0SHVlKGhzdiwgaSwgbGlnaHQpIHtcbiAgdmFyIGh1ZTtcbiAgLy8g5qC55o2u6Imy55u45LiN5ZCM77yM6Imy55u46L2s5ZCR5LiN5ZCMXG4gIGlmIChNYXRoLnJvdW5kKGhzdi5oKSA+PSA2MCAmJiBNYXRoLnJvdW5kKGhzdi5oKSA8PSAyNDApIHtcbiAgICBodWUgPSBsaWdodCA/IE1hdGgucm91bmQoaHN2LmgpIC0gaHVlU3RlcCAqIGkgOiBNYXRoLnJvdW5kKGhzdi5oKSArIGh1ZVN0ZXAgKiBpO1xuICB9IGVsc2Uge1xuICAgIGh1ZSA9IGxpZ2h0ID8gTWF0aC5yb3VuZChoc3YuaCkgKyBodWVTdGVwICogaSA6IE1hdGgucm91bmQoaHN2LmgpIC0gaHVlU3RlcCAqIGk7XG4gIH1cbiAgaWYgKGh1ZSA8IDApIHtcbiAgICBodWUgKz0gMzYwO1xuICB9IGVsc2UgaWYgKGh1ZSA+PSAzNjApIHtcbiAgICBodWUgLT0gMzYwO1xuICB9XG4gIHJldHVybiBodWU7XG59XG5mdW5jdGlvbiBnZXRTYXR1cmF0aW9uKGhzdiwgaSwgbGlnaHQpIHtcbiAgLy8gZ3JleSBjb2xvciBkb24ndCBjaGFuZ2Ugc2F0dXJhdGlvblxuICBpZiAoaHN2LmggPT09IDAgJiYgaHN2LnMgPT09IDApIHtcbiAgICByZXR1cm4gaHN2LnM7XG4gIH1cbiAgdmFyIHNhdHVyYXRpb247XG4gIGlmIChsaWdodCkge1xuICAgIHNhdHVyYXRpb24gPSBoc3YucyAtIHNhdHVyYXRpb25TdGVwICogaTtcbiAgfSBlbHNlIGlmIChpID09PSBkYXJrQ29sb3JDb3VudCkge1xuICAgIHNhdHVyYXRpb24gPSBoc3YucyArIHNhdHVyYXRpb25TdGVwO1xuICB9IGVsc2Uge1xuICAgIHNhdHVyYXRpb24gPSBoc3YucyArIHNhdHVyYXRpb25TdGVwMiAqIGk7XG4gIH1cbiAgLy8g6L6555WM5YC85L+u5q2jXG4gIGlmIChzYXR1cmF0aW9uID4gMSkge1xuICAgIHNhdHVyYXRpb24gPSAxO1xuICB9XG4gIC8vIOesrOS4gOagvOeahCBzIOmZkOWItuWcqCAwLjA2LTAuMSDkuYvpl7RcbiAgaWYgKGxpZ2h0ICYmIGkgPT09IGxpZ2h0Q29sb3JDb3VudCAmJiBzYXR1cmF0aW9uID4gMC4xKSB7XG4gICAgc2F0dXJhdGlvbiA9IDAuMTtcbiAgfVxuICBpZiAoc2F0dXJhdGlvbiA8IDAuMDYpIHtcbiAgICBzYXR1cmF0aW9uID0gMC4wNjtcbiAgfVxuICByZXR1cm4gTnVtYmVyKHNhdHVyYXRpb24udG9GaXhlZCgyKSk7XG59XG5mdW5jdGlvbiBnZXRWYWx1ZShoc3YsIGksIGxpZ2h0KSB7XG4gIHZhciB2YWx1ZTtcbiAgaWYgKGxpZ2h0KSB7XG4gICAgdmFsdWUgPSBoc3YudiArIGJyaWdodG5lc3NTdGVwMSAqIGk7XG4gIH0gZWxzZSB7XG4gICAgdmFsdWUgPSBoc3YudiAtIGJyaWdodG5lc3NTdGVwMiAqIGk7XG4gIH1cbiAgaWYgKHZhbHVlID4gMSkge1xuICAgIHZhbHVlID0gMTtcbiAgfVxuICByZXR1cm4gTnVtYmVyKHZhbHVlLnRvRml4ZWQoMikpO1xufVxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2VuZXJhdGUoY29sb3IpIHtcbiAgdmFyIG9wdHMgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHt9O1xuICB2YXIgcGF0dGVybnMgPSBbXTtcbiAgdmFyIHBDb2xvciA9IGlucHV0VG9SR0IoY29sb3IpO1xuICBmb3IgKHZhciBpID0gbGlnaHRDb2xvckNvdW50OyBpID4gMDsgaSAtPSAxKSB7XG4gICAgdmFyIGhzdiA9IHRvSHN2KHBDb2xvcik7XG4gICAgdmFyIGNvbG9yU3RyaW5nID0gdG9IZXgoaW5wdXRUb1JHQih7XG4gICAgICBoOiBnZXRIdWUoaHN2LCBpLCB0cnVlKSxcbiAgICAgIHM6IGdldFNhdHVyYXRpb24oaHN2LCBpLCB0cnVlKSxcbiAgICAgIHY6IGdldFZhbHVlKGhzdiwgaSwgdHJ1ZSlcbiAgICB9KSk7XG4gICAgcGF0dGVybnMucHVzaChjb2xvclN0cmluZyk7XG4gIH1cbiAgcGF0dGVybnMucHVzaCh0b0hleChwQ29sb3IpKTtcbiAgZm9yICh2YXIgX2kgPSAxOyBfaSA8PSBkYXJrQ29sb3JDb3VudDsgX2kgKz0gMSkge1xuICAgIHZhciBfaHN2ID0gdG9Ic3YocENvbG9yKTtcbiAgICB2YXIgX2NvbG9yU3RyaW5nID0gdG9IZXgoaW5wdXRUb1JHQih7XG4gICAgICBoOiBnZXRIdWUoX2hzdiwgX2kpLFxuICAgICAgczogZ2V0U2F0dXJhdGlvbihfaHN2LCBfaSksXG4gICAgICB2OiBnZXRWYWx1ZShfaHN2LCBfaSlcbiAgICB9KSk7XG4gICAgcGF0dGVybnMucHVzaChfY29sb3JTdHJpbmcpO1xuICB9XG5cbiAgLy8gZGFyayB0aGVtZSBwYXR0ZXJuc1xuICBpZiAob3B0cy50aGVtZSA9PT0gJ2RhcmsnKSB7XG4gICAgcmV0dXJuIGRhcmtDb2xvck1hcC5tYXAoZnVuY3Rpb24gKF9yZWYzKSB7XG4gICAgICB2YXIgaW5kZXggPSBfcmVmMy5pbmRleCxcbiAgICAgICAgb3BhY2l0eSA9IF9yZWYzLm9wYWNpdHk7XG4gICAgICB2YXIgZGFya0NvbG9yU3RyaW5nID0gdG9IZXgobWl4KGlucHV0VG9SR0Iob3B0cy5iYWNrZ3JvdW5kQ29sb3IgfHwgJyMxNDE0MTQnKSwgaW5wdXRUb1JHQihwYXR0ZXJuc1tpbmRleF0pLCBvcGFjaXR5ICogMTAwKSk7XG4gICAgICByZXR1cm4gZGFya0NvbG9yU3RyaW5nO1xuICAgIH0pO1xuICB9XG4gIHJldHVybiBwYXR0ZXJucztcbn0iLCJpbXBvcnQgZ2VuZXJhdGUgZnJvbSBcIi4vZ2VuZXJhdGVcIjtcbnZhciBwcmVzZXRQcmltYXJ5Q29sb3JzID0ge1xuICByZWQ6ICcjRjUyMjJEJyxcbiAgdm9sY2FubzogJyNGQTU0MUMnLFxuICBvcmFuZ2U6ICcjRkE4QzE2JyxcbiAgZ29sZDogJyNGQUFEMTQnLFxuICB5ZWxsb3c6ICcjRkFEQjE0JyxcbiAgbGltZTogJyNBMEQ5MTEnLFxuICBncmVlbjogJyM1MkM0MUEnLFxuICBjeWFuOiAnIzEzQzJDMicsXG4gIGJsdWU6ICcjMTY3N0ZGJyxcbiAgZ2Vla2JsdWU6ICcjMkY1NEVCJyxcbiAgcHVycGxlOiAnIzcyMkVEMScsXG4gIG1hZ2VudGE6ICcjRUIyRjk2JyxcbiAgZ3JleTogJyM2NjY2NjYnXG59O1xudmFyIHByZXNldFBhbGV0dGVzID0ge307XG52YXIgcHJlc2V0RGFya1BhbGV0dGVzID0ge307XG5PYmplY3Qua2V5cyhwcmVzZXRQcmltYXJ5Q29sb3JzKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgcHJlc2V0UGFsZXR0ZXNba2V5XSA9IGdlbmVyYXRlKHByZXNldFByaW1hcnlDb2xvcnNba2V5XSk7XG4gIHByZXNldFBhbGV0dGVzW2tleV0ucHJpbWFyeSA9IHByZXNldFBhbGV0dGVzW2tleV1bNV07XG5cbiAgLy8gZGFyayBwcmVzZXRQYWxldHRlc1xuICBwcmVzZXREYXJrUGFsZXR0ZXNba2V5XSA9IGdlbmVyYXRlKHByZXNldFByaW1hcnlDb2xvcnNba2V5XSwge1xuICAgIHRoZW1lOiAnZGFyaycsXG4gICAgYmFja2dyb3VuZENvbG9yOiAnIzE0MTQxNCdcbiAgfSk7XG4gIHByZXNldERhcmtQYWxldHRlc1trZXldLnByaW1hcnkgPSBwcmVzZXREYXJrUGFsZXR0ZXNba2V5XVs1XTtcbn0pO1xudmFyIHJlZCA9IHByZXNldFBhbGV0dGVzLnJlZDtcbnZhciB2b2xjYW5vID0gcHJlc2V0UGFsZXR0ZXMudm9sY2FubztcbnZhciBnb2xkID0gcHJlc2V0UGFsZXR0ZXMuZ29sZDtcbnZhciBvcmFuZ2UgPSBwcmVzZXRQYWxldHRlcy5vcmFuZ2U7XG52YXIgeWVsbG93ID0gcHJlc2V0UGFsZXR0ZXMueWVsbG93O1xudmFyIGxpbWUgPSBwcmVzZXRQYWxldHRlcy5saW1lO1xudmFyIGdyZWVuID0gcHJlc2V0UGFsZXR0ZXMuZ3JlZW47XG52YXIgY3lhbiA9IHByZXNldFBhbGV0dGVzLmN5YW47XG52YXIgYmx1ZSA9IHByZXNldFBhbGV0dGVzLmJsdWU7XG52YXIgZ2Vla2JsdWUgPSBwcmVzZXRQYWxldHRlcy5nZWVrYmx1ZTtcbnZhciBwdXJwbGUgPSBwcmVzZXRQYWxldHRlcy5wdXJwbGU7XG52YXIgbWFnZW50YSA9IHByZXNldFBhbGV0dGVzLm1hZ2VudGE7XG52YXIgZ3JleSA9IHByZXNldFBhbGV0dGVzLmdyZXk7XG52YXIgZ3JheSA9IHByZXNldFBhbGV0dGVzLmdyZXk7XG5leHBvcnQgeyBnZW5lcmF0ZSwgcHJlc2V0UGFsZXR0ZXMsIHByZXNldERhcmtQYWxldHRlcywgcHJlc2V0UHJpbWFyeUNvbG9ycywgcmVkLCB2b2xjYW5vLCBvcmFuZ2UsIGdvbGQsIHllbGxvdywgbGltZSwgZ3JlZW4sIGN5YW4sIGJsdWUsIGdlZWtibHVlLCBwdXJwbGUsIG1hZ2VudGEsIGdyZXksIGdyYXkgfTsiLCJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xudmFyIEljb25Db250ZXh0ID0gLyojX19QVVJFX18qL2NyZWF0ZUNvbnRleHQoe30pO1xuZXhwb3J0IGRlZmF1bHQgSWNvbkNvbnRleHQ7IiwiaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZDJcIjtcbmltcG9ydCBfdHlwZW9mIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS90eXBlb2ZcIjtcbmltcG9ydCB7IGdlbmVyYXRlIGFzIGdlbmVyYXRlQ29sb3IgfSBmcm9tICdAYW50LWRlc2lnbi9jb2xvcnMnO1xuaW1wb3J0IHsgdXBkYXRlQ1NTIH0gZnJvbSBcInJjLXV0aWwvZXMvRG9tL2R5bmFtaWNDU1NcIjtcbmltcG9ydCB7IGdldFNoYWRvd1Jvb3QgfSBmcm9tIFwicmMtdXRpbC9lcy9Eb20vc2hhZG93XCI7XG5pbXBvcnQgd2FybiBmcm9tIFwicmMtdXRpbC9lcy93YXJuaW5nXCI7XG5pbXBvcnQgUmVhY3QsIHsgdXNlQ29udGV4dCwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEljb25Db250ZXh0IGZyb20gXCIuL2NvbXBvbmVudHMvQ29udGV4dFwiO1xuZnVuY3Rpb24gY2FtZWxDYXNlKGlucHV0KSB7XG4gIHJldHVybiBpbnB1dC5yZXBsYWNlKC8tKC4pL2csIGZ1bmN0aW9uIChtYXRjaCwgZykge1xuICAgIHJldHVybiBnLnRvVXBwZXJDYXNlKCk7XG4gIH0pO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHdhcm5pbmcodmFsaWQsIG1lc3NhZ2UpIHtcbiAgd2Fybih2YWxpZCwgXCJbQGFudC1kZXNpZ24vaWNvbnNdIFwiLmNvbmNhdChtZXNzYWdlKSk7XG59XG5leHBvcnQgZnVuY3Rpb24gaXNJY29uRGVmaW5pdGlvbih0YXJnZXQpIHtcbiAgcmV0dXJuIF90eXBlb2YodGFyZ2V0KSA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIHRhcmdldC5uYW1lID09PSAnc3RyaW5nJyAmJiB0eXBlb2YgdGFyZ2V0LnRoZW1lID09PSAnc3RyaW5nJyAmJiAoX3R5cGVvZih0YXJnZXQuaWNvbikgPT09ICdvYmplY3QnIHx8IHR5cGVvZiB0YXJnZXQuaWNvbiA9PT0gJ2Z1bmN0aW9uJyk7XG59XG5leHBvcnQgZnVuY3Rpb24gbm9ybWFsaXplQXR0cnMoKSB7XG4gIHZhciBhdHRycyA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDoge307XG4gIHJldHVybiBPYmplY3Qua2V5cyhhdHRycykucmVkdWNlKGZ1bmN0aW9uIChhY2MsIGtleSkge1xuICAgIHZhciB2YWwgPSBhdHRyc1trZXldO1xuICAgIHN3aXRjaCAoa2V5KSB7XG4gICAgICBjYXNlICdjbGFzcyc6XG4gICAgICAgIGFjYy5jbGFzc05hbWUgPSB2YWw7XG4gICAgICAgIGRlbGV0ZSBhY2MuY2xhc3M7XG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgZGVsZXRlIGFjY1trZXldO1xuICAgICAgICBhY2NbY2FtZWxDYXNlKGtleSldID0gdmFsO1xuICAgIH1cbiAgICByZXR1cm4gYWNjO1xuICB9LCB7fSk7XG59XG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGUobm9kZSwga2V5LCByb290UHJvcHMpIHtcbiAgaWYgKCFyb290UHJvcHMpIHtcbiAgICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQobm9kZS50YWcsIF9vYmplY3RTcHJlYWQoe1xuICAgICAga2V5OiBrZXlcbiAgICB9LCBub3JtYWxpemVBdHRycyhub2RlLmF0dHJzKSksIChub2RlLmNoaWxkcmVuIHx8IFtdKS5tYXAoZnVuY3Rpb24gKGNoaWxkLCBpbmRleCkge1xuICAgICAgcmV0dXJuIGdlbmVyYXRlKGNoaWxkLCBcIlwiLmNvbmNhdChrZXksIFwiLVwiKS5jb25jYXQobm9kZS50YWcsIFwiLVwiKS5jb25jYXQoaW5kZXgpKTtcbiAgICB9KSk7XG4gIH1cbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KG5vZGUudGFnLCBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe1xuICAgIGtleToga2V5XG4gIH0sIG5vcm1hbGl6ZUF0dHJzKG5vZGUuYXR0cnMpKSwgcm9vdFByb3BzKSwgKG5vZGUuY2hpbGRyZW4gfHwgW10pLm1hcChmdW5jdGlvbiAoY2hpbGQsIGluZGV4KSB7XG4gICAgcmV0dXJuIGdlbmVyYXRlKGNoaWxkLCBcIlwiLmNvbmNhdChrZXksIFwiLVwiKS5jb25jYXQobm9kZS50YWcsIFwiLVwiKS5jb25jYXQoaW5kZXgpKTtcbiAgfSkpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGdldFNlY29uZGFyeUNvbG9yKHByaW1hcnlDb2xvcikge1xuICAvLyBjaG9vc2UgdGhlIHNlY29uZCBjb2xvclxuICByZXR1cm4gZ2VuZXJhdGVDb2xvcihwcmltYXJ5Q29sb3IpWzBdO1xufVxuZXhwb3J0IGZ1bmN0aW9uIG5vcm1hbGl6ZVR3b1RvbmVDb2xvcnModHdvVG9uZUNvbG9yKSB7XG4gIGlmICghdHdvVG9uZUNvbG9yKSB7XG4gICAgcmV0dXJuIFtdO1xuICB9XG4gIHJldHVybiBBcnJheS5pc0FycmF5KHR3b1RvbmVDb2xvcikgPyB0d29Ub25lQ29sb3IgOiBbdHdvVG9uZUNvbG9yXTtcbn1cblxuLy8gVGhlc2UgcHJvcHMgbWFrZSBzdXJlIHRoYXQgdGhlIFNWRyBiZWhhdmlvdXJzIGxpa2UgZ2VuZXJhbCB0ZXh0LlxuLy8gUmVmZXJlbmNlOiBodHRwczovL2Jsb2cucHJvdG90eXByLmlvL2FsaWduLXN2Zy1pY29ucy10by10ZXh0LWFuZC1zYXktZ29vZGJ5ZS10by1mb250LWljb25zLWQ0NGIzZDdiMjZiNFxuZXhwb3J0IHZhciBzdmdCYXNlUHJvcHMgPSB7XG4gIHdpZHRoOiAnMWVtJyxcbiAgaGVpZ2h0OiAnMWVtJyxcbiAgZmlsbDogJ2N1cnJlbnRDb2xvcicsXG4gICdhcmlhLWhpZGRlbic6ICd0cnVlJyxcbiAgZm9jdXNhYmxlOiAnZmFsc2UnXG59O1xuZXhwb3J0IHZhciBpY29uU3R5bGVzID0gXCJcXG4uYW50aWNvbiB7XFxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XFxuICBjb2xvcjogaW5oZXJpdDtcXG4gIGZvbnQtc3R5bGU6IG5vcm1hbDtcXG4gIGxpbmUtaGVpZ2h0OiAwO1xcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xcbiAgdGV4dC10cmFuc2Zvcm06IG5vbmU7XFxuICB2ZXJ0aWNhbC1hbGlnbjogLTAuMTI1ZW07XFxuICB0ZXh0LXJlbmRlcmluZzogb3B0aW1pemVMZWdpYmlsaXR5O1xcbiAgLXdlYmtpdC1mb250LXNtb290aGluZzogYW50aWFsaWFzZWQ7XFxuICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlO1xcbn1cXG5cXG4uYW50aWNvbiA+ICoge1xcbiAgbGluZS1oZWlnaHQ6IDE7XFxufVxcblxcbi5hbnRpY29uIHN2ZyB7XFxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XFxufVxcblxcbi5hbnRpY29uOjpiZWZvcmUge1xcbiAgZGlzcGxheTogbm9uZTtcXG59XFxuXFxuLmFudGljb24gLmFudGljb24taWNvbiB7XFxuICBkaXNwbGF5OiBibG9jaztcXG59XFxuXFxuLmFudGljb25bdGFiaW5kZXhdIHtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG59XFxuXFxuLmFudGljb24tc3Bpbjo6YmVmb3JlLFxcbi5hbnRpY29uLXNwaW4ge1xcbiAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xcbiAgLXdlYmtpdC1hbmltYXRpb246IGxvYWRpbmdDaXJjbGUgMXMgaW5maW5pdGUgbGluZWFyO1xcbiAgYW5pbWF0aW9uOiBsb2FkaW5nQ2lyY2xlIDFzIGluZmluaXRlIGxpbmVhcjtcXG59XFxuXFxuQC13ZWJraXQta2V5ZnJhbWVzIGxvYWRpbmdDaXJjbGUge1xcbiAgMTAwJSB7XFxuICAgIC13ZWJraXQtdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBsb2FkaW5nQ2lyY2xlIHtcXG4gIDEwMCUge1xcbiAgICAtd2Via2l0LXRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XFxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XFxuICB9XFxufVxcblwiO1xuZXhwb3J0IHZhciB1c2VJbnNlcnRTdHlsZXMgPSBmdW5jdGlvbiB1c2VJbnNlcnRTdHlsZXMoZWxlUmVmKSB7XG4gIHZhciBfdXNlQ29udGV4dCA9IHVzZUNvbnRleHQoSWNvbkNvbnRleHQpLFxuICAgIGNzcCA9IF91c2VDb250ZXh0LmNzcCxcbiAgICBwcmVmaXhDbHMgPSBfdXNlQ29udGV4dC5wcmVmaXhDbHM7XG4gIHZhciBtZXJnZWRTdHlsZVN0ciA9IGljb25TdHlsZXM7XG4gIGlmIChwcmVmaXhDbHMpIHtcbiAgICBtZXJnZWRTdHlsZVN0ciA9IG1lcmdlZFN0eWxlU3RyLnJlcGxhY2UoL2FudGljb24vZywgcHJlZml4Q2xzKTtcbiAgfVxuICB1c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHZhciBlbGUgPSBlbGVSZWYuY3VycmVudDtcbiAgICB2YXIgc2hhZG93Um9vdCA9IGdldFNoYWRvd1Jvb3QoZWxlKTtcbiAgICB1cGRhdGVDU1MobWVyZ2VkU3R5bGVTdHIsICdAYW50LWRlc2lnbi1pY29ucycsIHtcbiAgICAgIHByZXBlbmQ6IHRydWUsXG4gICAgICBjc3A6IGNzcCxcbiAgICAgIGF0dGFjaFRvOiBzaGFkb3dSb290XG4gICAgfSk7XG4gIH0sIFtdKTtcbn07IiwiaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbmltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG52YXIgX2V4Y2x1ZGVkID0gW1wiaWNvblwiLCBcImNsYXNzTmFtZVwiLCBcIm9uQ2xpY2tcIiwgXCJzdHlsZVwiLCBcInByaW1hcnlDb2xvclwiLCBcInNlY29uZGFyeUNvbG9yXCJdO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2VuZXJhdGUsIGdldFNlY29uZGFyeUNvbG9yLCBpc0ljb25EZWZpbml0aW9uLCB3YXJuaW5nLCB1c2VJbnNlcnRTdHlsZXMgfSBmcm9tIFwiLi4vdXRpbHNcIjtcbnZhciB0d29Ub25lQ29sb3JQYWxldHRlID0ge1xuICBwcmltYXJ5Q29sb3I6ICcjMzMzJyxcbiAgc2Vjb25kYXJ5Q29sb3I6ICcjRTZFNkU2JyxcbiAgY2FsY3VsYXRlZDogZmFsc2Vcbn07XG5mdW5jdGlvbiBzZXRUd29Ub25lQ29sb3JzKF9yZWYpIHtcbiAgdmFyIHByaW1hcnlDb2xvciA9IF9yZWYucHJpbWFyeUNvbG9yLFxuICAgIHNlY29uZGFyeUNvbG9yID0gX3JlZi5zZWNvbmRhcnlDb2xvcjtcbiAgdHdvVG9uZUNvbG9yUGFsZXR0ZS5wcmltYXJ5Q29sb3IgPSBwcmltYXJ5Q29sb3I7XG4gIHR3b1RvbmVDb2xvclBhbGV0dGUuc2Vjb25kYXJ5Q29sb3IgPSBzZWNvbmRhcnlDb2xvciB8fCBnZXRTZWNvbmRhcnlDb2xvcihwcmltYXJ5Q29sb3IpO1xuICB0d29Ub25lQ29sb3JQYWxldHRlLmNhbGN1bGF0ZWQgPSAhIXNlY29uZGFyeUNvbG9yO1xufVxuZnVuY3Rpb24gZ2V0VHdvVG9uZUNvbG9ycygpIHtcbiAgcmV0dXJuIF9vYmplY3RTcHJlYWQoe30sIHR3b1RvbmVDb2xvclBhbGV0dGUpO1xufVxudmFyIEljb25CYXNlID0gZnVuY3Rpb24gSWNvbkJhc2UocHJvcHMpIHtcbiAgdmFyIGljb24gPSBwcm9wcy5pY29uLFxuICAgIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICBvbkNsaWNrID0gcHJvcHMub25DbGljayxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIHByaW1hcnlDb2xvciA9IHByb3BzLnByaW1hcnlDb2xvcixcbiAgICBzZWNvbmRhcnlDb2xvciA9IHByb3BzLnNlY29uZGFyeUNvbG9yLFxuICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgdmFyIHN2Z1JlZiA9IFJlYWN0LnVzZVJlZigpO1xuICB2YXIgY29sb3JzID0gdHdvVG9uZUNvbG9yUGFsZXR0ZTtcbiAgaWYgKHByaW1hcnlDb2xvcikge1xuICAgIGNvbG9ycyA9IHtcbiAgICAgIHByaW1hcnlDb2xvcjogcHJpbWFyeUNvbG9yLFxuICAgICAgc2Vjb25kYXJ5Q29sb3I6IHNlY29uZGFyeUNvbG9yIHx8IGdldFNlY29uZGFyeUNvbG9yKHByaW1hcnlDb2xvcilcbiAgICB9O1xuICB9XG4gIHVzZUluc2VydFN0eWxlcyhzdmdSZWYpO1xuICB3YXJuaW5nKGlzSWNvbkRlZmluaXRpb24oaWNvbiksIFwiaWNvbiBzaG91bGQgYmUgaWNvbiBkZWZpbml0b24sIGJ1dCBnb3QgXCIuY29uY2F0KGljb24pKTtcbiAgaWYgKCFpc0ljb25EZWZpbml0aW9uKGljb24pKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgdmFyIHRhcmdldCA9IGljb247XG4gIGlmICh0YXJnZXQgJiYgdHlwZW9mIHRhcmdldC5pY29uID09PSAnZnVuY3Rpb24nKSB7XG4gICAgdGFyZ2V0ID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCB0YXJnZXQpLCB7fSwge1xuICAgICAgaWNvbjogdGFyZ2V0Lmljb24oY29sb3JzLnByaW1hcnlDb2xvciwgY29sb3JzLnNlY29uZGFyeUNvbG9yKVxuICAgIH0pO1xuICB9XG4gIHJldHVybiBnZW5lcmF0ZSh0YXJnZXQuaWNvbiwgXCJzdmctXCIuY29uY2F0KHRhcmdldC5uYW1lKSwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHtcbiAgICBjbGFzc05hbWU6IGNsYXNzTmFtZSxcbiAgICBvbkNsaWNrOiBvbkNsaWNrLFxuICAgIHN0eWxlOiBzdHlsZSxcbiAgICAnZGF0YS1pY29uJzogdGFyZ2V0Lm5hbWUsXG4gICAgd2lkdGg6ICcxZW0nLFxuICAgIGhlaWdodDogJzFlbScsXG4gICAgZmlsbDogJ2N1cnJlbnRDb2xvcicsXG4gICAgJ2FyaWEtaGlkZGVuJzogJ3RydWUnXG4gIH0sIHJlc3RQcm9wcyksIHt9LCB7XG4gICAgcmVmOiBzdmdSZWZcbiAgfSkpO1xufTtcbkljb25CYXNlLmRpc3BsYXlOYW1lID0gJ0ljb25SZWFjdCc7XG5JY29uQmFzZS5nZXRUd29Ub25lQ29sb3JzID0gZ2V0VHdvVG9uZUNvbG9ycztcbkljb25CYXNlLnNldFR3b1RvbmVDb2xvcnMgPSBzZXRUd29Ub25lQ29sb3JzO1xuZXhwb3J0IGRlZmF1bHQgSWNvbkJhc2U7IiwiaW1wb3J0IF9zbGljZWRUb0FycmF5IGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9zbGljZWRUb0FycmF5XCI7XG5pbXBvcnQgUmVhY3RJY29uIGZyb20gXCIuL0ljb25CYXNlXCI7XG5pbXBvcnQgeyBub3JtYWxpemVUd29Ub25lQ29sb3JzIH0gZnJvbSBcIi4uL3V0aWxzXCI7XG5leHBvcnQgZnVuY3Rpb24gc2V0VHdvVG9uZUNvbG9yKHR3b1RvbmVDb2xvcikge1xuICB2YXIgX25vcm1hbGl6ZVR3b1RvbmVDb2xvID0gbm9ybWFsaXplVHdvVG9uZUNvbG9ycyh0d29Ub25lQ29sb3IpLFxuICAgIF9ub3JtYWxpemVUd29Ub25lQ29sbzIgPSBfc2xpY2VkVG9BcnJheShfbm9ybWFsaXplVHdvVG9uZUNvbG8sIDIpLFxuICAgIHByaW1hcnlDb2xvciA9IF9ub3JtYWxpemVUd29Ub25lQ29sbzJbMF0sXG4gICAgc2Vjb25kYXJ5Q29sb3IgPSBfbm9ybWFsaXplVHdvVG9uZUNvbG8yWzFdO1xuICByZXR1cm4gUmVhY3RJY29uLnNldFR3b1RvbmVDb2xvcnMoe1xuICAgIHByaW1hcnlDb2xvcjogcHJpbWFyeUNvbG9yLFxuICAgIHNlY29uZGFyeUNvbG9yOiBzZWNvbmRhcnlDb2xvclxuICB9KTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRUd29Ub25lQ29sb3IoKSB7XG4gIHZhciBjb2xvcnMgPSBSZWFjdEljb24uZ2V0VHdvVG9uZUNvbG9ycygpO1xuICBpZiAoIWNvbG9ycy5jYWxjdWxhdGVkKSB7XG4gICAgcmV0dXJuIGNvbG9ycy5wcmltYXJ5Q29sb3I7XG4gIH1cbiAgcmV0dXJuIFtjb2xvcnMucHJpbWFyeUNvbG9yLCBjb2xvcnMuc2Vjb25kYXJ5Q29sb3JdO1xufSIsIid1c2UgY2xpZW50JztcblxuaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbmltcG9ydCBfZGVmaW5lUHJvcGVydHkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5XCI7XG5pbXBvcnQgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RXaXRob3V0UHJvcGVydGllc1wiO1xudmFyIF9leGNsdWRlZCA9IFtcImNsYXNzTmFtZVwiLCBcImljb25cIiwgXCJzcGluXCIsIFwicm90YXRlXCIsIFwidGFiSW5kZXhcIiwgXCJvbkNsaWNrXCIsIFwidHdvVG9uZUNvbG9yXCJdO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IGNsYXNzTmFtZXMgZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgeyBibHVlIH0gZnJvbSAnQGFudC1kZXNpZ24vY29sb3JzJztcbmltcG9ydCBDb250ZXh0IGZyb20gXCIuL0NvbnRleHRcIjtcbmltcG9ydCBSZWFjdEljb24gZnJvbSBcIi4vSWNvbkJhc2VcIjtcbmltcG9ydCB7IGdldFR3b1RvbmVDb2xvciwgc2V0VHdvVG9uZUNvbG9yIH0gZnJvbSBcIi4vdHdvVG9uZVByaW1hcnlDb2xvclwiO1xuaW1wb3J0IHsgbm9ybWFsaXplVHdvVG9uZUNvbG9ycyB9IGZyb20gXCIuLi91dGlsc1wiO1xuLy8gSW5pdGlhbCBzZXR0aW5nXG4vLyBzaG91bGQgbW92ZSBpdCB0byBhbnRkIG1haW4gcmVwbz9cbnNldFR3b1RvbmVDb2xvcihibHVlLnByaW1hcnkpO1xuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vRGVmaW5pdGVseVR5cGVkL0RlZmluaXRlbHlUeXBlZC9pc3N1ZXMvMzQ3NTcjaXNzdWVjb21tZW50LTQ4ODg0ODcyMFxuXG52YXIgSWNvbiA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBjbGFzc05hbWUgPSBwcm9wcy5jbGFzc05hbWUsXG4gICAgaWNvbiA9IHByb3BzLmljb24sXG4gICAgc3BpbiA9IHByb3BzLnNwaW4sXG4gICAgcm90YXRlID0gcHJvcHMucm90YXRlLFxuICAgIHRhYkluZGV4ID0gcHJvcHMudGFiSW5kZXgsXG4gICAgb25DbGljayA9IHByb3BzLm9uQ2xpY2ssXG4gICAgdHdvVG9uZUNvbG9yID0gcHJvcHMudHdvVG9uZUNvbG9yLFxuICAgIHJlc3RQcm9wcyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhwcm9wcywgX2V4Y2x1ZGVkKTtcbiAgdmFyIF9SZWFjdCR1c2VDb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChDb250ZXh0KSxcbiAgICBfUmVhY3QkdXNlQ29udGV4dCRwcmUgPSBfUmVhY3QkdXNlQ29udGV4dC5wcmVmaXhDbHMsXG4gICAgcHJlZml4Q2xzID0gX1JlYWN0JHVzZUNvbnRleHQkcHJlID09PSB2b2lkIDAgPyAnYW50aWNvbicgOiBfUmVhY3QkdXNlQ29udGV4dCRwcmUsXG4gICAgcm9vdENsYXNzTmFtZSA9IF9SZWFjdCR1c2VDb250ZXh0LnJvb3RDbGFzc05hbWU7XG4gIHZhciBjbGFzc1N0cmluZyA9IGNsYXNzTmFtZXMocm9vdENsYXNzTmFtZSwgcHJlZml4Q2xzLCBfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLVwiKS5jb25jYXQoaWNvbi5uYW1lKSwgISFpY29uLm5hbWUpLCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXNwaW5cIiksICEhc3BpbiB8fCBpY29uLm5hbWUgPT09ICdsb2FkaW5nJyksIGNsYXNzTmFtZSk7XG4gIHZhciBpY29uVGFiSW5kZXggPSB0YWJJbmRleDtcbiAgaWYgKGljb25UYWJJbmRleCA9PT0gdW5kZWZpbmVkICYmIG9uQ2xpY2spIHtcbiAgICBpY29uVGFiSW5kZXggPSAtMTtcbiAgfVxuICB2YXIgc3ZnU3R5bGUgPSByb3RhdGUgPyB7XG4gICAgbXNUcmFuc2Zvcm06IFwicm90YXRlKFwiLmNvbmNhdChyb3RhdGUsIFwiZGVnKVwiKSxcbiAgICB0cmFuc2Zvcm06IFwicm90YXRlKFwiLmNvbmNhdChyb3RhdGUsIFwiZGVnKVwiKVxuICB9IDogdW5kZWZpbmVkO1xuICB2YXIgX25vcm1hbGl6ZVR3b1RvbmVDb2xvID0gbm9ybWFsaXplVHdvVG9uZUNvbG9ycyh0d29Ub25lQ29sb3IpLFxuICAgIF9ub3JtYWxpemVUd29Ub25lQ29sbzIgPSBfc2xpY2VkVG9BcnJheShfbm9ybWFsaXplVHdvVG9uZUNvbG8sIDIpLFxuICAgIHByaW1hcnlDb2xvciA9IF9ub3JtYWxpemVUd29Ub25lQ29sbzJbMF0sXG4gICAgc2Vjb25kYXJ5Q29sb3IgPSBfbm9ybWFsaXplVHdvVG9uZUNvbG8yWzFdO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIsIF9leHRlbmRzKHtcbiAgICByb2xlOiBcImltZ1wiLFxuICAgIFwiYXJpYS1sYWJlbFwiOiBpY29uLm5hbWVcbiAgfSwgcmVzdFByb3BzLCB7XG4gICAgcmVmOiByZWYsXG4gICAgdGFiSW5kZXg6IGljb25UYWJJbmRleCxcbiAgICBvbkNsaWNrOiBvbkNsaWNrLFxuICAgIGNsYXNzTmFtZTogY2xhc3NTdHJpbmdcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0SWNvbiwge1xuICAgIGljb246IGljb24sXG4gICAgcHJpbWFyeUNvbG9yOiBwcmltYXJ5Q29sb3IsXG4gICAgc2Vjb25kYXJ5Q29sb3I6IHNlY29uZGFyeUNvbG9yLFxuICAgIHN0eWxlOiBzdmdTdHlsZVxuICB9KSk7XG59KTtcbkljb24uZGlzcGxheU5hbWUgPSAnQW50ZEljb24nO1xuSWNvbi5nZXRUd29Ub25lQ29sb3IgPSBnZXRUd29Ub25lQ29sb3I7XG5JY29uLnNldFR3b1RvbmVDb2xvciA9IHNldFR3b1RvbmVDb2xvcjtcbmV4cG9ydCBkZWZhdWx0IEljb247Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///46976
`)},73177:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   X: function() { return /* binding */ openVisibleCompatible; },
/* harmony export */   b: function() { return /* binding */ getVersion; }
/* harmony export */ });
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67159);
/* harmony import */ var _omitUndefined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(51812);
/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1977);
/* provided dependency */ var process = __webpack_require__(34155);



var getVersion = function getVersion() {
  var _process;
  if (typeof process === 'undefined') return antd__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z;
  return ((_process = process) === null || process === void 0 || (process = ({"NODE_ENV":"production","PUBLIC_PATH":"/"})) === null || process === void 0 ? void 0 : process.ANTD_VERSION) || antd__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z;
};
var openVisibleCompatible = function openVisibleCompatible(open, onOpenChange) {
  var props = (0,_index__WEBPACK_IMPORTED_MODULE_1__/* .compareVersions */ .n)(getVersion(), '4.23.0') > -1 ? {
    open: open,
    onOpenChange: onOpenChange
  } : {
    visible: open,
    onVisibleChange: onOpenChange
  };
  return (0,_omitUndefined__WEBPACK_IMPORTED_MODULE_2__/* .omitUndefined */ .Y)(props);
};
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzMxNzcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0I7QUFDa0I7QUFDUDtBQUNuQztBQUNQO0FBQ0EsYUFBYSxPQUFPLHlCQUF5QixxREFBTztBQUNwRCwyQ0FBMkMsT0FBUSxnQkFBZ0IsT0FBUSxHQUFHLDZDQUFZLGNBQWMsT0FBUSx1QkFBdUIsT0FBUSxrQkFBa0IscURBQU87QUFDeEs7QUFDQTtBQUNBLGNBQWMsZ0VBQWU7QUFDN0I7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxTQUFTLHNFQUFhO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLXV0aWxzL2VzL2NvbXBhcmVWZXJzaW9ucy9vcGVuVmlzaWJsZUNvbXBhdGlibGUuanM/OWFmZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgeyBvbWl0VW5kZWZpbmVkIH0gZnJvbSBcIi4uL29taXRVbmRlZmluZWRcIjtcbmltcG9ydCB7IGNvbXBhcmVWZXJzaW9ucyB9IGZyb20gXCIuL2luZGV4XCI7XG5leHBvcnQgdmFyIGdldFZlcnNpb24gPSBmdW5jdGlvbiBnZXRWZXJzaW9uKCkge1xuICB2YXIgX3Byb2Nlc3M7XG4gIGlmICh0eXBlb2YgcHJvY2VzcyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB2ZXJzaW9uO1xuICByZXR1cm4gKChfcHJvY2VzcyA9IHByb2Nlc3MpID09PSBudWxsIHx8IF9wcm9jZXNzID09PSB2b2lkIDAgfHwgKF9wcm9jZXNzID0gX3Byb2Nlc3MuZW52KSA9PT0gbnVsbCB8fCBfcHJvY2VzcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3Byb2Nlc3MuQU5URF9WRVJTSU9OKSB8fCB2ZXJzaW9uO1xufTtcbnZhciBvcGVuVmlzaWJsZUNvbXBhdGlibGUgPSBmdW5jdGlvbiBvcGVuVmlzaWJsZUNvbXBhdGlibGUob3Blbiwgb25PcGVuQ2hhbmdlKSB7XG4gIHZhciBwcm9wcyA9IGNvbXBhcmVWZXJzaW9ucyhnZXRWZXJzaW9uKCksICc0LjIzLjAnKSA+IC0xID8ge1xuICAgIG9wZW46IG9wZW4sXG4gICAgb25PcGVuQ2hhbmdlOiBvbk9wZW5DaGFuZ2VcbiAgfSA6IHtcbiAgICB2aXNpYmxlOiBvcGVuLFxuICAgIG9uVmlzaWJsZUNoYW5nZTogb25PcGVuQ2hhbmdlXG4gIH07XG4gIHJldHVybiBvbWl0VW5kZWZpbmVkKHByb3BzKTtcbn07XG5leHBvcnQgeyBvcGVuVmlzaWJsZUNvbXBhdGlibGUgfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///73177
`)},98912:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Q: function() { return /* binding */ FieldLabel; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/CloseCircleFilled.js
var asn_CloseCircleFilled = __webpack_require__(1085);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/components/AntdIcon.js + 6 modules
var AntdIcon = __webpack_require__(62914);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var CloseCircleFilled = function CloseCircleFilled(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_CloseCircleFilled/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_CloseCircleFilled = (/*#__PURE__*/react.forwardRef(CloseCircleFilled));
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/DownOutlined.js
var asn_DownOutlined = __webpack_require__(66023);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/icons/DownOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DownOutlined = function DownOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_DownOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_DownOutlined = (/*#__PURE__*/react.forwardRef(DownOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/index.js + 7 modules
var es = __webpack_require__(89451);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/FieldLabel/style.js



var genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)({}, token.componentCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    display: 'inline-flex',
    gap: token.marginXXS,
    alignItems: 'center',
    height: '30px',
    paddingBlock: 0,
    paddingInline: 8,
    fontSize: token.fontSize,
    lineHeight: '30px',
    borderRadius: '2px',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: token.colorBgTextHover
    },
    '&-active': (0,defineProperty/* default */.Z)({
      paddingBlock: 0,
      paddingInline: 8,
      backgroundColor: token.colorBgTextHover
    }, "&".concat(token.componentCls, "-allow-clear:hover:not(").concat(token.componentCls, "-disabled)"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-arrow"), {
      display: 'none'
    }), "".concat(token.componentCls, "-close"), {
      display: 'inline-flex'
    }))
  }, "".concat(token.antCls, "-select"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-select-clear"), {
    borderRadius: '50%'
  })), "".concat(token.antCls, "-picker"), (0,defineProperty/* default */.Z)({}, "".concat(token.antCls, "-picker-clear"), {
    borderRadius: '50%'
  })), '&-icon', (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    color: token.colorIcon,
    transition: 'color 0.3s',
    fontSize: 12,
    verticalAlign: 'middle'
  }, "&".concat(token.componentCls, "-close"), {
    display: 'none',
    fontSize: 12,
    alignItems: 'center',
    justifyContent: 'center',
    color: token.colorTextPlaceholder,
    borderRadius: '50%'
  }), '&:hover', {
    color: token.colorIconHover
  })), '&-disabled', (0,defineProperty/* default */.Z)({
    color: token.colorTextPlaceholder,
    cursor: 'not-allowed'
  }, "".concat(token.componentCls, "-icon"), {
    color: token.colorTextPlaceholder
  })), '&-small', (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({
    height: '24px',
    paddingBlock: 0,
    paddingInline: 4,
    fontSize: token.fontSizeSM,
    lineHeight: '24px'
  }, "&".concat(token.componentCls, "-active"), {
    paddingBlock: 0,
    paddingInline: 8
  }), "".concat(token.componentCls, "-icon"), {
    paddingBlock: 0,
    paddingInline: 0
  }), "".concat(token.componentCls, "-close"), {
    marginBlockStart: '-2px',
    paddingBlock: 4,
    paddingInline: 4,
    fontSize: '6px'
  })), '&-bordered', {
    height: '32px',
    paddingBlock: 0,
    paddingInline: 8,
    border: "".concat(token.lineWidth, "px solid ").concat(token.colorBorder),
    borderRadius: '@border-radius-base'
  }), '&-bordered&-small', {
    height: '24px',
    paddingBlock: 0,
    paddingInline: 8
  }), '&-bordered&-active', {
    backgroundColor: token.colorBgContainer
  }));
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('FieldLabel', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/FieldLabel/index.js









var FieldLabelFunction = function FieldLabelFunction(props, ref) {
  var _ConfigProvider$useCo, _ref2, _props$size;
  var label = props.label,
    onClear = props.onClear,
    value = props.value,
    disabled = props.disabled,
    onLabelClick = props.onLabelClick,
    ellipsis = props.ellipsis,
    placeholder = props.placeholder,
    className = props.className,
    formatter = props.formatter,
    bordered = props.bordered,
    style = props.style,
    downIcon = props.downIcon,
    _props$allowClear = props.allowClear,
    allowClear = _props$allowClear === void 0 ? true : _props$allowClear,
    _props$valueMaxLength = props.valueMaxLength,
    valueMaxLength = _props$valueMaxLength === void 0 ? 41 : _props$valueMaxLength;
  var _ref = (config_provider/* default */.ZP === null || config_provider/* default */.ZP === void 0 || (_ConfigProvider$useCo = config_provider/* default */.ZP.useConfig) === null || _ConfigProvider$useCo === void 0 ? void 0 : _ConfigProvider$useCo.call(config_provider/* default */.ZP)) || {
      componentSize: 'middle'
    },
    componentSize = _ref.componentSize;
  var size = componentSize;
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls('pro-core-field-label');
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var intl = (0,es/* useIntl */.YB)();
  var clearRef = (0,react.useRef)(null);
  var labelRef = (0,react.useRef)(null);
  (0,react.useImperativeHandle)(ref, function () {
    return {
      labelRef: labelRef,
      clearRef: clearRef
    };
  });
  var wrapElements = function wrapElements(array) {
    if (array.every(function (item) {
      return typeof item === 'string';
    })) return array.join(',');
    return array.map(function (item, index) {
      var comma = index === array.length - 1 ? '' : ',';
      if (typeof item === 'string') {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          children: [item, comma]
        }, index);
      }
      return /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
        style: {
          display: 'flex'
        },
        children: [item, comma]
      }, index);
    });
  };
  var formatterText = function formatterText(aValue) {
    if (formatter) {
      return formatter(aValue);
    }
    return Array.isArray(aValue) ? wrapElements(aValue) : aValue;
  };
  var getTextByValue = function getTextByValue(aLabel, aValue) {
    if (aValue !== undefined && aValue !== null && aValue !== '' && (!Array.isArray(aValue) || aValue.length)) {
      var _str$toString, _str$toString$substr;
      var prefix = aLabel ? /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
        onClick: function onClick() {
          onLabelClick === null || onLabelClick === void 0 || onLabelClick();
        },
        className: "".concat(prefixCls, "-text"),
        children: [aLabel, ': ']
      }) : '';
      var str = formatterText(aValue);
      if (!ellipsis) {
        return /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
          style: {
            display: 'inline-flex',
            alignItems: 'center'
          },
          children: [prefix, formatterText(aValue)]
        });
      }
      var getText = function getText() {
        var isArrayValue = Array.isArray(aValue) && aValue.length > 1;
        var unitText = intl.getMessage('form.lightFilter.itemUnit', '\u9879');
        if (typeof str === 'string' && str.length > valueMaxLength && isArrayValue) {
          return "...".concat(aValue.length).concat(unitText);
        }
        return '';
      };
      var tail = getText();
      return /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
        title: typeof str === 'string' ? str : undefined,
        style: {
          display: 'inline-flex',
          alignItems: 'center'
        },
        children: [prefix, /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
          style: {
            paddingInlineStart: 4,
            display: 'flex'
          },
          children: typeof str === 'string' ? str === null || str === void 0 || (_str$toString = str.toString()) === null || _str$toString === void 0 || (_str$toString$substr = _str$toString.substr) === null || _str$toString$substr === void 0 ? void 0 : _str$toString$substr.call(_str$toString, 0, valueMaxLength) : str
        }), tail]
      });
    }
    return aLabel || placeholder;
  };
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsxs)("span", {
    className: classnames_default()(prefixCls, hashId, "".concat(prefixCls, "-").concat((_ref2 = (_props$size = props.size) !== null && _props$size !== void 0 ? _props$size : size) !== null && _ref2 !== void 0 ? _ref2 : 'middle'), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-active"), !!value || value === 0), "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-bordered"), bordered), "".concat(prefixCls, "-allow-clear"), allowClear), className),
    style: style,
    ref: labelRef,
    onClick: function onClick() {
      var _props$onClick;
      props === null || props === void 0 || (_props$onClick = props.onClick) === null || _props$onClick === void 0 || _props$onClick.call(props);
    },
    children: [getTextByValue(label, value), (value || value === 0) && allowClear && /*#__PURE__*/(0,jsx_runtime.jsx)(icons_CloseCircleFilled, {
      role: "button",
      title: intl.getMessage('form.lightFilter.clear', '\u6E05\u9664'),
      className: classnames_default()("".concat(prefixCls, "-icon"), hashId, "".concat(prefixCls, "-close")),
      onClick: function onClick(e) {
        if (!disabled) onClear === null || onClear === void 0 || onClear();
        e.stopPropagation();
      },
      ref: clearRef
    }), downIcon !== false ? downIcon !== null && downIcon !== void 0 ? downIcon : /*#__PURE__*/(0,jsx_runtime.jsx)(icons_DownOutlined, {
      className: classnames_default()("".concat(prefixCls, "-icon"), hashId, "".concat(prefixCls, "-arrow"))
    }) : null]
  }));
};
var FieldLabel = /*#__PURE__*/react.forwardRef(FieldLabelFunction);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///98912
`)},1336:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  M: function() { return /* binding */ FilterDropdown; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/index.js + 7 modules
var config_provider = __webpack_require__(28459);
// EXTERNAL MODULE: ./node_modules/antd/es/popover/index.js
var popover = __webpack_require__(55241);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/index.js + 7 modules
var es = __webpack_require__(89451);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-provider/es/useStyle/index.js
var useStyle = __webpack_require__(98082);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/DropdownFooter/style.js



var genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)({}, token.componentCls, {
    display: 'flex',
    justifyContent: 'space-between',
    paddingBlock: 8,
    paddingInlineStart: 8,
    paddingInlineEnd: 8,
    borderBlockStart: "1px solid ".concat(token.colorSplit)
  });
};
function style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('DropdownFooter', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [genProStyle(proToken)];
  });
}
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/DropdownFooter/index.js






var DropdownFooter = function DropdownFooter(props) {
  var intl = (0,es/* useIntl */.YB)();
  var onClear = props.onClear,
    onConfirm = props.onConfirm,
    disabled = props.disabled,
    footerRender = props.footerRender;
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls('pro-core-dropdown-footer');
  var _useStyle = style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var defaultFooter = [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    style: {
      visibility: onClear ? 'visible' : 'hidden'
    },
    type: "link",
    size: "small",
    disabled: disabled,
    onClick: function onClick(e) {
      if (onClear) {
        onClear(e);
      }
      e.stopPropagation();
    },
    children: intl.getMessage('form.lightFilter.clear', '\u6E05\u9664')
  }, "clear"), /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    "data-type": "confirm",
    type: "primary",
    size: "small",
    onClick: onConfirm,
    disabled: disabled,
    children: intl.getMessage('form.lightFilter.confirm', '\u786E\u8BA4')
  }, "confirm")];
  if (footerRender === false || (footerRender === null || footerRender === void 0 ? void 0 : footerRender(onConfirm, onClear)) === false) {
    return null;
  }
  var renderDom = (footerRender === null || footerRender === void 0 ? void 0 : footerRender(onConfirm, onClear)) || defaultFooter;
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
    className: classnames_default()(prefixCls, hashId),
    onClick: function onClick(e) {
      return e.target.getAttribute('data-type') !== 'confirm' && e.stopPropagation();
    },
    children: renderDom
  }));
};

// EXTERNAL MODULE: ./node_modules/@ant-design/pro-utils/es/compareVersions/openVisibleCompatible.js
var openVisibleCompatible = __webpack_require__(73177);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/FilterDropdown/style.js



var style_genProStyle = function genProStyle(token) {
  return (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(token.componentCls, "-label"), {
    cursor: 'pointer'
  }), "".concat(token.componentCls, "-overlay"), {
    minWidth: '200px',
    marginBlockStart: '4px'
  }), "".concat(token.componentCls, "-content"), {
    paddingBlock: 16,
    paddingInline: 16
  });
};
function FilterDropdown_style_useStyle(prefixCls) {
  return (0,useStyle/* useStyle */.Xj)('FilterDropdown', function (token) {
    var proToken = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, token), {}, {
      componentCls: ".".concat(prefixCls)
    });
    return [style_genProStyle(proToken)];
  });
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/es/components/FilterDropdown/index.js











var FilterDropdown = function FilterDropdown(props) {
  var children = props.children,
    label = props.label,
    footer = props.footer,
    open = props.open,
    onOpenChange = props.onOpenChange,
    disabled = props.disabled,
    onVisibleChange = props.onVisibleChange,
    visible = props.visible,
    footerRender = props.footerRender,
    placement = props.placement;
  var _useContext = (0,react.useContext)(config_provider/* default.ConfigContext */.ZP.ConfigContext),
    getPrefixCls = _useContext.getPrefixCls;
  var prefixCls = getPrefixCls('pro-core-field-dropdown');
  var _useStyle = FilterDropdown_style_useStyle(prefixCls),
    wrapSSR = _useStyle.wrapSSR,
    hashId = _useStyle.hashId;
  var dropdownOpenProps = (0,openVisibleCompatible/* openVisibleCompatible */.X)(open || visible || false, onOpenChange || onVisibleChange);
  var htmlRef = (0,react.useRef)(null);
  return wrapSSR( /*#__PURE__*/(0,jsx_runtime.jsx)(popover/* default */.Z, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    placement: placement,
    trigger: ['click']
  }, dropdownOpenProps), {}, {
    overlayInnerStyle: {
      padding: 0
    },
    content: /*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      ref: htmlRef,
      className: classnames_default()("".concat(prefixCls, "-overlay"), (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-overlay-").concat(placement), placement), "hashId", hashId)),
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(config_provider/* default */.ZP, {
        getPopupContainer: function getPopupContainer() {
          return htmlRef.current || document.body;
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)("div", {
          className: "".concat(prefixCls, "-content ").concat(hashId).trim(),
          children: children
        })
      }), footer && /*#__PURE__*/(0,jsx_runtime.jsx)(DropdownFooter, (0,objectSpread2/* default */.Z)({
        disabled: disabled,
        footerRender: footerRender
      }, footer))]
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)("span", {
      className: "".concat(prefixCls, "-label ").concat(hashId).trim(),
      children: label
    })
  })));
};
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///1336
`)},41036:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J: function() { return /* binding */ ProFormContext; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);

var ProFormContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDEwMzYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUEwQjtBQUNuQixrQ0FBa0MsZ0RBQW1CLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9wcm8tdXRpbHMvZXMvY29tcG9uZW50cy9Qcm9Gb3JtQ29udGV4dC9pbmRleC5qcz82NDdlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5leHBvcnQgdmFyIFByb0Zvcm1Db250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe30pOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///41036
`)},23312:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cl: function() { return /* binding */ dateFormatterMap; },
/* harmony export */   lp: function() { return /* binding */ conversionMomentValue; }
/* harmony export */ });
/* unused harmony exports isPlainObject, convertMoment */
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(71002);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27484);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var dayjs_plugin_quarterOfYear__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96671);
/* harmony import */ var dayjs_plugin_quarterOfYear__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_quarterOfYear__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(88306);
/* harmony import */ var _isNil__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(74763);





dayjs__WEBPACK_IMPORTED_MODULE_0___default().extend((dayjs_plugin_quarterOfYear__WEBPACK_IMPORTED_MODULE_1___default()));
var dateFormatterMap = {
  time: 'HH:mm:ss',
  timeRange: 'HH:mm:ss',
  date: 'YYYY-MM-DD',
  dateWeek: 'YYYY-wo',
  dateMonth: 'YYYY-MM',
  dateQuarter: 'YYYY-[Q]Q',
  dateYear: 'YYYY',
  dateRange: 'YYYY-MM-DD',
  dateTime: 'YYYY-MM-DD HH:mm:ss',
  dateTimeRange: 'YYYY-MM-DD HH:mm:ss'
};
/**
 * \u5224\u65AD\u662F\u4E0D\u662F\u4E00\u4E2A object
 * @param  {any} o
 * @returns boolean
 */
function isObject(o) {
  return Object.prototype.toString.call(o) === '[object Object]';
}
/**
 * \u5224\u65AD\u662F\u5426\u662F\u4E00\u4E2A\u7684\u7B80\u5355\u7684 object
 * @param  {{constructor:any}} o
 * @returns boolean
 */
function isPlainObject(o) {
  if (isObject(o) === false) return false;

  // If has modified constructor
  var ctor = o.constructor;
  if (ctor === undefined) return true;

  // If has modified prototype
  var prot = ctor.prototype;
  if (isObject(prot) === false) return false;

  // If constructor does not have an Object-specific method
  if (prot.hasOwnProperty('isPrototypeOf') === false) {
    return false;
  }

  // Most likely a plain Object
  return true;
}

/**
 *  \u4E00\u4E2A\u6BD4\u8F83hack\u7684moment\u5224\u65AD\u5DE5\u5177
 * @param  {any} value
 * @returns boolean
 */
var isMoment = function isMoment(value) {
  return !!(value !== null && value !== void 0 && value._isAMomentObject);
};

/**
 * \u6839\u636E\u4E0D\u540C\u7684\u683C\u5F0F\u8F6C\u5316 dayjs
 * @param  {dayjs.Dayjs} value
 * @param  {string|((value:dayjs.Dayjs} dateFormatter
 * @param  {string} valueType
 */
var convertMoment = function convertMoment(value, dateFormatter, valueType) {
  if (!dateFormatter) {
    return value;
  }
  if (dayjs__WEBPACK_IMPORTED_MODULE_0___default().isDayjs(value) || isMoment(value)) {
    if (dateFormatter === 'number') {
      return value.valueOf();
    }
    if (dateFormatter === 'string') {
      return value.format(dateFormatterMap[valueType] || 'YYYY-MM-DD HH:mm:ss');
    }
    if (typeof dateFormatter === 'string' && dateFormatter !== 'string') {
      return value.format(dateFormatter);
    }
    if (typeof dateFormatter === 'function') {
      return dateFormatter(value, valueType);
    }
  }
  return value;
};

/**
 * \u8FD9\u91CC\u4E3B\u8981\u662F\u6765\u8F6C\u5316\u4E00\u4E0B\u6570\u636E \u5C06 dayjs \u8F6C\u5316\u4E3A string \u5C06 all \u9ED8\u8BA4\u5220\u9664
 * @param  {T} value
 * @param  {DateFormatter} dateFormatter
 * @param  {Record<string} valueTypeMap
 * @param  {ProFieldValueType;dateFormat:string;}|any>} |{valueType
 * @param  {boolean} omitNil?
 * @param  {NamePath} parentKey?
 */
var conversionMomentValue = function conversionMomentValue(value, dateFormatter, valueTypeMap, omitNil, parentKey) {
  var tmpValue = {};
  if (typeof window === 'undefined') return value;
  // \u5982\u679C value \u662F string | null | Blob\u7C7B\u578B \u5176\u4E2D\u4E4B\u4E00\uFF0C\u76F4\u63A5\u8FD4\u56DE
  // \u5F62\u5982 {key: [File, File]} \u7684\u8868\u5355\u5B57\u6BB5\u5F53\u8FDB\u884C\u7B2C\u4E8C\u6B21\u9012\u5F52\u65F6\u4F1A\u5BFC\u81F4\u5176\u76F4\u63A5\u8D8A\u8FC7 typeof value !== 'object' \u8FD9\u4E00\u5224\u65AD https://github.com/ant-design/pro-components/issues/2071
  if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(value) !== 'object' || (0,_isNil__WEBPACK_IMPORTED_MODULE_3__/* .isNil */ .k)(value) || value instanceof Blob || Array.isArray(value)) {
    return value;
  }
  Object.keys(value).forEach(function (valueKey) {
    var namePath = parentKey ? [parentKey, valueKey].flat(1) : [valueKey];
    var valueFormatMap = (0,rc_util_es_utils_get__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)(valueTypeMap, namePath) || 'text';
    var valueType = 'text';
    var dateFormat;
    if (typeof valueFormatMap === 'string') {
      valueType = valueFormatMap;
    } else if (valueFormatMap) {
      valueType = valueFormatMap.valueType;
      dateFormat = valueFormatMap.dateFormat;
    }
    var itemValue = value[valueKey];
    if ((0,_isNil__WEBPACK_IMPORTED_MODULE_3__/* .isNil */ .k)(itemValue) && omitNil) {
      return;
    }
    // \u5904\u7406\u5D4C\u5957\u7684\u60C5\u51B5
    if (isPlainObject(itemValue) &&
    // \u4E0D\u662F\u6570\u7EC4
    !Array.isArray(itemValue) &&
    // \u4E0D\u662F dayjs
    !dayjs__WEBPACK_IMPORTED_MODULE_0___default().isDayjs(itemValue) &&
    // \u4E0D\u662F moment
    !isMoment(itemValue)) {
      tmpValue[valueKey] = conversionMomentValue(itemValue, dateFormatter, valueTypeMap, omitNil, [valueKey]);
      return;
    }
    // \u5904\u7406 FormList \u7684 value
    if (Array.isArray(itemValue)) {
      tmpValue[valueKey] = itemValue.map(function (arrayValue, index) {
        if (dayjs__WEBPACK_IMPORTED_MODULE_0___default().isDayjs(arrayValue) || isMoment(arrayValue)) {
          return convertMoment(arrayValue, dateFormat || dateFormatter, valueType);
        }
        return conversionMomentValue(arrayValue, dateFormatter, valueTypeMap, omitNil, [valueKey, "".concat(index)].flat(1));
      });
      return;
    }
    tmpValue[valueKey] = convertMoment(itemValue, dateFormat || dateFormatter, valueType);
  });
  return tmpValue;
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///23312
`)},86190:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   c: function() { return /* binding */ dateArrayFormatter; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97685);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27484);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);


/**
 * \u901A\u8FC7 format \u6765\u683C\u5F0F\u5316\u65E5\u671F\uFF0C\u56E0\u4E3A\u652F\u6301\u4E86function \u6240\u4EE5\u9700\u8981\u5355\u72EC\u7684\u65B9\u6CD5\u6765\u5904\u7406
 * @param  {any} endText
 * @param  {FormatType} format
 * @return string
 */
var formatString = function formatString(endText, format) {
  if (typeof format === 'function') {
    return format(dayjs__WEBPACK_IMPORTED_MODULE_0___default()(endText));
  }
  return dayjs__WEBPACK_IMPORTED_MODULE_0___default()(endText).format(format);
};
/**
 * \u683C\u5F0F\u5316\u533A\u57DF\u65E5\u671F,\u5982\u679C\u662F\u4E00\u4E2A\u6570\u7EC4\uFF0C\u4F1A\u8FD4\u56DE start ~ end
 * @param  {any} value
 * @param  {FormatType | FormatType[]} format
 * returns string
 */
var dateArrayFormatter = function dateArrayFormatter(value, format) {
  var _ref = Array.isArray(value) ? value : [],
    _ref2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(_ref, 2),
    startText = _ref2[0],
    endText = _ref2[1];
  var formatFirst;
  var formatEnd;
  if (Array.isArray(format)) {
    formatFirst = format[0];
    formatEnd = format[1];
  } else {
    formatFirst = format;
    formatEnd = format;
  }

  // activePickerIndex for https://github.com/ant-design/ant-design/issues/22158
  var parsedStartText = startText ? formatString(startText, formatFirst) : '';
  var parsedEndText = endText ? formatString(endText, formatEnd) : '';
  var valueStr = parsedStartText && parsedEndText ? "".concat(parsedStartText, " ~ ").concat(parsedEndText) : '';
  return valueStr;
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///86190
`)},10178:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   D: function() { return /* binding */ useDebounceFn; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(74165);
/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15861);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _useRefFunction__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(48171);




/**
 * \u4E00\u4E2A\u53BB\u6296\u7684 hook\uFF0C\u4F20\u5165\u4E00\u4E2A function\uFF0C\u8FD4\u56DE\u4E00\u4E2A\u53BB\u6296\u540E\u7684 function
 * @param  {(...args:T) => Promise<any>} fn
 * @param  {number} wait?
 */
function useDebounceFn(fn, wait) {
  var callback = (0,_useRefFunction__WEBPACK_IMPORTED_MODULE_1__/* .useRefFunction */ .J)(fn);
  var timer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var cancel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
  }, []);
  var run = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)().mark(function _callee2() {
    var _len,
      args,
      _key,
      _args2 = arguments;
    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          for (_len = _args2.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = _args2[_key];
          }
          if (!(wait === 0 || wait === undefined)) {
            _context2.next = 3;
            break;
          }
          return _context2.abrupt("return", callback.apply(void 0, args));
        case 3:
          cancel();
          return _context2.abrupt("return", new Promise(function (resolve) {
            timer.current = setTimeout( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)().mark(function _callee() {
              return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)().wrap(function _callee$(_context) {
                while (1) switch (_context.prev = _context.next) {
                  case 0:
                    _context.t0 = resolve;
                    _context.next = 3;
                    return callback.apply(void 0, args);
                  case 3:
                    _context.t1 = _context.sent;
                    (0, _context.t0)(_context.t1);
                    return _context.abrupt("return");
                  case 6:
                  case "end":
                    return _context.stop();
                }
              }, _callee);
            })), wait);
          }));
        case 5:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [callback, cancel, wait]);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    return cancel;
  }, [cancel]);
  return {
    run: run,
    cancel: cancel
  };
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///10178
`)},27068:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Au: function() { return /* binding */ useDeepCompareEffectDebounce; },
/* harmony export */   KW: function() { return /* binding */ useDeepCompareEffect; },
/* harmony export */   Uf: function() { return /* binding */ useDeepCompareMemoize; }
/* harmony export */ });
/* unused harmony export isDeepEqual */
/* harmony import */ var _babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(74165);
/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15861);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _isDeepEqualReact__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60249);
/* harmony import */ var _useDebounceFn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10178);





var isDeepEqual = function isDeepEqual(a, b, ignoreKeys) {
  return (0,_isDeepEqualReact__WEBPACK_IMPORTED_MODULE_1__/* .isDeepEqualReact */ .A)(a, b, ignoreKeys);
};
function useDeepCompareMemoize(value, ignoreKeys) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  // it can be done by using useMemo as well
  // but useRef is rather cleaner and easier
  if (!isDeepEqual(value, ref.current, ignoreKeys)) {
    ref.current = value;
  }
  return ref.current;
}
function useDeepCompareEffect(effect, dependencies, ignoreKeys) {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, useDeepCompareMemoize(dependencies || [], ignoreKeys));
}
function useDeepCompareEffectDebounce(effect, dependencies, ignoreKeys, waitTime) {
  var effectDn = (0,_useDebounceFn__WEBPACK_IMPORTED_MODULE_2__/* .useDebounceFn */ .D)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)( /*#__PURE__*/(0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)().mark(function _callee() {
    return (0,_babel_runtime_helpers_esm_regeneratorRuntime__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          effect();
        case 1:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), waitTime || 16);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    effectDn.run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, useDeepCompareMemoize(dependencies || [], ignoreKeys));
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///27068
`)},74138:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(27068);



/**
 * \`useDeepCompareMemo\` will only recompute the memoized value when one of the
 * \`deps\` has changed.
 *
 * Usage note: only use this if \`deps\` are objects or arrays that contain
 * objects. Otherwise you should just use React.useMemo.
 *
 */
function useDeepCompareMemo(factory, dependencies) {
  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(factory, (0,_useDeepCompareEffect__WEBPACK_IMPORTED_MODULE_1__/* .useDeepCompareMemoize */ .Uf)(dependencies));
}
/* harmony default export */ __webpack_exports__.Z = (useDeepCompareMemo);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzQxMzguanMiLCJtYXBwaW5ncyI6Ijs7QUFBMEI7QUFDc0M7O0FBRWhFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMENBQWEsVUFBVSxzRkFBcUI7QUFDckQ7QUFDQSxzREFBZSxrQkFBa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9wcm8tdXRpbHMvZXMvaG9va3MvdXNlRGVlcENvbXBhcmVNZW1vL2luZGV4LmpzP2YxNzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZURlZXBDb21wYXJlTWVtb2l6ZSB9IGZyb20gXCIuLi91c2VEZWVwQ29tcGFyZUVmZmVjdFwiO1xuXG4vKipcbiAqIGB1c2VEZWVwQ29tcGFyZU1lbW9gIHdpbGwgb25seSByZWNvbXB1dGUgdGhlIG1lbW9pemVkIHZhbHVlIHdoZW4gb25lIG9mIHRoZVxuICogYGRlcHNgIGhhcyBjaGFuZ2VkLlxuICpcbiAqIFVzYWdlIG5vdGU6IG9ubHkgdXNlIHRoaXMgaWYgYGRlcHNgIGFyZSBvYmplY3RzIG9yIGFycmF5cyB0aGF0IGNvbnRhaW5cbiAqIG9iamVjdHMuIE90aGVyd2lzZSB5b3Ugc2hvdWxkIGp1c3QgdXNlIFJlYWN0LnVzZU1lbW8uXG4gKlxuICovXG5mdW5jdGlvbiB1c2VEZWVwQ29tcGFyZU1lbW8oZmFjdG9yeSwgZGVwZW5kZW5jaWVzKSB7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKGZhY3RvcnksIHVzZURlZXBDb21wYXJlTWVtb2l6ZShkZXBlbmRlbmNpZXMpKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHVzZURlZXBDb21wYXJlTWVtbzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///74138
`)},48171:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   J: function() { return /* binding */ useRefFunction; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(74902);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);


var useRefFunction = function useRefFunction(reFunction) {
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  ref.current = reFunction;
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    var _ref$current;
    for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
      rest[_key] = arguments[_key];
    }
    return (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.call.apply(_ref$current, [ref].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(rest)));
  }, []);
};
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDgxNzEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEU7QUFDbEM7QUFDNUM7QUFDQSxZQUFZLDZDQUFNO0FBQ2xCO0FBQ0EsU0FBUyxrREFBVztBQUNwQjtBQUNBLHdFQUF3RSxhQUFhO0FBQ3JGO0FBQ0E7QUFDQSwwSUFBMEksaUdBQWtCO0FBQzVKLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby11dGlscy9lcy9ob29rcy91c2VSZWZGdW5jdGlvbi9pbmRleC5qcz9iYWQ4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xudmFyIHVzZVJlZkZ1bmN0aW9uID0gZnVuY3Rpb24gdXNlUmVmRnVuY3Rpb24ocmVGdW5jdGlvbikge1xuICB2YXIgcmVmID0gdXNlUmVmKG51bGwpO1xuICByZWYuY3VycmVudCA9IHJlRnVuY3Rpb247XG4gIHJldHVybiB1c2VDYWxsYmFjayhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIF9yZWYkY3VycmVudDtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgcmVzdCA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIHJlc3RbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuICAgIHJldHVybiAoX3JlZiRjdXJyZW50ID0gcmVmLmN1cnJlbnQpID09PSBudWxsIHx8IF9yZWYkY3VycmVudCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3JlZiRjdXJyZW50LmNhbGwuYXBwbHkoX3JlZiRjdXJyZW50LCBbcmVmXS5jb25jYXQoX3RvQ29uc3VtYWJsZUFycmF5KHJlc3QpKSk7XG4gIH0sIFtdKTtcbn07XG5leHBvcnQgeyB1c2VSZWZGdW5jdGlvbiB9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///48171
`)},60249:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: function() { return /* binding */ isDeepEqualReact; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_createForOfIteratorHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(37762);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(71002);


/* eslint-disable no-restricted-syntax */
/* eslint-disable no-continue */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-self-compare */
/* eslint-disable eqeqeq */
/* eslint-disable no-plusplus */
// do not edit .js files directly - edit src/index.jst

function isDeepEqualReact(a, b, ignoreKeys, debug) {
  if (a === b) return true;
  if (a && b && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(a) === 'object' && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(b) === 'object') {
    if (a.constructor !== b.constructor) return false;
    var length;
    var i;
    var keys;
    if (Array.isArray(a)) {
      length = a.length;
      if (length != b.length) return false;
      for (i = length; i-- !== 0;) if (!isDeepEqualReact(a[i], b[i], ignoreKeys, debug)) return false;
      return true;
    }
    if (a instanceof Map && b instanceof Map) {
      if (a.size !== b.size) return false;
      var _iterator = (0,_babel_runtime_helpers_esm_createForOfIteratorHelper__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(a.entries()),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          i = _step.value;
          if (!b.has(i[0])) return false;
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var _iterator2 = (0,_babel_runtime_helpers_esm_createForOfIteratorHelper__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(a.entries()),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          i = _step2.value;
          if (!isDeepEqualReact(i[1], b.get(i[0]), ignoreKeys, debug)) return false;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return true;
    }
    if (a instanceof Set && b instanceof Set) {
      if (a.size !== b.size) return false;
      var _iterator3 = (0,_babel_runtime_helpers_esm_createForOfIteratorHelper__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(a.entries()),
        _step3;
      try {
        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
          i = _step3.value;
          if (!b.has(i[0])) return false;
        }
      } catch (err) {
        _iterator3.e(err);
      } finally {
        _iterator3.f();
      }
      return true;
    }
    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
      // @ts-ignore
      length = a.length;
      // @ts-ignore
      if (length != b.length) return false;
      // @ts-ignore
      for (i = length; i-- !== 0;) if (a[i] !== b[i]) return false;
      return true;
    }
    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
    if (a.valueOf !== Object.prototype.valueOf && a.valueOf) return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString && a.toString) return a.toString() === b.toString();

    // eslint-disable-next-line prefer-const
    keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length) return false;
    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
    for (i = length; i-- !== 0;) {
      var key = keys[i];
      if (ignoreKeys !== null && ignoreKeys !== void 0 && ignoreKeys.includes(key)) continue;
      if (key === '_owner' && a.$$typeof) {
        // React-specific: avoid traversing React elements' _owner.
        //  _owner contains circular references
        // and is not needed when comparing the actual elements (and not their owners)
        continue;
      }
      if (!isDeepEqualReact(a[key], b[key], ignoreKeys, debug)) {
        if (debug) {
          console.log(key);
        }
        return false;
      }
    }
    return true;
  }

  // true if both NaN, false otherwise
  return a !== a && b !== b;
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///60249
`)},74763:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   k: function() { return /* binding */ isNil; }
/* harmony export */ });
var isNil = function isNil(value) {
  return value === null || value === undefined;
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzQ3NjMuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLXV0aWxzL2VzL2lzTmlsL2luZGV4LmpzPzcyYTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBpc05pbCA9IGZ1bmN0aW9uIGlzTmlsKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///74763
`)},92210:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   T: function() { return /* binding */ merge; }
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(71002);


/* eslint-disable prefer-rest-params */

/**
 * \u7528\u4E8E\u5408\u5E76 n \u4E2A\u5BF9\u8C61
 * @param  {any[]} ...rest
 * @returns T
 */
var merge = function merge() {
  var obj = {};
  for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {
    rest[_key] = arguments[_key];
  }
  var il = rest.length;
  var key;
  var i = 0;
  for (; i < il; i += 1) {
    // eslint-disable-next-line no-restricted-syntax
    for (key in rest[i]) {
      if (rest[i].hasOwnProperty(key)) {
        if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(obj[key]) === 'object' && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Z)(rest[i][key]) === 'object' && obj[key] !== undefined && obj[key] !== null && !Array.isArray(obj[key]) && !Array.isArray(rest[i][key])) {
          obj[key] = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)({}, obj[key]), rest[i][key]);
        } else {
          obj[key] = rest[i][key];
        }
      }
    }
  }
  return obj;
};
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTIyMTAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUU7QUFDYjtBQUN4RDs7QUFFQTtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFLGFBQWE7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsUUFBUTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNGQUFPLDJCQUEyQixzRkFBTztBQUNyRCxxQkFBcUIsNkZBQWEsQ0FBQyw2RkFBYSxHQUFHO0FBQ25ELFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby11dGlscy9lcy9tZXJnZS9pbmRleC5qcz9iZjM5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0U3ByZWFkIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyXCI7XG5pbXBvcnQgX3R5cGVvZiBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vdHlwZW9mXCI7XG4vKiBlc2xpbnQtZGlzYWJsZSBwcmVmZXItcmVzdC1wYXJhbXMgKi9cblxuLyoqXG4gKiDnlKjkuo7lkIjlubYgbiDkuKrlr7nosaFcbiAqIEBwYXJhbSAge2FueVtdfSAuLi5yZXN0XG4gKiBAcmV0dXJucyBUXG4gKi9cbnZhciBtZXJnZSA9IGZ1bmN0aW9uIG1lcmdlKCkge1xuICB2YXIgb2JqID0ge307XG4gIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCByZXN0ID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIHJlc3RbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gIH1cbiAgdmFyIGlsID0gcmVzdC5sZW5ndGg7XG4gIHZhciBrZXk7XG4gIHZhciBpID0gMDtcbiAgZm9yICg7IGkgPCBpbDsgaSArPSAxKSB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXJlc3RyaWN0ZWQtc3ludGF4XG4gICAgZm9yIChrZXkgaW4gcmVzdFtpXSkge1xuICAgICAgaWYgKHJlc3RbaV0uaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICBpZiAoX3R5cGVvZihvYmpba2V5XSkgPT09ICdvYmplY3QnICYmIF90eXBlb2YocmVzdFtpXVtrZXldKSA9PT0gJ29iamVjdCcgJiYgb2JqW2tleV0gIT09IHVuZGVmaW5lZCAmJiBvYmpba2V5XSAhPT0gbnVsbCAmJiAhQXJyYXkuaXNBcnJheShvYmpba2V5XSkgJiYgIUFycmF5LmlzQXJyYXkocmVzdFtpXVtrZXldKSkge1xuICAgICAgICAgIG9ialtrZXldID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBvYmpba2V5XSksIHJlc3RbaV1ba2V5XSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgb2JqW2tleV0gPSByZXN0W2ldW2tleV07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9iajtcbn07XG5leHBvcnQgeyBtZXJnZSB9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///92210
`)},75661:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   x: function() { return /* binding */ nanoid; }
/* harmony export */ });
/* eslint-disable prefer-const */

var index = 0;
var genNanoid = function genNanoid() {
  var t = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 21;
  if (typeof window === 'undefined') return (index += 1).toFixed(0);
  if (!window.crypto) return (index += 1).toFixed(0);
  var e = '',
    r = crypto.getRandomValues(new Uint8Array(t));
  // eslint-disable-next-line no-param-reassign
  for (; t--;) {
    var n = 63 & r[t];
    e += n < 36 ? n.toString(36) : n < 62 ? (n - 26).toString(36).toUpperCase() : n < 63 ? '_' : '-';
  }
  return e;
};

/**
 * \u751F\u6210uuid\uFF0C\u5982\u679C\u4E0D\u652F\u6301 randomUUID\uFF0C\u5C31\u7528 genNanoid
 *
 * @returns string
 */
var nanoid = function nanoid() {
  if (typeof window === 'undefined') return genNanoid();
  // @ts-ignore
  if (window.crypto && window.crypto.randomUUID && typeof crypto.randomUUID == 'function') {
    // @ts-ignore
    return crypto.randomUUID();
  }
  return genNanoid();
};//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzU2NjEuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsSUFBSTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL3Byby11dGlscy9lcy9uYW5vaWQvaW5kZXguanM/NDVmMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSBwcmVmZXItY29uc3QgKi9cblxudmFyIGluZGV4ID0gMDtcbnZhciBnZW5OYW5vaWQgPSBmdW5jdGlvbiBnZW5OYW5vaWQoKSB7XG4gIHZhciB0ID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiAyMTtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gKGluZGV4ICs9IDEpLnRvRml4ZWQoMCk7XG4gIGlmICghd2luZG93LmNyeXB0bykgcmV0dXJuIChpbmRleCArPSAxKS50b0ZpeGVkKDApO1xuICB2YXIgZSA9ICcnLFxuICAgIHIgPSBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50OEFycmF5KHQpKTtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXBhcmFtLXJlYXNzaWduXG4gIGZvciAoOyB0LS07KSB7XG4gICAgdmFyIG4gPSA2MyAmIHJbdF07XG4gICAgZSArPSBuIDwgMzYgPyBuLnRvU3RyaW5nKDM2KSA6IG4gPCA2MiA/IChuIC0gMjYpLnRvU3RyaW5nKDM2KS50b1VwcGVyQ2FzZSgpIDogbiA8IDYzID8gJ18nIDogJy0nO1xuICB9XG4gIHJldHVybiBlO1xufTtcblxuLyoqXG4gKiDnlJ/miJB1dWlk77yM5aaC5p6c5LiN5pSv5oyBIHJhbmRvbVVVSUTvvIzlsLHnlKggZ2VuTmFub2lkXG4gKlxuICogQHJldHVybnMgc3RyaW5nXG4gKi9cbmV4cG9ydCB2YXIgbmFub2lkID0gZnVuY3Rpb24gbmFub2lkKCkge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBnZW5OYW5vaWQoKTtcbiAgLy8gQHRzLWlnbm9yZVxuICBpZiAod2luZG93LmNyeXB0byAmJiB3aW5kb3cuY3J5cHRvLnJhbmRvbVVVSUQgJiYgdHlwZW9mIGNyeXB0by5yYW5kb21VVUlEID09ICdmdW5jdGlvbicpIHtcbiAgICAvLyBAdHMtaWdub3JlXG4gICAgcmV0dXJuIGNyeXB0by5yYW5kb21VVUlEKCk7XG4gIH1cbiAgcmV0dXJuIGdlbk5hbm9pZCgpO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///75661
`)},22270:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   h: function() { return /* binding */ runFunction; }
/* harmony export */ });
/** \u5982\u679C\u662F\u4E2A\u65B9\u6CD5\u6267\u884C\u4E00\u4E0B\u5B83 */
function runFunction(valueEnum) {
  if (typeof valueEnum === 'function') {
    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      rest[_key - 1] = arguments[_key];
    }
    return valueEnum.apply(void 0, rest);
  }
  return valueEnum;
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjIyNzAuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDTztBQUNQO0FBQ0EsMkZBQTJGLGFBQWE7QUFDeEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vcHJvLXV0aWxzL2VzL3J1bkZ1bmN0aW9uL2luZGV4LmpzPzY2MDEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIOWmguaenOaYr+S4quaWueazleaJp+ihjOS4gOS4i+WugyAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJ1bkZ1bmN0aW9uKHZhbHVlRW51bSkge1xuICBpZiAodHlwZW9mIHZhbHVlRW51bSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCByZXN0ID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIHJlc3RbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cbiAgICByZXR1cm4gdmFsdWVFbnVtLmFwcGx5KHZvaWQgMCwgcmVzdCk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlRW51bTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///22270
`)},62914:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ AntdIcon; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/conversion.js
var conversion = __webpack_require__(86500);
// EXTERNAL MODULE: ./node_modules/@ctrl/tinycolor/dist/module/format-input.js
var format_input = __webpack_require__(1350);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/colors/es/generate.js

var hueStep = 2; // \u8272\u76F8\u9636\u68AF
var saturationStep = 0.16; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var saturationStep2 = 0.05; // \u9971\u548C\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var brightnessStep1 = 0.05; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6D45\u8272\u90E8\u5206
var brightnessStep2 = 0.15; // \u4EAE\u5EA6\u9636\u68AF\uFF0C\u6DF1\u8272\u90E8\u5206
var lightColorCount = 5; // \u6D45\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0A
var darkColorCount = 4; // \u6DF1\u8272\u6570\u91CF\uFF0C\u4E3B\u8272\u4E0B
// \u6697\u8272\u4E3B\u9898\u989C\u8272\u6620\u5C04\u5173\u7CFB\u8868
var darkColorMap = [{
  index: 7,
  opacity: 0.15
}, {
  index: 6,
  opacity: 0.25
}, {
  index: 5,
  opacity: 0.3
}, {
  index: 5,
  opacity: 0.45
}, {
  index: 5,
  opacity: 0.65
}, {
  index: 5,
  opacity: 0.85
}, {
  index: 4,
  opacity: 0.9
}, {
  index: 3,
  opacity: 0.95
}, {
  index: 2,
  opacity: 0.97
}, {
  index: 1,
  opacity: 0.98
}];
// Wrapper function ported from TinyColor.prototype.toHsv
// Keep it here because of \`hsv.h * 360\`
function toHsv(_ref) {
  var r = _ref.r,
    g = _ref.g,
    b = _ref.b;
  var hsv = (0,conversion/* rgbToHsv */.py)(r, g, b);
  return {
    h: hsv.h * 360,
    s: hsv.s,
    v: hsv.v
  };
}

// Wrapper function ported from TinyColor.prototype.toHexString
// Keep it here because of the prefix \`#\`
function toHex(_ref2) {
  var r = _ref2.r,
    g = _ref2.g,
    b = _ref2.b;
  return "#".concat((0,conversion/* rgbToHex */.vq)(r, g, b, false));
}

// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.
// Amount in range [0, 1]
// Assume color1 & color2 has no alpha, since the following src code did so.
function mix(rgb1, rgb2, amount) {
  var p = amount / 100;
  var rgb = {
    r: (rgb2.r - rgb1.r) * p + rgb1.r,
    g: (rgb2.g - rgb1.g) * p + rgb1.g,
    b: (rgb2.b - rgb1.b) * p + rgb1.b
  };
  return rgb;
}
function getHue(hsv, i, light) {
  var hue;
  // \u6839\u636E\u8272\u76F8\u4E0D\u540C\uFF0C\u8272\u76F8\u8F6C\u5411\u4E0D\u540C
  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {
    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;
  } else {
    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;
  }
  if (hue < 0) {
    hue += 360;
  } else if (hue >= 360) {
    hue -= 360;
  }
  return hue;
}
function getSaturation(hsv, i, light) {
  // grey color don't change saturation
  if (hsv.h === 0 && hsv.s === 0) {
    return hsv.s;
  }
  var saturation;
  if (light) {
    saturation = hsv.s - saturationStep * i;
  } else if (i === darkColorCount) {
    saturation = hsv.s + saturationStep;
  } else {
    saturation = hsv.s + saturationStep2 * i;
  }
  // \u8FB9\u754C\u503C\u4FEE\u6B63
  if (saturation > 1) {
    saturation = 1;
  }
  // \u7B2C\u4E00\u683C\u7684 s \u9650\u5236\u5728 0.06-0.1 \u4E4B\u95F4
  if (light && i === lightColorCount && saturation > 0.1) {
    saturation = 0.1;
  }
  if (saturation < 0.06) {
    saturation = 0.06;
  }
  return Number(saturation.toFixed(2));
}
function getValue(hsv, i, light) {
  var value;
  if (light) {
    value = hsv.v + brightnessStep1 * i;
  } else {
    value = hsv.v - brightnessStep2 * i;
  }
  if (value > 1) {
    value = 1;
  }
  return Number(value.toFixed(2));
}
function generate(color) {
  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var patterns = [];
  var pColor = (0,format_input/* inputToRGB */.uA)(color);
  for (var i = lightColorCount; i > 0; i -= 1) {
    var hsv = toHsv(pColor);
    var colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(hsv, i, true),
      s: getSaturation(hsv, i, true),
      v: getValue(hsv, i, true)
    }));
    patterns.push(colorString);
  }
  patterns.push(toHex(pColor));
  for (var _i = 1; _i <= darkColorCount; _i += 1) {
    var _hsv = toHsv(pColor);
    var _colorString = toHex((0,format_input/* inputToRGB */.uA)({
      h: getHue(_hsv, _i),
      s: getSaturation(_hsv, _i),
      v: getValue(_hsv, _i)
    }));
    patterns.push(_colorString);
  }

  // dark theme patterns
  if (opts.theme === 'dark') {
    return darkColorMap.map(function (_ref3) {
      var index = _ref3.index,
        opacity = _ref3.opacity;
      var darkColorString = toHex(mix((0,format_input/* inputToRGB */.uA)(opts.backgroundColor || '#141414'), (0,format_input/* inputToRGB */.uA)(patterns[index]), opacity * 100));
      return darkColorString;
    });
  }
  return patterns;
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/colors/es/index.js

var presetPrimaryColors = {
  red: '#F5222D',
  volcano: '#FA541C',
  orange: '#FA8C16',
  gold: '#FAAD14',
  yellow: '#FADB14',
  lime: '#A0D911',
  green: '#52C41A',
  cyan: '#13C2C2',
  blue: '#1677FF',
  geekblue: '#2F54EB',
  purple: '#722ED1',
  magenta: '#EB2F96',
  grey: '#666666'
};
var presetPalettes = {};
var presetDarkPalettes = {};
Object.keys(presetPrimaryColors).forEach(function (key) {
  presetPalettes[key] = generate(presetPrimaryColors[key]);
  presetPalettes[key].primary = presetPalettes[key][5];

  // dark presetPalettes
  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {
    theme: 'dark',
    backgroundColor: '#141414'
  });
  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];
});
var red = presetPalettes.red;
var volcano = presetPalettes.volcano;
var gold = presetPalettes.gold;
var orange = presetPalettes.orange;
var yellow = presetPalettes.yellow;
var lime = presetPalettes.lime;
var green = presetPalettes.green;
var cyan = presetPalettes.cyan;
var blue = presetPalettes.blue;
var geekblue = presetPalettes.geekblue;
var purple = presetPalettes.purple;
var magenta = presetPalettes.magenta;
var grey = presetPalettes.grey;
var gray = presetPalettes.grey;

;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/components/Context.js

var IconContext = /*#__PURE__*/(0,react.createContext)({});
/* harmony default export */ var Context = (IconContext);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/dynamicCSS.js
var dynamicCSS = __webpack_require__(44958);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/shadow.js
var shadow = __webpack_require__(27571);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/utils.js








function camelCase(input) {
  return input.replace(/-(.)/g, function (match, g) {
    return g.toUpperCase();
  });
}
function utils_warning(valid, message) {
  (0,warning/* default */.ZP)(valid, "[@ant-design/icons] ".concat(message));
}
function isIconDefinition(target) {
  return (0,esm_typeof/* default */.Z)(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && ((0,esm_typeof/* default */.Z)(target.icon) === 'object' || typeof target.icon === 'function');
}
function normalizeAttrs() {
  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  return Object.keys(attrs).reduce(function (acc, key) {
    var val = attrs[key];
    switch (key) {
      case 'class':
        acc.className = val;
        delete acc.class;
        break;
      default:
        delete acc[key];
        acc[camelCase(key)] = val;
    }
    return acc;
  }, {});
}
function utils_generate(node, key, rootProps) {
  if (!rootProps) {
    return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)({
      key: key
    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {
      return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
    }));
  }
  return /*#__PURE__*/react.createElement(node.tag, (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    key: key
  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {
    return utils_generate(child, "".concat(key, "-").concat(node.tag, "-").concat(index));
  }));
}
function getSecondaryColor(primaryColor) {
  // choose the second color
  return generate(primaryColor)[0];
}
function normalizeTwoToneColors(twoToneColor) {
  if (!twoToneColor) {
    return [];
  }
  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];
}

// These props make sure that the SVG behaviours like general text.
// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4
var svgBaseProps = {
  width: '1em',
  height: '1em',
  fill: 'currentColor',
  'aria-hidden': 'true',
  focusable: 'false'
};
var iconStyles = "\\n.anticon {\\n  display: inline-block;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n";
var useInsertStyles = function useInsertStyles(eleRef) {
  var _useContext = (0,react.useContext)(Context),
    csp = _useContext.csp,
    prefixCls = _useContext.prefixCls;
  var mergedStyleStr = iconStyles;
  if (prefixCls) {
    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);
  }
  (0,react.useEffect)(function () {
    var ele = eleRef.current;
    var shadowRoot = (0,shadow/* getShadowRoot */.A)(ele);
    (0,dynamicCSS/* updateCSS */.hq)(mergedStyleStr, '@ant-design-icons', {
      prepend: true,
      csp: csp,
      attachTo: shadowRoot
    });
  }, []);
};
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/components/IconBase.js


var _excluded = ["icon", "className", "onClick", "style", "primaryColor", "secondaryColor"];


var twoToneColorPalette = {
  primaryColor: '#333',
  secondaryColor: '#E6E6E6',
  calculated: false
};
function setTwoToneColors(_ref) {
  var primaryColor = _ref.primaryColor,
    secondaryColor = _ref.secondaryColor;
  twoToneColorPalette.primaryColor = primaryColor;
  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);
  twoToneColorPalette.calculated = !!secondaryColor;
}
function getTwoToneColors() {
  return (0,objectSpread2/* default */.Z)({}, twoToneColorPalette);
}
var IconBase = function IconBase(props) {
  var icon = props.icon,
    className = props.className,
    onClick = props.onClick,
    style = props.style,
    primaryColor = props.primaryColor,
    secondaryColor = props.secondaryColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var svgRef = react.useRef();
  var colors = twoToneColorPalette;
  if (primaryColor) {
    colors = {
      primaryColor: primaryColor,
      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)
    };
  }
  useInsertStyles(svgRef);
  utils_warning(isIconDefinition(icon), "icon should be icon definiton, but got ".concat(icon));
  if (!isIconDefinition(icon)) {
    return null;
  }
  var target = icon;
  if (target && typeof target.icon === 'function') {
    target = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, target), {}, {
      icon: target.icon(colors.primaryColor, colors.secondaryColor)
    });
  }
  return utils_generate(target.icon, "svg-".concat(target.name), (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({
    className: className,
    onClick: onClick,
    style: style,
    'data-icon': target.name,
    width: '1em',
    height: '1em',
    fill: 'currentColor',
    'aria-hidden': 'true'
  }, restProps), {}, {
    ref: svgRef
  }));
};
IconBase.displayName = 'IconReact';
IconBase.getTwoToneColors = getTwoToneColors;
IconBase.setTwoToneColors = setTwoToneColors;
/* harmony default export */ var components_IconBase = (IconBase);
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js



function setTwoToneColor(twoToneColor) {
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return components_IconBase.setTwoToneColors({
    primaryColor: primaryColor,
    secondaryColor: secondaryColor
  });
}
function getTwoToneColor() {
  var colors = components_IconBase.getTwoToneColors();
  if (!colors.calculated) {
    return colors.primaryColor;
  }
  return [colors.primaryColor, colors.secondaryColor];
}
;// CONCATENATED MODULE: ./node_modules/@ant-design/pro-utils/node_modules/@ant-design/icons/es/components/AntdIcon.js
'use client';





var AntdIcon_excluded = ["className", "icon", "spin", "rotate", "tabIndex", "onClick", "twoToneColor"];







// Initial setting
// should move it to antd main repo?
setTwoToneColor(blue.primary);

// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720

var Icon = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var className = props.className,
    icon = props.icon,
    spin = props.spin,
    rotate = props.rotate,
    tabIndex = props.tabIndex,
    onClick = props.onClick,
    twoToneColor = props.twoToneColor,
    restProps = (0,objectWithoutProperties/* default */.Z)(props, AntdIcon_excluded);
  var _React$useContext = react.useContext(Context),
    _React$useContext$pre = _React$useContext.prefixCls,
    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,
    rootClassName = _React$useContext.rootClassName;
  var classString = classnames_default()(rootClassName, prefixCls, (0,defineProperty/* default */.Z)((0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-").concat(icon.name), !!icon.name), "".concat(prefixCls, "-spin"), !!spin || icon.name === 'loading'), className);
  var iconTabIndex = tabIndex;
  if (iconTabIndex === undefined && onClick) {
    iconTabIndex = -1;
  }
  var svgStyle = rotate ? {
    msTransform: "rotate(".concat(rotate, "deg)"),
    transform: "rotate(".concat(rotate, "deg)")
  } : undefined;
  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),
    _normalizeTwoToneColo2 = (0,slicedToArray/* default */.Z)(_normalizeTwoToneColo, 2),
    primaryColor = _normalizeTwoToneColo2[0],
    secondaryColor = _normalizeTwoToneColo2[1];
  return /*#__PURE__*/react.createElement("span", (0,esm_extends/* default */.Z)({
    role: "img",
    "aria-label": icon.name
  }, restProps, {
    ref: ref,
    tabIndex: iconTabIndex,
    onClick: onClick,
    className: classString
  }), /*#__PURE__*/react.createElement(components_IconBase, {
    icon: icon,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    style: svgStyle
  }));
});
Icon.displayName = 'AntdIcon';
Icon.getTwoToneColor = getTwoToneColor;
Icon.setTwoToneColor = setTwoToneColor;
/* harmony default export */ var AntdIcon = (Icon);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///62914
`)},96671:function(module){eval(`!function(t,n){ true?module.exports=n():0}(this,(function(){"use strict";var t="month",n="quarter";return function(e,i){var r=i.prototype;r.quarter=function(t){return this.$utils().u(t)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(t-1))};var s=r.add;r.add=function(e,i){return e=Number(e),this.$utils().p(i)===n?this.add(3*e,t):s.bind(this)(e,i)};var u=r.startOf;r.startOf=function(e,i){var r=this.$utils(),s=!!r.u(i)||i;if(r.p(e)===n){var o=this.quarter()-1;return s?this.month(3*o).startOf(t).startOf("day"):this.month(3*o+2).endOf(t).endOf("day")}return u.bind(this)(e,i)}}}));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiOTY2NzEuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxLQUFvRCxvQkFBb0IsQ0FBb0ksQ0FBQyxrQkFBa0IsYUFBYSwwQkFBMEIscUJBQXFCLGtCQUFrQixzQkFBc0IsNEZBQTRGLFlBQVksb0JBQW9CLDZFQUE2RSxnQkFBZ0Isd0JBQXdCLGtDQUFrQyxlQUFlLHVCQUF1QiwyRkFBMkYsMkJBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvZGF5anMvcGx1Z2luL3F1YXJ0ZXJPZlllYXIuanM/N2RlNSJdLCJzb3VyY2VzQ29udGVudCI6WyIhZnVuY3Rpb24odCxuKXtcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJ1bmRlZmluZWRcIiE9dHlwZW9mIG1vZHVsZT9tb2R1bGUuZXhwb3J0cz1uKCk6XCJmdW5jdGlvblwiPT10eXBlb2YgZGVmaW5lJiZkZWZpbmUuYW1kP2RlZmluZShuKToodD1cInVuZGVmaW5lZFwiIT10eXBlb2YgZ2xvYmFsVGhpcz9nbG9iYWxUaGlzOnR8fHNlbGYpLmRheWpzX3BsdWdpbl9xdWFydGVyT2ZZZWFyPW4oKX0odGhpcywoZnVuY3Rpb24oKXtcInVzZSBzdHJpY3RcIjt2YXIgdD1cIm1vbnRoXCIsbj1cInF1YXJ0ZXJcIjtyZXR1cm4gZnVuY3Rpb24oZSxpKXt2YXIgcj1pLnByb3RvdHlwZTtyLnF1YXJ0ZXI9ZnVuY3Rpb24odCl7cmV0dXJuIHRoaXMuJHV0aWxzKCkudSh0KT9NYXRoLmNlaWwoKHRoaXMubW9udGgoKSsxKS8zKTp0aGlzLm1vbnRoKHRoaXMubW9udGgoKSUzKzMqKHQtMSkpfTt2YXIgcz1yLmFkZDtyLmFkZD1mdW5jdGlvbihlLGkpe3JldHVybiBlPU51bWJlcihlKSx0aGlzLiR1dGlscygpLnAoaSk9PT1uP3RoaXMuYWRkKDMqZSx0KTpzLmJpbmQodGhpcykoZSxpKX07dmFyIHU9ci5zdGFydE9mO3Iuc3RhcnRPZj1mdW5jdGlvbihlLGkpe3ZhciByPXRoaXMuJHV0aWxzKCkscz0hIXIudShpKXx8aTtpZihyLnAoZSk9PT1uKXt2YXIgbz10aGlzLnF1YXJ0ZXIoKS0xO3JldHVybiBzP3RoaXMubW9udGgoMypvKS5zdGFydE9mKHQpLnN0YXJ0T2YoXCJkYXlcIik6dGhpcy5tb250aCgzKm8rMikuZW5kT2YodCkuZW5kT2YoXCJkYXlcIil9cmV0dXJuIHUuYmluZCh0aGlzKShlLGkpfX19KSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///96671
`)}}]);
