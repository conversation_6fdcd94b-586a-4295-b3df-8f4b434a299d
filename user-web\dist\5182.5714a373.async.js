(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5182],{5966:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(97685);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(91);
/* harmony import */ var _ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(21770);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(98138);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(55241);
/* harmony import */ var omit_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(97435);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);
/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(265);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(85893);



var _excluded = ["fieldProps", "proFieldProps"],
  _excluded2 = ["fieldProps", "proFieldProps"];







var valueType = 'text';
/**
 * \u6587\u672C\u7EC4\u4EF6
 *
 * @param
 */
var ProFormText = function ProFormText(_ref) {
  var fieldProps = _ref.fieldProps,
    proFieldProps = _ref.proFieldProps,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref, _excluded);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
    valueType: valueType,
    fieldProps: fieldProps,
    filedConfig: {
      valueType: valueType
    },
    proFieldProps: proFieldProps
  }, rest));
};
var PasssWordStrength = function PasssWordStrength(props) {
  var _useMountMergeState = (0,_ant_design_pro_utils__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .Z)(props.open || false, {
      value: props.open,
      onChange: props.onOpenChange
    }),
    _useMountMergeState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(_useMountMergeState, 2),
    open = _useMountMergeState2[0],
    setOpen = _useMountMergeState2[1];
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Z.Item, {
    shouldUpdate: true,
    noStyle: true,
    children: function children(form) {
      var _props$statusRender;
      var value = form.getFieldValue(props.name || []);
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
        getPopupContainer: function getPopupContainer(node) {
          if (node && node.parentNode) {
            return node.parentNode;
          }
          return node;
        },
        onOpenChange: setOpen,
        content: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)("div", {
          style: {
            padding: '4px 0'
          },
          children: [(_props$statusRender = props.statusRender) === null || _props$statusRender === void 0 ? void 0 : _props$statusRender.call(props, value), props.strengthText ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("div", {
            style: {
              marginTop: 10
            },
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)("span", {
              children: props.strengthText
            })
          }) : null]
        }),
        overlayStyle: {
          width: 240
        },
        placement: "right"
      }, props.popoverProps), {}, {
        open: open,
        children: props.children
      }));
    }
  });
};
var Password = function Password(_ref2) {
  var fieldProps = _ref2.fieldProps,
    proFieldProps = _ref2.proFieldProps,
    rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(_ref2, _excluded2);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Z)(_useState, 2),
    open = _useState2[0],
    setOpen = _useState2[1];
  if (fieldProps !== null && fieldProps !== void 0 && fieldProps.statusRender && rest.name) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PasssWordStrength, {
      name: rest.name,
      statusRender: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.statusRender,
      popoverProps: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.popoverProps,
      strengthText: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.strengthText,
      open: open,
      onOpenChange: setOpen,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
        valueType: "password",
        fieldProps: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({}, (0,omit_js__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Z)(fieldProps, ['statusRender', 'popoverProps', 'strengthText'])), {}, {
          onBlur: function onBlur(e) {
            var _fieldProps$onBlur;
            fieldProps === null || fieldProps === void 0 || (_fieldProps$onBlur = fieldProps.onBlur) === null || _fieldProps$onBlur === void 0 || _fieldProps$onBlur.call(fieldProps, e);
            setOpen(false);
          },
          onClick: function onClick(e) {
            var _fieldProps$onClick;
            fieldProps === null || fieldProps === void 0 || (_fieldProps$onClick = fieldProps.onClick) === null || _fieldProps$onClick === void 0 || _fieldProps$onClick.call(fieldProps, e);
            setOpen(true);
          }
        }),
        proFieldProps: proFieldProps,
        filedConfig: {
          valueType: valueType
        }
      }, rest))
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_Field__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z)({
    valueType: "password",
    fieldProps: fieldProps,
    proFieldProps: proFieldProps,
    filedConfig: {
      valueType: valueType
    }
  }, rest));
};
var WrappedProFormText = ProFormText;
WrappedProFormText.Password = Password;

// @ts-ignore
// eslint-disable-next-line no-param-reassign
WrappedProFormText.displayName = 'ProFormComponent';
/* harmony default export */ __webpack_exports__.Z = (WrappedProFormText);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///5966
`)},9146:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ ImgCrop; }
});

// EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs
var tslib_es6 = __webpack_require__(97582);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
// EXTERNAL MODULE: ./node_modules/antd/es/version/index.js + 1 modules
var version = __webpack_require__(67159);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/upload/index.js + 26 modules
var upload = __webpack_require__(78367);
;// CONCATENATED MODULE: ./node_modules/compare-versions/lib/esm/utils.js
const semver = /^[v^~<>=]*?(\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+))?(?:-([\\da-z\\-]+(?:\\.[\\da-z\\-]+)*))?(?:\\+[\\da-z\\-]+(?:\\.[\\da-z\\-]+)*)?)?)?$/i;
const validateAndParse = (version) => {
    if (typeof version !== 'string') {
        throw new TypeError('Invalid argument expected string');
    }
    const match = version.match(semver);
    if (!match) {
        throw new Error(\`Invalid argument not valid semver ('\${version}' received)\`);
    }
    match.shift();
    return match;
};
const isWildcard = (s) => s === '*' || s === 'x' || s === 'X';
const tryParse = (v) => {
    const n = parseInt(v, 10);
    return isNaN(n) ? v : n;
};
const forceType = (a, b) => typeof a !== typeof b ? [String(a), String(b)] : [a, b];
const compareStrings = (a, b) => {
    if (isWildcard(a) || isWildcard(b))
        return 0;
    const [ap, bp] = forceType(tryParse(a), tryParse(b));
    if (ap > bp)
        return 1;
    if (ap < bp)
        return -1;
    return 0;
};
const compareSegments = (a, b) => {
    for (let i = 0; i < Math.max(a.length, b.length); i++) {
        const r = compareStrings(a[i] || '0', b[i] || '0');
        if (r !== 0)
            return r;
    }
    return 0;
};
//# sourceMappingURL=utils.js.map
;// CONCATENATED MODULE: ./node_modules/compare-versions/lib/esm/compareVersions.js

/**
 * Compare [semver](https://semver.org/) version strings to find greater, equal or lesser.
 * This library supports the full semver specification, including comparing versions with different number of digits like \`1.0.0\`, \`1.0\`, \`1\`, and pre-release versions like \`1.0.0-alpha\`.
 * @param v1 - First version to compare
 * @param v2 - Second version to compare
 * @returns Numeric value compatible with the [Array.sort(fn) interface](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#Parameters).
 */
const compareVersions = (v1, v2) => {
    // validate input and split into segments
    const n1 = validateAndParse(v1);
    const n2 = validateAndParse(v2);
    // pop off the patch
    const p1 = n1.pop();
    const p2 = n2.pop();
    // validate numbers
    const r = compareSegments(n1, n2);
    if (r !== 0)
        return r;
    // validate pre-release
    if (p1 && p2) {
        return compareSegments(p1.split('.'), p2.split('.'));
    }
    else if (p1 || p2) {
        return p1 ? -1 : 1;
    }
    return 0;
};
//# sourceMappingURL=compareVersions.js.map
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/antd/es/slider/index.js + 16 modules
var slider = __webpack_require__(71338);
;// CONCATENATED MODULE: ./node_modules/react-easy-crop/node_modules/tslib/tslib.es6.js
/*! *****************************************************************************\r
Copyright (c) Microsoft Corporation.\r
\r
Permission to use, copy, modify, and/or distribute this software for any\r
purpose with or without fee is hereby granted.\r
\r
THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r
PERFORMANCE OF THIS SOFTWARE.\r
***************************************************************************** */\r
/* global Reflect, Promise */\r
\r
var extendStatics = function(d, b) {\r
    extendStatics = Object.setPrototypeOf ||\r
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r
    return extendStatics(d, b);\r
};\r
\r
function __extends(d, b) {\r
    extendStatics(d, b);\r
    function __() { this.constructor = d; }\r
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r
}\r
\r
var __assign = function() {\r
    __assign = Object.assign || function __assign(t) {\r
        for (var s, i = 1, n = arguments.length; i < n; i++) {\r
            s = arguments[i];\r
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r
        }\r
        return t;\r
    }\r
    return __assign.apply(this, arguments);\r
}\r
\r
function __rest(s, e) {\r
    var t = {};\r
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r
        t[p] = s[p];\r
    if (s != null && typeof Object.getOwnPropertySymbols === "function")\r
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r
                t[p[i]] = s[p[i]];\r
        }\r
    return t;\r
}\r
\r
function __decorate(decorators, target, key, desc) {\r
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);\r
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r
    return c > 3 && r && Object.defineProperty(target, key, r), r;\r
}\r
\r
function __param(paramIndex, decorator) {\r
    return function (target, key) { decorator(target, key, paramIndex); }\r
}\r
\r
function __metadata(metadataKey, metadataValue) {\r
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);\r
}\r
\r
function __awaiter(thisArg, _arguments, P, generator) {\r
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r
    return new (P || (P = Promise))(function (resolve, reject) {\r
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }\r
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r
        step((generator = generator.apply(thisArg, _arguments || [])).next());\r
    });\r
}\r
\r
function __generator(thisArg, body) {\r
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;\r
    function verb(n) { return function (v) { return step([n, v]); }; }\r
    function step(op) {\r
        if (f) throw new TypeError("Generator is already executing.");\r
        while (_) try {\r
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r
            if (y = 0, t) op = [op[0] & 2, t.value];\r
            switch (op[0]) {\r
                case 0: case 1: t = op; break;\r
                case 4: _.label++; return { value: op[1], done: false };\r
                case 5: _.label++; y = op[1]; op = [0]; continue;\r
                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r
                default:\r
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r
                    if (t[2]) _.ops.pop();\r
                    _.trys.pop(); continue;\r
            }\r
            op = body.call(thisArg, _);\r
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r
    }\r
}\r
\r
var __createBinding = Object.create ? (function(o, m, k, k2) {\r
    if (k2 === undefined) k2 = k;\r
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r
}) : (function(o, m, k, k2) {\r
    if (k2 === undefined) k2 = k;\r
    o[k2] = m[k];\r
});\r
\r
function __exportStar(m, o) {\r
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r
}\r
\r
function __values(o) {\r
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;\r
    if (m) return m.call(o);\r
    if (o && typeof o.length === "number") return {\r
        next: function () {\r
            if (o && i >= o.length) o = void 0;\r
            return { value: o && o[i++], done: !o };\r
        }\r
    };\r
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");\r
}\r
\r
function __read(o, n) {\r
    var m = typeof Symbol === "function" && o[Symbol.iterator];\r
    if (!m) return o;\r
    var i = m.call(o), r, ar = [], e;\r
    try {\r
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r
    }\r
    catch (error) { e = { error: error }; }\r
    finally {\r
        try {\r
            if (r && !r.done && (m = i["return"])) m.call(i);\r
        }\r
        finally { if (e) throw e.error; }\r
    }\r
    return ar;\r
}\r
\r
function __spread() {\r
    for (var ar = [], i = 0; i < arguments.length; i++)\r
        ar = ar.concat(__read(arguments[i]));\r
    return ar;\r
}\r
\r
function __spreadArrays() {\r
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r
    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r
            r[k] = a[j];\r
    return r;\r
};\r
\r
function __await(v) {\r
    return this instanceof __await ? (this.v = v, this) : new __await(v);\r
}\r
\r
function __asyncGenerator(thisArg, _arguments, generator) {\r
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");\r
    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r
    return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i;\r
    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r
    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r
    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r
    function fulfill(value) { resume("next", value); }\r
    function reject(value) { resume("throw", value); }\r
    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r
}\r
\r
function __asyncDelegator(o) {\r
    var i, p;\r
    return i = {}, verb("next"), verb("throw", function (e) { throw e; }), verb("return"), i[Symbol.iterator] = function () { return this; }, i;\r
    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === "return" } : f ? f(v) : v; } : f; }\r
}\r
\r
function __asyncValues(o) {\r
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");\r
    var m = o[Symbol.asyncIterator], i;\r
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);\r
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r
}\r
\r
function __makeTemplateObject(cooked, raw) {\r
    if (Object.defineProperty) { Object.defineProperty(cooked, "raw", { value: raw }); } else { cooked.raw = raw; }\r
    return cooked;\r
};\r
\r
var __setModuleDefault = Object.create ? (function(o, v) {\r
    Object.defineProperty(o, "default", { enumerable: true, value: v });\r
}) : function(o, v) {\r
    o["default"] = v;\r
};\r
\r
function __importStar(mod) {\r
    if (mod && mod.__esModule) return mod;\r
    var result = {};\r
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r
    __setModuleDefault(result, mod);\r
    return result;\r
}\r
\r
function __importDefault(mod) {\r
    return (mod && mod.__esModule) ? mod : { default: mod };\r
}\r
\r
function __classPrivateFieldGet(receiver, privateMap) {\r
    if (!privateMap.has(receiver)) {\r
        throw new TypeError("attempted to get private field on non-instance");\r
    }\r
    return privateMap.get(receiver);\r
}\r
\r
function __classPrivateFieldSet(receiver, privateMap, value) {\r
    if (!privateMap.has(receiver)) {\r
        throw new TypeError("attempted to set private field on non-instance");\r
    }\r
    privateMap.set(receiver, value);\r
    return value;\r
}\r

// EXTERNAL MODULE: ./node_modules/normalize-wheel/index.js
var normalize_wheel = __webpack_require__(52796);
var normalize_wheel_default = /*#__PURE__*/__webpack_require__.n(normalize_wheel);
;// CONCATENATED MODULE: ./node_modules/react-easy-crop/index.module.js




/**\r
 * Compute the dimension of the crop area based on media size,\r
 * aspect ratio and optionally rotation\r
 */
function getCropSize(mediaWidth, mediaHeight, containerWidth, containerHeight, aspect, rotation) {
  if (rotation === void 0) {
    rotation = 0;
  }
  var _a = rotateSize(mediaWidth, mediaHeight, rotation),
    width = _a.width,
    height = _a.height;
  var fittingWidth = Math.min(width, containerWidth);
  var fittingHeight = Math.min(height, containerHeight);
  if (fittingWidth > fittingHeight * aspect) {
    return {
      width: fittingHeight * aspect,
      height: fittingHeight
    };
  }
  return {
    width: fittingWidth,
    height: fittingWidth / aspect
  };
}
/**\r
 * Compute media zoom.\r
 * We fit the media into the container with "max-width: 100%; max-height: 100%;"\r
 */
function getMediaZoom(mediaSize) {
  // Take the axis with more pixels to improve accuracy
  return mediaSize.width > mediaSize.height ? mediaSize.width / mediaSize.naturalWidth : mediaSize.height / mediaSize.naturalHeight;
}
/**\r
 * Ensure a new media position stays in the crop area.\r
 */
function restrictPosition(position, mediaSize, cropSize, zoom, rotation) {
  if (rotation === void 0) {
    rotation = 0;
  }
  var _a = rotateSize(mediaSize.width, mediaSize.height, rotation),
    width = _a.width,
    height = _a.height;
  return {
    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),
    y: restrictPositionCoord(position.y, height, cropSize.height, zoom)
  };
}
function restrictPositionCoord(position, mediaSize, cropSize, zoom) {
  var maxPosition = mediaSize * zoom / 2 - cropSize / 2;
  return clamp(position, -maxPosition, maxPosition);
}
function getDistanceBetweenPoints(pointA, pointB) {
  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2));
}
function getRotationBetweenPoints(pointA, pointB) {
  return Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180 / Math.PI;
}
/**\r
 * Compute the output cropped area of the media in percentages and pixels.\r
 * x/y are the top-left coordinates on the src media\r
 */
function computeCroppedArea(crop, mediaSize, cropSize, aspect, zoom, rotation, restrictPosition) {
  if (rotation === void 0) {
    rotation = 0;
  }
  if (restrictPosition === void 0) {
    restrictPosition = true;
  }
  // if the media is rotated by the user, we cannot limit the position anymore
  // as it might need to be negative.
  var limitAreaFn = restrictPosition ? limitArea : noOp;
  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);
  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);
  // calculate the crop area in percentages
  // in the rotated space
  var croppedAreaPercentages = {
    x: limitAreaFn(100, ((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width * 100),
    y: limitAreaFn(100, ((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) / mediaBBoxSize.height * 100),
    width: limitAreaFn(100, cropSize.width / mediaBBoxSize.width * 100 / zoom),
    height: limitAreaFn(100, cropSize.height / mediaBBoxSize.height * 100 / zoom)
  };
  // we compute the pixels size naively
  var widthInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.width, croppedAreaPercentages.width * mediaNaturalBBoxSize.width / 100));
  var heightInPixels = Math.round(limitAreaFn(mediaNaturalBBoxSize.height, croppedAreaPercentages.height * mediaNaturalBBoxSize.height / 100));
  var isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect;
  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)
  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height
  // thus we want to compute the width from the height and aspect for accuracy.
  // Otherwise, we compute the height from width and aspect.
  var sizePixels = isImgWiderThanHigh ? {
    width: Math.round(heightInPixels * aspect),
    height: heightInPixels
  } : {
    width: widthInPixels,
    height: Math.round(widthInPixels / aspect)
  };
  var croppedAreaPixels = __assign(__assign({}, sizePixels), {
    x: Math.round(limitAreaFn(mediaNaturalBBoxSize.width - sizePixels.width, croppedAreaPercentages.x * mediaNaturalBBoxSize.width / 100)),
    y: Math.round(limitAreaFn(mediaNaturalBBoxSize.height - sizePixels.height, croppedAreaPercentages.y * mediaNaturalBBoxSize.height / 100))
  });
  return {
    croppedAreaPercentages: croppedAreaPercentages,
    croppedAreaPixels: croppedAreaPixels
  };
}
/**\r
 * Ensure the returned value is between 0 and max\r
 */
function limitArea(max, value) {
  return Math.min(max, Math.max(0, value));
}
function noOp(_max, value) {
  return value;
}
/**\r
 * Compute crop and zoom from the croppedAreaPercentages.\r
 */
function getInitialCropFromCroppedAreaPercentages(croppedAreaPercentages, mediaSize, rotation, cropSize, minZoom, maxZoom) {
  var mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation);
  // This is the inverse process of computeCroppedArea
  var zoom = clamp(cropSize.width / mediaBBoxSize.width * (100 / croppedAreaPercentages.width), minZoom, maxZoom);
  var crop = {
    x: zoom * mediaBBoxSize.width / 2 - cropSize.width / 2 - mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),
    y: zoom * mediaBBoxSize.height / 2 - cropSize.height / 2 - mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100)
  };
  return {
    crop: crop,
    zoom: zoom
  };
}
/**\r
 * Compute zoom from the croppedAreaPixels\r
 */
function getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize) {
  var mediaZoom = getMediaZoom(mediaSize);
  return cropSize.height > cropSize.width ? cropSize.height / (croppedAreaPixels.height * mediaZoom) : cropSize.width / (croppedAreaPixels.width * mediaZoom);
}
/**\r
 * Compute crop and zoom from the croppedAreaPixels\r
 */
function getInitialCropFromCroppedAreaPixels(croppedAreaPixels, mediaSize, rotation, cropSize, minZoom, maxZoom) {
  if (rotation === void 0) {
    rotation = 0;
  }
  var mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation);
  var zoom = clamp(getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize), minZoom, maxZoom);
  var cropZoom = cropSize.height > cropSize.width ? cropSize.height / croppedAreaPixels.height : cropSize.width / croppedAreaPixels.width;
  var crop = {
    x: ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,
    y: ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) * cropZoom
  };
  return {
    crop: crop,
    zoom: zoom
  };
}
/**\r
 * Return the point that is the center of point a and b\r
 */
function getCenter(a, b) {
  return {
    x: (b.x + a.x) / 2,
    y: (b.y + a.y) / 2
  };
}
function getRadianAngle(degreeValue) {
  return degreeValue * Math.PI / 180;
}
/**\r
 * Returns the new bounding area of a rotated rectangle.\r
 */
function rotateSize(width, height, rotation) {
  var rotRad = getRadianAngle(rotation);
  return {
    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),
    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height)
  };
}
/**\r
 * Clamp value between min and max\r
 */
function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}
/**\r
 * Combine multiple class names into a single string.\r
 */
function classNames() {
  var args = [];
  for (var _i = 0; _i < arguments.length; _i++) {
    args[_i] = arguments[_i];
  }
  return args.filter(function (value) {
    if (typeof value === 'string' && value.length > 0) {
      return true;
    }
    return false;
  }).join(' ').trim();
}

var css_248z = ".reactEasyCrop_Container {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  overflow: hidden;\\n  user-select: none;\\n  touch-action: none;\\n  cursor: move;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.reactEasyCrop_Image,\\n.reactEasyCrop_Video {\\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\\n}\\n\\n.reactEasyCrop_Contain {\\n  max-width: 100%;\\n  max-height: 100%;\\n  margin: auto;\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n.reactEasyCrop_Cover_Horizontal {\\n  width: 100%;\\n  height: auto;\\n}\\n.reactEasyCrop_Cover_Vertical {\\n  width: auto;\\n  height: 100%;\\n}\\n\\n.reactEasyCrop_CropArea {\\n  position: absolute;\\n  left: 50%;\\n  top: 50%;\\n  transform: translate(-50%, -50%);\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  box-sizing: border-box;\\n  box-shadow: 0 0 0 9999em;\\n  color: rgba(0, 0, 0, 0.5);\\n  overflow: hidden;\\n}\\n\\n.reactEasyCrop_CropAreaRound {\\n  border-radius: 50%;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::before {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 0;\\n  bottom: 0;\\n  left: 33.33%;\\n  right: 33.33%;\\n  border-top: 0;\\n  border-bottom: 0;\\n}\\n\\n.reactEasyCrop_CropAreaGrid::after {\\n  content: ' ';\\n  box-sizing: border-box;\\n  position: absolute;\\n  border: 1px solid rgba(255, 255, 255, 0.5);\\n  top: 33.33%;\\n  bottom: 33.33%;\\n  left: 0;\\n  right: 0;\\n  border-left: 0;\\n  border-right: 0;\\n}\\n";

var MIN_ZOOM = 1;
var MAX_ZOOM = 3;
var Cropper = /** @class */function (_super) {
  __extends(Cropper, _super);
  function Cropper() {
    var _this = _super !== null && _super.apply(this, arguments) || this;
    _this.imageRef = react.createRef();
    _this.videoRef = react.createRef();
    _this.containerPosition = {
      x: 0,
      y: 0
    };
    _this.containerRef = null;
    _this.styleRef = null;
    _this.containerRect = null;
    _this.mediaSize = {
      width: 0,
      height: 0,
      naturalWidth: 0,
      naturalHeight: 0
    };
    _this.dragStartPosition = {
      x: 0,
      y: 0
    };
    _this.dragStartCrop = {
      x: 0,
      y: 0
    };
    _this.gestureZoomStart = 0;
    _this.gestureRotationStart = 0;
    _this.isTouching = false;
    _this.lastPinchDistance = 0;
    _this.lastPinchRotation = 0;
    _this.rafDragTimeout = null;
    _this.rafPinchTimeout = null;
    _this.wheelTimer = null;
    _this.currentDoc = typeof document !== 'undefined' ? document : null;
    _this.currentWindow = typeof window !== 'undefined' ? window : null;
    _this.resizeObserver = null;
    _this.state = {
      cropSize: null,
      hasWheelJustStarted: false,
      mediaObjectFit: undefined
    };
    _this.initResizeObserver = function () {
      if (typeof window.ResizeObserver === 'undefined' || !_this.containerRef) {
        return;
      }
      var isFirstResize = true;
      _this.resizeObserver = new window.ResizeObserver(function (entries) {
        if (isFirstResize) {
          isFirstResize = false; // observe() is called on mount, we don't want to trigger a recompute on mount
          return;
        }
        _this.computeSizes();
      });
      _this.resizeObserver.observe(_this.containerRef);
    };
    // this is to prevent Safari on iOS >= 10 to zoom the page
    _this.preventZoomSafari = function (e) {
      return e.preventDefault();
    };
    _this.cleanEvents = function () {
      if (!_this.currentDoc) return;
      _this.currentDoc.removeEventListener('mousemove', _this.onMouseMove);
      _this.currentDoc.removeEventListener('mouseup', _this.onDragStopped);
      _this.currentDoc.removeEventListener('touchmove', _this.onTouchMove);
      _this.currentDoc.removeEventListener('touchend', _this.onDragStopped);
      _this.currentDoc.removeEventListener('gesturemove', _this.onGestureMove);
      _this.currentDoc.removeEventListener('gestureend', _this.onGestureEnd);
      _this.currentDoc.removeEventListener('scroll', _this.onScroll);
    };
    _this.clearScrollEvent = function () {
      if (_this.containerRef) _this.containerRef.removeEventListener('wheel', _this.onWheel);
      if (_this.wheelTimer) {
        clearTimeout(_this.wheelTimer);
      }
    };
    _this.onMediaLoad = function () {
      var cropSize = _this.computeSizes();
      if (cropSize) {
        _this.emitCropData();
        _this.setInitialCrop(cropSize);
      }
      if (_this.props.onMediaLoaded) {
        _this.props.onMediaLoaded(_this.mediaSize);
      }
    };
    _this.setInitialCrop = function (cropSize) {
      if (_this.props.initialCroppedAreaPercentages) {
        var _a = getInitialCropFromCroppedAreaPercentages(_this.props.initialCroppedAreaPercentages, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),
          crop = _a.crop,
          zoom = _a.zoom;
        _this.props.onCropChange(crop);
        _this.props.onZoomChange && _this.props.onZoomChange(zoom);
      } else if (_this.props.initialCroppedAreaPixels) {
        var _b = getInitialCropFromCroppedAreaPixels(_this.props.initialCroppedAreaPixels, _this.mediaSize, _this.props.rotation, cropSize, _this.props.minZoom, _this.props.maxZoom),
          crop = _b.crop,
          zoom = _b.zoom;
        _this.props.onCropChange(crop);
        _this.props.onZoomChange && _this.props.onZoomChange(zoom);
      }
    };
    _this.computeSizes = function () {
      var _a, _b, _c, _d, _e, _f;
      var mediaRef = _this.imageRef.current || _this.videoRef.current;
      if (mediaRef && _this.containerRef) {
        _this.containerRect = _this.containerRef.getBoundingClientRect();
        _this.saveContainerPosition();
        var containerAspect = _this.containerRect.width / _this.containerRect.height;
        var naturalWidth = ((_a = _this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = _this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;
        var naturalHeight = ((_c = _this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = _this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;
        var isMediaScaledDown = mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight;
        var mediaAspect = naturalWidth / naturalHeight;
        // We do not rely on the offsetWidth/offsetHeight if the media is scaled down
        // as the values they report are rounded. That will result in precision losses
        // when calculating zoom. We use the fact that the media is positionned relative
        // to the container. That allows us to use the container's dimensions
        // and natural aspect ratio of the media to calculate accurate media size.
        // However, for this to work, the container should not be rotated
        var renderedMediaSize = void 0;
        if (isMediaScaledDown) {
          switch (_this.state.mediaObjectFit) {
            default:
            case 'contain':
              renderedMediaSize = containerAspect > mediaAspect ? {
                width: _this.containerRect.height * mediaAspect,
                height: _this.containerRect.height
              } : {
                width: _this.containerRect.width,
                height: _this.containerRect.width / mediaAspect
              };
              break;
            case 'horizontal-cover':
              renderedMediaSize = {
                width: _this.containerRect.width,
                height: _this.containerRect.width / mediaAspect
              };
              break;
            case 'vertical-cover':
              renderedMediaSize = {
                width: _this.containerRect.height * mediaAspect,
                height: _this.containerRect.height
              };
              break;
          }
        } else {
          renderedMediaSize = {
            width: mediaRef.offsetWidth,
            height: mediaRef.offsetHeight
          };
        }
        _this.mediaSize = __assign(__assign({}, renderedMediaSize), {
          naturalWidth: naturalWidth,
          naturalHeight: naturalHeight
        });
        // set media size in the parent
        if (_this.props.setMediaSize) {
          _this.props.setMediaSize(_this.mediaSize);
        }
        var cropSize = _this.props.cropSize ? _this.props.cropSize : getCropSize(_this.mediaSize.width, _this.mediaSize.height, _this.containerRect.width, _this.containerRect.height, _this.props.aspect, _this.props.rotation);
        if (((_e = _this.state.cropSize) === null || _e === void 0 ? void 0 : _e.height) !== cropSize.height || ((_f = _this.state.cropSize) === null || _f === void 0 ? void 0 : _f.width) !== cropSize.width) {
          _this.props.onCropSizeChange && _this.props.onCropSizeChange(cropSize);
        }
        _this.setState({
          cropSize: cropSize
        }, _this.recomputeCropPosition);
        // pass crop size to parent
        if (_this.props.setCropSize) {
          _this.props.setCropSize(cropSize);
        }
        return cropSize;
      }
    };
    _this.saveContainerPosition = function () {
      if (_this.containerRef) {
        var bounds = _this.containerRef.getBoundingClientRect();
        _this.containerPosition = {
          x: bounds.left,
          y: bounds.top
        };
      }
    };
    _this.onMouseDown = function (e) {
      if (!_this.currentDoc) return;
      e.preventDefault();
      _this.currentDoc.addEventListener('mousemove', _this.onMouseMove);
      _this.currentDoc.addEventListener('mouseup', _this.onDragStopped);
      _this.saveContainerPosition();
      _this.onDragStart(Cropper.getMousePoint(e));
    };
    _this.onMouseMove = function (e) {
      return _this.onDrag(Cropper.getMousePoint(e));
    };
    _this.onScroll = function (e) {
      if (!_this.currentDoc) return;
      e.preventDefault();
      _this.saveContainerPosition();
    };
    _this.onTouchStart = function (e) {
      if (!_this.currentDoc) return;
      _this.isTouching = true;
      if (_this.props.onTouchRequest && !_this.props.onTouchRequest(e)) {
        return;
      }
      _this.currentDoc.addEventListener('touchmove', _this.onTouchMove, {
        passive: false
      }); // iOS 11 now defaults to passive: true
      _this.currentDoc.addEventListener('touchend', _this.onDragStopped);
      _this.saveContainerPosition();
      if (e.touches.length === 2) {
        _this.onPinchStart(e);
      } else if (e.touches.length === 1) {
        _this.onDragStart(Cropper.getTouchPoint(e.touches[0]));
      }
    };
    _this.onTouchMove = function (e) {
      // Prevent whole page from scrolling on iOS.
      e.preventDefault();
      if (e.touches.length === 2) {
        _this.onPinchMove(e);
      } else if (e.touches.length === 1) {
        _this.onDrag(Cropper.getTouchPoint(e.touches[0]));
      }
    };
    _this.onGestureStart = function (e) {
      if (!_this.currentDoc) return;
      e.preventDefault();
      _this.currentDoc.addEventListener('gesturechange', _this.onGestureMove);
      _this.currentDoc.addEventListener('gestureend', _this.onGestureEnd);
      _this.gestureZoomStart = _this.props.zoom;
      _this.gestureRotationStart = _this.props.rotation;
    };
    _this.onGestureMove = function (e) {
      e.preventDefault();
      if (_this.isTouching) {
        // this is to avoid conflict between gesture and touch events
        return;
      }
      var point = Cropper.getMousePoint(e);
      var newZoom = _this.gestureZoomStart - 1 + e.scale;
      _this.setNewZoom(newZoom, point, {
        shouldUpdatePosition: true
      });
      if (_this.props.onRotationChange) {
        var newRotation = _this.gestureRotationStart + e.rotation;
        _this.props.onRotationChange(newRotation);
      }
    };
    _this.onGestureEnd = function (e) {
      _this.cleanEvents();
    };
    _this.onDragStart = function (_a) {
      var _b, _c;
      var x = _a.x,
        y = _a.y;
      _this.dragStartPosition = {
        x: x,
        y: y
      };
      _this.dragStartCrop = __assign({}, _this.props.crop);
      (_c = (_b = _this.props).onInteractionStart) === null || _c === void 0 ? void 0 : _c.call(_b);
    };
    _this.onDrag = function (_a) {
      var x = _a.x,
        y = _a.y;
      if (!_this.currentWindow) return;
      if (_this.rafDragTimeout) _this.currentWindow.cancelAnimationFrame(_this.rafDragTimeout);
      _this.rafDragTimeout = _this.currentWindow.requestAnimationFrame(function () {
        if (!_this.state.cropSize) return;
        if (x === undefined || y === undefined) return;
        var offsetX = x - _this.dragStartPosition.x;
        var offsetY = y - _this.dragStartPosition.y;
        var requestedPosition = {
          x: _this.dragStartCrop.x + offsetX,
          y: _this.dragStartCrop.y + offsetY
        };
        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : requestedPosition;
        _this.props.onCropChange(newPosition);
      });
    };
    _this.onDragStopped = function () {
      var _a, _b;
      _this.isTouching = false;
      _this.cleanEvents();
      _this.emitCropData();
      (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);
    };
    _this.onWheel = function (e) {
      if (!_this.currentWindow) return;
      if (_this.props.onWheelRequest && !_this.props.onWheelRequest(e)) {
        return;
      }
      e.preventDefault();
      var point = Cropper.getMousePoint(e);
      var pixelY = normalize_wheel_default()(e).pixelY;
      var newZoom = _this.props.zoom - pixelY * _this.props.zoomSpeed / 200;
      _this.setNewZoom(newZoom, point, {
        shouldUpdatePosition: true
      });
      if (!_this.state.hasWheelJustStarted) {
        _this.setState({
          hasWheelJustStarted: true
        }, function () {
          var _a, _b;
          return (_b = (_a = _this.props).onInteractionStart) === null || _b === void 0 ? void 0 : _b.call(_a);
        });
      }
      if (_this.wheelTimer) {
        clearTimeout(_this.wheelTimer);
      }
      _this.wheelTimer = _this.currentWindow.setTimeout(function () {
        return _this.setState({
          hasWheelJustStarted: false
        }, function () {
          var _a, _b;
          return (_b = (_a = _this.props).onInteractionEnd) === null || _b === void 0 ? void 0 : _b.call(_a);
        });
      }, 250);
    };
    _this.getPointOnContainer = function (_a, containerTopLeft) {
      var x = _a.x,
        y = _a.y;
      if (!_this.containerRect) {
        throw new Error('The Cropper is not mounted');
      }
      return {
        x: _this.containerRect.width / 2 - (x - containerTopLeft.x),
        y: _this.containerRect.height / 2 - (y - containerTopLeft.y)
      };
    };
    _this.getPointOnMedia = function (_a) {
      var x = _a.x,
        y = _a.y;
      var _b = _this.props,
        crop = _b.crop,
        zoom = _b.zoom;
      return {
        x: (x + crop.x) / zoom,
        y: (y + crop.y) / zoom
      };
    };
    _this.setNewZoom = function (zoom, point, _a) {
      var _b = _a === void 0 ? {} : _a,
        _c = _b.shouldUpdatePosition,
        shouldUpdatePosition = _c === void 0 ? true : _c;
      if (!_this.state.cropSize || !_this.props.onZoomChange) return;
      var newZoom = clamp(zoom, _this.props.minZoom, _this.props.maxZoom);
      if (shouldUpdatePosition) {
        var zoomPoint = _this.getPointOnContainer(point, _this.containerPosition);
        var zoomTarget = _this.getPointOnMedia(zoomPoint);
        var requestedPosition = {
          x: zoomTarget.x * newZoom - zoomPoint.x,
          y: zoomTarget.y * newZoom - zoomPoint.y
        };
        var newPosition = _this.props.restrictPosition ? restrictPosition(requestedPosition, _this.mediaSize, _this.state.cropSize, newZoom, _this.props.rotation) : requestedPosition;
        _this.props.onCropChange(newPosition);
      }
      _this.props.onZoomChange(newZoom);
    };
    _this.getCropData = function () {
      if (!_this.state.cropSize) {
        return null;
      }
      // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)
      var restrictedPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;
      return computeCroppedArea(restrictedPosition, _this.mediaSize, _this.state.cropSize, _this.getAspect(), _this.props.zoom, _this.props.rotation, _this.props.restrictPosition);
    };
    _this.emitCropData = function () {
      var cropData = _this.getCropData();
      if (!cropData) return;
      var croppedAreaPercentages = cropData.croppedAreaPercentages,
        croppedAreaPixels = cropData.croppedAreaPixels;
      if (_this.props.onCropComplete) {
        _this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels);
      }
      if (_this.props.onCropAreaChange) {
        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);
      }
    };
    _this.emitCropAreaChange = function () {
      var cropData = _this.getCropData();
      if (!cropData) return;
      var croppedAreaPercentages = cropData.croppedAreaPercentages,
        croppedAreaPixels = cropData.croppedAreaPixels;
      if (_this.props.onCropAreaChange) {
        _this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels);
      }
    };
    _this.recomputeCropPosition = function () {
      if (!_this.state.cropSize) return;
      var newPosition = _this.props.restrictPosition ? restrictPosition(_this.props.crop, _this.mediaSize, _this.state.cropSize, _this.props.zoom, _this.props.rotation) : _this.props.crop;
      _this.props.onCropChange(newPosition);
      _this.emitCropData();
    };
    return _this;
  }
  Cropper.prototype.componentDidMount = function () {
    if (!this.currentDoc || !this.currentWindow) return;
    if (this.containerRef) {
      if (this.containerRef.ownerDocument) {
        this.currentDoc = this.containerRef.ownerDocument;
      }
      if (this.currentDoc.defaultView) {
        this.currentWindow = this.currentDoc.defaultView;
      }
      this.initResizeObserver();
      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant
      if (typeof window.ResizeObserver === 'undefined') {
        this.currentWindow.addEventListener('resize', this.computeSizes);
      }
      this.props.zoomWithScroll && this.containerRef.addEventListener('wheel', this.onWheel, {
        passive: false
      });
      this.containerRef.addEventListener('gesturestart', this.onGestureStart);
    }
    this.currentDoc.addEventListener('scroll', this.onScroll);
    if (!this.props.disableAutomaticStylesInjection) {
      this.styleRef = this.currentDoc.createElement('style');
      this.styleRef.setAttribute('type', 'text/css');
      if (this.props.nonce) {
        this.styleRef.setAttribute('nonce', this.props.nonce);
      }
      this.styleRef.innerHTML = css_248z;
      this.currentDoc.head.appendChild(this.styleRef);
    }
    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called
    if (this.imageRef.current && this.imageRef.current.complete) {
      this.onMediaLoad();
    }
    // set image and video refs in the parent if the callbacks exist
    if (this.props.setImageRef) {
      this.props.setImageRef(this.imageRef);
    }
    if (this.props.setVideoRef) {
      this.props.setVideoRef(this.videoRef);
    }
  };
  Cropper.prototype.componentWillUnmount = function () {
    var _a, _b;
    if (!this.currentDoc || !this.currentWindow) return;
    if (typeof window.ResizeObserver === 'undefined') {
      this.currentWindow.removeEventListener('resize', this.computeSizes);
    }
    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();
    if (this.containerRef) {
      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari);
    }
    if (this.styleRef) {
      (_b = this.styleRef.parentNode) === null || _b === void 0 ? void 0 : _b.removeChild(this.styleRef);
    }
    this.cleanEvents();
    this.props.zoomWithScroll && this.clearScrollEvent();
  };
  Cropper.prototype.componentDidUpdate = function (prevProps) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    if (prevProps.rotation !== this.props.rotation) {
      this.computeSizes();
      this.recomputeCropPosition();
    } else if (prevProps.aspect !== this.props.aspect) {
      this.computeSizes();
    } else if (prevProps.objectFit !== this.props.objectFit) {
      this.computeSizes();
    } else if (prevProps.zoom !== this.props.zoom) {
      this.recomputeCropPosition();
    } else if (((_a = prevProps.cropSize) === null || _a === void 0 ? void 0 : _a.height) !== ((_b = this.props.cropSize) === null || _b === void 0 ? void 0 : _b.height) || ((_c = prevProps.cropSize) === null || _c === void 0 ? void 0 : _c.width) !== ((_d = this.props.cropSize) === null || _d === void 0 ? void 0 : _d.width)) {
      this.computeSizes();
    } else if (((_e = prevProps.crop) === null || _e === void 0 ? void 0 : _e.x) !== ((_f = this.props.crop) === null || _f === void 0 ? void 0 : _f.x) || ((_g = prevProps.crop) === null || _g === void 0 ? void 0 : _g.y) !== ((_h = this.props.crop) === null || _h === void 0 ? void 0 : _h.y)) {
      this.emitCropAreaChange();
    }
    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {
      this.props.zoomWithScroll ? this.containerRef.addEventListener('wheel', this.onWheel, {
        passive: false
      }) : this.clearScrollEvent();
    }
    if (prevProps.video !== this.props.video) {
      (_j = this.videoRef.current) === null || _j === void 0 ? void 0 : _j.load();
    }
    var objectFit = this.getObjectFit();
    if (objectFit !== this.state.mediaObjectFit) {
      this.setState({
        mediaObjectFit: objectFit
      }, this.computeSizes);
    }
  };
  Cropper.prototype.getAspect = function () {
    var _a = this.props,
      cropSize = _a.cropSize,
      aspect = _a.aspect;
    if (cropSize) {
      return cropSize.width / cropSize.height;
    }
    return aspect;
  };
  Cropper.prototype.getObjectFit = function () {
    var _a, _b, _c, _d;
    if (this.props.objectFit === 'cover') {
      var mediaRef = this.imageRef.current || this.videoRef.current;
      if (mediaRef && this.containerRef) {
        this.containerRect = this.containerRef.getBoundingClientRect();
        var containerAspect = this.containerRect.width / this.containerRect.height;
        var naturalWidth = ((_a = this.imageRef.current) === null || _a === void 0 ? void 0 : _a.naturalWidth) || ((_b = this.videoRef.current) === null || _b === void 0 ? void 0 : _b.videoWidth) || 0;
        var naturalHeight = ((_c = this.imageRef.current) === null || _c === void 0 ? void 0 : _c.naturalHeight) || ((_d = this.videoRef.current) === null || _d === void 0 ? void 0 : _d.videoHeight) || 0;
        var mediaAspect = naturalWidth / naturalHeight;
        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover';
      }
      return 'horizontal-cover';
    }
    return this.props.objectFit;
  };
  Cropper.prototype.onPinchStart = function (e) {
    var pointA = Cropper.getTouchPoint(e.touches[0]);
    var pointB = Cropper.getTouchPoint(e.touches[1]);
    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB);
    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB);
    this.onDragStart(getCenter(pointA, pointB));
  };
  Cropper.prototype.onPinchMove = function (e) {
    var _this = this;
    if (!this.currentDoc || !this.currentWindow) return;
    var pointA = Cropper.getTouchPoint(e.touches[0]);
    var pointB = Cropper.getTouchPoint(e.touches[1]);
    var center = getCenter(pointA, pointB);
    this.onDrag(center);
    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout);
    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(function () {
      var distance = getDistanceBetweenPoints(pointA, pointB);
      var newZoom = _this.props.zoom * (distance / _this.lastPinchDistance);
      _this.setNewZoom(newZoom, center, {
        shouldUpdatePosition: false
      });
      _this.lastPinchDistance = distance;
      var rotation = getRotationBetweenPoints(pointA, pointB);
      var newRotation = _this.props.rotation + (rotation - _this.lastPinchRotation);
      _this.props.onRotationChange && _this.props.onRotationChange(newRotation);
      _this.lastPinchRotation = rotation;
    });
  };
  Cropper.prototype.render = function () {
    var _this = this;
    var _a = this.props,
      image = _a.image,
      video = _a.video,
      mediaProps = _a.mediaProps,
      transform = _a.transform,
      _b = _a.crop,
      x = _b.x,
      y = _b.y,
      rotation = _a.rotation,
      zoom = _a.zoom,
      cropShape = _a.cropShape,
      showGrid = _a.showGrid,
      _c = _a.style,
      containerStyle = _c.containerStyle,
      cropAreaStyle = _c.cropAreaStyle,
      mediaStyle = _c.mediaStyle,
      _d = _a.classes,
      containerClassName = _d.containerClassName,
      cropAreaClassName = _d.cropAreaClassName,
      mediaClassName = _d.mediaClassName;
    var objectFit = this.state.mediaObjectFit;
    return react.createElement("div", {
      onMouseDown: this.onMouseDown,
      onTouchStart: this.onTouchStart,
      ref: function ref(el) {
        return _this.containerRef = el;
      },
      "data-testid": "container",
      style: containerStyle,
      className: classNames('reactEasyCrop_Container', containerClassName)
    }, image ? react.createElement("img", __assign({
      alt: "",
      className: classNames('reactEasyCrop_Image', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)
    }, mediaProps, {
      src: image,
      ref: this.imageRef,
      style: __assign(__assign({}, mediaStyle), {
        transform: transform || "translate(".concat(x, "px, ").concat(y, "px) rotate(").concat(rotation, "deg) scale(").concat(zoom, ")")
      }),
      onLoad: this.onMediaLoad
    })) : video && react.createElement("video", __assign({
      autoPlay: true,
      loop: true,
      muted: true,
      className: classNames('reactEasyCrop_Video', objectFit === 'contain' && 'reactEasyCrop_Contain', objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal', objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical', mediaClassName)
    }, mediaProps, {
      ref: this.videoRef,
      onLoadedMetadata: this.onMediaLoad,
      style: __assign(__assign({}, mediaStyle), {
        transform: transform || "translate(".concat(x, "px, ").concat(y, "px) rotate(").concat(rotation, "deg) scale(").concat(zoom, ")")
      }),
      controls: false
    }), (Array.isArray(video) ? video : [{
      src: video
    }]).map(function (item) {
      return react.createElement("source", __assign({
        key: item.src
      }, item));
    })), this.state.cropSize && react.createElement("div", {
      style: __assign(__assign({}, cropAreaStyle), {
        width: this.state.cropSize.width,
        height: this.state.cropSize.height
      }),
      "data-testid": "cropper",
      className: classNames('reactEasyCrop_CropArea', cropShape === 'round' && 'reactEasyCrop_CropAreaRound', showGrid && 'reactEasyCrop_CropAreaGrid', cropAreaClassName)
    }));
  };
  Cropper.defaultProps = {
    zoom: 1,
    rotation: 0,
    aspect: 4 / 3,
    maxZoom: MAX_ZOOM,
    minZoom: MIN_ZOOM,
    cropShape: 'rect',
    objectFit: 'contain',
    showGrid: true,
    style: {},
    classes: {},
    mediaProps: {},
    zoomSpeed: 1,
    restrictPosition: true,
    zoomWithScroll: true
  };
  Cropper.getMousePoint = function (e) {
    return {
      x: Number(e.clientX),
      y: Number(e.clientY)
    };
  };
  Cropper.getTouchPoint = function (touch) {
    return {
      x: Number(touch.clientX),
      y: Number(touch.clientY)
    };
  };
  return Cropper;
}(react.Component);


//# sourceMappingURL=index.module.js.map

;// CONCATENATED MODULE: ./node_modules/antd-img-crop/dist/antd-img-crop.esm.js











const PREFIX = 'img-crop';
const ZOOM_INITIAL = 1;
const ZOOM_STEP = 0.1;
const ROTATION_INITIAL = 0;
const ROTATION_MIN = -180;
const ROTATION_MAX = 180;
const ROTATION_STEP = 1;
const ASPECT_MIN = 0.5;
const ASPECT_MAX = 2;
const ASPECT_STEP = 0.01;

const EasyCrop = (0,react.forwardRef)((props, ref) => {
    const { cropperRef, zoomSlider, rotationSlider, aspectSlider, showReset, resetBtnText, modalImage, aspect: ASPECT_INITIAL, minZoom, maxZoom, cropShape, showGrid, cropperProps, } = props;
    const [zoom, setZoom] = (0,react.useState)(ZOOM_INITIAL);
    const [rotation, setRotation] = (0,react.useState)(ROTATION_INITIAL);
    const [aspect, setAspect] = (0,react.useState)(ASPECT_INITIAL);
    const isResetActive = zoom !== ZOOM_INITIAL ||
        rotation !== ROTATION_INITIAL ||
        aspect !== ASPECT_INITIAL;
    const onReset = () => {
        setZoom(ZOOM_INITIAL);
        setRotation(ROTATION_INITIAL);
        setAspect(ASPECT_INITIAL);
    };
    const [crop, onCropChange] = (0,react.useState)({ x: 0, y: 0 });
    const cropPixelsRef = (0,react.useRef)({ width: 0, height: 0, x: 0, y: 0 });
    const onCropComplete = (0,react.useCallback)((_, croppedAreaPixels) => {
        cropPixelsRef.current = croppedAreaPixels;
    }, []);
    (0,react.useImperativeHandle)(ref, () => ({
        rotation,
        cropPixelsRef,
        onReset,
    }));
    const wrapperClass = '[display:flex] [align-items:center] [width:60%] [margin-inline:auto]';
    const buttonClass = '[display:flex] [align-items:center] [justify-content:center] [height:32px] [width:32px] [background:transparent] [border:0] [font-family:inherit] [font-size:18px] [cursor:pointer] disabled:[opacity:20%] disabled:[cursor:default]';
    const sliderClass = '[flex:1]';
    return ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [(0,jsx_runtime.jsx)(Cropper, Object.assign({}, cropperProps, { ref: cropperRef, image: modalImage, crop: crop, 
                //
                zoom: zoom, rotation: rotation, aspect: aspect, minZoom: minZoom, maxZoom: maxZoom, zoomWithScroll: zoomSlider, 
                //
                cropShape: cropShape, showGrid: showGrid, onCropChange: onCropChange, onZoomChange: setZoom, onRotationChange: setRotation, onCropComplete: onCropComplete, classes: {
                    containerClassName: \`\${PREFIX}-container ![position:relative] [width:100%] [height:40vh] [&~section:first-of-type]:[margin-top:16px] [&~section:last-of-type]:[margin-bottom:16px]\`,
                    mediaClassName: \`\${PREFIX}-media\`,
                } })), zoomSlider && ((0,jsx_runtime.jsxs)("section", { className: \`\${PREFIX}-control \${PREFIX}-control-zoom \${wrapperClass}\`, children: [(0,jsx_runtime.jsx)("button", { className: buttonClass, onClick: () => setZoom(zoom - ZOOM_STEP), disabled: zoom - ZOOM_STEP < minZoom, children: "\\uFF0D" }), (0,jsx_runtime.jsx)(slider/* default */.Z, { className: sliderClass, min: minZoom, max: maxZoom, step: ZOOM_STEP, value: zoom, onChange: setZoom }), (0,jsx_runtime.jsx)("button", { className: buttonClass, onClick: () => setZoom(zoom + ZOOM_STEP), disabled: zoom + ZOOM_STEP > maxZoom, children: "\\uFF0B" })] })), rotationSlider && ((0,jsx_runtime.jsxs)("section", { className: \`\${PREFIX}-control \${PREFIX}-control-rotation \${wrapperClass}\`, children: [(0,jsx_runtime.jsx)("button", { className: \`\${buttonClass} [font-size:16px]\`, onClick: () => setRotation(rotation - ROTATION_STEP), disabled: rotation === ROTATION_MIN, children: "\\u21BA" }), (0,jsx_runtime.jsx)(slider/* default */.Z, { className: sliderClass, min: ROTATION_MIN, max: ROTATION_MAX, step: ROTATION_STEP, value: rotation, onChange: setRotation }), (0,jsx_runtime.jsx)("button", { className: \`\${buttonClass} [font-size:16px]\`, onClick: () => setRotation(rotation + ROTATION_STEP), disabled: rotation === ROTATION_MAX, children: "\\u21BB" })] })), aspectSlider && ((0,jsx_runtime.jsxs)("section", { className: \`\${PREFIX}-control \${PREFIX}-control-aspect \${wrapperClass}\`, children: [(0,jsx_runtime.jsx)("button", { className: buttonClass, onClick: () => setAspect(aspect - ASPECT_STEP), disabled: aspect - ASPECT_STEP < ASPECT_MIN, children: "\\u2195\\uFE0F" }), (0,jsx_runtime.jsx)(slider/* default */.Z, { className: sliderClass, min: ASPECT_MIN, max: ASPECT_MAX, step: ASPECT_STEP, value: aspect, onChange: setAspect }), (0,jsx_runtime.jsx)("button", { className: buttonClass, onClick: () => setAspect(aspect + ASPECT_STEP), disabled: aspect + ASPECT_STEP > ASPECT_MAX, children: "\\u2194\\uFE0F" })] })), showReset && (zoomSlider || rotationSlider || aspectSlider) && ((0,jsx_runtime.jsx)(es_button/* default */.ZP, { className: "[bottom:20px] [position:absolute]", style: isResetActive ? {} : { opacity: 0.3, pointerEvents: 'none' }, onClick: onReset, children: resetBtnText }))] }));
});
var EasyCrop$1 = (0,react.memo)(EasyCrop);

function styleInject(css, ref) {
  if ( ref === void 0 ) ref = {};
  var insertAt = ref.insertAt;

  if (!css || typeof document === 'undefined') { return; }

  var head = document.head || document.getElementsByTagName('head')[0];
  var style = document.createElement('style');
  style.type = 'text/css';

  if (insertAt === 'top') {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }

  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}

var antd_img_crop_esm_css_248z = ".visible{visibility:visible}.grid{display:grid}.\\\\!\\\\[position\\\\:relative\\\\]{position:relative!important}.\\\\[align-items\\\\:center\\\\]{align-items:center}.\\\\[background\\\\:transparent\\\\]{background:transparent}.\\\\[border\\\\:0\\\\]{border:0}.\\\\[bottom\\\\:20px\\\\]{bottom:20px}.\\\\[cursor\\\\:pointer\\\\]{cursor:pointer}.\\\\[display\\\\:flex\\\\]{display:flex}.\\\\[flex\\\\:1\\\\]{flex:1}.\\\\[font-family\\\\:inherit\\\\]{font-family:inherit}.\\\\[font-size\\\\:16px\\\\]{font-size:16px}.\\\\[font-size\\\\:18px\\\\]{font-size:18px}.\\\\[height\\\\:32px\\\\]{height:32px}.\\\\[height\\\\:40vh\\\\]{height:40vh}.\\\\[justify-content\\\\:center\\\\]{justify-content:center}.\\\\[margin-inline\\\\:auto\\\\]{margin-inline:auto}.\\\\[position\\\\:absolute\\\\]{position:absolute}.\\\\[width\\\\:100\\\\%\\\\]{width:100%}.\\\\[width\\\\:32px\\\\]{width:32px}.\\\\[width\\\\:60\\\\%\\\\]{width:60%}.disabled\\\\:\\\\[cursor\\\\:default\\\\]:disabled{cursor:default}.disabled\\\\:\\\\[opacity\\\\:20\\\\%\\\\]:disabled{opacity:20%}.\\\\[\\\\&\\\\~section\\\\:first-of-type\\\\]\\\\:\\\\[margin-top\\\\:16px\\\\]~section:first-of-type{margin-top:16px}.\\\\[\\\\&\\\\~section\\\\:last-of-type\\\\]\\\\:\\\\[margin-bottom\\\\:16px\\\\]~section:last-of-type{margin-bottom:16px}";
styleInject(antd_img_crop_esm_css_248z,{"insertAt":"top"});

const openProp = compareVersions(version/* default */.Z, '4.23.0') === -1 ? 'visible' : 'open';
const deprecate = (obj, old, now) => {
    if (old in obj) {
        console.error(\`\\\`\${old}\\\` is deprecated, please use \\\`\${now}\\\` instead\`);
        return obj[old];
    }
    return obj[now];
};
const ImgCrop = (0,react.forwardRef)((props, cropperRef) => {
    const { quality = 0.4, fillColor = 'white', 
    // @ts-ignore
    zoomSlider: ZOOM_SLIDER = true, 
    // @ts-ignore
    rotationSlider: ROTATION_SLIDER = false, aspectSlider = false, showReset = false, resetText, aspect = 1, minZoom = 1, maxZoom = 3, 
    // @ts-ignore
    cropShape: CROP_SHAPE = 'rect', 
    // @ts-ignore
    showGrid: SHOW_GRID = false, cropperProps, modalClassName, modalTitle, modalWidth, modalOk, modalCancel, onModalOk, onModalCancel, modalProps, beforeCrop, children, } = props;
    /**
     * init
     */
    const zoomSlider = deprecate(props, 'zoom', 'zoomSlider') || true;
    const rotationSlider = deprecate(props, 'rotate', 'rotationSlider') || false;
    const cropShape = deprecate(props, 'shape', 'cropShape') || 'rect';
    const showGrid = deprecate(props, 'grid', 'showGrid') || false;
    if ('onUploadFail' in props) {
        console.error(\`\\\`onUploadFail\\\` is removed, because the only way it is called, is when the file is rejected by beforeUpload\`);
    }
    deprecate(props, 'modalMaskTransitionName', 'modalProps.maskTransitionName');
    deprecate(props, 'modalTransitionName', 'modalProps.transitionName');
    const cb = (0,react.useRef)({});
    cb.current.onModalOk = onModalOk;
    cb.current.onModalCancel = onModalCancel;
    cb.current.beforeCrop = beforeCrop;
    /**
     * crop
     */
    const easyCropRef = (0,react.useRef)(null);
    const getCropCanvas = (0,react.useCallback)((target) => {
        var _a;
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const context = ((_a = target === null || target === void 0 ? void 0 : target.getRootNode) === null || _a === void 0 ? void 0 : _a.call(target)) || document;
        const imgSource = context.querySelector(\`.\${PREFIX}-media\`);
        const { width: cropWidth, height: cropHeight, x: cropX, y: cropY, } = easyCropRef.current.cropPixelsRef.current;
        if (rotationSlider &&
            easyCropRef.current.rotation !== ROTATION_INITIAL) {
            const { naturalWidth: imgWidth, naturalHeight: imgHeight } = imgSource;
            const angle = easyCropRef.current.rotation * (Math.PI / 180);
            // get container for rotated image
            const sine = Math.abs(Math.sin(angle));
            const cosine = Math.abs(Math.cos(angle));
            const squareWidth = imgWidth * cosine + imgHeight * sine;
            const squareHeight = imgHeight * cosine + imgWidth * sine;
            canvas.width = squareWidth;
            canvas.height = squareHeight;
            ctx.fillStyle = fillColor;
            ctx.fillRect(0, 0, squareWidth, squareHeight);
            // rotate container
            const squareHalfWidth = squareWidth / 2;
            const squareHalfHeight = squareHeight / 2;
            ctx.translate(squareHalfWidth, squareHalfHeight);
            ctx.rotate(angle);
            ctx.translate(-squareHalfWidth, -squareHalfHeight);
            // draw rotated image
            const imgX = (squareWidth - imgWidth) / 2;
            const imgY = (squareHeight - imgHeight) / 2;
            ctx.drawImage(imgSource, 0, 0, imgWidth, imgHeight, imgX, imgY, imgWidth, imgHeight);
            // crop rotated image
            const imgData = ctx.getImageData(0, 0, squareWidth, squareHeight);
            canvas.width = cropWidth;
            canvas.height = cropHeight;
            ctx.putImageData(imgData, -cropX, -cropY);
        }
        else {
            canvas.width = cropWidth;
            canvas.height = cropHeight;
            ctx.fillStyle = fillColor;
            ctx.fillRect(0, 0, cropWidth, cropHeight);
            ctx.drawImage(imgSource, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);
        }
        return canvas;
    }, [fillColor, rotationSlider]);
    /**
     * upload
     */
    const [modalImage, setModalImage] = (0,react.useState)('');
    const onCancel = (0,react.useRef)();
    const onOk = (0,react.useRef)();
    const runBeforeUpload = (0,react.useCallback)(({ beforeUpload, file, resolve, reject, }) => (0,tslib_es6/* __awaiter */.mG)(void 0, void 0, void 0, function* () {
        const rawFile = file;
        if (typeof beforeUpload !== 'function') {
            resolve(rawFile);
            return;
        }
        try {
            // https://ant.design/components/upload-cn#api
            // https://github.com/ant-design/ant-design/blob/master/components/upload/Upload.tsx#L152-L178
            const result = yield beforeUpload(file, [file]);
            if (result === false) {
                resolve(false);
            }
            else {
                resolve((result !== true && result) || rawFile);
            }
        }
        catch (err) {
            reject(err);
        }
    }), []);
    const getNewBeforeUpload = (0,react.useCallback)((beforeUpload) => {
        return ((file, fileList) => {
            return new Promise((resolve, reject) => (0,tslib_es6/* __awaiter */.mG)(void 0, void 0, void 0, function* () {
                let processedFile = file;
                if (typeof cb.current.beforeCrop === 'function') {
                    try {
                        const result = yield cb.current.beforeCrop(file, fileList);
                        if (result === false) {
                            return runBeforeUpload({ beforeUpload, file, resolve, reject }); // not open modal
                        }
                        if (result !== true) {
                            processedFile = result || file; // will open modal
                        }
                    }
                    catch (err) {
                        return runBeforeUpload({ beforeUpload, file, resolve, reject }); // not open modal
                    }
                }
                // read file
                const reader = new FileReader();
                reader.addEventListener('load', () => {
                    if (typeof reader.result === 'string') {
                        setModalImage(reader.result); // open modal
                    }
                });
                reader.readAsDataURL(processedFile);
                // on modal cancel
                onCancel.current = () => {
                    var _a, _b;
                    setModalImage('');
                    easyCropRef.current.onReset();
                    let hasResolveCalled = false;
                    (_b = (_a = cb.current).onModalCancel) === null || _b === void 0 ? void 0 : _b.call(_a, (LIST_IGNORE) => {
                        resolve(LIST_IGNORE);
                        hasResolveCalled = true;
                    });
                    if (!hasResolveCalled) {
                        resolve(upload/* default */.Z.LIST_IGNORE);
                    }
                };
                // on modal confirm
                onOk.current = (event) => (0,tslib_es6/* __awaiter */.mG)(void 0, void 0, void 0, function* () {
                    setModalImage('');
                    easyCropRef.current.onReset();
                    const canvas = getCropCanvas(event.target);
                    const { type, name, uid } = processedFile;
                    canvas.toBlob((blob) => (0,tslib_es6/* __awaiter */.mG)(void 0, void 0, void 0, function* () {
                        const newFile = new File([blob], name, { type });
                        Object.assign(newFile, { uid });
                        runBeforeUpload({
                            beforeUpload,
                            file: newFile,
                            resolve: (file) => {
                                var _a, _b;
                                resolve(file);
                                (_b = (_a = cb.current).onModalOk) === null || _b === void 0 ? void 0 : _b.call(_a, file);
                            },
                            reject: (err) => {
                                var _a, _b;
                                reject(err);
                                (_b = (_a = cb.current).onModalOk) === null || _b === void 0 ? void 0 : _b.call(_a, err);
                            },
                        });
                    }), type, quality);
                });
            }));
        });
    }, [getCropCanvas, quality, runBeforeUpload]);
    const getNewUpload = (0,react.useCallback)((children) => {
        const upload = Array.isArray(children) ? children[0] : children;
        const _a = upload.props, { beforeUpload, accept } = _a, restUploadProps = (0,tslib_es6/* __rest */._T)(_a, ["beforeUpload", "accept"]);
        return Object.assign(Object.assign({}, upload), { props: Object.assign(Object.assign({}, restUploadProps), { accept: accept || 'image/*', beforeUpload: getNewBeforeUpload(beforeUpload) }) });
    }, [getNewBeforeUpload]);
    /**
     * modal
     */
    const modalBaseProps = (0,react.useMemo)(() => {
        const obj = {};
        if (modalWidth !== undefined)
            obj.width = modalWidth;
        if (modalOk !== undefined)
            obj.okText = modalOk;
        if (modalCancel !== undefined)
            obj.cancelText = modalCancel;
        return obj;
    }, [modalCancel, modalOk, modalWidth]);
    const wrapClassName = \`\${PREFIX}-modal\${modalClassName ? \` \${modalClassName}\` : ''}\`;
    const lang = typeof window === 'undefined' ? '' : window.navigator.language;
    const isCN = lang === 'zh-CN';
    const title = modalTitle || (isCN ? '\u7F16\u8F91\u56FE\u7247' : 'Edit image');
    const resetBtnText = resetText || (isCN ? '\u91CD\u7F6E' : 'Reset');
    return ((0,jsx_runtime.jsxs)(jsx_runtime.Fragment, { children: [getNewUpload(children), modalImage && ((0,jsx_runtime.jsx)(modal/* default */.Z, Object.assign({}, modalProps, modalBaseProps, { [openProp]: true, title: title, onCancel: onCancel.current, onOk: onOk.current, wrapClassName: wrapClassName, maskClosable: false, destroyOnClose: true, children: (0,jsx_runtime.jsx)(EasyCrop$1, { ref: easyCropRef, cropperRef: cropperRef, zoomSlider: zoomSlider, rotationSlider: rotationSlider, aspectSlider: aspectSlider, showReset: showReset, resetBtnText: resetBtnText, modalImage: modalImage, aspect: aspect, minZoom: minZoom, maxZoom: maxZoom, cropShape: cropShape, showGrid: showGrid, cropperProps: cropperProps }) })))] }));
});


//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///9146
`)},52796:function(module,__unused_webpack_exports,__webpack_require__){eval(`module.exports = __webpack_require__(10643);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTI3OTYuanMiLCJtYXBwaW5ncyI6IkFBQUEsMkNBQW1EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW50LWRlc2lnbi1wcm8vLi9ub2RlX21vZHVsZXMvbm9ybWFsaXplLXdoZWVsL2luZGV4LmpzPzEyYWQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL3NyYy9ub3JtYWxpemVXaGVlbC5qcycpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///52796
`)},13264:function(module){"use strict";eval(`/**
 * Copyright (c) 2015, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule ExecutionEnvironment
 */

/*jslint evil: true */



var canUseDOM = !!(
  typeof window !== 'undefined' &&
  window.document &&
  window.document.createElement
);

/**
 * Simple, lightweight module assisting with the detection and context of
 * Worker. Helps avoid circular dependencies and allows code to reason about
 * whether or not they are in a Worker, even if they never include the main
 * \`ReactWorker\` dependency.
 */
var ExecutionEnvironment = {

  canUseDOM: canUseDOM,

  canUseWorkers: typeof Worker !== 'undefined',

  canUseEventListeners:
    canUseDOM && !!(window.addEventListener || window.attachEvent),

  canUseViewport: canUseDOM && !!window.screen,

  isInWorker: !canUseDOM // For now, this is true - might change in the future.

};

module.exports = ExecutionEnvironment;
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///13264
`)},84518:function(module){eval(`/**
 * Copyright 2004-present Facebook. All Rights Reserved.
 *
 * @providesModule UserAgent_DEPRECATED
 */

/**
 *  Provides entirely client-side User Agent and OS detection. You should prefer
 *  the non-deprecated UserAgent module when possible, which exposes our
 *  authoritative server-side PHP-based detection to the client.
 *
 *  Usage is straightforward:
 *
 *    if (UserAgent_DEPRECATED.ie()) {
 *      //  IE
 *    }
 *
 *  You can also do version checks:
 *
 *    if (UserAgent_DEPRECATED.ie() >= 7) {
 *      //  IE7 or better
 *    }
 *
 *  The browser functions will return NaN if the browser does not match, so
 *  you can also do version compares the other way:
 *
 *    if (UserAgent_DEPRECATED.ie() < 7) {
 *      //  IE6 or worse
 *    }
 *
 *  Note that the version is a float and may include a minor version number,
 *  so you should always use range operators to perform comparisons, not
 *  strict equality.
 *
 *  **Note:** You should **strongly** prefer capability detection to browser
 *  version detection where it's reasonable:
 *
 *    http://www.quirksmode.org/js/support.html
 *
 *  Further, we have a large number of mature wrapper functions and classes
 *  which abstract away many browser irregularities. Check the documentation,
 *  grep for things, or <NAME_EMAIL> before writing yet
 *  another copy of "event || window.event".
 *
 */

var _populated = false;

// Browsers
var _ie, _firefox, _opera, _webkit, _chrome;

// Actual IE browser for compatibility mode
var _ie_real_version;

// Platforms
var _osx, _windows, _linux, _android;

// Architectures
var _win64;

// Devices
var _iphone, _ipad, _native;

var _mobile;

function _populate() {
  if (_populated) {
    return;
  }

  _populated = true;

  // To work around buggy JS libraries that can't handle multi-digit
  // version numbers, Opera 10's user agent string claims it's Opera
  // 9, then later includes a Version/X.Y field:
  //
  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10
  var uas = navigator.userAgent;
  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);
  var os    = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);

  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);
  _ipad = /\\b(iP[ao]d)/.exec(uas);
  _android = /Android/i.exec(uas);
  _native = /FBAN\\/\\w+;/i.exec(uas);
  _mobile = /Mobile/i.exec(uas);

  // Note that the IE team blog would have you believe you should be checking
  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming
  // from either x64 or ia64;  so ultimately, you should just check for Win64
  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit
  // Windows will send 'WOW64' instead.
  _win64 = !!(/Win64/.exec(uas));

  if (agent) {
    _ie = agent[1] ? parseFloat(agent[1]) : (
          agent[5] ? parseFloat(agent[5]) : NaN);
    // IE compatibility mode
    if (_ie && document && document.documentMode) {
      _ie = document.documentMode;
    }
    // grab the "true" ie version from the trident token if available
    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);
    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;

    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;
    _opera   = agent[3] ? parseFloat(agent[3]) : NaN;
    _webkit  = agent[4] ? parseFloat(agent[4]) : NaN;
    if (_webkit) {
      // We do not add the regexp to the above test, because it will always
      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in
      // the userAgent string.
      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);
      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;
    } else {
      _chrome = NaN;
    }
  } else {
    _ie = _firefox = _opera = _chrome = _webkit = NaN;
  }

  if (os) {
    if (os[1]) {
      // Detect OS X version.  If no version number matches, set _osx to true.
      // Version examples:  10, 10_6_1, 10.7
      // Parses version number as a float, taking only first two sets of
      // digits.  If only one set of digits is found, returns just the major
      // version number.
      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);

      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;
    } else {
      _osx = false;
    }
    _windows = !!os[2];
    _linux   = !!os[3];
  } else {
    _osx = _windows = _linux = false;
  }
}

var UserAgent_DEPRECATED = {

  /**
   *  Check if the UA is Internet Explorer.
   *
   *
   *  @return float|NaN Version number (if match) or NaN.
   */
  ie: function() {
    return _populate() || _ie;
  },

  /**
   * Check if we're in Internet Explorer compatibility mode.
   *
   * @return bool true if in compatibility mode, false if
   * not compatibility mode or not ie
   */
  ieCompatibilityMode: function() {
    return _populate() || (_ie_real_version > _ie);
  },


  /**
   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we
   * only need this because Skype can't handle 64-bit IE yet.  We need to remove
   * this when we don't need it -- tracked by #601957.
   */
  ie64: function() {
    return UserAgent_DEPRECATED.ie() && _win64;
  },

  /**
   *  Check if the UA is Firefox.
   *
   *
   *  @return float|NaN Version number (if match) or NaN.
   */
  firefox: function() {
    return _populate() || _firefox;
  },


  /**
   *  Check if the UA is Opera.
   *
   *
   *  @return float|NaN Version number (if match) or NaN.
   */
  opera: function() {
    return _populate() || _opera;
  },


  /**
   *  Check if the UA is WebKit.
   *
   *
   *  @return float|NaN Version number (if match) or NaN.
   */
  webkit: function() {
    return _populate() || _webkit;
  },

  /**
   *  For Push
   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit
   */
  safari: function() {
    return UserAgent_DEPRECATED.webkit();
  },

  /**
   *  Check if the UA is a Chrome browser.
   *
   *
   *  @return float|NaN Version number (if match) or NaN.
   */
  chrome : function() {
    return _populate() || _chrome;
  },


  /**
   *  Check if the user is running Windows.
   *
   *  @return bool \`true' if the user's OS is Windows.
   */
  windows: function() {
    return _populate() || _windows;
  },


  /**
   *  Check if the user is running Mac OS X.
   *
   *  @return float|bool   Returns a float if a version number is detected,
   *                       otherwise true/false.
   */
  osx: function() {
    return _populate() || _osx;
  },

  /**
   * Check if the user is running Linux.
   *
   * @return bool \`true' if the user's OS is some flavor of Linux.
   */
  linux: function() {
    return _populate() || _linux;
  },

  /**
   * Check if the user is running on an iPhone or iPod platform.
   *
   * @return bool \`true' if the user is running some flavor of the
   *    iPhone OS.
   */
  iphone: function() {
    return _populate() || _iphone;
  },

  mobile: function() {
    return _populate() || (_iphone || _ipad || _android || _mobile);
  },

  nativeApp: function() {
    // webviews inside of the native apps
    return _populate() || _native;
  },

  android: function() {
    return _populate() || _android;
  },

  ipad: function() {
    return _populate() || _ipad;
  }
};

module.exports = UserAgent_DEPRECATED;
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///84518
`)},96534:function(module,__unused_webpack_exports,__webpack_require__){"use strict";eval(`/**
 * Copyright 2013-2015, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule isEventSupported
 */



var ExecutionEnvironment = __webpack_require__(13264);

var useHasFeature;
if (ExecutionEnvironment.canUseDOM) {
  useHasFeature =
    document.implementation &&
    document.implementation.hasFeature &&
    // always returns true in newer browsers as per the standard.
    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature
    document.implementation.hasFeature('', '') !== true;
}

/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as \`change\`,
 * \`reset\`, \`load\`, \`error\`, and \`select\`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */
function isEventSupported(eventNameSuffix, capture) {
  if (!ExecutionEnvironment.canUseDOM ||
      capture && !('addEventListener' in document)) {
    return false;
  }

  var eventName = 'on' + eventNameSuffix;
  var isSupported = eventName in document;

  if (!isSupported) {
    var element = document.createElement('div');
    element.setAttribute(eventName, 'return;');
    isSupported = typeof element[eventName] === 'function';
  }

  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {
    // This is the only way to test support for the \`wheel\` event in IE9+.
    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');
  }

  return isSupported;
}

module.exports = isEventSupported;
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///96534
`)},10643:function(module,__unused_webpack_exports,__webpack_require__){"use strict";eval(`/**
 * Copyright (c) 2015, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule normalizeWheel
 * @typechecks
 */



var UserAgent_DEPRECATED = __webpack_require__(84518);

var isEventSupported = __webpack_require__(96534);


// Reasonable defaults
var PIXEL_STEP  = 10;
var LINE_HEIGHT = 40;
var PAGE_HEIGHT = 800;

/**
 * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is
 * complicated, thus this doc is long and (hopefully) detailed enough to answer
 * your questions.
 *
 * If you need to react to the mouse wheel in a predictable way, this code is
 * like your bestest friend. * hugs *
 *
 * As of today, there are 4 DOM event types you can listen to:
 *
 *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)
 *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari
 *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!
 *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003
 *
 * So what to do?  The is the best:
 *
 *   normalizeWheel.getEventType();
 *
 * In your event callback, use this code to get sane interpretation of the
 * deltas.  This code will return an object with properties:
 *
 *   spinX   -- normalized spin speed (use for zoom) - x plane
 *   spinY   -- " - y plane
 *   pixelX  -- normalized distance (to pixels) - x plane
 *   pixelY  -- " - y plane
 *
 * Wheel values are provided by the browser assuming you are using the wheel to
 * scroll a web page by a number of lines or pixels (or pages).  Values can vary
 * significantly on different platforms and browsers, forgetting that you can
 * scroll at different speeds.  Some devices (like trackpads) emit more events
 * at smaller increments with fine granularity, and some emit massive jumps with
 * linear speed or acceleration.
 *
 * This code does its best to normalize the deltas for you:
 *
 *   - spin is trying to normalize how far the wheel was spun (or trackpad
 *     dragged).  This is super useful for zoom support where you want to
 *     throw away the chunky scroll steps on the PC and make those equal to
 *     the slow and smooth tiny steps on the Mac. Key data: This code tries to
 *     resolve a single slow step on a wheel to 1.
 *
 *   - pixel is normalizing the desired scroll delta in pixel units.  You'll
 *     get the crazy differences between browsers, but at least it'll be in
 *     pixels!
 *
 *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This
 *     should translate to positive value zooming IN, negative zooming OUT.
 *     This matches the newer 'wheel' event.
 *
 * Why are there spinX, spinY (or pixels)?
 *
 *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn
 *     with a mouse.  It results in side-scrolling in the browser by default.
 *
 *   - spinY is what you expect -- it's the classic axis of a mouse wheel.
 *
 *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and
 *     probably is by browsers in conjunction with fancy 3D controllers .. but
 *     you know.
 *
 * Implementation info:
 *
 * Examples of 'wheel' event if you scroll slowly (down) by one step with an
 * average mouse:
 *
 *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)
 *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)
 *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)
 *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)
 *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)
 *
 * On the trackpad:
 *
 *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)
 *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)
 *
 * On other/older browsers.. it's more complicated as there can be multiple and
 * also missing delta values.
 *
 * The 'wheel' event is more standard:
 *
 * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents
 *
 * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and
 * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain
 * backward compatibility with older events.  Those other values help us
 * better normalize spin speed.  Example of what the browsers provide:
 *
 *                          | event.wheelDelta | event.detail
 *        ------------------+------------------+--------------
 *          Safari v5/OS X  |       -120       |       0
 *          Safari v5/Win7  |       -120       |       0
 *         Chrome v17/OS X  |       -120       |       0
 *         Chrome v17/Win7  |       -120       |       0
 *                IE9/Win7  |       -120       |   undefined
 *         Firefox v4/OS X  |     undefined    |       1
 *         Firefox v4/Win7  |     undefined    |       3
 *
 */
function normalizeWheel(/*object*/ event) /*object*/ {
  var sX = 0, sY = 0,       // spinX, spinY
      pX = 0, pY = 0;       // pixelX, pixelY

  // Legacy
  if ('detail'      in event) { sY = event.detail; }
  if ('wheelDelta'  in event) { sY = -event.wheelDelta / 120; }
  if ('wheelDeltaY' in event) { sY = -event.wheelDeltaY / 120; }
  if ('wheelDeltaX' in event) { sX = -event.wheelDeltaX / 120; }

  // side scrolling on FF with DOMMouseScroll
  if ( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {
    sX = sY;
    sY = 0;
  }

  pX = sX * PIXEL_STEP;
  pY = sY * PIXEL_STEP;

  if ('deltaY' in event) { pY = event.deltaY; }
  if ('deltaX' in event) { pX = event.deltaX; }

  if ((pX || pY) && event.deltaMode) {
    if (event.deltaMode == 1) {          // delta in LINE units
      pX *= LINE_HEIGHT;
      pY *= LINE_HEIGHT;
    } else {                             // delta in PAGE units
      pX *= PAGE_HEIGHT;
      pY *= PAGE_HEIGHT;
    }
  }

  // Fall-back if spin cannot be determined
  if (pX && !sX) { sX = (pX < 1) ? -1 : 1; }
  if (pY && !sY) { sY = (pY < 1) ? -1 : 1; }

  return { spinX  : sX,
           spinY  : sY,
           pixelX : pX,
           pixelY : pY };
}


/**
 * The best combination if you prefer spinX + spinY normalization.  It favors
 * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with
 * 'wheel' event, making spin speed determination impossible.
 */
normalizeWheel.getEventType = function() /*string*/ {
  return (UserAgent_DEPRECATED.firefox())
           ? 'DOMMouseScroll'
           : (isEventSupported('wheel'))
               ? 'wheel'
               : 'mousewheel';
};

module.exports = normalizeWheel;
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///10643
`)}}]);
