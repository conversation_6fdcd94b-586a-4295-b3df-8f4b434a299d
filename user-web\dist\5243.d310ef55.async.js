"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5243],{48820:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var CopyOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z" } }] }, "name": "copy", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (CopyOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDg4MjAuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxxQkFBcUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsa1lBQWtZLEdBQUc7QUFDMWhCLHNEQUFlLFlBQVksRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FudC1kZXNpZ24tcHJvLy4vbm9kZV9tb2R1bGVzL0BhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vQ29weU91dGxpbmVkLmpzPzc1YmUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBpY29uIGZpbGUgaXMgZ2VuZXJhdGVkIGF1dG9tYXRpY2FsbHkuXG52YXIgQ29weU91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk04MzIgNjRIMjk2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDQ5NnY2ODhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFY5NmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzA0IDE5MkgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjUzMC43YzAgOC41IDMuNCAxNi42IDkuNCAyMi42bDE3My4zIDE3My4zYzIuMiAyLjIgNC43IDQgNy40IDUuNXYxLjloNC4yYzMuNSAxLjMgNy4yIDIgMTEgMkg3MDRjMTcuNyAwIDMyLTE0LjMgMzItMzJWMjI0YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zNTAgODU2LjJMMjYzLjkgNzcwSDM1MHY4Ni4yek02NjQgODg4SDQxNFY3NDZjMC0yMi4xLTE3LjktNDAtNDAtNDBIMjMyVjI2NGg0MzJ2NjI0elwiIH0gfV0gfSwgXCJuYW1lXCI6IFwiY29weVwiLCBcInRoZW1lXCI6IFwib3V0bGluZWRcIiB9O1xuZXhwb3J0IGRlZmF1bHQgQ29weU91dGxpbmVkO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///48820
`)},38703:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ es_progress; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CheckCircleFilled.js
var CheckCircleFilled = __webpack_require__(19735);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CheckOutlined.js
var CheckOutlined = __webpack_require__(64894);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js
var CloseCircleFilled = __webpack_require__(17012);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseOutlined.js
var CloseOutlined = __webpack_require__(62208);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(98423);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/common.js

var defaultProps = {
  percent: 0,
  prefixCls: 'rc-progress',
  strokeColor: '#2db7f5',
  strokeLinecap: 'round',
  strokeWidth: 1,
  trailColor: '#D9D9D9',
  trailWidth: 1,
  gapPosition: 'bottom'
};
var useTransitionDuration = function useTransitionDuration() {
  var pathsRef = (0,react.useRef)([]);
  var prevTimeStamp = (0,react.useRef)(null);
  (0,react.useEffect)(function () {
    var now = Date.now();
    var updated = false;
    pathsRef.current.forEach(function (path) {
      if (!path) {
        return;
      }
      updated = true;
      var pathStyle = path.style;
      pathStyle.transitionDuration = '.3s, .3s, .3s, .06s';
      if (prevTimeStamp.current && now - prevTimeStamp.current < 100) {
        pathStyle.transitionDuration = '0s, 0s';
      }
    });
    if (updated) {
      prevTimeStamp.current = Date.now();
    }
  });
  return pathsRef.current;
};
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/Line.js



var _excluded = ["className", "percent", "prefixCls", "strokeColor", "strokeLinecap", "strokeWidth", "style", "trailColor", "trailWidth", "transition"];



var Line = function Line(props) {
  var _defaultProps$props = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, defaultProps), props),
    className = _defaultProps$props.className,
    percent = _defaultProps$props.percent,
    prefixCls = _defaultProps$props.prefixCls,
    strokeColor = _defaultProps$props.strokeColor,
    strokeLinecap = _defaultProps$props.strokeLinecap,
    strokeWidth = _defaultProps$props.strokeWidth,
    style = _defaultProps$props.style,
    trailColor = _defaultProps$props.trailColor,
    trailWidth = _defaultProps$props.trailWidth,
    transition = _defaultProps$props.transition,
    restProps = (0,objectWithoutProperties/* default */.Z)(_defaultProps$props, _excluded);
  // eslint-disable-next-line no-param-reassign
  delete restProps.gapPosition;
  var percentList = Array.isArray(percent) ? percent : [percent];
  var strokeColorList = Array.isArray(strokeColor) ? strokeColor : [strokeColor];
  var paths = useTransitionDuration();
  var center = strokeWidth / 2;
  var right = 100 - strokeWidth / 2;
  var pathString = "M ".concat(strokeLinecap === 'round' ? center : 0, ",").concat(center, "\\n         L ").concat(strokeLinecap === 'round' ? right : 100, ",").concat(center);
  var viewBoxString = "0 0 100 ".concat(strokeWidth);
  var stackPtg = 0;
  return /*#__PURE__*/react.createElement("svg", (0,esm_extends/* default */.Z)({
    className: classnames_default()("".concat(prefixCls, "-line"), className),
    viewBox: viewBoxString,
    preserveAspectRatio: "none",
    style: style
  }, restProps), /*#__PURE__*/react.createElement("path", {
    className: "".concat(prefixCls, "-line-trail"),
    d: pathString,
    strokeLinecap: strokeLinecap,
    stroke: trailColor,
    strokeWidth: trailWidth || strokeWidth,
    fillOpacity: "0"
  }), percentList.map(function (ptg, index) {
    var dashPercent = 1;
    switch (strokeLinecap) {
      case 'round':
        dashPercent = 1 - strokeWidth / 100;
        break;
      case 'square':
        dashPercent = 1 - strokeWidth / 2 / 100;
        break;
      default:
        dashPercent = 1;
        break;
    }
    var pathStyle = {
      strokeDasharray: "".concat(ptg * dashPercent, "px, 100px"),
      strokeDashoffset: "-".concat(stackPtg, "px"),
      transition: transition || 'stroke-dashoffset 0.3s ease 0s, stroke-dasharray .3s ease 0s, stroke 0.3s linear'
    };
    var color = strokeColorList[index] || strokeColorList[strokeColorList.length - 1];
    stackPtg += ptg;
    return /*#__PURE__*/react.createElement("path", {
      key: index,
      className: "".concat(prefixCls, "-line-path"),
      d: pathString,
      strokeLinecap: strokeLinecap,
      stroke: color,
      strokeWidth: strokeWidth,
      fillOpacity: "0",
      ref: function ref(elem) {
        // https://reactjs.org/docs/refs-and-the-dom.html#callback-refs
        // React will call the ref callback with the DOM element when the component mounts,
        // and call it with \`null\` when it unmounts.
        // Refs are guaranteed to be up-to-date before componentDidMount or componentDidUpdate fires.
        paths[index] = elem;
      },
      style: pathStyle
    });
  }));
};
if (false) {}
/* harmony default export */ var es_Line = (Line);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/rc-util/es/Dom/canUseDom.js
var canUseDom = __webpack_require__(98924);
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/hooks/useId.js



var uuid = 0;
/** Is client side and not jsdom */
var isBrowserClient =  true && (0,canUseDom/* default */.Z)();
/** Get unique id for accessibility usage */
function getUUID() {
  var retId;
  // Test never reach
  /* istanbul ignore if */
  if (isBrowserClient) {
    retId = uuid;
    uuid += 1;
  } else {
    retId = 'TEST_OR_SSR';
  }
  return retId;
}
/* harmony default export */ var useId = (function (id) {
  // Inner id for accessibility usage. Only work in client side
  var _React$useState = react.useState(),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    innerId = _React$useState2[0],
    setInnerId = _React$useState2[1];
  react.useEffect(function () {
    setInnerId("rc_progress_".concat(getUUID()));
  }, []);
  return id || innerId;
});
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/Circle/PtgCircle.js


var Block = function Block(_ref) {
  var bg = _ref.bg,
    children = _ref.children;
  return /*#__PURE__*/react.createElement("div", {
    style: {
      width: '100%',
      height: '100%',
      background: bg
    }
  }, children);
};
function getPtgColors(color, scale) {
  return Object.keys(color).map(function (key) {
    var parsedKey = parseFloat(key);
    var ptgKey = "".concat(Math.floor(parsedKey * scale), "%");
    return "".concat(color[key], " ").concat(ptgKey);
  });
}
var PtgCircle = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var prefixCls = props.prefixCls,
    color = props.color,
    gradientId = props.gradientId,
    radius = props.radius,
    circleStyleForStack = props.style,
    ptg = props.ptg,
    strokeLinecap = props.strokeLinecap,
    strokeWidth = props.strokeWidth,
    size = props.size,
    gapDegree = props.gapDegree;
  var isGradient = color && (0,esm_typeof/* default */.Z)(color) === 'object';
  var stroke = isGradient ? "#FFF" : undefined;
  // ========================== Circle ==========================
  var halfSize = size / 2;
  var circleNode = /*#__PURE__*/react.createElement("circle", {
    className: "".concat(prefixCls, "-circle-path"),
    r: radius,
    cx: halfSize,
    cy: halfSize,
    stroke: stroke,
    strokeLinecap: strokeLinecap,
    strokeWidth: strokeWidth,
    opacity: ptg === 0 ? 0 : 1,
    style: circleStyleForStack,
    ref: ref
  });
  // ========================== Render ==========================
  if (!isGradient) {
    return circleNode;
  }
  var maskId = "".concat(gradientId, "-conic");
  var fromDeg = gapDegree ? "".concat(180 + gapDegree / 2, "deg") : '0deg';
  var conicColors = getPtgColors(color, (360 - gapDegree) / 360);
  var linearColors = getPtgColors(color, 1);
  var conicColorBg = "conic-gradient(from ".concat(fromDeg, ", ").concat(conicColors.join(', '), ")");
  var linearColorBg = "linear-gradient(to ".concat(gapDegree ? 'bottom' : 'top', ", ").concat(linearColors.join(', '), ")");
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("mask", {
    id: maskId
  }, circleNode), /*#__PURE__*/react.createElement("foreignObject", {
    x: 0,
    y: 0,
    width: size,
    height: size,
    mask: "url(#".concat(maskId, ")")
  }, /*#__PURE__*/react.createElement(Block, {
    bg: linearColorBg
  }, /*#__PURE__*/react.createElement(Block, {
    bg: conicColorBg
  }))));
});
if (false) {}
/* harmony default export */ var Circle_PtgCircle = (PtgCircle);
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/Circle/util.js
var VIEW_BOX_SIZE = 100;
var getCircleStyle = function getCircleStyle(perimeter, perimeterWithoutGap, offset, percent, rotateDeg, gapDegree, gapPosition, strokeColor, strokeLinecap, strokeWidth) {
  var stepSpace = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : 0;
  var offsetDeg = offset / 100 * 360 * ((360 - gapDegree) / 360);
  var positionDeg = gapDegree === 0 ? 0 : {
    bottom: 0,
    top: 180,
    left: 90,
    right: -90
  }[gapPosition];
  var strokeDashoffset = (100 - percent) / 100 * perimeterWithoutGap;
  // Fix percent accuracy when strokeLinecap is round
  // https://github.com/ant-design/ant-design/issues/35009
  if (strokeLinecap === 'round' && percent !== 100) {
    strokeDashoffset += strokeWidth / 2;
    // when percent is small enough (<= 1%), keep smallest value to avoid it's disappearance
    if (strokeDashoffset >= perimeterWithoutGap) {
      strokeDashoffset = perimeterWithoutGap - 0.01;
    }
  }
  var halfSize = VIEW_BOX_SIZE / 2;
  return {
    stroke: typeof strokeColor === 'string' ? strokeColor : undefined,
    strokeDasharray: "".concat(perimeterWithoutGap, "px ").concat(perimeter),
    strokeDashoffset: strokeDashoffset + stepSpace,
    transform: "rotate(".concat(rotateDeg + offsetDeg + positionDeg, "deg)"),
    transformOrigin: "".concat(halfSize, "px ").concat(halfSize, "px"),
    transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s',
    fillOpacity: 0
  };
};
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/Circle/index.js




var Circle_excluded = ["id", "prefixCls", "steps", "strokeWidth", "trailWidth", "gapDegree", "gapPosition", "trailColor", "strokeLinecap", "style", "className", "strokeColor", "percent"];






function toArray(value) {
  var mergedValue = value !== null && value !== void 0 ? value : [];
  return Array.isArray(mergedValue) ? mergedValue : [mergedValue];
}
var Circle = function Circle(props) {
  var _defaultProps$props = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, defaultProps), props),
    id = _defaultProps$props.id,
    prefixCls = _defaultProps$props.prefixCls,
    steps = _defaultProps$props.steps,
    strokeWidth = _defaultProps$props.strokeWidth,
    trailWidth = _defaultProps$props.trailWidth,
    _defaultProps$props$g = _defaultProps$props.gapDegree,
    gapDegree = _defaultProps$props$g === void 0 ? 0 : _defaultProps$props$g,
    gapPosition = _defaultProps$props.gapPosition,
    trailColor = _defaultProps$props.trailColor,
    strokeLinecap = _defaultProps$props.strokeLinecap,
    style = _defaultProps$props.style,
    className = _defaultProps$props.className,
    strokeColor = _defaultProps$props.strokeColor,
    percent = _defaultProps$props.percent,
    restProps = (0,objectWithoutProperties/* default */.Z)(_defaultProps$props, Circle_excluded);
  var halfSize = VIEW_BOX_SIZE / 2;
  var mergedId = useId(id);
  var gradientId = "".concat(mergedId, "-gradient");
  var radius = halfSize - strokeWidth / 2;
  var perimeter = Math.PI * 2 * radius;
  var rotateDeg = gapDegree > 0 ? 90 + gapDegree / 2 : -90;
  var perimeterWithoutGap = perimeter * ((360 - gapDegree) / 360);
  var _ref = (0,esm_typeof/* default */.Z)(steps) === 'object' ? steps : {
      count: steps,
      space: 2
    },
    stepCount = _ref.count,
    stepSpace = _ref.space;
  var percentList = toArray(percent);
  var strokeColorList = toArray(strokeColor);
  var gradient = strokeColorList.find(function (color) {
    return color && (0,esm_typeof/* default */.Z)(color) === 'object';
  });
  var isConicGradient = gradient && (0,esm_typeof/* default */.Z)(gradient) === 'object';
  var mergedStrokeLinecap = isConicGradient ? 'butt' : strokeLinecap;
  var circleStyle = getCircleStyle(perimeter, perimeterWithoutGap, 0, 100, rotateDeg, gapDegree, gapPosition, trailColor, mergedStrokeLinecap, strokeWidth);
  var paths = useTransitionDuration();
  var getStokeList = function getStokeList() {
    var stackPtg = 0;
    return percentList.map(function (ptg, index) {
      var color = strokeColorList[index] || strokeColorList[strokeColorList.length - 1];
      var circleStyleForStack = getCircleStyle(perimeter, perimeterWithoutGap, stackPtg, ptg, rotateDeg, gapDegree, gapPosition, color, mergedStrokeLinecap, strokeWidth);
      stackPtg += ptg;
      return /*#__PURE__*/react.createElement(Circle_PtgCircle, {
        key: index,
        color: color,
        ptg: ptg,
        radius: radius,
        prefixCls: prefixCls,
        gradientId: gradientId,
        style: circleStyleForStack,
        strokeLinecap: mergedStrokeLinecap,
        strokeWidth: strokeWidth,
        gapDegree: gapDegree,
        ref: function ref(elem) {
          // https://reactjs.org/docs/refs-and-the-dom.html#callback-refs
          // React will call the ref callback with the DOM element when the component mounts,
          // and call it with \`null\` when it unmounts.
          // Refs are guaranteed to be up-to-date before componentDidMount or componentDidUpdate fires.
          paths[index] = elem;
        },
        size: VIEW_BOX_SIZE
      });
    }).reverse();
  };
  var getStepStokeList = function getStepStokeList() {
    // only show the first percent when pass steps
    var current = Math.round(stepCount * (percentList[0] / 100));
    var stepPtg = 100 / stepCount;
    var stackPtg = 0;
    return new Array(stepCount).fill(null).map(function (_, index) {
      var color = index <= current - 1 ? strokeColorList[0] : trailColor;
      var stroke = color && (0,esm_typeof/* default */.Z)(color) === 'object' ? "url(#".concat(gradientId, ")") : undefined;
      var circleStyleForStack = getCircleStyle(perimeter, perimeterWithoutGap, stackPtg, stepPtg, rotateDeg, gapDegree, gapPosition, color, 'butt', strokeWidth, stepSpace);
      stackPtg += (perimeterWithoutGap - circleStyleForStack.strokeDashoffset + stepSpace) * 100 / perimeterWithoutGap;
      return /*#__PURE__*/react.createElement("circle", {
        key: index,
        className: "".concat(prefixCls, "-circle-path"),
        r: radius,
        cx: halfSize,
        cy: halfSize,
        stroke: stroke,
        strokeWidth: strokeWidth,
        opacity: 1,
        style: circleStyleForStack,
        ref: function ref(elem) {
          paths[index] = elem;
        }
      });
    });
  };
  return /*#__PURE__*/react.createElement("svg", (0,esm_extends/* default */.Z)({
    className: classnames_default()("".concat(prefixCls, "-circle"), className),
    viewBox: "0 0 ".concat(VIEW_BOX_SIZE, " ").concat(VIEW_BOX_SIZE),
    style: style,
    id: id,
    role: "presentation"
  }, restProps), !stepCount && /*#__PURE__*/react.createElement("circle", {
    className: "".concat(prefixCls, "-circle-trail"),
    r: radius,
    cx: halfSize,
    cy: halfSize,
    stroke: trailColor,
    strokeLinecap: mergedStrokeLinecap,
    strokeWidth: trailWidth || strokeWidth,
    style: circleStyle
  }), stepCount ? getStepStokeList() : getStokeList());
};
if (false) {}
/* harmony default export */ var es_Circle = (Circle);
;// CONCATENATED MODULE: ./node_modules/rc-progress/es/index.js



/* harmony default export */ var es = ({
  Line: es_Line,
  Circle: es_Circle
});
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(83062);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/colors/es/index.js + 1 modules
var colors_es = __webpack_require__(78589);
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/utils.js

function validProgress(progress) {
  if (!progress || progress < 0) {
    return 0;
  }
  if (progress > 100) {
    return 100;
  }
  return progress;
}
function getSuccessPercent(_ref) {
  let {
    success,
    successPercent
  } = _ref;
  let percent = successPercent;
  /** @deprecated Use \`percent\` instead */
  if (success && 'progress' in success) {
    percent = success.progress;
  }
  if (success && 'percent' in success) {
    percent = success.percent;
  }
  return percent;
}
const getPercentage = _ref2 => {
  let {
    percent,
    success,
    successPercent
  } = _ref2;
  const realSuccessPercent = validProgress(getSuccessPercent({
    success,
    successPercent
  }));
  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];
};
const getStrokeColor = _ref3 => {
  let {
    success = {},
    strokeColor
  } = _ref3;
  const {
    strokeColor: successColor
  } = success;
  return [successColor || colors_es/* presetPrimaryColors */.ez.green, strokeColor || null];
};
const getSize = (size, type, extra) => {
  var _a, _b, _c, _d;
  let width = -1;
  let height = -1;
  if (type === 'step') {
    const steps = extra.steps;
    const strokeWidth = extra.strokeWidth;
    if (typeof size === 'string' || typeof size === 'undefined') {
      width = size === 'small' ? 2 : 14;
      height = strokeWidth !== null && strokeWidth !== void 0 ? strokeWidth : 8;
    } else if (typeof size === 'number') {
      [width, height] = [size, size];
    } else {
      [width = 14, height = 8] = size;
    }
    width *= steps;
  } else if (type === 'line') {
    const strokeWidth = extra === null || extra === void 0 ? void 0 : extra.strokeWidth;
    if (typeof size === 'string' || typeof size === 'undefined') {
      height = strokeWidth || (size === 'small' ? 6 : 8);
    } else if (typeof size === 'number') {
      [width, height] = [size, size];
    } else {
      [width = -1, height = 8] = size;
    }
  } else if (type === 'circle' || type === 'dashboard') {
    if (typeof size === 'string' || typeof size === 'undefined') {
      [width, height] = size === 'small' ? [60, 60] : [120, 120];
    } else if (typeof size === 'number') {
      [width, height] = [size, size];
    } else {
      width = (_b = (_a = size[0]) !== null && _a !== void 0 ? _a : size[1]) !== null && _b !== void 0 ? _b : 120;
      height = (_d = (_c = size[0]) !== null && _c !== void 0 ? _c : size[1]) !== null && _d !== void 0 ? _d : 120;
    }
  }
  return [width, height];
};
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/Circle.js
"use client";






const CIRCLE_MIN_STROKE_WIDTH = 3;
const getMinPercent = width => CIRCLE_MIN_STROKE_WIDTH / width * 100;
const Circle_Circle = props => {
  const {
    prefixCls,
    trailColor = null,
    strokeLinecap = 'round',
    gapPosition,
    gapDegree,
    width: originWidth = 120,
    type,
    children,
    success,
    size = originWidth
  } = props;
  const [width, height] = getSize(size, 'circle');
  let {
    strokeWidth
  } = props;
  if (strokeWidth === undefined) {
    strokeWidth = Math.max(getMinPercent(width), 6);
  }
  const circleStyle = {
    width,
    height,
    fontSize: width * 0.15 + 6
  };
  const realGapDegree = react.useMemo(() => {
    // Support gapDeg = 0 when type = 'dashboard'
    if (gapDegree || gapDegree === 0) {
      return gapDegree;
    }
    if (type === 'dashboard') {
      return 75;
    }
    return undefined;
  }, [gapDegree, type]);
  const gapPos = gapPosition || type === 'dashboard' && 'bottom' || undefined;
  // using className to style stroke color
  const isGradient = Object.prototype.toString.call(props.strokeColor) === '[object Object]';
  const strokeColor = getStrokeColor({
    success,
    strokeColor: props.strokeColor
  });
  const wrapperClassName = classnames_default()(\`\${prefixCls}-inner\`, {
    [\`\${prefixCls}-circle-gradient\`]: isGradient
  });
  const circleContent = /*#__PURE__*/react.createElement(es_Circle, {
    percent: getPercentage(props),
    strokeWidth: strokeWidth,
    trailWidth: strokeWidth,
    strokeColor: strokeColor,
    strokeLinecap: strokeLinecap,
    trailColor: trailColor,
    prefixCls: prefixCls,
    gapDegree: realGapDegree,
    gapPosition: gapPos
  });
  return /*#__PURE__*/react.createElement("div", {
    className: wrapperClassName,
    style: circleStyle
  }, width <= 20 ? ( /*#__PURE__*/react.createElement(tooltip/* default */.Z, {
    title: children
  }, /*#__PURE__*/react.createElement("span", null, circleContent))) : ( /*#__PURE__*/react.createElement(react.Fragment, null, circleContent, children)));
};
/* harmony default export */ var progress_Circle = (Circle_Circle);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(14747);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/statistic.js
var statistic = __webpack_require__(45503);
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/style/index.js



const LineStrokeColorVar = '--progress-line-stroke-color';
const Percent = '--progress-percent';
const genAntProgressActive = isRtl => {
  const direction = isRtl ? '100%' : '-100%';
  return new cssinjs_es/* Keyframes */.E4(\`antProgress\${isRtl ? 'RTL' : 'LTR'}Active\`, {
    '0%': {
      transform: \`translateX(\${direction}) scaleX(0)\`,
      opacity: 0.1
    },
    '20%': {
      transform: \`translateX(\${direction}) scaleX(0)\`,
      opacity: 0.5
    },
    to: {
      transform: 'translateX(0) scaleX(1)',
      opacity: 0
    }
  });
};
const genBaseStyle = token => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: Object.assign(Object.assign({}, (0,style/* resetComponent */.Wf)(token)), {
      display: 'inline-block',
      '&-rtl': {
        direction: 'rtl'
      },
      '&-line': {
        position: 'relative',
        width: '100%',
        fontSize: token.fontSize
      },
      [\`\${progressCls}-outer\`]: {
        display: 'inline-block',
        width: '100%'
      },
      [\`&\${progressCls}-show-info\`]: {
        [\`\${progressCls}-outer\`]: {
          marginInlineEnd: \`calc(-2em - \${(0,cssinjs_es/* unit */.bf)(token.marginXS)})\`,
          paddingInlineEnd: \`calc(2em + \${(0,cssinjs_es/* unit */.bf)(token.paddingXS)})\`
        }
      },
      [\`\${progressCls}-inner\`]: {
        position: 'relative',
        display: 'inline-block',
        width: '100%',
        overflow: 'hidden',
        verticalAlign: 'middle',
        backgroundColor: token.remainingColor,
        borderRadius: token.lineBorderRadius
      },
      [\`\${progressCls}-inner:not(\${progressCls}-circle-gradient)\`]: {
        [\`\${progressCls}-circle-path\`]: {
          stroke: token.defaultColor
        }
      },
      [\`\${progressCls}-success-bg, \${progressCls}-bg\`]: {
        position: 'relative',
        background: token.defaultColor,
        borderRadius: token.lineBorderRadius,
        transition: \`all \${token.motionDurationSlow} \${token.motionEaseInOutCirc}\`
      },
      [\`\${progressCls}-bg\`]: {
        overflow: 'hidden',
        '&::after': {
          content: '""',
          background: {
            _multi_value_: true,
            value: ['inherit', \`var(\${LineStrokeColorVar})\`]
          },
          height: '100%',
          width: \`calc(1 / var(\${Percent}) * 100%)\`,
          display: 'block'
        }
      },
      [\`\${progressCls}-success-bg\`]: {
        position: 'absolute',
        insetBlockStart: 0,
        insetInlineStart: 0,
        backgroundColor: token.colorSuccess
      },
      [\`\${progressCls}-text\`]: {
        display: 'inline-block',
        width: '2em',
        marginInlineStart: token.marginXS,
        color: token.colorText,
        lineHeight: 1,
        whiteSpace: 'nowrap',
        textAlign: 'start',
        verticalAlign: 'middle',
        wordBreak: 'normal',
        [iconPrefixCls]: {
          fontSize: token.fontSize
        }
      },
      [\`&\${progressCls}-status-active\`]: {
        [\`\${progressCls}-bg::before\`]: {
          position: 'absolute',
          inset: 0,
          backgroundColor: token.colorBgContainer,
          borderRadius: token.lineBorderRadius,
          opacity: 0,
          animationName: genAntProgressActive(),
          animationDuration: token.progressActiveMotionDuration,
          animationTimingFunction: token.motionEaseOutQuint,
          animationIterationCount: 'infinite',
          content: '""'
        }
      },
      [\`&\${progressCls}-rtl\${progressCls}-status-active\`]: {
        [\`\${progressCls}-bg::before\`]: {
          animationName: genAntProgressActive(true)
        }
      },
      [\`&\${progressCls}-status-exception\`]: {
        [\`\${progressCls}-bg\`]: {
          backgroundColor: token.colorError
        },
        [\`\${progressCls}-text\`]: {
          color: token.colorError
        }
      },
      [\`&\${progressCls}-status-exception \${progressCls}-inner:not(\${progressCls}-circle-gradient)\`]: {
        [\`\${progressCls}-circle-path\`]: {
          stroke: token.colorError
        }
      },
      [\`&\${progressCls}-status-success\`]: {
        [\`\${progressCls}-bg\`]: {
          backgroundColor: token.colorSuccess
        },
        [\`\${progressCls}-text\`]: {
          color: token.colorSuccess
        }
      },
      [\`&\${progressCls}-status-success \${progressCls}-inner:not(\${progressCls}-circle-gradient)\`]: {
        [\`\${progressCls}-circle-path\`]: {
          stroke: token.colorSuccess
        }
      }
    })
  };
};
const genCircleStyle = token => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: {
      [\`\${progressCls}-circle-trail\`]: {
        stroke: token.remainingColor
      },
      [\`&\${progressCls}-circle \${progressCls}-inner\`]: {
        position: 'relative',
        lineHeight: 1,
        backgroundColor: 'transparent'
      },
      [\`&\${progressCls}-circle \${progressCls}-text\`]: {
        position: 'absolute',
        insetBlockStart: '50%',
        insetInlineStart: 0,
        width: '100%',
        margin: 0,
        padding: 0,
        color: token.circleTextColor,
        fontSize: token.circleTextFontSize,
        lineHeight: 1,
        whiteSpace: 'normal',
        textAlign: 'center',
        transform: 'translateY(-50%)',
        [iconPrefixCls]: {
          fontSize: token.circleIconFontSize
        }
      },
      [\`\${progressCls}-circle&-status-exception\`]: {
        [\`\${progressCls}-text\`]: {
          color: token.colorError
        }
      },
      [\`\${progressCls}-circle&-status-success\`]: {
        [\`\${progressCls}-text\`]: {
          color: token.colorSuccess
        }
      }
    },
    [\`\${progressCls}-inline-circle\`]: {
      lineHeight: 1,
      [\`\${progressCls}-inner\`]: {
        verticalAlign: 'bottom'
      }
    }
  };
};
const genStepStyle = token => {
  const {
    componentCls: progressCls
  } = token;
  return {
    [progressCls]: {
      [\`\${progressCls}-steps\`]: {
        display: 'inline-block',
        '&-outer': {
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center'
        },
        '&-item': {
          flexShrink: 0,
          minWidth: token.progressStepMinWidth,
          marginInlineEnd: token.progressStepMarginInlineEnd,
          backgroundColor: token.remainingColor,
          transition: \`all \${token.motionDurationSlow}\`,
          '&-active': {
            backgroundColor: token.defaultColor
          }
        }
      }
    }
  };
};
const genSmallLine = token => {
  const {
    componentCls: progressCls,
    iconCls: iconPrefixCls
  } = token;
  return {
    [progressCls]: {
      [\`\${progressCls}-small&-line, \${progressCls}-small&-line \${progressCls}-text \${iconPrefixCls}\`]: {
        fontSize: token.fontSizeSM
      }
    }
  };
};
const prepareComponentToken = token => ({
  circleTextColor: token.colorText,
  defaultColor: token.colorInfo,
  remainingColor: token.colorFillSecondary,
  lineBorderRadius: 100,
  // magic for capsule shape, should be a very large number
  circleTextFontSize: '1em',
  circleIconFontSize: \`\${token.fontSize / token.fontSizeSM}em\`
});
/* harmony default export */ var progress_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('Progress', token => {
  const progressStepMarginInlineEnd = token.calc(token.marginXXS).div(2).equal();
  const progressToken = (0,statistic/* merge */.TS)(token, {
    progressStepMarginInlineEnd,
    progressStepMinWidth: progressStepMarginInlineEnd,
    progressActiveMotionDuration: '2.4s'
  });
  return [genBaseStyle(progressToken), genCircleStyle(progressToken), genStepStyle(progressToken), genSmallLine(progressToken)];
}, prepareComponentToken));
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/Line.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};





/**
 * @example
 *   {
 *     "0%": "#afc163",
 *     "75%": "#009900",
 *     "50%": "green", // ====> '#afc163 0%, #66FF00 25%, #00CC00 50%, #009900 75%, #ffffff 100%'
 *     "25%": "#66FF00",
 *     "100%": "#ffffff"
 *   }
 */
const sortGradient = gradients => {
  let tempArr = [];
  Object.keys(gradients).forEach(key => {
    const formattedKey = parseFloat(key.replace(/%/g, ''));
    if (!isNaN(formattedKey)) {
      tempArr.push({
        key: formattedKey,
        value: gradients[key]
      });
    }
  });
  tempArr = tempArr.sort((a, b) => a.key - b.key);
  return tempArr.map(_ref => {
    let {
      key,
      value
    } = _ref;
    return \`\${value} \${key}%\`;
  }).join(', ');
};
/**
 * Then this man came to realize the truth: Besides six pence, there is the moon. Besides bread and
 * butter, there is the bug. And... Besides women, there is the code.
 *
 * @example
 *   {
 *     "0%": "#afc163",
 *     "25%": "#66FF00",
 *     "50%": "#00CC00", // ====>  linear-gradient(to right, #afc163 0%, #66FF00 25%,
 *     "75%": "#009900", //        #00CC00 50%, #009900 75%, #ffffff 100%)
 *     "100%": "#ffffff"
 *   }
 */
const handleGradient = (strokeColor, directionConfig) => {
  const {
      from = colors_es/* presetPrimaryColors */.ez.blue,
      to = colors_es/* presetPrimaryColors */.ez.blue,
      direction = directionConfig === 'rtl' ? 'to left' : 'to right'
    } = strokeColor,
    rest = __rest(strokeColor, ["from", "to", "direction"]);
  if (Object.keys(rest).length !== 0) {
    const sortedGradients = sortGradient(rest);
    const background = \`linear-gradient(\${direction}, \${sortedGradients})\`;
    return {
      background,
      [LineStrokeColorVar]: background
    };
  }
  const background = \`linear-gradient(\${direction}, \${from}, \${to})\`;
  return {
    background,
    [LineStrokeColorVar]: background
  };
};
const Line_Line = props => {
  const {
    prefixCls,
    direction: directionConfig,
    percent,
    size,
    strokeWidth,
    strokeColor,
    strokeLinecap = 'round',
    children,
    trailColor = null,
    success
  } = props;
  const backgroundProps = strokeColor && typeof strokeColor !== 'string' ? handleGradient(strokeColor, directionConfig) : {
    [LineStrokeColorVar]: strokeColor,
    background: strokeColor
  };
  const borderRadius = strokeLinecap === 'square' || strokeLinecap === 'butt' ? 0 : undefined;
  const mergedSize = size !== null && size !== void 0 ? size : [-1, strokeWidth || (size === 'small' ? 6 : 8)];
  const [width, height] = getSize(mergedSize, 'line', {
    strokeWidth
  });
  if (false) {}
  const trailStyle = {
    backgroundColor: trailColor || undefined,
    borderRadius
  };
  const percentStyle = Object.assign(Object.assign({
    width: \`\${validProgress(percent)}%\`,
    height,
    borderRadius
  }, backgroundProps), {
    [Percent]: validProgress(percent) / 100
  });
  const successPercent = getSuccessPercent(props);
  const successPercentStyle = {
    width: \`\${validProgress(successPercent)}%\`,
    height,
    borderRadius,
    backgroundColor: success === null || success === void 0 ? void 0 : success.strokeColor
  };
  const outerStyle = {
    width: width < 0 ? '100%' : width,
    height
  };
  return /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-outer\`,
    style: outerStyle
  }, /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-inner\`,
    style: trailStyle
  }, /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-bg\`,
    style: percentStyle
  }), successPercent !== undefined ? ( /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-success-bg\`,
    style: successPercentStyle
  })) : null)), children);
};
/* harmony default export */ var progress_Line = (Line_Line);
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/Steps.js
"use client";




const Steps = props => {
  const {
    size,
    steps,
    percent = 0,
    strokeWidth = 8,
    strokeColor,
    trailColor = null,
    prefixCls,
    children
  } = props;
  const current = Math.round(steps * (percent / 100));
  const stepWidth = size === 'small' ? 2 : 14;
  const mergedSize = size !== null && size !== void 0 ? size : [stepWidth, strokeWidth];
  const [width, height] = getSize(mergedSize, 'step', {
    steps,
    strokeWidth
  });
  const unitWidth = width / steps;
  const styledSteps = new Array(steps);
  for (let i = 0; i < steps; i++) {
    const color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;
    styledSteps[i] = /*#__PURE__*/react.createElement("div", {
      key: i,
      className: classnames_default()(\`\${prefixCls}-steps-item\`, {
        [\`\${prefixCls}-steps-item-active\`]: i <= current - 1
      }),
      style: {
        backgroundColor: i <= current - 1 ? color : trailColor,
        width: unitWidth,
        height
      }
    });
  }
  return /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-steps-outer\`
  }, styledSteps, children);
};
/* harmony default export */ var progress_Steps = (Steps);
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/progress.js
"use client";

var progress_rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};














const ProgressTypes = (/* unused pure expression or super */ null && (['line', 'circle', 'dashboard']));
const ProgressStatuses = ['normal', 'exception', 'active', 'success'];
const Progress = /*#__PURE__*/react.forwardRef((props, ref) => {
  const {
      prefixCls: customizePrefixCls,
      className,
      rootClassName,
      steps,
      strokeColor,
      percent = 0,
      size = 'default',
      showInfo = true,
      type = 'line',
      status,
      format,
      style
    } = props,
    restProps = progress_rest(props, ["prefixCls", "className", "rootClassName", "steps", "strokeColor", "percent", "size", "showInfo", "type", "status", "format", "style"]);
  const percentNumber = react.useMemo(() => {
    var _a, _b;
    const successPercent = getSuccessPercent(props);
    return parseInt(successPercent !== undefined ? (_a = successPercent !== null && successPercent !== void 0 ? successPercent : 0) === null || _a === void 0 ? void 0 : _a.toString() : (_b = percent !== null && percent !== void 0 ? percent : 0) === null || _b === void 0 ? void 0 : _b.toString(), 10);
  }, [percent, props.success, props.successPercent]);
  const progressStatus = react.useMemo(() => {
    if (!ProgressStatuses.includes(status) && percentNumber >= 100) {
      return 'success';
    }
    return status || 'normal';
  }, [status, percentNumber]);
  const {
    getPrefixCls,
    direction,
    progress: progressStyle
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('progress', customizePrefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = progress_style(prefixCls);
  const progressInfo = react.useMemo(() => {
    if (!showInfo) {
      return null;
    }
    const successPercent = getSuccessPercent(props);
    let text;
    const textFormatter = format || (number => \`\${number}%\`);
    const isLineType = type === 'line';
    if (format || progressStatus !== 'exception' && progressStatus !== 'success') {
      text = textFormatter(validProgress(percent), validProgress(successPercent));
    } else if (progressStatus === 'exception') {
      text = isLineType ? /*#__PURE__*/react.createElement(CloseCircleFilled/* default */.Z, null) : /*#__PURE__*/react.createElement(CloseOutlined/* default */.Z, null);
    } else if (progressStatus === 'success') {
      text = isLineType ? /*#__PURE__*/react.createElement(CheckCircleFilled/* default */.Z, null) : /*#__PURE__*/react.createElement(CheckOutlined/* default */.Z, null);
    }
    return /*#__PURE__*/react.createElement("span", {
      className: \`\${prefixCls}-text\`,
      title: typeof text === 'string' ? text : undefined
    }, text);
  }, [showInfo, percent, percentNumber, progressStatus, type, prefixCls, format]);
  if (false) {}
  const strokeColorNotArray = Array.isArray(strokeColor) ? strokeColor[0] : strokeColor;
  const strokeColorNotGradient = typeof strokeColor === 'string' || Array.isArray(strokeColor) ? strokeColor : undefined;
  let progress;
  // Render progress shape
  if (type === 'line') {
    progress = steps ? ( /*#__PURE__*/react.createElement(progress_Steps, Object.assign({}, props, {
      strokeColor: strokeColorNotGradient,
      prefixCls: prefixCls,
      steps: steps
    }), progressInfo)) : ( /*#__PURE__*/react.createElement(progress_Line, Object.assign({}, props, {
      strokeColor: strokeColorNotArray,
      prefixCls: prefixCls,
      direction: direction
    }), progressInfo));
  } else if (type === 'circle' || type === 'dashboard') {
    progress = /*#__PURE__*/react.createElement(progress_Circle, Object.assign({}, props, {
      strokeColor: strokeColorNotArray,
      prefixCls: prefixCls,
      progressStatus: progressStatus
    }), progressInfo);
  }
  const classString = classnames_default()(prefixCls, \`\${prefixCls}-status-\${progressStatus}\`, \`\${prefixCls}-\${type === 'dashboard' && 'circle' || steps && 'steps' || type}\`, {
    [\`\${prefixCls}-inline-circle\`]: type === 'circle' && getSize(size, 'circle')[0] <= 20,
    [\`\${prefixCls}-show-info\`]: showInfo,
    [\`\${prefixCls}-\${size}\`]: typeof size === 'string',
    [\`\${prefixCls}-rtl\`]: direction === 'rtl'
  }, progressStyle === null || progressStyle === void 0 ? void 0 : progressStyle.className, className, rootClassName, hashId, cssVarCls);
  return wrapCSSVar( /*#__PURE__*/react.createElement("div", Object.assign({
    ref: ref,
    style: Object.assign(Object.assign({}, progressStyle === null || progressStyle === void 0 ? void 0 : progressStyle.style), style),
    className: classString,
    role: "progressbar",
    "aria-valuenow": percentNumber
  }, (0,omit/* default */.Z)(restProps, ['trailColor', 'strokeWidth', 'width', 'gapDegree', 'gapPosition', 'strokeLinecap', 'success', 'successPercent'])), progress));
});
if (false) {}
/* harmony default export */ var progress = (Progress);
;// CONCATENATED MODULE: ./node_modules/antd/es/progress/index.js
"use client";


/* harmony default export */ var es_progress = (progress);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///38703
`)}}]);
