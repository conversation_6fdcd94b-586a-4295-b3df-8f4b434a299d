"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[5419],{49495:function(__unused_webpack_module,__webpack_exports__){eval(`// This icon file is generated automatically.
var DownloadOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z" } }] }, "name": "download", "theme": "outlined" };
/* harmony default export */ __webpack_exports__.Z = (DownloadOutlined);
//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDk0OTUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSx5QkFBeUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsd1RBQXdULEdBQUc7QUFDcGQsc0RBQWUsZ0JBQWdCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL0Rvd25sb2FkT3V0bGluZWQuanM/Yzg3YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGljb24gZmlsZSBpcyBnZW5lcmF0ZWQgYXV0b21hdGljYWxseS5cbnZhciBEb3dubG9hZE91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk01MDUuNyA2NjFhOCA4IDAgMDAxMi42IDBsMTEyLTE0MS43YzQuMS01LjIuNC0xMi45LTYuMy0xMi45aC03NC4xVjE2OGMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDh2MzM4LjNINDAwYy02LjcgMC0xMC40IDcuNy02LjMgMTIuOWwxMTIgMTQxLjh6TTg3OCA2MjZoLTYwYy00LjQgMC04IDMuNi04IDh2MTU0SDIxNFY2MzRjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4djE5OGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2ODRjMTcuNyAwIDMyLTE0LjMgMzItMzJWNjM0YzAtNC40LTMuNi04LTgtOHpcIiB9IH1dIH0sIFwibmFtZVwiOiBcImRvd25sb2FkXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBEb3dubG9hZE91dGxpbmVkO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9
//# sourceURL=webpack-internal:///49495
`)},78367:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ es_upload; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/react-dom/index.js
var react_dom = __webpack_require__(73935);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(15671);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(43144);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js
var assertThisInitialized = __webpack_require__(97326);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/inherits.js + 1 modules
var inherits = __webpack_require__(32531);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createSuper.js
var createSuper = __webpack_require__(29388);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(74165);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(15861);
// EXTERNAL MODULE: ./node_modules/rc-util/es/pickAttrs.js
var pickAttrs = __webpack_require__(64217);
// EXTERNAL MODULE: ./node_modules/rc-util/es/warning.js
var warning = __webpack_require__(80334);
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/attr-accept.js

/* harmony default export */ var attr_accept = (function (file, acceptedFiles) {
  if (file && acceptedFiles) {
    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');
    var fileName = file.name || '';
    var mimeType = file.type || '';
    var baseMimeType = mimeType.replace(/\\/.*$/, '');
    return acceptedFilesArray.some(function (type) {
      var validType = type.trim();
      // This is something like */*,*  allow all files
      if (/^\\*(\\/\\*)?$/.test(type)) {
        return true;
      }

      // like .jpg, .png
      if (validType.charAt(0) === '.') {
        var lowerFileName = fileName.toLowerCase();
        var lowerType = validType.toLowerCase();
        var affixList = [lowerType];
        if (lowerType === '.jpg' || lowerType === '.jpeg') {
          affixList = ['.jpg', '.jpeg'];
        }
        return affixList.some(function (affix) {
          return lowerFileName.endsWith(affix);
        });
      }

      // This is something like a image/* mime type
      if (/\\/\\*$/.test(validType)) {
        return baseMimeType === validType.replace(/\\/.*$/, '');
      }

      // Full match
      if (mimeType === validType) {
        return true;
      }

      // Invalidate type should skip
      if (/^\\w+$/.test(validType)) {
        (0,warning/* default */.ZP)(false, "Upload takes an invalidate 'accept' type '".concat(validType, "'.Skip for check."));
        return true;
      }
      return false;
    });
  }
  return true;
});
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/request.js
function getError(option, xhr) {
  var msg = "cannot ".concat(option.method, " ").concat(option.action, " ").concat(xhr.status, "'");
  var err = new Error(msg);
  err.status = xhr.status;
  err.method = option.method;
  err.url = option.action;
  return err;
}
function getBody(xhr) {
  var text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }
  try {
    return JSON.parse(text);
  } catch (e) {
    return text;
  }
}
function upload(option) {
  // eslint-disable-next-line no-undef
  var xhr = new XMLHttpRequest();
  if (option.onProgress && xhr.upload) {
    xhr.upload.onprogress = function progress(e) {
      if (e.total > 0) {
        e.percent = e.loaded / e.total * 100;
      }
      option.onProgress(e);
    };
  }

  // eslint-disable-next-line no-undef
  var formData = new FormData();
  if (option.data) {
    Object.keys(option.data).forEach(function (key) {
      var value = option.data[key];
      // support key-value array data
      if (Array.isArray(value)) {
        value.forEach(function (item) {
          // { list: [ 11, 22 ] }
          // formData.append('list[]', 11);
          formData.append("".concat(key, "[]"), item);
        });
        return;
      }
      formData.append(key, value);
    });
  }

  // eslint-disable-next-line no-undef
  if (option.file instanceof Blob) {
    formData.append(option.filename, option.file, option.file.name);
  } else {
    formData.append(option.filename, option.file);
  }
  xhr.onerror = function error(e) {
    option.onError(e);
  };
  xhr.onload = function onload() {
    // allow success when 2xx status
    // see https://github.com/react-component/upload/issues/34
    if (xhr.status < 200 || xhr.status >= 300) {
      return option.onError(getError(option, xhr), getBody(xhr));
    }
    return option.onSuccess(getBody(xhr), xhr);
  };
  xhr.open(option.method, option.action, true);

  // Has to be after \`.open()\`. See https://github.com/enyo/dropzone/issues/179
  if (option.withCredentials && 'withCredentials' in xhr) {
    xhr.withCredentials = true;
  }
  var headers = option.headers || {};

  // when set headers['X-Requested-With'] = null , can close default XHR header
  // see https://github.com/react-component/upload/issues/33
  if (headers['X-Requested-With'] !== null) {
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  }
  Object.keys(headers).forEach(function (h) {
    if (headers[h] !== null) {
      xhr.setRequestHeader(h, headers[h]);
    }
  });
  xhr.send(formData);
  return {
    abort: function abort() {
      xhr.abort();
    }
  };
}
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/traverseFileTree.js
function loopFiles(item, callback) {
  var dirReader = item.createReader();
  var fileList = [];
  function sequence() {
    dirReader.readEntries(function (entries) {
      var entryList = Array.prototype.slice.apply(entries);
      fileList = fileList.concat(entryList);

      // Check if all the file has been viewed
      var isFinished = !entryList.length;
      if (isFinished) {
        callback(fileList);
      } else {
        sequence();
      }
    });
  }
  sequence();
}
var traverseFileTree = function traverseFileTree(files, callback, isAccepted) {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  var _traverseFileTree = function _traverseFileTree(item, path) {
    if (!item) {
      return;
    }
    // eslint-disable-next-line no-param-reassign
    item.path = path || '';
    if (item.isFile) {
      item.file(function (file) {
        if (isAccepted(file)) {
          // https://github.com/ant-design/ant-design/issues/16426
          if (item.fullPath && !file.webkitRelativePath) {
            Object.defineProperties(file, {
              webkitRelativePath: {
                writable: true
              }
            });
            // eslint-disable-next-line no-param-reassign
            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');
            Object.defineProperties(file, {
              webkitRelativePath: {
                writable: false
              }
            });
          }
          callback([file]);
        }
      });
    } else if (item.isDirectory) {
      loopFiles(item, function (entries) {
        entries.forEach(function (entryItem) {
          _traverseFileTree(entryItem, "".concat(path).concat(item.name, "/"));
        });
      });
    }
  };
  files.forEach(function (file) {
    _traverseFileTree(file.webkitGetAsEntry());
  });
};
/* harmony default export */ var es_traverseFileTree = (traverseFileTree);
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/uid.js
var now = +new Date();
var index = 0;
function uid() {
  // eslint-disable-next-line no-plusplus
  return "rc-upload-".concat(now, "-").concat(++index);
}
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/AjaxUploader.js













var _excluded = ["component", "prefixCls", "className", "classNames", "disabled", "id", "style", "styles", "multiple", "accept", "capture", "children", "directory", "openFileDialogOnClick", "onMouseEnter", "onMouseLeave", "hasControlInside"];
/* eslint react/no-is-mounted:0,react/sort-comp:0,react/prop-types:0 */







var AjaxUploader = /*#__PURE__*/function (_Component) {
  (0,inherits/* default */.Z)(AjaxUploader, _Component);
  var _super = (0,createSuper/* default */.Z)(AjaxUploader);
  function AjaxUploader() {
    var _this;
    (0,classCallCheck/* default */.Z)(this, AjaxUploader);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _super.call.apply(_super, [this].concat(args));
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "state", {
      uid: uid()
    });
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "reqs", {});
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "fileInput", void 0);
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "_isMounted", void 0);
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "onChange", function (e) {
      var _this$props = _this.props,
        accept = _this$props.accept,
        directory = _this$props.directory;
      var files = e.target.files;
      var acceptedFiles = (0,toConsumableArray/* default */.Z)(files).filter(function (file) {
        return !directory || attr_accept(file, accept);
      });
      _this.uploadFiles(acceptedFiles);
      _this.reset();
    });
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "onClick", function (event) {
      var el = _this.fileInput;
      if (!el) {
        return;
      }
      var target = event.target;
      var onClick = _this.props.onClick;
      if (target && target.tagName === 'BUTTON') {
        var parent = el.parentNode;
        parent.focus();
        target.blur();
      }
      el.click();
      if (onClick) {
        onClick(event);
      }
    });
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "onKeyDown", function (e) {
      if (e.key === 'Enter') {
        _this.onClick(e);
      }
    });
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "onFileDrop", function (e) {
      var multiple = _this.props.multiple;
      e.preventDefault();
      if (e.type === 'dragover') {
        return;
      }
      if (_this.props.directory) {
        es_traverseFileTree(Array.prototype.slice.call(e.dataTransfer.items), _this.uploadFiles, function (_file) {
          return attr_accept(_file, _this.props.accept);
        });
      } else {
        var files = (0,toConsumableArray/* default */.Z)(e.dataTransfer.files).filter(function (file) {
          return attr_accept(file, _this.props.accept);
        });
        if (multiple === false) {
          files = files.slice(0, 1);
        }
        _this.uploadFiles(files);
      }
    });
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "uploadFiles", function (files) {
      var originFiles = (0,toConsumableArray/* default */.Z)(files);
      var postFiles = originFiles.map(function (file) {
        // eslint-disable-next-line no-param-reassign
        file.uid = uid();
        return _this.processFile(file, originFiles);
      });

      // Batch upload files
      Promise.all(postFiles).then(function (fileList) {
        var onBatchStart = _this.props.onBatchStart;
        onBatchStart === null || onBatchStart === void 0 || onBatchStart(fileList.map(function (_ref) {
          var origin = _ref.origin,
            parsedFile = _ref.parsedFile;
          return {
            file: origin,
            parsedFile: parsedFile
          };
        }));
        fileList.filter(function (file) {
          return file.parsedFile !== null;
        }).forEach(function (file) {
          _this.post(file);
        });
      });
    });
    /**
     * Process file before upload. When all the file is ready, we start upload.
     */
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "processFile", /*#__PURE__*/function () {
      var _ref2 = (0,asyncToGenerator/* default */.Z)( /*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee(file, fileList) {
        var beforeUpload, transformedFile, action, mergedAction, data, mergedData, parsedData, parsedFile, mergedParsedFile;
        return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              beforeUpload = _this.props.beforeUpload;
              transformedFile = file;
              if (!beforeUpload) {
                _context.next = 14;
                break;
              }
              _context.prev = 3;
              _context.next = 6;
              return beforeUpload(file, fileList);
            case 6:
              transformedFile = _context.sent;
              _context.next = 12;
              break;
            case 9:
              _context.prev = 9;
              _context.t0 = _context["catch"](3);
              // Rejection will also trade as false
              transformedFile = false;
            case 12:
              if (!(transformedFile === false)) {
                _context.next = 14;
                break;
              }
              return _context.abrupt("return", {
                origin: file,
                parsedFile: null,
                action: null,
                data: null
              });
            case 14:
              // Get latest action
              action = _this.props.action;
              if (!(typeof action === 'function')) {
                _context.next = 21;
                break;
              }
              _context.next = 18;
              return action(file);
            case 18:
              mergedAction = _context.sent;
              _context.next = 22;
              break;
            case 21:
              mergedAction = action;
            case 22:
              // Get latest data
              data = _this.props.data;
              if (!(typeof data === 'function')) {
                _context.next = 29;
                break;
              }
              _context.next = 26;
              return data(file);
            case 26:
              mergedData = _context.sent;
              _context.next = 30;
              break;
            case 29:
              mergedData = data;
            case 30:
              parsedData =
              // string type is from legacy \`transformFile\`.
              // Not sure if this will work since no related test case works with it
              ((0,esm_typeof/* default */.Z)(transformedFile) === 'object' || typeof transformedFile === 'string') && transformedFile ? transformedFile : file;
              if (parsedData instanceof File) {
                parsedFile = parsedData;
              } else {
                parsedFile = new File([parsedData], file.name, {
                  type: file.type
                });
              }
              mergedParsedFile = parsedFile;
              mergedParsedFile.uid = file.uid;
              return _context.abrupt("return", {
                origin: file,
                data: mergedData,
                parsedFile: mergedParsedFile,
                action: mergedAction
              });
            case 35:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[3, 9]]);
      }));
      return function (_x, _x2) {
        return _ref2.apply(this, arguments);
      };
    }());
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "saveFileInput", function (node) {
      _this.fileInput = node;
    });
    return _this;
  }
  (0,createClass/* default */.Z)(AjaxUploader, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this._isMounted = true;
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this._isMounted = false;
      this.abort();
    }
  }, {
    key: "post",
    value: function post(_ref3) {
      var _this2 = this;
      var data = _ref3.data,
        origin = _ref3.origin,
        action = _ref3.action,
        parsedFile = _ref3.parsedFile;
      if (!this._isMounted) {
        return;
      }
      var _this$props2 = this.props,
        onStart = _this$props2.onStart,
        customRequest = _this$props2.customRequest,
        name = _this$props2.name,
        headers = _this$props2.headers,
        withCredentials = _this$props2.withCredentials,
        method = _this$props2.method;
      var uid = origin.uid;
      var request = customRequest || upload;
      var requestOption = {
        action: action,
        filename: name,
        data: data,
        file: parsedFile,
        headers: headers,
        withCredentials: withCredentials,
        method: method || 'post',
        onProgress: function onProgress(e) {
          var onProgress = _this2.props.onProgress;
          onProgress === null || onProgress === void 0 || onProgress(e, parsedFile);
        },
        onSuccess: function onSuccess(ret, xhr) {
          var onSuccess = _this2.props.onSuccess;
          onSuccess === null || onSuccess === void 0 || onSuccess(ret, parsedFile, xhr);
          delete _this2.reqs[uid];
        },
        onError: function onError(err, ret) {
          var onError = _this2.props.onError;
          onError === null || onError === void 0 || onError(err, ret, parsedFile);
          delete _this2.reqs[uid];
        }
      };
      onStart(origin);
      this.reqs[uid] = request(requestOption);
    }
  }, {
    key: "reset",
    value: function reset() {
      this.setState({
        uid: uid()
      });
    }
  }, {
    key: "abort",
    value: function abort(file) {
      var reqs = this.reqs;
      if (file) {
        var uid = file.uid ? file.uid : file;
        if (reqs[uid] && reqs[uid].abort) {
          reqs[uid].abort();
        }
        delete reqs[uid];
      } else {
        Object.keys(reqs).forEach(function (uid) {
          if (reqs[uid] && reqs[uid].abort) {
            reqs[uid].abort();
          }
          delete reqs[uid];
        });
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _clsx;
      var _this$props3 = this.props,
        Tag = _this$props3.component,
        prefixCls = _this$props3.prefixCls,
        className = _this$props3.className,
        _this$props3$classNam = _this$props3.classNames,
        classNames = _this$props3$classNam === void 0 ? {} : _this$props3$classNam,
        disabled = _this$props3.disabled,
        id = _this$props3.id,
        style = _this$props3.style,
        _this$props3$styles = _this$props3.styles,
        styles = _this$props3$styles === void 0 ? {} : _this$props3$styles,
        multiple = _this$props3.multiple,
        accept = _this$props3.accept,
        capture = _this$props3.capture,
        children = _this$props3.children,
        directory = _this$props3.directory,
        openFileDialogOnClick = _this$props3.openFileDialogOnClick,
        onMouseEnter = _this$props3.onMouseEnter,
        onMouseLeave = _this$props3.onMouseLeave,
        hasControlInside = _this$props3.hasControlInside,
        otherProps = (0,objectWithoutProperties/* default */.Z)(_this$props3, _excluded);
      var cls = classnames_default()((_clsx = {}, (0,defineProperty/* default */.Z)(_clsx, prefixCls, true), (0,defineProperty/* default */.Z)(_clsx, "".concat(prefixCls, "-disabled"), disabled), (0,defineProperty/* default */.Z)(_clsx, className, className), _clsx));
      // because input don't have directory/webkitdirectory type declaration
      var dirProps = directory ? {
        directory: 'directory',
        webkitdirectory: 'webkitdirectory'
      } : {};
      var events = disabled ? {} : {
        onClick: openFileDialogOnClick ? this.onClick : function () {},
        onKeyDown: openFileDialogOnClick ? this.onKeyDown : function () {},
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave,
        onDrop: this.onFileDrop,
        onDragOver: this.onFileDrop,
        tabIndex: hasControlInside ? undefined : '0'
      };
      return /*#__PURE__*/react.createElement(Tag, (0,esm_extends/* default */.Z)({}, events, {
        className: cls,
        role: hasControlInside ? undefined : 'button',
        style: style
      }), /*#__PURE__*/react.createElement("input", (0,esm_extends/* default */.Z)({}, (0,pickAttrs/* default */.Z)(otherProps, {
        aria: true,
        data: true
      }), {
        id: id,
        disabled: disabled,
        type: "file",
        ref: this.saveFileInput,
        onClick: function onClick(e) {
          return e.stopPropagation();
        } // https://github.com/ant-design/ant-design/issues/19948
        ,
        key: this.state.uid,
        style: (0,objectSpread2/* default */.Z)({
          display: 'none'
        }, styles.input),
        className: classNames.input,
        accept: accept
      }, dirProps, {
        multiple: multiple,
        onChange: this.onChange
      }, capture != null ? {
        capture: capture
      } : {})), children);
    }
  }]);
  return AjaxUploader;
}(react.Component);
/* harmony default export */ var es_AjaxUploader = (AjaxUploader);
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/Upload.js







/* eslint react/prop-types:0 */


function empty() {}
var Upload = /*#__PURE__*/function (_Component) {
  (0,inherits/* default */.Z)(Upload, _Component);
  var _super = (0,createSuper/* default */.Z)(Upload);
  function Upload() {
    var _this;
    (0,classCallCheck/* default */.Z)(this, Upload);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _super.call.apply(_super, [this].concat(args));
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "uploader", void 0);
    (0,defineProperty/* default */.Z)((0,assertThisInitialized/* default */.Z)(_this), "saveUploader", function (node) {
      _this.uploader = node;
    });
    return _this;
  }
  (0,createClass/* default */.Z)(Upload, [{
    key: "abort",
    value: function abort(file) {
      this.uploader.abort(file);
    }
  }, {
    key: "render",
    value: function render() {
      return /*#__PURE__*/react.createElement(es_AjaxUploader, (0,esm_extends/* default */.Z)({}, this.props, {
        ref: this.saveUploader
      }));
    }
  }]);
  return Upload;
}(react.Component);
(0,defineProperty/* default */.Z)(Upload, "defaultProps", {
  component: 'span',
  prefixCls: 'rc-upload',
  data: {},
  headers: {},
  name: 'file',
  multipart: false,
  onStart: empty,
  onError: empty,
  onSuccess: empty,
  multiple: false,
  beforeUpload: null,
  customRequest: null,
  withCredentials: false,
  openFileDialogOnClick: true,
  hasControlInside: false
});
/* harmony default export */ var es_Upload = (Upload);
;// CONCATENATED MODULE: ./node_modules/rc-upload/es/index.js

/* harmony default export */ var es = (es_Upload);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/DisabledContext.js
var DisabledContext = __webpack_require__(98866);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/useLocale.js
var useLocale = __webpack_require__(10110);
// EXTERNAL MODULE: ./node_modules/antd/es/locale/en_US.js + 1 modules
var en_US = __webpack_require__(24457);
// EXTERNAL MODULE: ./node_modules/antd/es/style/index.js
var style = __webpack_require__(14747);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/collapse.js
var collapse = __webpack_require__(33507);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/genComponentStyleHook.js + 5 modules
var genComponentStyleHook = __webpack_require__(91945);
// EXTERNAL MODULE: ./node_modules/antd/es/theme/util/statistic.js
var statistic = __webpack_require__(45503);
// EXTERNAL MODULE: ./node_modules/@ant-design/cssinjs/es/index.js + 35 modules
var cssinjs_es = __webpack_require__(36846);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/dragger.js

const genDraggerStyle = token => {
  const {
    componentCls,
    iconCls
  } = token;
  return {
    [\`\${componentCls}-wrapper\`]: {
      [\`\${componentCls}-drag\`]: {
        position: 'relative',
        width: '100%',
        height: '100%',
        textAlign: 'center',
        background: token.colorFillAlter,
        border: \`\${(0,cssinjs_es/* unit */.bf)(token.lineWidth)} dashed \${token.colorBorder}\`,
        borderRadius: token.borderRadiusLG,
        cursor: 'pointer',
        transition: \`border-color \${token.motionDurationSlow}\`,
        [componentCls]: {
          padding: token.padding
        },
        [\`\${componentCls}-btn\`]: {
          display: 'table',
          width: '100%',
          height: '100%',
          outline: 'none',
          borderRadius: token.borderRadiusLG,
          '&:focus-visible': {
            outline: \`\${(0,cssinjs_es/* unit */.bf)(token.lineWidthFocus)} solid \${token.colorPrimaryBorder}\`
          }
        },
        [\`\${componentCls}-drag-container\`]: {
          display: 'table-cell',
          verticalAlign: 'middle'
        },
        [\`
          &:not(\${componentCls}-disabled):hover,
          &-hover:not(\${componentCls}-disabled)
        \`]: {
          borderColor: token.colorPrimaryHover
        },
        [\`p\${componentCls}-drag-icon\`]: {
          marginBottom: token.margin,
          [iconCls]: {
            color: token.colorPrimary,
            fontSize: token.uploadThumbnailSize
          }
        },
        [\`p\${componentCls}-text\`]: {
          margin: \`0 0 \${(0,cssinjs_es/* unit */.bf)(token.marginXXS)}\`,
          color: token.colorTextHeading,
          fontSize: token.fontSizeLG
        },
        [\`p\${componentCls}-hint\`]: {
          color: token.colorTextDescription,
          fontSize: token.fontSize
        },
        // ===================== Disabled =====================
        [\`&\${componentCls}-disabled\`]: {
          [\`p\${componentCls}-drag-icon \${iconCls},
            p\${componentCls}-text,
            p\${componentCls}-hint
          \`]: {
            color: token.colorTextDisabled
          }
        }
      }
    }
  };
};
/* harmony default export */ var dragger = (genDraggerStyle);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/list.js


const genListStyle = token => {
  const {
    componentCls,
    antCls,
    iconCls,
    fontSize,
    lineHeight,
    calc
  } = token;
  const itemCls = \`\${componentCls}-list-item\`;
  const actionsCls = \`\${itemCls}-actions\`;
  const actionCls = \`\${itemCls}-action\`;
  const listItemHeightSM = token.fontHeightSM;
  return {
    [\`\${componentCls}-wrapper\`]: {
      [\`\${componentCls}-list\`]: Object.assign(Object.assign({}, (0,style/* clearFix */.dF)()), {
        lineHeight: token.lineHeight,
        [itemCls]: {
          position: 'relative',
          height: calc(token.lineHeight).mul(fontSize).equal(),
          marginTop: token.marginXS,
          fontSize,
          display: 'flex',
          alignItems: 'center',
          transition: \`background-color \${token.motionDurationSlow}\`,
          '&:hover': {
            backgroundColor: token.controlItemBgHover
          },
          [\`\${itemCls}-name\`]: Object.assign(Object.assign({}, style/* textEllipsis */.vS), {
            padding: \`0 \${(0,cssinjs_es/* unit */.bf)(token.paddingXS)}\`,
            lineHeight,
            flex: 'auto',
            transition: \`all \${token.motionDurationSlow}\`
          }),
          [actionsCls]: {
            [actionCls]: {
              opacity: 0
            },
            [iconCls]: {
              color: token.actionsColor,
              transition: \`all \${token.motionDurationSlow}\`
            },
            [\`
              \${actionCls}:focus-visible,
              &.picture \${actionCls}
            \`]: {
              opacity: 1
            },
            [\`\${actionCls}\${antCls}-btn\`]: {
              height: listItemHeightSM,
              border: 0,
              lineHeight: 1
            }
          },
          [\`\${componentCls}-icon \${iconCls}\`]: {
            color: token.colorTextDescription,
            fontSize
          },
          [\`\${itemCls}-progress\`]: {
            position: 'absolute',
            bottom: token.calc(token.uploadProgressOffset).mul(-1).equal(),
            width: '100%',
            paddingInlineStart: calc(fontSize).add(token.paddingXS).equal(),
            fontSize,
            lineHeight: 0,
            pointerEvents: 'none',
            '> div': {
              margin: 0
            }
          }
        },
        [\`\${itemCls}:hover \${actionCls}\`]: {
          opacity: 1
        },
        [\`\${itemCls}-error\`]: {
          color: token.colorError,
          [\`\${itemCls}-name, \${componentCls}-icon \${iconCls}\`]: {
            color: token.colorError
          },
          [actionsCls]: {
            [\`\${iconCls}, \${iconCls}:hover\`]: {
              color: token.colorError
            },
            [actionCls]: {
              opacity: 1
            }
          }
        },
        [\`\${componentCls}-list-item-container\`]: {
          transition: \`opacity \${token.motionDurationSlow}, height \${token.motionDurationSlow}\`,
          // For smooth removing animation
          '&::before': {
            display: 'table',
            width: 0,
            height: 0,
            content: '""'
          }
        }
      })
    }
  };
};
/* harmony default export */ var list = (genListStyle);
// EXTERNAL MODULE: ./node_modules/antd/es/style/motion/fade.js
var fade = __webpack_require__(16932);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/motion.js


const uploadAnimateInlineIn = new cssinjs_es/* Keyframes */.E4('uploadAnimateInlineIn', {
  from: {
    width: 0,
    height: 0,
    margin: 0,
    padding: 0,
    opacity: 0
  }
});
const uploadAnimateInlineOut = new cssinjs_es/* Keyframes */.E4('uploadAnimateInlineOut', {
  to: {
    width: 0,
    height: 0,
    margin: 0,
    padding: 0,
    opacity: 0
  }
});
// =========================== Motion ===========================
const genMotionStyle = token => {
  const {
    componentCls
  } = token;
  const inlineCls = \`\${componentCls}-animate-inline\`;
  return [{
    [\`\${componentCls}-wrapper\`]: {
      [\`\${inlineCls}-appear, \${inlineCls}-enter, \${inlineCls}-leave\`]: {
        animationDuration: token.motionDurationSlow,
        animationTimingFunction: token.motionEaseInOutCirc,
        animationFillMode: 'forwards'
      },
      [\`\${inlineCls}-appear, \${inlineCls}-enter\`]: {
        animationName: uploadAnimateInlineIn
      },
      [\`\${inlineCls}-leave\`]: {
        animationName: uploadAnimateInlineOut
      }
    }
  }, {
    [\`\${componentCls}-wrapper\`]: (0,fade/* initFadeMotion */.J$)(token)
  }, uploadAnimateInlineIn, uploadAnimateInlineOut];
};
/* harmony default export */ var motion = (genMotionStyle);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/colors/es/index.js + 1 modules
var colors_es = __webpack_require__(78589);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/picture.js



const genPictureStyle = token => {
  const {
    componentCls,
    iconCls,
    uploadThumbnailSize,
    uploadProgressOffset,
    calc
  } = token;
  const listCls = \`\${componentCls}-list\`;
  const itemCls = \`\${listCls}-item\`;
  return {
    [\`\${componentCls}-wrapper\`]: {
      // \${listCls} \u589E\u52A0\u4F18\u5148\u7EA7
      [\`
        \${listCls}\${listCls}-picture,
        \${listCls}\${listCls}-picture-card,
        \${listCls}\${listCls}-picture-circle
      \`]: {
        [itemCls]: {
          position: 'relative',
          height: calc(uploadThumbnailSize).add(calc(token.lineWidth).mul(2)).add(calc(token.paddingXS).mul(2)).equal(),
          padding: token.paddingXS,
          border: \`\${(0,cssinjs_es/* unit */.bf)(token.lineWidth)} \${token.lineType} \${token.colorBorder}\`,
          borderRadius: token.borderRadiusLG,
          '&:hover': {
            background: 'transparent'
          },
          [\`\${itemCls}-thumbnail\`]: Object.assign(Object.assign({}, style/* textEllipsis */.vS), {
            width: uploadThumbnailSize,
            height: uploadThumbnailSize,
            lineHeight: (0,cssinjs_es/* unit */.bf)(calc(uploadThumbnailSize).add(token.paddingSM).equal()),
            textAlign: 'center',
            flex: 'none',
            [iconCls]: {
              fontSize: token.fontSizeHeading2,
              color: token.colorPrimary
            },
            img: {
              display: 'block',
              width: '100%',
              height: '100%',
              overflow: 'hidden'
            }
          }),
          [\`\${itemCls}-progress\`]: {
            bottom: uploadProgressOffset,
            width: \`calc(100% - \${(0,cssinjs_es/* unit */.bf)(calc(token.paddingSM).mul(2).equal())})\`,
            marginTop: 0,
            paddingInlineStart: calc(uploadThumbnailSize).add(token.paddingXS).equal()
          }
        },
        [\`\${itemCls}-error\`]: {
          borderColor: token.colorError,
          // Adjust the color of the error icon : https://github.com/ant-design/ant-design/pull/24160
          [\`\${itemCls}-thumbnail \${iconCls}\`]: {
            [\`svg path[fill='\${colors_es/* blue */.iN[0]}']\`]: {
              fill: token.colorErrorBg
            },
            [\`svg path[fill='\${colors_es/* blue */.iN.primary}']\`]: {
              fill: token.colorError
            }
          }
        },
        [\`\${itemCls}-uploading\`]: {
          borderStyle: 'dashed',
          [\`\${itemCls}-name\`]: {
            marginBottom: uploadProgressOffset
          }
        }
      },
      [\`\${listCls}\${listCls}-picture-circle \${itemCls}\`]: {
        [\`&, &::before, \${itemCls}-thumbnail\`]: {
          borderRadius: '50%'
        }
      }
    }
  };
};
const genPictureCardStyle = token => {
  const {
    componentCls,
    iconCls,
    fontSizeLG,
    colorTextLightSolid,
    calc
  } = token;
  const listCls = \`\${componentCls}-list\`;
  const itemCls = \`\${listCls}-item\`;
  const uploadPictureCardSize = token.uploadPicCardSize;
  return {
    [\`
      \${componentCls}-wrapper\${componentCls}-picture-card-wrapper,
      \${componentCls}-wrapper\${componentCls}-picture-circle-wrapper
    \`]: Object.assign(Object.assign({}, (0,style/* clearFix */.dF)()), {
      display: 'inline-block',
      width: '100%',
      [\`\${componentCls}\${componentCls}-select\`]: {
        width: uploadPictureCardSize,
        height: uploadPictureCardSize,
        marginInlineEnd: token.marginXS,
        marginBottom: token.marginXS,
        textAlign: 'center',
        verticalAlign: 'top',
        backgroundColor: token.colorFillAlter,
        border: \`\${(0,cssinjs_es/* unit */.bf)(token.lineWidth)} dashed \${token.colorBorder}\`,
        borderRadius: token.borderRadiusLG,
        cursor: 'pointer',
        transition: \`border-color \${token.motionDurationSlow}\`,
        [\`> \${componentCls}\`]: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          textAlign: 'center'
        },
        [\`&:not(\${componentCls}-disabled):hover\`]: {
          borderColor: token.colorPrimary
        }
      },
      // list
      [\`\${listCls}\${listCls}-picture-card, \${listCls}\${listCls}-picture-circle\`]: {
        [\`\${listCls}-item-container\`]: {
          display: 'inline-block',
          width: uploadPictureCardSize,
          height: uploadPictureCardSize,
          marginBlock: \`0 \${(0,cssinjs_es/* unit */.bf)(token.marginXS)}\`,
          marginInline: \`0 \${(0,cssinjs_es/* unit */.bf)(token.marginXS)}\`,
          verticalAlign: 'top'
        },
        '&::after': {
          display: 'none'
        },
        [itemCls]: {
          height: '100%',
          margin: 0,
          '&::before': {
            position: 'absolute',
            zIndex: 1,
            width: \`calc(100% - \${(0,cssinjs_es/* unit */.bf)(calc(token.paddingXS).mul(2).equal())})\`,
            height: \`calc(100% - \${(0,cssinjs_es/* unit */.bf)(calc(token.paddingXS).mul(2).equal())})\`,
            backgroundColor: token.colorBgMask,
            opacity: 0,
            transition: \`all \${token.motionDurationSlow}\`,
            content: '" "'
          }
        },
        [\`\${itemCls}:hover\`]: {
          [\`&::before, \${itemCls}-actions\`]: {
            opacity: 1
          }
        },
        [\`\${itemCls}-actions\`]: {
          position: 'absolute',
          insetInlineStart: 0,
          zIndex: 10,
          width: '100%',
          whiteSpace: 'nowrap',
          textAlign: 'center',
          opacity: 0,
          transition: \`all \${token.motionDurationSlow}\`,
          [\`
            \${iconCls}-eye,
            \${iconCls}-download,
            \${iconCls}-delete
          \`]: {
            zIndex: 10,
            width: fontSizeLG,
            margin: \`0 \${(0,cssinjs_es/* unit */.bf)(token.marginXXS)}\`,
            fontSize: fontSizeLG,
            cursor: 'pointer',
            transition: \`all \${token.motionDurationSlow}\`,
            color: colorTextLightSolid,
            '&:hover': {
              color: colorTextLightSolid
            },
            svg: {
              verticalAlign: 'baseline'
            }
          }
        },
        [\`\${itemCls}-thumbnail, \${itemCls}-thumbnail img\`]: {
          position: 'static',
          display: 'block',
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        },
        [\`\${itemCls}-name\`]: {
          display: 'none',
          textAlign: 'center'
        },
        [\`\${itemCls}-file + \${itemCls}-name\`]: {
          position: 'absolute',
          bottom: token.margin,
          display: 'block',
          width: \`calc(100% - \${(0,cssinjs_es/* unit */.bf)(calc(token.paddingXS).mul(2).equal())})\`
        },
        [\`\${itemCls}-uploading\`]: {
          [\`&\${itemCls}\`]: {
            backgroundColor: token.colorFillAlter
          },
          [\`&::before, \${iconCls}-eye, \${iconCls}-download, \${iconCls}-delete\`]: {
            display: 'none'
          }
        },
        [\`\${itemCls}-progress\`]: {
          bottom: token.marginXL,
          width: \`calc(100% - \${(0,cssinjs_es/* unit */.bf)(calc(token.paddingXS).mul(2).equal())})\`,
          paddingInlineStart: 0
        }
      }
    }),
    [\`\${componentCls}-wrapper\${componentCls}-picture-circle-wrapper\`]: {
      [\`\${componentCls}\${componentCls}-select\`]: {
        borderRadius: '50%'
      }
    }
  };
};

;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/rtl.js
// =========================== Motion ===========================
const genRtlStyle = token => {
  const {
    componentCls
  } = token;
  return {
    [\`\${componentCls}-rtl\`]: {
      direction: 'rtl'
    }
  };
};
/* harmony default export */ var rtl = (genRtlStyle);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/style/index.js








const genBaseStyle = token => {
  const {
    componentCls,
    colorTextDisabled
  } = token;
  return {
    [\`\${componentCls}-wrapper\`]: Object.assign(Object.assign({}, (0,style/* resetComponent */.Wf)(token)), {
      [componentCls]: {
        outline: 0,
        "input[type='file']": {
          cursor: 'pointer'
        }
      },
      [\`\${componentCls}-select\`]: {
        display: 'inline-block'
      },
      [\`\${componentCls}-disabled\`]: {
        color: colorTextDisabled,
        cursor: 'not-allowed'
      }
    })
  };
};
const prepareComponentToken = token => ({
  actionsColor: token.colorTextDescription
});
// ============================== Export ==============================
/* harmony default export */ var upload_style = ((0,genComponentStyleHook/* genStyleHooks */.I$)('Upload', token => {
  const {
    fontSizeHeading3,
    fontHeight,
    lineWidth,
    controlHeightLG,
    calc
  } = token;
  const uploadToken = (0,statistic/* merge */.TS)(token, {
    uploadThumbnailSize: calc(fontSizeHeading3).mul(2).equal(),
    uploadProgressOffset: calc(calc(fontHeight).div(2)).add(lineWidth).equal(),
    uploadPicCardSize: calc(controlHeightLG).mul(2.55).equal()
  });
  return [genBaseStyle(uploadToken), dragger(uploadToken), genPictureStyle(uploadToken), genPictureCardStyle(uploadToken), list(uploadToken), motion(uploadToken), rtl(uploadToken), (0,collapse/* default */.Z)(uploadToken)];
}, prepareComponentToken));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/FileTwoTone.js
// This icon file is generated automatically.
var FileTwoTone = { "icon": function render(primaryColor, secondaryColor) { return { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M534 352V136H232v752h560V394H576a42 42 0 01-42-42z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z", "fill": primaryColor } }] }; }, "name": "file", "theme": "twotone" };
/* harmony default export */ var asn_FileTwoTone = (FileTwoTone);

// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/components/AntdIcon.js + 3 modules
var AntdIcon = __webpack_require__(93771);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/FileTwoTone.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var FileTwoTone_FileTwoTone = function FileTwoTone(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_FileTwoTone
  }));
};
if (false) {}
/* harmony default export */ var icons_FileTwoTone = (/*#__PURE__*/react.forwardRef(FileTwoTone_FileTwoTone));
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js
var LoadingOutlined = __webpack_require__(19267);
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/PaperClipOutlined.js
// This icon file is generated automatically.
var PaperClipOutlined = { "icon": { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z" } }] }, "name": "paper-clip", "theme": "outlined" };
/* harmony default export */ var asn_PaperClipOutlined = (PaperClipOutlined);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/PaperClipOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PaperClipOutlined_PaperClipOutlined = function PaperClipOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_PaperClipOutlined
  }));
};
if (false) {}
/* harmony default export */ var icons_PaperClipOutlined = (/*#__PURE__*/react.forwardRef(PaperClipOutlined_PaperClipOutlined));
;// CONCATENATED MODULE: ./node_modules/@ant-design/icons-svg/es/asn/PictureTwoTone.js
// This icon file is generated automatically.
var PictureTwoTone = { "icon": function render(primaryColor, secondaryColor) { return { "tag": "svg", "attrs": { "viewBox": "64 64 896 896", "focusable": "false" }, "children": [{ "tag": "path", "attrs": { "d": "M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z", "fill": primaryColor } }, { "tag": "path", "attrs": { "d": "M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M276 368a28 28 0 1056 0 28 28 0 10-56 0z", "fill": secondaryColor } }, { "tag": "path", "attrs": { "d": "M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z", "fill": primaryColor } }] }; }, "name": "picture", "theme": "twotone" };
/* harmony default export */ var asn_PictureTwoTone = (PictureTwoTone);

;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/PictureTwoTone.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var PictureTwoTone_PictureTwoTone = function PictureTwoTone(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_PictureTwoTone
  }));
};
if (false) {}
/* harmony default export */ var icons_PictureTwoTone = (/*#__PURE__*/react.forwardRef(PictureTwoTone_PictureTwoTone));
// EXTERNAL MODULE: ./node_modules/rc-motion/es/index.js + 12 modules
var rc_motion_es = __webpack_require__(82225);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/hooks/useForceUpdate.js
var useForceUpdate = __webpack_require__(57838);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/motion.js
var _util_motion = __webpack_require__(33603);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/reactNode.js
var reactNode = __webpack_require__(96159);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/utils.js

function file2Obj(file) {
  return Object.assign(Object.assign({}, file), {
    lastModified: file.lastModified,
    lastModifiedDate: file.lastModifiedDate,
    name: file.name,
    size: file.size,
    type: file.type,
    uid: file.uid,
    percent: 0,
    originFileObj: file
  });
}
/** Upload fileList. Replace file if exist or just push into it. */
function updateFileList(file, fileList) {
  const nextFileList = (0,toConsumableArray/* default */.Z)(fileList);
  const fileIndex = nextFileList.findIndex(_ref => {
    let {
      uid
    } = _ref;
    return uid === file.uid;
  });
  if (fileIndex === -1) {
    nextFileList.push(file);
  } else {
    nextFileList[fileIndex] = file;
  }
  return nextFileList;
}
function getFileItem(file, fileList) {
  const matchKey = file.uid !== undefined ? 'uid' : 'name';
  return fileList.filter(item => item[matchKey] === file[matchKey])[0];
}
function removeFileItem(file, fileList) {
  const matchKey = file.uid !== undefined ? 'uid' : 'name';
  const removed = fileList.filter(item => item[matchKey] !== file[matchKey]);
  if (removed.length === fileList.length) {
    return null;
  }
  return removed;
}
// ==================== Default Image Preview ====================
const extname = function () {
  let url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  const temp = url.split('/');
  const filename = temp[temp.length - 1];
  const filenameWithoutSuffix = filename.split(/#|\\?/)[0];
  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];
};
const isImageFileType = type => type.indexOf('image/') === 0;
const isImageUrl = file => {
  if (file.type && !file.thumbUrl) {
    return isImageFileType(file.type);
  }
  const url = file.thumbUrl || file.url || '';
  const extension = extname(url);
  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {
    return true;
  }
  if (/^data:/.test(url)) {
    // other file types of base64
    return false;
  }
  if (extension) {
    // other file types which have extension
    return false;
  }
  return true;
};
const MEASURE_SIZE = 200;
function previewImage(file) {
  return new Promise(resolve => {
    if (!file.type || !isImageFileType(file.type)) {
      resolve('');
      return;
    }
    const canvas = document.createElement('canvas');
    canvas.width = MEASURE_SIZE;
    canvas.height = MEASURE_SIZE;
    canvas.style.cssText = \`position: fixed; left: 0; top: 0; width: \${MEASURE_SIZE}px; height: \${MEASURE_SIZE}px; z-index: 9999; display: none;\`;
    document.body.appendChild(canvas);
    const ctx = canvas.getContext('2d');
    const img = new Image();
    img.onload = () => {
      const {
        width,
        height
      } = img;
      let drawWidth = MEASURE_SIZE;
      let drawHeight = MEASURE_SIZE;
      let offsetX = 0;
      let offsetY = 0;
      if (width > height) {
        drawHeight = height * (MEASURE_SIZE / width);
        offsetY = -(drawHeight - drawWidth) / 2;
      } else {
        drawWidth = width * (MEASURE_SIZE / height);
        offsetX = -(drawWidth - drawHeight) / 2;
      }
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      const dataURL = canvas.toDataURL();
      document.body.removeChild(canvas);
      window.URL.revokeObjectURL(img.src);
      resolve(dataURL);
    };
    img.crossOrigin = 'anonymous';
    if (file.type.startsWith('image/svg+xml')) {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result && typeof reader.result === 'string') {
          img.src = reader.result;
        }
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('image/gif')) {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result) {
          resolve(reader.result);
        }
      };
      reader.readAsDataURL(file);
    } else {
      img.src = window.URL.createObjectURL(file);
    }
  });
}
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/DeleteOutlined.js
var asn_DeleteOutlined = __webpack_require__(47046);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/DeleteOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DeleteOutlined = function DeleteOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_DeleteOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_DeleteOutlined = (/*#__PURE__*/react.forwardRef(DeleteOutlined));
// EXTERNAL MODULE: ./node_modules/@ant-design/icons-svg/es/asn/DownloadOutlined.js
var asn_DownloadOutlined = __webpack_require__(49495);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/DownloadOutlined.js

// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY




var DownloadOutlined = function DownloadOutlined(props, ref) {
  return /*#__PURE__*/react.createElement(AntdIcon/* default */.Z, (0,esm_extends/* default */.Z)({}, props, {
    ref: ref,
    icon: asn_DownloadOutlined/* default */.Z
  }));
};
if (false) {}
/* harmony default export */ var icons_DownloadOutlined = (/*#__PURE__*/react.forwardRef(DownloadOutlined));
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/EyeOutlined.js
var EyeOutlined = __webpack_require__(1208);
// EXTERNAL MODULE: ./node_modules/antd/es/progress/index.js + 13 modules
var progress = __webpack_require__(38703);
// EXTERNAL MODULE: ./node_modules/antd/es/tooltip/index.js + 3 modules
var tooltip = __webpack_require__(83062);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/UploadList/ListItem.js
"use client";










const ListItem = /*#__PURE__*/react.forwardRef((_ref, ref) => {
  let {
    prefixCls,
    className,
    style,
    locale,
    listType,
    file,
    items,
    progress: progressProps,
    iconRender,
    actionIconRender,
    itemRender,
    isImgUrl,
    showPreviewIcon,
    showRemoveIcon,
    showDownloadIcon,
    previewIcon: customPreviewIcon,
    removeIcon: customRemoveIcon,
    downloadIcon: customDownloadIcon,
    onPreview,
    onDownload,
    onClose
  } = _ref;
  var _a, _b;
  // Status: which will ignore \`removed\` status
  const {
    status
  } = file;
  const [mergedStatus, setMergedStatus] = react.useState(status);
  react.useEffect(() => {
    if (status !== 'removed') {
      setMergedStatus(status);
    }
  }, [status]);
  // Delay to show the progress bar
  const [showProgress, setShowProgress] = react.useState(false);
  react.useEffect(() => {
    const timer = setTimeout(() => {
      setShowProgress(true);
    }, 300);
    return () => {
      clearTimeout(timer);
    };
  }, []);
  const iconNode = iconRender(file);
  let icon = /*#__PURE__*/react.createElement("div", {
    className: \`\${prefixCls}-icon\`
  }, iconNode);
  if (listType === 'picture' || listType === 'picture-card' || listType === 'picture-circle') {
    if (mergedStatus === 'uploading' || !file.thumbUrl && !file.url) {
      const uploadingClassName = classnames_default()(\`\${prefixCls}-list-item-thumbnail\`, {
        [\`\${prefixCls}-list-item-file\`]: mergedStatus !== 'uploading'
      });
      icon = /*#__PURE__*/react.createElement("div", {
        className: uploadingClassName
      }, iconNode);
    } else {
      const thumbnail = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? ( /*#__PURE__*/react.createElement("img", {
        src: file.thumbUrl || file.url,
        alt: file.name,
        className: \`\${prefixCls}-list-item-image\`,
        crossOrigin: file.crossOrigin
      })) : iconNode;
      const aClassName = classnames_default()(\`\${prefixCls}-list-item-thumbnail\`, {
        [\`\${prefixCls}-list-item-file\`]: isImgUrl && !isImgUrl(file)
      });
      icon = /*#__PURE__*/react.createElement("a", {
        className: aClassName,
        onClick: e => onPreview(file, e),
        href: file.url || file.thumbUrl,
        target: "_blank",
        rel: "noopener noreferrer"
      }, thumbnail);
    }
  }
  const listItemClassName = classnames_default()(\`\${prefixCls}-list-item\`, \`\${prefixCls}-list-item-\${mergedStatus}\`);
  const linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;
  const removeIcon = showRemoveIcon ? actionIconRender((typeof customRemoveIcon === 'function' ? customRemoveIcon(file) : customRemoveIcon) || ( /*#__PURE__*/react.createElement(icons_DeleteOutlined, null)), () => onClose(file), prefixCls, locale.removeFile,
  // acceptUploadDisabled is true, only remove icon will follow Upload disabled prop
  // https://github.com/ant-design/ant-design/issues/46171
  true) : null;
  const downloadIcon = showDownloadIcon && mergedStatus === 'done' ? actionIconRender((typeof customDownloadIcon === 'function' ? customDownloadIcon(file) : customDownloadIcon) || /*#__PURE__*/react.createElement(icons_DownloadOutlined, null), () => onDownload(file), prefixCls, locale.downloadFile) : null;
  const downloadOrDelete = listType !== 'picture-card' && listType !== 'picture-circle' && ( /*#__PURE__*/react.createElement("span", {
    key: "download-delete",
    className: classnames_default()(\`\${prefixCls}-list-item-actions\`, {
      picture: listType === 'picture'
    })
  }, downloadIcon, removeIcon));
  const listItemNameClass = classnames_default()(\`\${prefixCls}-list-item-name\`);
  const fileName = file.url ? [/*#__PURE__*/react.createElement("a", Object.assign({
    key: "view",
    target: "_blank",
    rel: "noopener noreferrer",
    className: listItemNameClass,
    title: file.name
  }, linkProps, {
    href: file.url,
    onClick: e => onPreview(file, e)
  }), file.name), downloadOrDelete] : [/*#__PURE__*/react.createElement("span", {
    key: "view",
    className: listItemNameClass,
    onClick: e => onPreview(file, e),
    title: file.name
  }, file.name), downloadOrDelete];
  const previewIcon = showPreviewIcon && (file.url || file.thumbUrl) ? ( /*#__PURE__*/react.createElement("a", {
    href: file.url || file.thumbUrl,
    target: "_blank",
    rel: "noopener noreferrer",
    onClick: e => onPreview(file, e),
    title: locale.previewFile
  }, typeof customPreviewIcon === 'function' ? customPreviewIcon(file) : customPreviewIcon || /*#__PURE__*/react.createElement(EyeOutlined/* default */.Z, null))) : null;
  const pictureCardActions = (listType === 'picture-card' || listType === 'picture-circle') && mergedStatus !== 'uploading' && ( /*#__PURE__*/react.createElement("span", {
    className: \`\${prefixCls}-list-item-actions\`
  }, previewIcon, mergedStatus === 'done' && downloadIcon, removeIcon));
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  const rootPrefixCls = getPrefixCls();
  const dom = /*#__PURE__*/react.createElement("div", {
    className: listItemClassName
  }, icon, fileName, pictureCardActions, showProgress && ( /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, {
    motionName: \`\${rootPrefixCls}-fade\`,
    visible: mergedStatus === 'uploading',
    motionDeadline: 2000
  }, _ref2 => {
    let {
      className: motionClassName
    } = _ref2;
    // show loading icon if upload progress listener is disabled
    const loadingProgress = 'percent' in file ? ( /*#__PURE__*/react.createElement(progress/* default */.Z, Object.assign({}, progressProps, {
      type: "line",
      percent: file.percent,
      "aria-label": file['aria-label'],
      "aria-labelledby": file['aria-labelledby']
    }))) : null;
    return /*#__PURE__*/react.createElement("div", {
      className: classnames_default()(\`\${prefixCls}-list-item-progress\`, motionClassName)
    }, loadingProgress);
  })));
  const message = file.response && typeof file.response === 'string' ? file.response : ((_a = file.error) === null || _a === void 0 ? void 0 : _a.statusText) || ((_b = file.error) === null || _b === void 0 ? void 0 : _b.message) || locale.uploadError;
  const item = mergedStatus === 'error' ? ( /*#__PURE__*/react.createElement(tooltip/* default */.Z, {
    title: message,
    getPopupContainer: node => node.parentNode
  }, dom)) : dom;
  return /*#__PURE__*/react.createElement("div", {
    className: classnames_default()(\`\${prefixCls}-list-item-container\`, className),
    style: style,
    ref: ref
  }, itemRender ? itemRender(item, file, items, {
    download: onDownload.bind(null, file),
    preview: onPreview.bind(null, file),
    remove: onClose.bind(null, file)
  }) : item);
});
/* harmony default export */ var UploadList_ListItem = (ListItem);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/UploadList/index.js
"use client";
















const InternalUploadList = (props, ref) => {
  const {
    listType = 'text',
    previewFile = previewImage,
    onPreview,
    onDownload,
    onRemove,
    locale,
    iconRender,
    isImageUrl: isImgUrl = isImageUrl,
    prefixCls: customizePrefixCls,
    items = [],
    showPreviewIcon = true,
    showRemoveIcon = true,
    showDownloadIcon = false,
    removeIcon,
    previewIcon,
    downloadIcon,
    progress = {
      size: [-1, 2],
      showInfo: false
    },
    appendAction,
    appendActionVisible = true,
    itemRender,
    disabled
  } = props;
  const forceUpdate = (0,useForceUpdate/* default */.Z)();
  const [motionAppear, setMotionAppear] = react.useState(false);
  // ============================= Effect =============================
  react.useEffect(() => {
    if (listType !== 'picture' && listType !== 'picture-card' && listType !== 'picture-circle') {
      return;
    }
    (items || []).forEach(file => {
      if (typeof document === 'undefined' || typeof window === 'undefined' || !window.FileReader || !window.File || !(file.originFileObj instanceof File || file.originFileObj instanceof Blob) || file.thumbUrl !== undefined) {
        return;
      }
      file.thumbUrl = '';
      if (previewFile) {
        previewFile(file.originFileObj).then(previewDataUrl => {
          // Need append '' to avoid dead loop
          file.thumbUrl = previewDataUrl || '';
          forceUpdate();
        });
      }
    });
  }, [listType, items, previewFile]);
  react.useEffect(() => {
    setMotionAppear(true);
  }, []);
  // ============================= Events =============================
  const onInternalPreview = (file, e) => {
    if (!onPreview) {
      return;
    }
    e === null || e === void 0 ? void 0 : e.preventDefault();
    return onPreview(file);
  };
  const onInternalDownload = file => {
    if (typeof onDownload === 'function') {
      onDownload(file);
    } else if (file.url) {
      window.open(file.url);
    }
  };
  const onInternalClose = file => {
    onRemove === null || onRemove === void 0 ? void 0 : onRemove(file);
  };
  const internalIconRender = file => {
    if (iconRender) {
      return iconRender(file, listType);
    }
    const isLoading = file.status === 'uploading';
    const fileIcon = isImgUrl && isImgUrl(file) ? /*#__PURE__*/react.createElement(icons_PictureTwoTone, null) : /*#__PURE__*/react.createElement(icons_FileTwoTone, null);
    let icon = isLoading ? /*#__PURE__*/react.createElement(LoadingOutlined/* default */.Z, null) : /*#__PURE__*/react.createElement(icons_PaperClipOutlined, null);
    if (listType === 'picture') {
      icon = isLoading ? /*#__PURE__*/react.createElement(LoadingOutlined/* default */.Z, null) : fileIcon;
    } else if (listType === 'picture-card' || listType === 'picture-circle') {
      icon = isLoading ? locale.uploading : fileIcon;
    }
    return icon;
  };
  const actionIconRender = (customIcon, callback, prefixCls, title, acceptUploadDisabled) => {
    const btnProps = {
      type: 'text',
      size: 'small',
      title,
      onClick: e => {
        callback();
        if ((0,reactNode/* isValidElement */.l$)(customIcon) && customIcon.props.onClick) {
          customIcon.props.onClick(e);
        }
      },
      className: \`\${prefixCls}-list-item-action\`
    };
    if (acceptUploadDisabled) {
      btnProps.disabled = disabled;
    }
    if ((0,reactNode/* isValidElement */.l$)(customIcon)) {
      const btnIcon = (0,reactNode/* cloneElement */.Tm)(customIcon, Object.assign(Object.assign({}, customIcon.props), {
        onClick: () => {}
      }));
      return /*#__PURE__*/react.createElement(es_button/* default */.ZP, Object.assign({}, btnProps, {
        icon: btnIcon
      }));
    }
    return /*#__PURE__*/react.createElement(es_button/* default */.ZP, Object.assign({}, btnProps), /*#__PURE__*/react.createElement("span", null, customIcon));
  };
  // ============================== Ref ===============================
  // Test needs
  react.useImperativeHandle(ref, () => ({
    handlePreview: onInternalPreview,
    handleDownload: onInternalDownload
  }));
  const {
    getPrefixCls
  } = react.useContext(context/* ConfigContext */.E_);
  // ============================= Render =============================
  const prefixCls = getPrefixCls('upload', customizePrefixCls);
  const rootPrefixCls = getPrefixCls();
  const listClassNames = classnames_default()(\`\${prefixCls}-list\`, \`\${prefixCls}-list-\${listType}\`);
  // >>> Motion config
  const motionKeyList = (0,toConsumableArray/* default */.Z)(items.map(file => ({
    key: file.uid,
    file
  })));
  const animationDirection = listType === 'picture-card' || listType === 'picture-circle' ? 'animate-inline' : 'animate';
  // const transitionName = list.length === 0 ? '' : \`\${prefixCls}-\${animationDirection}\`;
  let motionConfig = {
    motionDeadline: 2000,
    motionName: \`\${prefixCls}-\${animationDirection}\`,
    keys: motionKeyList,
    motionAppear
  };
  const listItemMotion = react.useMemo(() => {
    const motion = Object.assign({}, (0,_util_motion/* default */.Z)(rootPrefixCls));
    delete motion.onAppearEnd;
    delete motion.onEnterEnd;
    delete motion.onLeaveEnd;
    return motion;
  }, [rootPrefixCls]);
  if (listType !== 'picture-card' && listType !== 'picture-circle') {
    motionConfig = Object.assign(Object.assign({}, listItemMotion), motionConfig);
  }
  return /*#__PURE__*/react.createElement("div", {
    className: listClassNames
  }, /*#__PURE__*/react.createElement(rc_motion_es/* CSSMotionList */.V4, Object.assign({}, motionConfig, {
    component: false
  }), _ref => {
    let {
      key,
      file,
      className: motionClassName,
      style: motionStyle
    } = _ref;
    return /*#__PURE__*/react.createElement(UploadList_ListItem, {
      key: key,
      locale: locale,
      prefixCls: prefixCls,
      className: motionClassName,
      style: motionStyle,
      file: file,
      items: items,
      progress: progress,
      listType: listType,
      isImgUrl: isImgUrl,
      showPreviewIcon: showPreviewIcon,
      showRemoveIcon: showRemoveIcon,
      showDownloadIcon: showDownloadIcon,
      removeIcon: removeIcon,
      previewIcon: previewIcon,
      downloadIcon: downloadIcon,
      iconRender: internalIconRender,
      actionIconRender: actionIconRender,
      itemRender: itemRender,
      onPreview: onInternalPreview,
      onDownload: onInternalDownload,
      onClose: onInternalClose
    });
  }), appendAction && ( /*#__PURE__*/react.createElement(rc_motion_es/* default */.ZP, Object.assign({}, motionConfig, {
    visible: appendActionVisible,
    forceRender: true
  }), _ref2 => {
    let {
      className: motionClassName,
      style: motionStyle
    } = _ref2;
    return (0,reactNode/* cloneElement */.Tm)(appendAction, oriProps => ({
      className: classnames_default()(oriProps.className, motionClassName),
      style: Object.assign(Object.assign(Object.assign({}, motionStyle), {
        // prevent the element has hover css pseudo-class that may cause animation to end prematurely.
        pointerEvents: motionClassName ? 'none' : undefined
      }), oriProps.style)
    }));
  })));
};
const UploadList = /*#__PURE__*/react.forwardRef(InternalUploadList);
if (false) {}
/* harmony default export */ var upload_UploadList = (UploadList);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/Upload.js
"use client";


var __awaiter = undefined && undefined.__awaiter || function (thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function (resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function (resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};













const LIST_IGNORE = \`__LIST_IGNORE_\${Date.now()}__\`;
const InternalUpload = (props, ref) => {
  const {
    fileList,
    defaultFileList,
    onRemove,
    showUploadList = true,
    listType = 'text',
    onPreview,
    onDownload,
    onChange,
    onDrop,
    previewFile,
    disabled: customDisabled,
    locale: propLocale,
    iconRender,
    isImageUrl,
    progress,
    prefixCls: customizePrefixCls,
    className,
    type = 'select',
    children,
    style,
    itemRender,
    maxCount,
    data = {},
    multiple = false,
    hasControlInside = true,
    action = '',
    accept = '',
    supportServerRender = true,
    rootClassName
  } = props;
  // ===================== Disabled =====================
  const disabled = react.useContext(DisabledContext/* default */.Z);
  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;
  const [mergedFileList, setMergedFileList] = (0,useMergedState/* default */.Z)(defaultFileList || [], {
    value: fileList,
    postState: list => list !== null && list !== void 0 ? list : []
  });
  const [dragState, setDragState] = react.useState('drop');
  const upload = react.useRef(null);
  if (false) {}
  // Control mode will auto fill file uid if not provided
  react.useMemo(() => {
    const timestamp = Date.now();
    (fileList || []).forEach((file, index) => {
      if (!file.uid && !Object.isFrozen(file)) {
        file.uid = \`__AUTO__\${timestamp}_\${index}__\`;
      }
    });
  }, [fileList]);
  const onInternalChange = (file, changedFileList, event) => {
    let cloneList = (0,toConsumableArray/* default */.Z)(changedFileList);
    let exceedMaxCount = false;
    // Cut to match count
    if (maxCount === 1) {
      cloneList = cloneList.slice(-1);
    } else if (maxCount) {
      exceedMaxCount = cloneList.length > maxCount;
      cloneList = cloneList.slice(0, maxCount);
    }
    // Prevent React18 auto batch since input[upload] trigger process at same time
    // which makes fileList closure problem
    (0,react_dom.flushSync)(() => {
      setMergedFileList(cloneList);
    });
    const changeInfo = {
      file: file,
      fileList: cloneList
    };
    if (event) {
      changeInfo.event = event;
    }
    if (!exceedMaxCount ||
    // We should ignore event if current file is exceed \`maxCount\`
    cloneList.some(f => f.uid === file.uid)) {
      (0,react_dom.flushSync)(() => {
        onChange === null || onChange === void 0 ? void 0 : onChange(changeInfo);
      });
    }
  };
  const mergedBeforeUpload = (file, fileListArgs) => __awaiter(void 0, void 0, void 0, function* () {
    const {
      beforeUpload,
      transformFile
    } = props;
    let parsedFile = file;
    if (beforeUpload) {
      const result = yield beforeUpload(file, fileListArgs);
      if (result === false) {
        return false;
      }
      // Hack for LIST_IGNORE, we add additional info to remove from the list
      delete file[LIST_IGNORE];
      if (result === LIST_IGNORE) {
        Object.defineProperty(file, LIST_IGNORE, {
          value: true,
          configurable: true
        });
        return false;
      }
      if (typeof result === 'object' && result) {
        parsedFile = result;
      }
    }
    if (transformFile) {
      parsedFile = yield transformFile(parsedFile);
    }
    return parsedFile;
  });
  const onBatchStart = batchFileInfoList => {
    // Skip file which marked as \`LIST_IGNORE\`, these file will not add to file list
    const filteredFileInfoList = batchFileInfoList.filter(info => !info.file[LIST_IGNORE]);
    // Nothing to do since no file need upload
    if (!filteredFileInfoList.length) {
      return;
    }
    const objectFileList = filteredFileInfoList.map(info => file2Obj(info.file));
    // Concat new files with prev files
    let newFileList = (0,toConsumableArray/* default */.Z)(mergedFileList);
    objectFileList.forEach(fileObj => {
      // Replace file if exist
      newFileList = updateFileList(fileObj, newFileList);
    });
    objectFileList.forEach((fileObj, index) => {
      // Repeat trigger \`onChange\` event for compatible
      let triggerFileObj = fileObj;
      if (!filteredFileInfoList[index].parsedFile) {
        // \`beforeUpload\` return false
        const {
          originFileObj
        } = fileObj;
        let clone;
        try {
          clone = new File([originFileObj], originFileObj.name, {
            type: originFileObj.type
          });
        } catch (e) {
          clone = new Blob([originFileObj], {
            type: originFileObj.type
          });
          clone.name = originFileObj.name;
          clone.lastModifiedDate = new Date();
          clone.lastModified = new Date().getTime();
        }
        clone.uid = fileObj.uid;
        triggerFileObj = clone;
      } else {
        // Inject \`uploading\` status
        fileObj.status = 'uploading';
      }
      onInternalChange(triggerFileObj, newFileList);
    });
  };
  const onSuccess = (response, file, xhr) => {
    try {
      if (typeof response === 'string') {
        response = JSON.parse(response);
      }
    } catch (e) {
      /* do nothing */
    }
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.status = 'done';
    targetItem.percent = 100;
    targetItem.response = response;
    targetItem.xhr = xhr;
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList);
  };
  const onProgress = (e, file) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.status = 'uploading';
    targetItem.percent = e.percent;
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList, e);
  };
  const onError = (error, response, file) => {
    // removed
    if (!getFileItem(file, mergedFileList)) {
      return;
    }
    const targetItem = file2Obj(file);
    targetItem.error = error;
    targetItem.response = response;
    targetItem.status = 'error';
    const nextFileList = updateFileList(targetItem, mergedFileList);
    onInternalChange(targetItem, nextFileList);
  };
  const handleRemove = file => {
    let currentFile;
    Promise.resolve(typeof onRemove === 'function' ? onRemove(file) : onRemove).then(ret => {
      var _a;
      // Prevent removing file
      if (ret === false) {
        return;
      }
      const removedFileList = removeFileItem(file, mergedFileList);
      if (removedFileList) {
        currentFile = Object.assign(Object.assign({}, file), {
          status: 'removed'
        });
        mergedFileList === null || mergedFileList === void 0 ? void 0 : mergedFileList.forEach(item => {
          const matchKey = currentFile.uid !== undefined ? 'uid' : 'name';
          if (item[matchKey] === currentFile[matchKey] && !Object.isFrozen(item)) {
            item.status = 'removed';
          }
        });
        (_a = upload.current) === null || _a === void 0 ? void 0 : _a.abort(currentFile);
        onInternalChange(currentFile, removedFileList);
      }
    });
  };
  const onFileDrop = e => {
    setDragState(e.type);
    if (e.type === 'drop') {
      onDrop === null || onDrop === void 0 ? void 0 : onDrop(e);
    }
  };
  // Test needs
  react.useImperativeHandle(ref, () => ({
    onBatchStart,
    onSuccess,
    onProgress,
    onError,
    fileList: mergedFileList,
    upload: upload.current
  }));
  const {
    getPrefixCls,
    direction,
    upload: ctxUpload
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('upload', customizePrefixCls);
  const rcUploadProps = Object.assign(Object.assign({
    onBatchStart,
    onError,
    onProgress,
    onSuccess
  }, props), {
    data,
    multiple,
    action,
    accept,
    supportServerRender,
    prefixCls,
    disabled: mergedDisabled,
    beforeUpload: mergedBeforeUpload,
    onChange: undefined,
    hasControlInside
  });
  delete rcUploadProps.className;
  delete rcUploadProps.style;
  // Remove id to avoid open by label when trigger is hidden
  // !children: https://github.com/ant-design/ant-design/issues/14298
  // disabled: https://github.com/ant-design/ant-design/issues/16478
  //           https://github.com/ant-design/ant-design/issues/24197
  if (!children || mergedDisabled) {
    delete rcUploadProps.id;
  }
  const wrapperCls = \`\${prefixCls}-wrapper\`;
  const [wrapCSSVar, hashId, cssVarCls] = upload_style(prefixCls, wrapperCls);
  const [contextLocale] = (0,useLocale/* default */.Z)('Upload', en_US/* default */.Z.Upload);
  const {
    showRemoveIcon,
    showPreviewIcon,
    showDownloadIcon,
    removeIcon,
    previewIcon,
    downloadIcon
  } = typeof showUploadList === 'boolean' ? {} : showUploadList;
  // use showRemoveIcon if it is specified explicitly
  const realShowRemoveIcon = typeof showRemoveIcon === 'undefined' ? !mergedDisabled : !!showRemoveIcon;
  const renderUploadList = (button, buttonVisible) => {
    if (!showUploadList) {
      return button;
    }
    return /*#__PURE__*/react.createElement(upload_UploadList, {
      prefixCls: prefixCls,
      listType: listType,
      items: mergedFileList,
      previewFile: previewFile,
      onPreview: onPreview,
      onDownload: onDownload,
      onRemove: handleRemove,
      showRemoveIcon: realShowRemoveIcon,
      showPreviewIcon: showPreviewIcon,
      showDownloadIcon: showDownloadIcon,
      removeIcon: removeIcon,
      previewIcon: previewIcon,
      downloadIcon: downloadIcon,
      iconRender: iconRender,
      locale: Object.assign(Object.assign({}, contextLocale), propLocale),
      isImageUrl: isImageUrl,
      progress: progress,
      appendAction: button,
      appendActionVisible: buttonVisible,
      itemRender: itemRender,
      disabled: mergedDisabled
    });
  };
  const mergedCls = classnames_default()(wrapperCls, className, rootClassName, hashId, cssVarCls, ctxUpload === null || ctxUpload === void 0 ? void 0 : ctxUpload.className, {
    [\`\${prefixCls}-rtl\`]: direction === 'rtl',
    [\`\${prefixCls}-picture-card-wrapper\`]: listType === 'picture-card',
    [\`\${prefixCls}-picture-circle-wrapper\`]: listType === 'picture-circle'
  });
  const mergedStyle = Object.assign(Object.assign({}, ctxUpload === null || ctxUpload === void 0 ? void 0 : ctxUpload.style), style);
  if (type === 'drag') {
    const dragCls = classnames_default()(hashId, prefixCls, \`\${prefixCls}-drag\`, {
      [\`\${prefixCls}-drag-uploading\`]: mergedFileList.some(file => file.status === 'uploading'),
      [\`\${prefixCls}-drag-hover\`]: dragState === 'dragover',
      [\`\${prefixCls}-disabled\`]: mergedDisabled,
      [\`\${prefixCls}-rtl\`]: direction === 'rtl'
    });
    return wrapCSSVar( /*#__PURE__*/react.createElement("span", {
      className: mergedCls
    }, /*#__PURE__*/react.createElement("div", {
      className: dragCls,
      style: mergedStyle,
      onDrop: onFileDrop,
      onDragOver: onFileDrop,
      onDragLeave: onFileDrop
    }, /*#__PURE__*/react.createElement(es, Object.assign({}, rcUploadProps, {
      ref: upload,
      className: \`\${prefixCls}-btn\`
    }), /*#__PURE__*/react.createElement("div", {
      className: \`\${prefixCls}-drag-container\`
    }, children))), renderUploadList()));
  }
  const uploadButtonCls = classnames_default()(prefixCls, \`\${prefixCls}-select\`, {
    [\`\${prefixCls}-disabled\`]: mergedDisabled
  });
  const uploadButton = /*#__PURE__*/react.createElement("div", {
    className: uploadButtonCls,
    style: children ? undefined : {
      display: 'none'
    }
  }, /*#__PURE__*/react.createElement(es, Object.assign({}, rcUploadProps, {
    ref: upload
  })));
  if (listType === 'picture-card' || listType === 'picture-circle') {
    return wrapCSSVar( /*#__PURE__*/react.createElement("span", {
      className: mergedCls
    }, renderUploadList(uploadButton, !!children)));
  }
  return wrapCSSVar( /*#__PURE__*/react.createElement("span", {
    className: mergedCls
  }, uploadButton, renderUploadList()));
};
const Upload_Upload = /*#__PURE__*/react.forwardRef(InternalUpload);
if (false) {}
/* harmony default export */ var upload_Upload = (Upload_Upload);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/Dragger.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};


const Dragger = /*#__PURE__*/react.forwardRef((_a, ref) => {
  var {
      style,
      height,
      hasControlInside = false
    } = _a,
    restProps = __rest(_a, ["style", "height", "hasControlInside"]);
  return /*#__PURE__*/react.createElement(upload_Upload, Object.assign({
    ref: ref,
    hasControlInside: hasControlInside
  }, restProps, {
    type: "drag",
    style: Object.assign(Object.assign({}, style), {
      height
    })
  }));
});
if (false) {}
/* harmony default export */ var upload_Dragger = (Dragger);
;// CONCATENATED MODULE: ./node_modules/antd/es/upload/index.js
"use client";



const es_upload_Upload = upload_Upload;
es_upload_Upload.Dragger = upload_Dragger;
es_upload_Upload.LIST_IGNORE = LIST_IGNORE;
/* harmony default export */ var es_upload = (es_upload_Upload);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///78367
`)}}]);
