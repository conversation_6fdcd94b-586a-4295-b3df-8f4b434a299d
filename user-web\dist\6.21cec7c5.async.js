"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[6],{72599:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ input_Input; },
  n: function() { return /* binding */ triggerFocus; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/rc-input/es/index.js + 2 modules
var es = __webpack_require__(67656);
// EXTERNAL MODULE: ./node_modules/rc-util/es/ref.js
var es_ref = __webpack_require__(42550);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/statusUtils.js
var statusUtils = __webpack_require__(9708);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/DisabledContext.js
var DisabledContext = __webpack_require__(98866);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useSize.js
var useSize = __webpack_require__(98675);
// EXTERNAL MODULE: ./node_modules/antd/es/form/context.js
var form_context = __webpack_require__(65223);
// EXTERNAL MODULE: ./node_modules/antd/es/space/Compact.js
var Compact = __webpack_require__(4173);
// EXTERNAL MODULE: ./node_modules/antd/es/input/hooks/useRemovePasswordTimeout.js
var useRemovePasswordTimeout = __webpack_require__(72922);
// EXTERNAL MODULE: ./node_modules/antd/es/input/style/index.js
var input_style = __webpack_require__(47673);
;// CONCATENATED MODULE: ./node_modules/antd/es/input/utils.js
// eslint-disable-next-line import/prefer-default-export
function hasPrefixSuffix(props) {
  return !!(props.prefix || props.suffix || props.allowClear || props.showCount);
}
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
// EXTERNAL MODULE: ./node_modules/antd/es/form/hooks/useVariants.js
var useVariants = __webpack_require__(27833);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js
var CloseCircleFilled = __webpack_require__(17012);
;// CONCATENATED MODULE: ./node_modules/antd/es/_util/getAllowClear.js
"use client";



const getAllowClear = allowClear => {
  let mergedAllowClear;
  if (typeof allowClear === 'object' && (allowClear === null || allowClear === void 0 ? void 0 : allowClear.clearIcon)) {
    mergedAllowClear = allowClear;
  } else if (allowClear) {
    mergedAllowClear = {
      clearIcon: /*#__PURE__*/react.createElement(CloseCircleFilled/* default */.Z, null)
    };
  }
  return mergedAllowClear;
};
/* harmony default export */ var _util_getAllowClear = (getAllowClear);
;// CONCATENATED MODULE: ./node_modules/antd/es/input/Input.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

















function triggerFocus(element, option) {
  if (!element) {
    return;
  }
  element.focus(option);
  // Selection content
  const {
    cursor
  } = option || {};
  if (cursor) {
    const len = element.value.length;
    switch (cursor) {
      case 'start':
        element.setSelectionRange(0, 0);
        break;
      case 'end':
        element.setSelectionRange(len, len);
        break;
      default:
        element.setSelectionRange(0, len);
        break;
    }
  }
}
const Input = /*#__PURE__*/(0,react.forwardRef)((props, ref) => {
  var _a;
  const {
      prefixCls: customizePrefixCls,
      bordered = true,
      status: customStatus,
      size: customSize,
      disabled: customDisabled,
      onBlur,
      onFocus,
      suffix,
      allowClear,
      addonAfter,
      addonBefore,
      className,
      style,
      styles,
      rootClassName,
      onChange,
      classNames: classes,
      variant: customVariant
    } = props,
    rest = __rest(props, ["prefixCls", "bordered", "status", "size", "disabled", "onBlur", "onFocus", "suffix", "allowClear", "addonAfter", "addonBefore", "className", "style", "styles", "rootClassName", "onChange", "classNames", "variant"]);
  if (false) {}
  const {
    getPrefixCls,
    direction,
    input
  } = react.useContext(context/* ConfigContext */.E_);
  const prefixCls = getPrefixCls('input', customizePrefixCls);
  const inputRef = (0,react.useRef)(null);
  // Style
  const rootCls = (0,useCSSVarCls/* default */.Z)(prefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = (0,input_style/* default */.ZP)(prefixCls, rootCls);
  // ===================== Compact Item =====================
  const {
    compactSize,
    compactItemClassnames
  } = (0,Compact/* useCompactItemContext */.ri)(prefixCls, direction);
  // ===================== Size =====================
  const mergedSize = (0,useSize/* default */.Z)(ctx => {
    var _a;
    return (_a = customSize !== null && customSize !== void 0 ? customSize : compactSize) !== null && _a !== void 0 ? _a : ctx;
  });
  // ===================== Disabled =====================
  const disabled = react.useContext(DisabledContext/* default */.Z);
  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;
  // ===================== Status =====================
  const {
    status: contextStatus,
    hasFeedback,
    feedbackIcon
  } = (0,react.useContext)(form_context/* FormItemInputContext */.aM);
  const mergedStatus = (0,statusUtils/* getMergedStatus */.F)(contextStatus, customStatus);
  // ===================== Focus warning =====================
  const inputHasPrefixSuffix = hasPrefixSuffix(props) || !!hasFeedback;
  const prevHasPrefixSuffix = (0,react.useRef)(inputHasPrefixSuffix);
  /* eslint-disable react-hooks/rules-of-hooks */
  if (false) {}
  /* eslint-enable */
  // ===================== Remove Password value =====================
  const removePasswordTimeout = (0,useRemovePasswordTimeout/* default */.Z)(inputRef, true);
  const handleBlur = e => {
    removePasswordTimeout();
    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);
  };
  const handleFocus = e => {
    removePasswordTimeout();
    onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);
  };
  const handleChange = e => {
    removePasswordTimeout();
    onChange === null || onChange === void 0 ? void 0 : onChange(e);
  };
  const suffixNode = (hasFeedback || suffix) && ( /*#__PURE__*/react.createElement(react.Fragment, null, suffix, hasFeedback && feedbackIcon));
  const mergedAllowClear = _util_getAllowClear(allowClear);
  const [variant, enableVariantCls] = (0,useVariants/* default */.Z)(customVariant, bordered);
  return wrapCSSVar( /*#__PURE__*/react.createElement(es/* default */.Z, Object.assign({
    ref: (0,es_ref/* composeRef */.sQ)(ref, inputRef),
    prefixCls: prefixCls,
    autoComplete: input === null || input === void 0 ? void 0 : input.autoComplete
  }, rest, {
    disabled: mergedDisabled,
    onBlur: handleBlur,
    onFocus: handleFocus,
    style: Object.assign(Object.assign({}, input === null || input === void 0 ? void 0 : input.style), style),
    styles: Object.assign(Object.assign({}, input === null || input === void 0 ? void 0 : input.styles), styles),
    suffix: suffixNode,
    allowClear: mergedAllowClear,
    className: classnames_default()(className, rootClassName, cssVarCls, rootCls, compactItemClassnames, input === null || input === void 0 ? void 0 : input.className),
    onChange: handleChange,
    addonAfter: addonAfter && ( /*#__PURE__*/react.createElement(Compact/* NoCompactStyle */.BR, null, /*#__PURE__*/react.createElement(form_context/* NoFormStyle */.Ux, {
      override: true,
      status: true
    }, addonAfter))),
    addonBefore: addonBefore && ( /*#__PURE__*/react.createElement(Compact/* NoCompactStyle */.BR, null, /*#__PURE__*/react.createElement(form_context/* NoFormStyle */.Ux, {
      override: true,
      status: true
    }, addonBefore))),
    classNames: Object.assign(Object.assign(Object.assign({}, classes), input === null || input === void 0 ? void 0 : input.classNames), {
      input: classnames_default()({
        [\`\${prefixCls}-sm\`]: mergedSize === 'small',
        [\`\${prefixCls}-lg\`]: mergedSize === 'large',
        [\`\${prefixCls}-rtl\`]: direction === 'rtl'
      }, classes === null || classes === void 0 ? void 0 : classes.input, (_a = input === null || input === void 0 ? void 0 : input.classNames) === null || _a === void 0 ? void 0 : _a.input, hashId),
      variant: classnames_default()({
        [\`\${prefixCls}-\${variant}\`]: enableVariantCls
      }, (0,statusUtils/* getStatusClassNames */.Z)(prefixCls, mergedStatus)),
      affixWrapper: classnames_default()({
        [\`\${prefixCls}-affix-wrapper-sm\`]: mergedSize === 'small',
        [\`\${prefixCls}-affix-wrapper-lg\`]: mergedSize === 'large',
        [\`\${prefixCls}-affix-wrapper-rtl\`]: direction === 'rtl'
      }, hashId),
      wrapper: classnames_default()({
        [\`\${prefixCls}-group-rtl\`]: direction === 'rtl'
      }, hashId),
      groupWrapper: classnames_default()({
        [\`\${prefixCls}-group-wrapper-sm\`]: mergedSize === 'small',
        [\`\${prefixCls}-group-wrapper-lg\`]: mergedSize === 'large',
        [\`\${prefixCls}-group-wrapper-rtl\`]: direction === 'rtl',
        [\`\${prefixCls}-group-wrapper-\${variant}\`]: enableVariantCls
      }, (0,statusUtils/* getStatusClassNames */.Z)(\`\${prefixCls}-group-wrapper\`, mergedStatus, hasFeedback), hashId)
    })
  })));
});
/* harmony default export */ var input_Input = (Input);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///72599
`)},70006:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ input_TextArea; }
});

// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js
var CloseCircleFilled = __webpack_require__(17012);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/rc-input/es/index.js + 2 modules
var es = __webpack_require__(67656);
// EXTERNAL MODULE: ./node_modules/rc-input/es/hooks/useCount.js
var useCount = __webpack_require__(82234);
// EXTERNAL MODULE: ./node_modules/rc-input/es/utils/commonUtils.js
var commonUtils = __webpack_require__(87887);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/rc-resize-observer/es/index.js + 4 modules
var rc_resize_observer_es = __webpack_require__(48555);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useLayoutEffect.js
var useLayoutEffect = __webpack_require__(8410);
// EXTERNAL MODULE: ./node_modules/rc-util/es/raf.js
var raf = __webpack_require__(75164);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/rc-textarea/es/calculateNodeHeight.js
// Thanks to https://github.com/andreypopp/react-textarea-autosize/

/**
 * calculateNodeHeight(uiTextNode, useCache = false)
 */

var HIDDEN_TEXTAREA_STYLE = "\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n";
var SIZING_STYLE = ['letter-spacing', 'line-height', 'padding-top', 'padding-bottom', 'font-family', 'font-weight', 'font-size', 'font-variant', 'text-rendering', 'text-transform', 'width', 'text-indent', 'padding-left', 'padding-right', 'border-width', 'box-sizing', 'word-break', 'white-space'];
var computedStyleCache = {};
var hiddenTextarea;
function calculateNodeStyling(node) {
  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var nodeRef = node.getAttribute('id') || node.getAttribute('data-reactid') || node.getAttribute('name');
  if (useCache && computedStyleCache[nodeRef]) {
    return computedStyleCache[nodeRef];
  }
  var style = window.getComputedStyle(node);
  var boxSizing = style.getPropertyValue('box-sizing') || style.getPropertyValue('-moz-box-sizing') || style.getPropertyValue('-webkit-box-sizing');
  var paddingSize = parseFloat(style.getPropertyValue('padding-bottom')) + parseFloat(style.getPropertyValue('padding-top'));
  var borderSize = parseFloat(style.getPropertyValue('border-bottom-width')) + parseFloat(style.getPropertyValue('border-top-width'));
  var sizingStyle = SIZING_STYLE.map(function (name) {
    return "".concat(name, ":").concat(style.getPropertyValue(name));
  }).join(';');
  var nodeInfo = {
    sizingStyle: sizingStyle,
    paddingSize: paddingSize,
    borderSize: borderSize,
    boxSizing: boxSizing
  };
  if (useCache && nodeRef) {
    computedStyleCache[nodeRef] = nodeInfo;
  }
  return nodeInfo;
}
function calculateAutoSizeStyle(uiTextNode) {
  var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
  var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
  if (!hiddenTextarea) {
    hiddenTextarea = document.createElement('textarea');
    hiddenTextarea.setAttribute('tab-index', '-1');
    hiddenTextarea.setAttribute('aria-hidden', 'true');
    document.body.appendChild(hiddenTextarea);
  }

  // Fix wrap="off" issue
  // https://github.com/ant-design/ant-design/issues/6577
  if (uiTextNode.getAttribute('wrap')) {
    hiddenTextarea.setAttribute('wrap', uiTextNode.getAttribute('wrap'));
  } else {
    hiddenTextarea.removeAttribute('wrap');
  }

  // Copy all CSS properties that have an impact on the height of the content in
  // the textbox
  var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache),
    paddingSize = _calculateNodeStyling.paddingSize,
    borderSize = _calculateNodeStyling.borderSize,
    boxSizing = _calculateNodeStyling.boxSizing,
    sizingStyle = _calculateNodeStyling.sizingStyle;

  // Need to have the overflow attribute to hide the scrollbar otherwise
  // text-lines will not calculated properly as the shadow will technically be
  // narrower for content
  hiddenTextarea.setAttribute('style', "".concat(sizingStyle, ";").concat(HIDDEN_TEXTAREA_STYLE));
  hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || '';
  var minHeight = undefined;
  var maxHeight = undefined;
  var overflowY;
  var height = hiddenTextarea.scrollHeight;
  if (boxSizing === 'border-box') {
    // border-box: add border, since height = content + padding + border
    height += borderSize;
  } else if (boxSizing === 'content-box') {
    // remove padding, since height = content
    height -= paddingSize;
  }
  if (minRows !== null || maxRows !== null) {
    // measure height of a textarea with a single row
    hiddenTextarea.value = ' ';
    var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;
    if (minRows !== null) {
      minHeight = singleRowHeight * minRows;
      if (boxSizing === 'border-box') {
        minHeight = minHeight + paddingSize + borderSize;
      }
      height = Math.max(minHeight, height);
    }
    if (maxRows !== null) {
      maxHeight = singleRowHeight * maxRows;
      if (boxSizing === 'border-box') {
        maxHeight = maxHeight + paddingSize + borderSize;
      }
      overflowY = height > maxHeight ? '' : 'hidden';
      height = Math.min(maxHeight, height);
    }
  }
  var style = {
    height: height,
    overflowY: overflowY,
    resize: 'none'
  };
  if (minHeight) {
    style.minHeight = minHeight;
  }
  if (maxHeight) {
    style.maxHeight = maxHeight;
  }
  return style;
}
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/rc-textarea/es/ResizableTextArea.js






var _excluded = ["prefixCls", "onPressEnter", "defaultValue", "value", "autoSize", "onResize", "className", "style", "disabled", "onChange", "onInternalAutoSize"];







var RESIZE_START = 0;
var RESIZE_MEASURING = 1;
var RESIZE_STABLE = 2;
var ResizableTextArea = /*#__PURE__*/react.forwardRef(function (props, ref) {
  var _ref = props,
    prefixCls = _ref.prefixCls,
    onPressEnter = _ref.onPressEnter,
    defaultValue = _ref.defaultValue,
    value = _ref.value,
    autoSize = _ref.autoSize,
    onResize = _ref.onResize,
    className = _ref.className,
    style = _ref.style,
    disabled = _ref.disabled,
    onChange = _ref.onChange,
    onInternalAutoSize = _ref.onInternalAutoSize,
    restProps = (0,objectWithoutProperties/* default */.Z)(_ref, _excluded);

  // =============================== Value ================================
  var _useMergedState = (0,useMergedState/* default */.Z)(defaultValue, {
      value: value,
      postState: function postState(val) {
        return val !== null && val !== void 0 ? val : '';
      }
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    mergedValue = _useMergedState2[0],
    setMergedValue = _useMergedState2[1];
  var onInternalChange = function onInternalChange(event) {
    setMergedValue(event.target.value);
    onChange === null || onChange === void 0 || onChange(event);
  };

  // ================================ Ref =================================
  var textareaRef = react.useRef();
  react.useImperativeHandle(ref, function () {
    return {
      textArea: textareaRef.current
    };
  });

  // ============================== AutoSize ==============================
  var _React$useMemo = react.useMemo(function () {
      if (autoSize && (0,esm_typeof/* default */.Z)(autoSize) === 'object') {
        return [autoSize.minRows, autoSize.maxRows];
      }
      return [];
    }, [autoSize]),
    _React$useMemo2 = (0,slicedToArray/* default */.Z)(_React$useMemo, 2),
    minRows = _React$useMemo2[0],
    maxRows = _React$useMemo2[1];
  var needAutoSize = !!autoSize;

  // =============================== Scroll ===============================
  // https://github.com/ant-design/ant-design/issues/21870
  var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {
    try {
      // FF has bug with jump of scroll to top. We force back here.
      if (document.activeElement === textareaRef.current) {
        var _textareaRef$current = textareaRef.current,
          selectionStart = _textareaRef$current.selectionStart,
          selectionEnd = _textareaRef$current.selectionEnd,
          scrollTop = _textareaRef$current.scrollTop;

        // Fix Safari bug which not rollback when break line
        // This makes Chinese IME can't input. Do not fix this
        // const { value: tmpValue } = textareaRef.current;
        // textareaRef.current.value = '';
        // textareaRef.current.value = tmpValue;

        textareaRef.current.setSelectionRange(selectionStart, selectionEnd);
        textareaRef.current.scrollTop = scrollTop;
      }
    } catch (e) {
      // Fix error in Chrome:
      // Failed to read the 'selectionStart' property from 'HTMLInputElement'
      // http://stackoverflow.com/q/21177489/3040605
    }
  };

  // =============================== Resize ===============================
  var _React$useState = react.useState(RESIZE_STABLE),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    resizeState = _React$useState2[0],
    setResizeState = _React$useState2[1];
  var _React$useState3 = react.useState(),
    _React$useState4 = (0,slicedToArray/* default */.Z)(_React$useState3, 2),
    autoSizeStyle = _React$useState4[0],
    setAutoSizeStyle = _React$useState4[1];
  var startResize = function startResize() {
    setResizeState(RESIZE_START);
    if (false) {}
  };

  // Change to trigger resize measure
  (0,useLayoutEffect/* default */.Z)(function () {
    if (needAutoSize) {
      startResize();
    }
  }, [value, minRows, maxRows, needAutoSize]);
  (0,useLayoutEffect/* default */.Z)(function () {
    if (resizeState === RESIZE_START) {
      setResizeState(RESIZE_MEASURING);
    } else if (resizeState === RESIZE_MEASURING) {
      var textareaStyles = calculateAutoSizeStyle(textareaRef.current, false, minRows, maxRows);

      // Safari has bug that text will keep break line on text cut when it's prev is break line.
      // ZombieJ: This not often happen. So we just skip it.
      // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;
      // const { value: tmpValue } = textareaRef.current;
      // textareaRef.current.value = '';
      // textareaRef.current.value = tmpValue;

      // if (document.activeElement === textareaRef.current) {
      //   textareaRef.current.scrollTop = scrollTop;
      //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);
      // }

      setResizeState(RESIZE_STABLE);
      setAutoSizeStyle(textareaStyles);
    } else {
      fixFirefoxAutoScroll();
    }
  }, [resizeState]);

  // We lock resize trigger by raf to avoid Safari warning
  var resizeRafRef = react.useRef();
  var cleanRaf = function cleanRaf() {
    raf/* default */.Z.cancel(resizeRafRef.current);
  };
  var onInternalResize = function onInternalResize(size) {
    if (resizeState === RESIZE_STABLE) {
      onResize === null || onResize === void 0 || onResize(size);
      if (autoSize) {
        cleanRaf();
        resizeRafRef.current = (0,raf/* default */.Z)(function () {
          startResize();
        });
      }
    }
  };
  react.useEffect(function () {
    return cleanRaf;
  }, []);

  // =============================== Render ===============================
  var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;
  var mergedStyle = (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, style), mergedAutoSizeStyle);
  if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {
    mergedStyle.overflowY = 'hidden';
    mergedStyle.overflowX = 'hidden';
  }
  return /*#__PURE__*/react.createElement(rc_resize_observer_es/* default */.Z, {
    onResize: onInternalResize,
    disabled: !(autoSize || onResize)
  }, /*#__PURE__*/react.createElement("textarea", (0,esm_extends/* default */.Z)({}, restProps, {
    ref: textareaRef,
    style: mergedStyle,
    className: classnames_default()(prefixCls, className, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-disabled"), disabled)),
    disabled: disabled,
    value: mergedValue,
    onChange: onInternalChange
  })));
});
/* harmony default export */ var es_ResizableTextArea = (ResizableTextArea);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/rc-textarea/es/TextArea.js






var TextArea_excluded = ["defaultValue", "value", "onFocus", "onBlur", "onChange", "allowClear", "maxLength", "onCompositionStart", "onCompositionEnd", "suffix", "prefixCls", "showCount", "count", "className", "style", "disabled", "hidden", "classNames", "styles", "onResize"];







var TextArea = /*#__PURE__*/react.forwardRef(function (_ref, ref) {
  var _countConfig$max, _clsx;
  var defaultValue = _ref.defaultValue,
    customValue = _ref.value,
    onFocus = _ref.onFocus,
    onBlur = _ref.onBlur,
    onChange = _ref.onChange,
    allowClear = _ref.allowClear,
    maxLength = _ref.maxLength,
    onCompositionStart = _ref.onCompositionStart,
    onCompositionEnd = _ref.onCompositionEnd,
    suffix = _ref.suffix,
    _ref$prefixCls = _ref.prefixCls,
    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,
    showCount = _ref.showCount,
    count = _ref.count,
    className = _ref.className,
    style = _ref.style,
    disabled = _ref.disabled,
    hidden = _ref.hidden,
    classNames = _ref.classNames,
    styles = _ref.styles,
    onResize = _ref.onResize,
    rest = (0,objectWithoutProperties/* default */.Z)(_ref, TextArea_excluded);
  var _useMergedState = (0,useMergedState/* default */.Z)(defaultValue, {
      value: customValue,
      defaultValue: defaultValue
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    value = _useMergedState2[0],
    setValue = _useMergedState2[1];
  var formatValue = value === undefined || value === null ? '' : String(value);
  var _React$useState = react.useState(false),
    _React$useState2 = (0,slicedToArray/* default */.Z)(_React$useState, 2),
    focused = _React$useState2[0],
    setFocused = _React$useState2[1];
  var compositionRef = react.useRef(false);
  var _React$useState3 = react.useState(null),
    _React$useState4 = (0,slicedToArray/* default */.Z)(_React$useState3, 2),
    textareaResized = _React$useState4[0],
    setTextareaResized = _React$useState4[1];

  // =============================== Ref ================================
  var resizableTextAreaRef = (0,react.useRef)(null);
  var getTextArea = function getTextArea() {
    var _resizableTextAreaRef;
    return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;
  };
  var focus = function focus() {
    getTextArea().focus();
  };
  (0,react.useImperativeHandle)(ref, function () {
    return {
      resizableTextArea: resizableTextAreaRef.current,
      focus: focus,
      blur: function blur() {
        getTextArea().blur();
      }
    };
  });
  (0,react.useEffect)(function () {
    setFocused(function (prev) {
      return !disabled && prev;
    });
  }, [disabled]);

  // =========================== Select Range ===========================
  var _React$useState5 = react.useState(null),
    _React$useState6 = (0,slicedToArray/* default */.Z)(_React$useState5, 2),
    selection = _React$useState6[0],
    setSelection = _React$useState6[1];
  react.useEffect(function () {
    if (selection) {
      var _getTextArea;
      (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,toConsumableArray/* default */.Z)(selection));
    }
  }, [selection]);

  // ============================== Count ===============================
  var countConfig = (0,useCount/* default */.Z)(count, showCount);
  var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;

  // Max length value
  var hasMaxLength = Number(mergedMax) > 0;
  var valueLength = countConfig.strategy(formatValue);
  var isOutOfRange = !!mergedMax && valueLength > mergedMax;

  // ============================== Change ==============================
  var triggerChange = function triggerChange(e, currentValue) {
    var cutValue = currentValue;
    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {
      cutValue = countConfig.exceedFormatter(currentValue, {
        max: countConfig.max
      });
      if (currentValue !== cutValue) {
        setSelection([getTextArea().selectionStart || 0, getTextArea().selectionEnd || 0]);
      }
    }
    setValue(cutValue);
    (0,commonUtils/* resolveOnChange */.rJ)(e.currentTarget, e, onChange, cutValue);
  };

  // =========================== Value Update ===========================
  var onInternalCompositionStart = function onInternalCompositionStart(e) {
    compositionRef.current = true;
    onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);
  };
  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {
    compositionRef.current = false;
    triggerChange(e, e.currentTarget.value);
    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);
  };
  var onInternalChange = function onInternalChange(e) {
    triggerChange(e, e.target.value);
  };
  var handleKeyDown = function handleKeyDown(e) {
    var onPressEnter = rest.onPressEnter,
      onKeyDown = rest.onKeyDown;
    if (e.key === 'Enter' && onPressEnter) {
      onPressEnter(e);
    }
    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);
  };
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    onFocus === null || onFocus === void 0 || onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setFocused(false);
    onBlur === null || onBlur === void 0 || onBlur(e);
  };

  // ============================== Reset ===============================
  var handleReset = function handleReset(e) {
    setValue('');
    focus();
    (0,commonUtils/* resolveOnChange */.rJ)(getTextArea(), e, onChange);
  };
  var suffixNode = suffix;
  var dataCount;
  if (countConfig.show) {
    if (countConfig.showFormatter) {
      dataCount = countConfig.showFormatter({
        value: formatValue,
        count: valueLength,
        maxLength: mergedMax
      });
    } else {
      dataCount = "".concat(valueLength).concat(hasMaxLength ? " / ".concat(mergedMax) : '');
    }
    suffixNode = /*#__PURE__*/react.createElement(react.Fragment, null, suffixNode, /*#__PURE__*/react.createElement("span", {
      className: classnames_default()("".concat(prefixCls, "-data-count"), classNames === null || classNames === void 0 ? void 0 : classNames.count),
      style: styles === null || styles === void 0 ? void 0 : styles.count
    }, dataCount));
  }
  var handleResize = function handleResize(size) {
    var _getTextArea2;
    onResize === null || onResize === void 0 || onResize(size);
    if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {
      setTextareaResized(true);
    }
  };
  var isPureTextArea = !rest.autoSize && !showCount && !allowClear;
  return /*#__PURE__*/react.createElement(es/* BaseInput */.Q, {
    value: formatValue,
    allowClear: allowClear,
    handleReset: handleReset,
    suffix: suffixNode,
    prefixCls: prefixCls,
    classNames: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, classNames), {}, {
      affixWrapper: classnames_default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (_clsx = {}, (0,defineProperty/* default */.Z)(_clsx, "".concat(prefixCls, "-show-count"), showCount), (0,defineProperty/* default */.Z)(_clsx, "".concat(prefixCls, "-textarea-allow-clear"), allowClear), _clsx))
    }),
    disabled: disabled,
    focused: focused,
    className: classnames_default()(className, isOutOfRange && "".concat(prefixCls, "-out-of-range")),
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, style), textareaResized && !isPureTextArea ? {
      height: 'auto'
    } : {}),
    dataAttrs: {
      affixWrapper: {
        'data-count': typeof dataCount === 'string' ? dataCount : undefined
      }
    },
    hidden: hidden
  }, /*#__PURE__*/react.createElement(es_ResizableTextArea, (0,esm_extends/* default */.Z)({}, rest, {
    maxLength: maxLength,
    onKeyDown: handleKeyDown,
    onChange: onInternalChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onCompositionStart: onInternalCompositionStart,
    onCompositionEnd: onInternalCompositionEnd,
    className: classnames_default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {
      resize: style === null || style === void 0 ? void 0 : style.resize
    }),
    disabled: disabled,
    prefixCls: prefixCls,
    onResize: handleResize,
    ref: resizableTextAreaRef
  })));
});
/* harmony default export */ var es_TextArea = (TextArea);
;// CONCATENATED MODULE: ./node_modules/antd/node_modules/rc-textarea/es/index.js


/* harmony default export */ var rc_textarea_es = (es_TextArea);
// EXTERNAL MODULE: ./node_modules/antd/es/_util/statusUtils.js
var statusUtils = __webpack_require__(9708);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/context.js
var context = __webpack_require__(53124);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/DisabledContext.js
var DisabledContext = __webpack_require__(98866);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useSize.js
var useSize = __webpack_require__(98675);
// EXTERNAL MODULE: ./node_modules/antd/es/form/context.js
var form_context = __webpack_require__(65223);
// EXTERNAL MODULE: ./node_modules/antd/es/input/Input.js + 2 modules
var Input = __webpack_require__(72599);
// EXTERNAL MODULE: ./node_modules/antd/es/input/style/index.js
var style = __webpack_require__(47673);
// EXTERNAL MODULE: ./node_modules/antd/es/config-provider/hooks/useCSSVarCls.js
var useCSSVarCls = __webpack_require__(35792);
// EXTERNAL MODULE: ./node_modules/antd/es/form/hooks/useVariants.js
var useVariants = __webpack_require__(27833);
;// CONCATENATED MODULE: ./node_modules/antd/es/input/TextArea.js
"use client";

var __rest = undefined && undefined.__rest || function (s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};















const TextArea_TextArea = /*#__PURE__*/(0,react.forwardRef)((props, ref) => {
  var _a;
  const {
      prefixCls: customizePrefixCls,
      bordered = true,
      size: customizeSize,
      disabled: customDisabled,
      status: customStatus,
      allowClear,
      classNames: classes,
      rootClassName,
      className,
      variant: customVariant
    } = props,
    rest = __rest(props, ["prefixCls", "bordered", "size", "disabled", "status", "allowClear", "classNames", "rootClassName", "className", "variant"]);
  if (false) {}
  const {
    getPrefixCls,
    direction
  } = react.useContext(context/* ConfigContext */.E_);
  // ===================== Size =====================
  const mergedSize = (0,useSize/* default */.Z)(customizeSize);
  // ===================== Disabled =====================
  const disabled = react.useContext(DisabledContext/* default */.Z);
  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;
  // ===================== Status =====================
  const {
    status: contextStatus,
    hasFeedback,
    feedbackIcon
  } = react.useContext(form_context/* FormItemInputContext */.aM);
  const mergedStatus = (0,statusUtils/* getMergedStatus */.F)(contextStatus, customStatus);
  // ===================== Ref =====================
  const innerRef = react.useRef(null);
  react.useImperativeHandle(ref, () => {
    var _a;
    return {
      resizableTextArea: (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea,
      focus: option => {
        var _a, _b;
        (0,Input/* triggerFocus */.n)((_b = (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.resizableTextArea) === null || _b === void 0 ? void 0 : _b.textArea, option);
      },
      blur: () => {
        var _a;
        return (_a = innerRef.current) === null || _a === void 0 ? void 0 : _a.blur();
      }
    };
  });
  const prefixCls = getPrefixCls('input', customizePrefixCls);
  // Allow clear
  let mergedAllowClear;
  if (typeof allowClear === 'object' && (allowClear === null || allowClear === void 0 ? void 0 : allowClear.clearIcon)) {
    mergedAllowClear = allowClear;
  } else if (allowClear) {
    mergedAllowClear = {
      clearIcon: /*#__PURE__*/react.createElement(CloseCircleFilled/* default */.Z, null)
    };
  }
  // ===================== Style =====================
  const rootCls = (0,useCSSVarCls/* default */.Z)(prefixCls);
  const [wrapCSSVar, hashId, cssVarCls] = (0,style/* default */.ZP)(prefixCls, rootCls);
  const [variant, enableVariantCls] = (0,useVariants/* default */.Z)(customVariant, bordered);
  return wrapCSSVar( /*#__PURE__*/react.createElement(rc_textarea_es, Object.assign({}, rest, {
    disabled: mergedDisabled,
    allowClear: mergedAllowClear,
    className: classnames_default()(cssVarCls, rootCls, className, rootClassName),
    classNames: Object.assign(Object.assign({}, classes), {
      textarea: classnames_default()({
        [\`\${prefixCls}-sm\`]: mergedSize === 'small',
        [\`\${prefixCls}-lg\`]: mergedSize === 'large'
      }, hashId, classes === null || classes === void 0 ? void 0 : classes.textarea),
      variant: classnames_default()({
        [\`\${prefixCls}-\${variant}\`]: enableVariantCls
      }, (0,statusUtils/* getStatusClassNames */.Z)(prefixCls, mergedStatus)),
      affixWrapper: classnames_default()(\`\${prefixCls}-textarea-affix-wrapper\`, {
        [\`\${prefixCls}-affix-wrapper-rtl\`]: direction === 'rtl',
        [\`\${prefixCls}-affix-wrapper-sm\`]: mergedSize === 'small',
        [\`\${prefixCls}-affix-wrapper-lg\`]: mergedSize === 'large',
        [\`\${prefixCls}-textarea-show-count\`]: props.showCount || ((_a = props.count) === null || _a === void 0 ? void 0 : _a.show)
      }, hashId)
    }),
    prefixCls: prefixCls,
    suffix: hasFeedback && /*#__PURE__*/react.createElement("span", {
      className: \`\${prefixCls}-textarea-suffix\`
    }, feedbackIcon),
    ref: innerRef
  })));
});
/* harmony default export */ var input_TextArea = (TextArea_TextArea);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///70006
`)},72922:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Z: function() { return /* binding */ useRemovePasswordTimeout; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);

function useRemovePasswordTimeout(inputRef, triggerOnMount) {
  const removePasswordTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);
  const removePasswordTimeout = () => {
    removePasswordTimeoutRef.current.push(setTimeout(() => {
      var _a, _b, _c, _d;
      if (((_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.input) && ((_b = inputRef.current) === null || _b === void 0 ? void 0 : _b.input.getAttribute('type')) === 'password' && ((_c = inputRef.current) === null || _c === void 0 ? void 0 : _c.input.hasAttribute('value'))) {
        (_d = inputRef.current) === null || _d === void 0 ? void 0 : _d.input.removeAttribute('value');
      }
    }));
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    if (triggerOnMount) {
      removePasswordTimeout();
    }
    return () => removePasswordTimeoutRef.current.forEach(timer => {
      if (timer) {
        clearTimeout(timer);
      }
    });
  }, []);
  return removePasswordTimeout;
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNzI5MjIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUEwQztBQUMzQjtBQUNmLG1DQUFtQyw2Q0FBTTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9hbnRkL2VzL2lucHV0L2hvb2tzL3VzZVJlbW92ZVBhc3N3b3JkVGltZW91dC5qcz81ODgyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlUmVtb3ZlUGFzc3dvcmRUaW1lb3V0KGlucHV0UmVmLCB0cmlnZ2VyT25Nb3VudCkge1xuICBjb25zdCByZW1vdmVQYXNzd29yZFRpbWVvdXRSZWYgPSB1c2VSZWYoW10pO1xuICBjb25zdCByZW1vdmVQYXNzd29yZFRpbWVvdXQgPSAoKSA9PiB7XG4gICAgcmVtb3ZlUGFzc3dvcmRUaW1lb3V0UmVmLmN1cnJlbnQucHVzaChzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHZhciBfYSwgX2IsIF9jLCBfZDtcbiAgICAgIGlmICgoKF9hID0gaW5wdXRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmlucHV0KSAmJiAoKF9iID0gaW5wdXRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmlucHV0LmdldEF0dHJpYnV0ZSgndHlwZScpKSA9PT0gJ3Bhc3N3b3JkJyAmJiAoKF9jID0gaW5wdXRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2MgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jLmlucHV0Lmhhc0F0dHJpYnV0ZSgndmFsdWUnKSkpIHtcbiAgICAgICAgKF9kID0gaW5wdXRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLmlucHV0LnJlbW92ZUF0dHJpYnV0ZSgndmFsdWUnKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH07XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHRyaWdnZXJPbk1vdW50KSB7XG4gICAgICByZW1vdmVQYXNzd29yZFRpbWVvdXQoKTtcbiAgICB9XG4gICAgcmV0dXJuICgpID0+IHJlbW92ZVBhc3N3b3JkVGltZW91dFJlZi5jdXJyZW50LmZvckVhY2godGltZXIgPT4ge1xuICAgICAgaWYgKHRpbWVyKSB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgICB9XG4gICAgfSk7XG4gIH0sIFtdKTtcbiAgcmV0dXJuIHJlbW92ZVBhc3N3b3JkVGltZW91dDtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=
//# sourceURL=webpack-internal:///72922
`)},82234:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Z: function() { return /* binding */ useCount; }
/* harmony export */ });
/* unused harmony export inCountRange */
/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(91);
/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1413);
/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(71002);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67294);



var _excluded = ["show"];

/**
 * Cut \`value\` by the \`count.max\` prop.
 */
function inCountRange(value, countConfig) {
  if (!countConfig.max) {
    return true;
  }
  var count = countConfig.strategy(value);
  return count <= countConfig.max;
}
function useCount(count, showCount) {
  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {
    var mergedConfig = {};
    if (showCount) {
      mergedConfig.show = (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(showCount) === 'object' && showCount.formatter ? showCount.formatter : !!showCount;
    }
    mergedConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, mergedConfig), count);
    var _ref = mergedConfig,
      show = _ref.show,
      rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)(_ref, _excluded);
    return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)({}, rest), {}, {
      show: !!show,
      showFormatter: typeof show === 'function' ? show : undefined,
      strategy: rest.strategy || function (value) {
        return value.length;
      }
    });
  }, [count, showCount]);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///82234
`)},67656:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Q: function() { return /* reexport */ es_BaseInput; },
  Z: function() { return /* binding */ es; }
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js
var objectSpread2 = __webpack_require__(1413);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js
var esm_extends = __webpack_require__(87462);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(4942);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(71002);
// EXTERNAL MODULE: ./node_modules/classnames/index.js
var classnames = __webpack_require__(93967);
var classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/rc-input/es/utils/commonUtils.js
var commonUtils = __webpack_require__(87887);
;// CONCATENATED MODULE: ./node_modules/rc-input/es/BaseInput.js







var BaseInput = function BaseInput(props) {
  var _element$props, _element$props2;
  var inputEl = props.inputElement,
    children = props.children,
    prefixCls = props.prefixCls,
    prefix = props.prefix,
    suffix = props.suffix,
    addonBefore = props.addonBefore,
    addonAfter = props.addonAfter,
    className = props.className,
    style = props.style,
    disabled = props.disabled,
    readOnly = props.readOnly,
    focused = props.focused,
    triggerFocus = props.triggerFocus,
    allowClear = props.allowClear,
    value = props.value,
    handleReset = props.handleReset,
    hidden = props.hidden,
    classes = props.classes,
    classNames = props.classNames,
    dataAttrs = props.dataAttrs,
    styles = props.styles,
    components = props.components;
  var inputElement = children !== null && children !== void 0 ? children : inputEl;
  var AffixWrapperComponent = (components === null || components === void 0 ? void 0 : components.affixWrapper) || 'span';
  var GroupWrapperComponent = (components === null || components === void 0 ? void 0 : components.groupWrapper) || 'span';
  var WrapperComponent = (components === null || components === void 0 ? void 0 : components.wrapper) || 'span';
  var GroupAddonComponent = (components === null || components === void 0 ? void 0 : components.groupAddon) || 'span';
  var containerRef = (0,react.useRef)(null);
  var onInputClick = function onInputClick(e) {
    var _containerRef$current;
    if ((_containerRef$current = containerRef.current) !== null && _containerRef$current !== void 0 && _containerRef$current.contains(e.target)) {
      triggerFocus === null || triggerFocus === void 0 || triggerFocus();
    }
  };
  var hasAffix = (0,commonUtils/* hasPrefixSuffix */.X3)(props);
  var element = /*#__PURE__*/(0,react.cloneElement)(inputElement, {
    value: value,
    className: classnames_default()(inputElement.props.className, !hasAffix && (classNames === null || classNames === void 0 ? void 0 : classNames.variant)) || null
  });

  // ================== Prefix & Suffix ================== //
  if (hasAffix) {
    var _clsx2;
    // ================== Clear Icon ================== //
    var clearIcon = null;
    if (allowClear) {
      var _clsx;
      var needClear = !disabled && !readOnly && value;
      var clearIconCls = "".concat(prefixCls, "-clear-icon");
      var iconNode = (0,esm_typeof/* default */.Z)(allowClear) === 'object' && allowClear !== null && allowClear !== void 0 && allowClear.clearIcon ? allowClear.clearIcon : '\u2716';
      clearIcon = /*#__PURE__*/react.createElement("span", {
        onClick: handleReset
        // Do not trigger onBlur when clear input
        // https://github.com/ant-design/ant-design/issues/31200
        ,
        onMouseDown: function onMouseDown(e) {
          return e.preventDefault();
        },
        className: classnames_default()(clearIconCls, (_clsx = {}, (0,defineProperty/* default */.Z)(_clsx, "".concat(clearIconCls, "-hidden"), !needClear), (0,defineProperty/* default */.Z)(_clsx, "".concat(clearIconCls, "-has-suffix"), !!suffix), _clsx)),
        role: "button",
        tabIndex: -1
      }, iconNode);
    }
    var affixWrapperPrefixCls = "".concat(prefixCls, "-affix-wrapper");
    var affixWrapperCls = classnames_default()(affixWrapperPrefixCls, (_clsx2 = {}, (0,defineProperty/* default */.Z)(_clsx2, "".concat(prefixCls, "-disabled"), disabled), (0,defineProperty/* default */.Z)(_clsx2, "".concat(affixWrapperPrefixCls, "-disabled"), disabled), (0,defineProperty/* default */.Z)(_clsx2, "".concat(affixWrapperPrefixCls, "-focused"), focused), (0,defineProperty/* default */.Z)(_clsx2, "".concat(affixWrapperPrefixCls, "-readonly"), readOnly), (0,defineProperty/* default */.Z)(_clsx2, "".concat(affixWrapperPrefixCls, "-input-with-clear-btn"), suffix && allowClear && value), _clsx2), classes === null || classes === void 0 ? void 0 : classes.affixWrapper, classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, classNames === null || classNames === void 0 ? void 0 : classNames.variant);
    var suffixNode = (suffix || allowClear) && /*#__PURE__*/react.createElement("span", {
      className: classnames_default()("".concat(prefixCls, "-suffix"), classNames === null || classNames === void 0 ? void 0 : classNames.suffix),
      style: styles === null || styles === void 0 ? void 0 : styles.suffix
    }, clearIcon, suffix);
    element = /*#__PURE__*/react.createElement(AffixWrapperComponent, (0,esm_extends/* default */.Z)({
      className: affixWrapperCls,
      style: styles === null || styles === void 0 ? void 0 : styles.affixWrapper,
      onClick: onInputClick
    }, dataAttrs === null || dataAttrs === void 0 ? void 0 : dataAttrs.affixWrapper, {
      ref: containerRef
    }), prefix && /*#__PURE__*/react.createElement("span", {
      className: classnames_default()("".concat(prefixCls, "-prefix"), classNames === null || classNames === void 0 ? void 0 : classNames.prefix),
      style: styles === null || styles === void 0 ? void 0 : styles.prefix
    }, prefix), element, suffixNode);
  }

  // ================== Addon ================== //
  if ((0,commonUtils/* hasAddon */.He)(props)) {
    var wrapperCls = "".concat(prefixCls, "-group");
    var addonCls = "".concat(wrapperCls, "-addon");
    var groupWrapperCls = "".concat(wrapperCls, "-wrapper");
    var mergedWrapperClassName = classnames_default()("".concat(prefixCls, "-wrapper"), wrapperCls, classes === null || classes === void 0 ? void 0 : classes.wrapper, classNames === null || classNames === void 0 ? void 0 : classNames.wrapper);
    var mergedGroupClassName = classnames_default()(groupWrapperCls, (0,defineProperty/* default */.Z)({}, "".concat(groupWrapperCls, "-disabled"), disabled), classes === null || classes === void 0 ? void 0 : classes.group, classNames === null || classNames === void 0 ? void 0 : classNames.groupWrapper);

    // Need another wrapper for changing display:table to display:inline-block
    // and put style prop in wrapper
    element = /*#__PURE__*/react.createElement(GroupWrapperComponent, {
      className: mergedGroupClassName
    }, /*#__PURE__*/react.createElement(WrapperComponent, {
      className: mergedWrapperClassName
    }, addonBefore && /*#__PURE__*/react.createElement(GroupAddonComponent, {
      className: addonCls
    }, addonBefore), element, addonAfter && /*#__PURE__*/react.createElement(GroupAddonComponent, {
      className: addonCls
    }, addonAfter)));
  }

  // \`className\` and \`style\` are always on the root element
  return /*#__PURE__*/react.cloneElement(element, {
    className: classnames_default()((_element$props = element.props) === null || _element$props === void 0 ? void 0 : _element$props.className, className) || null,
    style: (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({}, (_element$props2 = element.props) === null || _element$props2 === void 0 ? void 0 : _element$props2.style), style),
    hidden: hidden
  });
};
/* harmony default export */ var es_BaseInput = (BaseInput);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(74902);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(97685);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(91);
// EXTERNAL MODULE: ./node_modules/rc-util/es/hooks/useMergedState.js
var useMergedState = __webpack_require__(21770);
// EXTERNAL MODULE: ./node_modules/rc-util/es/omit.js
var omit = __webpack_require__(98423);
// EXTERNAL MODULE: ./node_modules/rc-input/es/hooks/useCount.js
var useCount = __webpack_require__(82234);
;// CONCATENATED MODULE: ./node_modules/rc-input/es/Input.js






var _excluded = ["autoComplete", "onChange", "onFocus", "onBlur", "onPressEnter", "onKeyDown", "prefixCls", "disabled", "htmlSize", "className", "maxLength", "suffix", "showCount", "count", "type", "classes", "classNames", "styles", "onCompositionStart", "onCompositionEnd"];







var Input = /*#__PURE__*/(0,react.forwardRef)(function (props, ref) {
  var autoComplete = props.autoComplete,
    onChange = props.onChange,
    onFocus = props.onFocus,
    onBlur = props.onBlur,
    onPressEnter = props.onPressEnter,
    onKeyDown = props.onKeyDown,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-input' : _props$prefixCls,
    disabled = props.disabled,
    htmlSize = props.htmlSize,
    className = props.className,
    maxLength = props.maxLength,
    suffix = props.suffix,
    showCount = props.showCount,
    count = props.count,
    _props$type = props.type,
    type = _props$type === void 0 ? 'text' : _props$type,
    classes = props.classes,
    classNames = props.classNames,
    styles = props.styles,
    _onCompositionStart = props.onCompositionStart,
    onCompositionEnd = props.onCompositionEnd,
    rest = (0,objectWithoutProperties/* default */.Z)(props, _excluded);
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.Z)(_useState, 2),
    focused = _useState2[0],
    setFocused = _useState2[1];
  var compositionRef = (0,react.useRef)(false);
  var inputRef = (0,react.useRef)(null);
  var focus = function focus(option) {
    if (inputRef.current) {
      (0,commonUtils/* triggerFocus */.nH)(inputRef.current, option);
    }
  };

  // ====================== Value =======================
  var _useMergedState = (0,useMergedState/* default */.Z)(props.defaultValue, {
      value: props.value
    }),
    _useMergedState2 = (0,slicedToArray/* default */.Z)(_useMergedState, 2),
    value = _useMergedState2[0],
    setValue = _useMergedState2[1];
  var formatValue = value === undefined || value === null ? '' : String(value);

  // =================== Select Range ===================
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.Z)(_useState3, 2),
    selection = _useState4[0],
    setSelection = _useState4[1];

  // ====================== Count =======================
  var countConfig = (0,useCount/* default */.Z)(count, showCount);
  var mergedMax = countConfig.max || maxLength;
  var valueLength = countConfig.strategy(formatValue);
  var isOutOfRange = !!mergedMax && valueLength > mergedMax;

  // ======================= Ref ========================
  (0,react.useImperativeHandle)(ref, function () {
    return {
      focus: focus,
      blur: function blur() {
        var _inputRef$current;
        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 || _inputRef$current.blur();
      },
      setSelectionRange: function setSelectionRange(start, end, direction) {
        var _inputRef$current2;
        (_inputRef$current2 = inputRef.current) === null || _inputRef$current2 === void 0 || _inputRef$current2.setSelectionRange(start, end, direction);
      },
      select: function select() {
        var _inputRef$current3;
        (_inputRef$current3 = inputRef.current) === null || _inputRef$current3 === void 0 || _inputRef$current3.select();
      },
      input: inputRef.current
    };
  });
  (0,react.useEffect)(function () {
    setFocused(function (prev) {
      return prev && disabled ? false : prev;
    });
  }, [disabled]);
  var triggerChange = function triggerChange(e, currentValue, info) {
    var cutValue = currentValue;
    if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {
      cutValue = countConfig.exceedFormatter(currentValue, {
        max: countConfig.max
      });
      if (currentValue !== cutValue) {
        var _inputRef$current4, _inputRef$current5;
        setSelection([((_inputRef$current4 = inputRef.current) === null || _inputRef$current4 === void 0 ? void 0 : _inputRef$current4.selectionStart) || 0, ((_inputRef$current5 = inputRef.current) === null || _inputRef$current5 === void 0 ? void 0 : _inputRef$current5.selectionEnd) || 0]);
      }
    } else if (info.source === 'compositionEnd') {
      // Avoid triggering twice
      // https://github.com/ant-design/ant-design/issues/46587
      return;
    }
    setValue(cutValue);
    if (inputRef.current) {
      (0,commonUtils/* resolveOnChange */.rJ)(inputRef.current, e, onChange, cutValue);
    }
  };
  (0,react.useEffect)(function () {
    if (selection) {
      var _inputRef$current6;
      (_inputRef$current6 = inputRef.current) === null || _inputRef$current6 === void 0 || _inputRef$current6.setSelectionRange.apply(_inputRef$current6, (0,toConsumableArray/* default */.Z)(selection));
    }
  }, [selection]);
  var onInternalChange = function onInternalChange(e) {
    triggerChange(e, e.target.value, {
      source: 'change'
    });
  };
  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {
    compositionRef.current = false;
    triggerChange(e, e.currentTarget.value, {
      source: 'compositionEnd'
    });
    onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);
  };
  var handleKeyDown = function handleKeyDown(e) {
    if (onPressEnter && e.key === 'Enter') {
      onPressEnter(e);
    }
    onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);
  };
  var handleFocus = function handleFocus(e) {
    setFocused(true);
    onFocus === null || onFocus === void 0 || onFocus(e);
  };
  var handleBlur = function handleBlur(e) {
    setFocused(false);
    onBlur === null || onBlur === void 0 || onBlur(e);
  };
  var handleReset = function handleReset(e) {
    setValue('');
    focus();
    if (inputRef.current) {
      (0,commonUtils/* resolveOnChange */.rJ)(inputRef.current, e, onChange);
    }
  };

  // ====================== Input =======================
  var outOfRangeCls = isOutOfRange && "".concat(prefixCls, "-out-of-range");
  var getInputElement = function getInputElement() {
    // Fix https://fb.me/react-unknown-prop
    var otherProps = (0,omit/* default */.Z)(props, ['prefixCls', 'onPressEnter', 'addonBefore', 'addonAfter', 'prefix', 'suffix', 'allowClear',
    // Input elements must be either controlled or uncontrolled,
    // specify either the value prop, or the defaultValue prop, but not both.
    'defaultValue', 'showCount', 'count', 'classes', 'htmlSize', 'styles', 'classNames']);
    return /*#__PURE__*/react.createElement("input", (0,esm_extends/* default */.Z)({
      autoComplete: autoComplete
    }, otherProps, {
      onChange: onInternalChange,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      className: classnames_default()(prefixCls, (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-disabled"), disabled), classNames === null || classNames === void 0 ? void 0 : classNames.input),
      style: styles === null || styles === void 0 ? void 0 : styles.input,
      ref: inputRef,
      size: htmlSize,
      type: type,
      onCompositionStart: function onCompositionStart(e) {
        compositionRef.current = true;
        _onCompositionStart === null || _onCompositionStart === void 0 || _onCompositionStart(e);
      },
      onCompositionEnd: onInternalCompositionEnd
    }));
  };
  var getSuffix = function getSuffix() {
    // Max length value
    var hasMaxLength = Number(mergedMax) > 0;
    if (suffix || countConfig.show) {
      var dataCount = countConfig.showFormatter ? countConfig.showFormatter({
        value: formatValue,
        count: valueLength,
        maxLength: mergedMax
      }) : "".concat(valueLength).concat(hasMaxLength ? " / ".concat(mergedMax) : '');
      return /*#__PURE__*/react.createElement(react.Fragment, null, countConfig.show && /*#__PURE__*/react.createElement("span", {
        className: classnames_default()("".concat(prefixCls, "-show-count-suffix"), (0,defineProperty/* default */.Z)({}, "".concat(prefixCls, "-show-count-has-suffix"), !!suffix), classNames === null || classNames === void 0 ? void 0 : classNames.count),
        style: (0,objectSpread2/* default */.Z)({}, styles === null || styles === void 0 ? void 0 : styles.count)
      }, dataCount), suffix);
    }
    return null;
  };

  // ====================== Render ======================
  return /*#__PURE__*/react.createElement(es_BaseInput, (0,esm_extends/* default */.Z)({}, rest, {
    prefixCls: prefixCls,
    className: classnames_default()(className, outOfRangeCls),
    handleReset: handleReset,
    value: formatValue,
    focused: focused,
    triggerFocus: focus,
    suffix: getSuffix(),
    disabled: disabled,
    classes: classes,
    classNames: classNames,
    styles: styles
  }), getInputElement());
});
/* harmony default export */ var es_Input = (Input);
;// CONCATENATED MODULE: ./node_modules/rc-input/es/index.js



/* harmony default export */ var es = (es_Input);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///67656
`)},87887:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   He: function() { return /* binding */ hasAddon; },
/* harmony export */   X3: function() { return /* binding */ hasPrefixSuffix; },
/* harmony export */   nH: function() { return /* binding */ triggerFocus; },
/* harmony export */   rJ: function() { return /* binding */ resolveOnChange; }
/* harmony export */ });
function hasAddon(props) {
  return !!(props.addonBefore || props.addonAfter);
}
function hasPrefixSuffix(props) {
  return !!(props.prefix || props.suffix || props.allowClear);
}
function resolveOnChange(target, e, onChange, targetValue) {
  if (!onChange) {
    return;
  }
  var event = e;
  if (e.type === 'click') {
    // Clone a new target for event.
    // Avoid the following usage, the setQuery method gets the original value.
    //
    // const [query, setQuery] = React.useState('');
    // <Input
    //   allowClear
    //   value={query}
    //   onChange={(e)=> {
    //     setQuery((prevStatus) => e.target.value);
    //   }}
    // />

    // A bug report filed on WebKit's Bugzilla tracker, dating back to 2009, specifically addresses the issue of cloneNode() not copying files of <input type="file"> elements.
    // As of the last update, this bug was still marked as "NEW," indicating that it might not have been resolved yet\u200B\u200B.
    // https://bugs.webkit.org/show_bug.cgi?id=28123
    var currentTarget = target.cloneNode(true);

    // click clear icon
    event = Object.create(e, {
      target: {
        value: currentTarget
      },
      currentTarget: {
        value: currentTarget
      }
    });
    currentTarget.value = '';
    onChange(event);
    return;
  }

  // Trigger by composition event, this means we need force change the input value
  // https://github.com/ant-design/ant-design/issues/45737
  // https://github.com/ant-design/ant-design/issues/46598
  if (target.type !== 'file' && targetValue !== undefined) {
    // A bug report filed on WebKit's Bugzilla tracker, dating back to 2009, specifically addresses the issue of cloneNode() not copying files of <input type="file"> elements.
    // As of the last update, this bug was still marked as "NEW," indicating that it might not have been resolved yet\u200B\u200B.
    // https://bugs.webkit.org/show_bug.cgi?id=28123
    var _currentTarget = target.cloneNode(true);
    event = Object.create(e, {
      target: {
        value: _currentTarget
      },
      currentTarget: {
        value: _currentTarget
      }
    });
    _currentTarget.value = targetValue;
    onChange(event);
    return;
  }
  onChange(event);
}
function triggerFocus(element, option) {
  if (!element) return;
  element.focus(option);

  // Selection content
  var _ref = option || {},
    cursor = _ref.cursor;
  if (cursor) {
    var len = element.value.length;
    switch (cursor) {
      case 'start':
        element.setSelectionRange(0, 0);
        break;
      case 'end':
        element.setSelectionRange(len, len);
        break;
      default:
        element.setSelectionRange(0, len);
    }
  }
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///87887
`)}}]);
