(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8047],{66607:function(module){eval(`!function(e,i){ true?module.exports=i():0}(this,(function(){"use strict";return function(e,i,t){i.prototype.isBetween=function(e,i,s,f){var n=t(e),o=t(i),r="("===(f=f||"()")[0],u=")"===f[1];return(r?this.isAfter(n,s):!this.isBefore(n,s))&&(u?this.isBefore(o,s):!this.isAfter(o,s))||(r?this.isBefore(n,s):!this.isAfter(n,s))&&(u?this.isAfter(o,s):!this.isBefore(o,s))}}}));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjY2MDcuanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxLQUFvRCxvQkFBb0IsQ0FBZ0ksQ0FBQyxrQkFBa0IsYUFBYSx1QkFBdUIsd0NBQXdDLHNEQUFzRCxtTEFBbUwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbnQtZGVzaWduLXByby8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNCZXR3ZWVuLmpzP2RkMzMiXSwic291cmNlc0NvbnRlbnQiOlsiIWZ1bmN0aW9uKGUsaSl7XCJvYmplY3RcIj09dHlwZW9mIGV4cG9ydHMmJlwidW5kZWZpbmVkXCIhPXR5cGVvZiBtb2R1bGU/bW9kdWxlLmV4cG9ydHM9aSgpOlwiZnVuY3Rpb25cIj09dHlwZW9mIGRlZmluZSYmZGVmaW5lLmFtZD9kZWZpbmUoaSk6KGU9XCJ1bmRlZmluZWRcIiE9dHlwZW9mIGdsb2JhbFRoaXM/Z2xvYmFsVGhpczplfHxzZWxmKS5kYXlqc19wbHVnaW5faXNCZXR3ZWVuPWkoKX0odGhpcywoZnVuY3Rpb24oKXtcInVzZSBzdHJpY3RcIjtyZXR1cm4gZnVuY3Rpb24oZSxpLHQpe2kucHJvdG90eXBlLmlzQmV0d2Vlbj1mdW5jdGlvbihlLGkscyxmKXt2YXIgbj10KGUpLG89dChpKSxyPVwiKFwiPT09KGY9Znx8XCIoKVwiKVswXSx1PVwiKVwiPT09ZlsxXTtyZXR1cm4ocj90aGlzLmlzQWZ0ZXIobixzKTohdGhpcy5pc0JlZm9yZShuLHMpKSYmKHU/dGhpcy5pc0JlZm9yZShvLHMpOiF0aGlzLmlzQWZ0ZXIobyxzKSl8fChyP3RoaXMuaXNCZWZvcmUobixzKTohdGhpcy5pc0FmdGVyKG4scykpJiYodT90aGlzLmlzQWZ0ZXIobyxzKTohdGhpcy5pc0JlZm9yZShvLHMpKX19fSkpOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==
//# sourceURL=webpack-internal:///66607
`)},70178:function(module){eval(`!function(t,i){ true?module.exports=i():0}(this,(function(){"use strict";var t="minute",i=/[+-]\\d\\d(?::?\\d\\d)?/g,e=/([+-]|\\d\\d)/g;return function(s,f,n){var u=f.prototype;n.utc=function(t){var i={date:t,utc:!0,args:arguments};return new f(i)},u.utc=function(i){var e=n(this.toDate(),{locale:this.$L,utc:!0});return i?e.add(this.utcOffset(),t):e},u.local=function(){return n(this.toDate(),{locale:this.$L,utc:!1})};var o=u.parse;u.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),o.call(this,t)};var r=u.init;u.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else r.call(this)};var a=u.utcOffset;u.utcOffset=function(s,f){var n=this.$utils().u;if(n(s))return this.$u?0:n(this.$offset)?a.call(this):this.$offset;if("string"==typeof s&&(s=function(t){void 0===t&&(t="");var s=t.match(i);if(!s)return null;var f=(""+s[0]).match(e)||["-",0,0],n=f[0],u=60*+f[1]+ +f[2];return 0===u?0:"+"===n?u:-u}(s),null===s))return this;var u=Math.abs(s)<=16?60*s:s,o=this;if(f)return o.$offset=u,o.$u=0===s,o;if(0!==s){var r=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(o=this.local().add(u+r,t)).$offset=u,o.$x.$localOffset=r}else o=this.utc();return o};var h=u.format;u.format=function(t){var i=t||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return h.call(this,i)},u.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*t},u.isUTC=function(){return!!this.$u},u.toISOString=function(){return this.toDate().toISOString()},u.toString=function(){return this.toDate().toUTCString()};var l=u.toDate;u.toDate=function(t){return"s"===t&&this.$offset?n(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():l.call(this)};var c=u.diff;u.diff=function(t,i,e){if(t&&this.$u===t.$u)return c.call(this,t,i,e);var s=this.local(),f=n(t).local();return c.call(s,f,i,e)}}}));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///70178
`)},34757:function(module,exports,__webpack_require__){eval(`var __WEBPACK_AMD_DEFINE_RESULT__;/**\r
 * \u519C\u5386\uFF08\u9634\u5386\uFF09\u4E07\u5E74\u5386\r
 * LunarCalendar\r
 * vervison : v0.1.4\r
 * Github : https://github.com/zzyss86/LunarCalendar\r
 * HomePage : http://www.tuijs.com/\r
 * Author : JasonZhou\r
 * Email : <EMAIL>\r
 */\r
\r
(function(){\r
	var extend = function(o, c){\r
		if(o && c && typeof c == "object"){\r
			for(var p in c){\r
				o[p] = c[p];\r
			}\r
		}\r
		return o;\r
	};\r
	\r
	var creatLenArr = function(year,month,len,start){\r
		var arr = [];\r
			start = start || 0;\r
		if(len<1)return arr;\r
		var k = start;\r
		for(var i=0;i<len;i++){\r
			arr.push({year:year,month:month,day:k});\r
			k++;\r
		}\r
		return arr;\r
	};\r
	\r
	var errorCode = { //\u9519\u8BEF\u7801\u5217\u8868\r
		100 : '\u8F93\u5165\u7684\u5E74\u4EFD\u8D85\u8FC7\u4E86\u53EF\u67E5\u8BE2\u8303\u56F4\uFF0C\u4EC5\u652F\u63011891\u81F32100\u5E74',\r
		101 : '\u53C2\u6570\u8F93\u5165\u9519\u8BEF\uFF0C\u8BF7\u67E5\u9605\u6587\u6863'\r
	};\r
	\r
	var cache = null; //\u67D0\u5E74\u76F8\u540C\u8BA1\u7B97\u8FDB\u884Ccache\uFF0C\u4EE5\u52A0\u901F\u8BA1\u7B97\u901F\u5EA6\r
	var cacheUtil = { //cache\u7BA1\u7406\u5DE5\u5177\r
		current : '',\r
		setCurrent : function(year){\r
			if(this.current != year){\r
				this.current = year;\r
				this.clear();\r
			}\r
		},\r
		set : function(key,value){\r
			if(!cache) cache = {};\r
			cache[key] = value;\r
			return cache[key];\r
		},\r
		get : function(key){\r
			if(!cache) cache = {};\r
			return cache[key];\r
		},\r
		clear : function(){\r
			cache = null;\r
		}\r
	};\r
	\r
	var formateDayD4 = function(month,day){\r
		month = month+1;\r
		month = month<10 ? '0'+month : month;\r
		day = day<10 ? '0'+day : day;\r
		return 'd'+month+day;\r
	};\r
	\r
	var minYear = 1890;//\u6700\u5C0F\u5E74\u9650\r
	var maxYear = 2100;//\u6700\u5927\u5E74\u9650\r
	var DATA = {\r
		heavenlyStems: ['\u7532', '\u4E59', '\u4E19', '\u4E01', '\u620A', '\u5DF1', '\u5E9A', '\u8F9B', '\u58EC', '\u7678'], //\u5929\u5E72\r
		earthlyBranches: ['\u5B50', '\u4E11', '\u5BC5', '\u536F', '\u8FB0', '\u5DF3', '\u5348', '\u672A', '\u7533', '\u9149', '\u620C', '\u4EA5'], //\u5730\u652F\r
		zodiac: ['\u9F20','\u725B','\u864E','\u5154','\u9F99','\u86C7','\u9A6C','\u7F8A','\u7334','\u9E21','\u72D7','\u732A'], //\u5BF9\u5E94\u5730\u652F\u5341\u4E8C\u751F\u8096\r
		solarTerm: ['\u5C0F\u5BD2', '\u5927\u5BD2', '\u7ACB\u6625', '\u96E8\u6C34', '\u60CA\u86F0', '\u6625\u5206', '\u6E05\u660E', '\u8C37\u96E8', '\u7ACB\u590F', '\u5C0F\u6EE1', '\u8292\u79CD', '\u590F\u81F3', '\u5C0F\u6691', '\u5927\u6691', '\u7ACB\u79CB', '\u5904\u6691', '\u767D\u9732', '\u79CB\u5206', '\u5BD2\u9732', '\u971C\u964D', '\u7ACB\u51AC', '\u5C0F\u96EA', '\u5927\u96EA','\u51AC\u81F3'], //\u4E8C\u5341\u56DB\u8282\u6C14\r
		monthCn: ['\u6B63', '\u4E8C', '\u4E09', '\u56DB', '\u4E94', '\u516D', '\u4E03', '\u516B', '\u4E5D', '\u5341', '\u5341\u4E00', '\u5341\u4E8C'],\r
		dateCn: ['\u521D\u4E00', '\u521D\u4E8C', '\u521D\u4E09', '\u521D\u56DB', '\u521D\u4E94', '\u521D\u516D', '\u521D\u4E03', '\u521D\u516B', '\u521D\u4E5D', '\u521D\u5341', '\u5341\u4E00', '\u5341\u4E8C', '\u5341\u4E09', '\u5341\u56DB', '\u5341\u4E94', '\u5341\u516D', '\u5341\u4E03', '\u5341\u516B', '\u5341\u4E5D', '\u4E8C\u5341', '\u5EFF\u4E00', '\u5EFF\u4E8C', '\u5EFF\u4E09', '\u5EFF\u56DB', '\u5EFF\u4E94', '\u5EFF\u516D', '\u5EFF\u4E03', '\u5EFF\u516B', '\u5EFF\u4E5D', '\u4E09\u5341', '\u5345\u4E00']\r
	};\r
	\r
	//\u4E2D\u56FD\u8282\u65E5\u653E\u5047\u5B89\u6392\uFF0C\u5916\u90E8\u8BBE\u7F6E\uFF0C0\u65E0\u7279\u6B8A\u5B89\u6392\uFF0C1\u5DE5\u4F5C\uFF0C2\u653E\u5047\r
	var worktime = {};\r
	//\u9ED8\u8BA4\u8BBE\u7F6E2013-2014\u5E74\u653E\u5047\u5B89\u6392\r
	worktime.y2013 = {"d0101":2,"d0102":2,"d0103":2,"d0105":1,"d0106":1,"d0209":2,"d0210":2,"d0211":2,"d0212":2,"d0213":2,"d0214":2,"d0215":2,"d0216":1,"d0217":1,"d0404":2,"d0405":2,"d0406":2,"d0407":1,"d0427":1,"d0428":1,"d0429":2,"d0430":2,"d0501":2,"d0608":1,"d0609":1,"d0610":2,"d0611":2,"d0612":2,"d0919":2,"d0920":2,"d0921":2,"d0922":1,"d0929":1,"d1001":2,"d1002":2,"d1003":2,"d1004":2,"d1005":2,"d1006":2,"d1007":2,"d1012":1};\r
	worktime.y2014 = {"d0101":2,"d0126":1,"d0131":2,"d0201":2,"d0202":2,"d0203":2,"d0204":2,"d0205":2,"d0206":2,"d0208":1,"d0405":2,"d0407":2,"d0501":2,"d0502":2,"d0503":2,"d0504":1,"d0602":2,"d0908":2,"d0928":1,"d1001":2,"d1002":2,"d1003":2,"d1004":2,"d1005":2,"d1006":2,"d1007":2,"d1011":1};\r
	\r
	//\u516C\u5386\u8282\u65E5\r
	var solarFestival = {\r
		'd0101':'\u5143\u65E6\u8282',\r
		'd0202':'\u4E16\u754C\u6E7F\u5730\u65E5',\r
		'd0210':'\u56FD\u9645\u6C14\u8C61\u8282',\r
		'd0214':'\u60C5\u4EBA\u8282',\r
		'd0301':'\u56FD\u9645\u6D77\u8C79\u65E5',\r
		'd0303':'\u5168\u56FD\u7231\u8033\u65E5',\r
		'd0305':'\u5B66\u96F7\u950B\u7EAA\u5FF5\u65E5',\r
		'd0308':'\u5987\u5973\u8282',\r
		'd0312':'\u690D\u6811\u8282 \u5B59\u4E2D\u5C71\u901D\u4E16\u7EAA\u5FF5\u65E5',\r
		'd0314':'\u56FD\u9645\u8B66\u5BDF\u65E5',\r
		'd0315':'\u6D88\u8D39\u8005\u6743\u76CA\u65E5',\r
		'd0317':'\u4E2D\u56FD\u56FD\u533B\u8282 \u56FD\u9645\u822A\u6D77\u65E5',\r
		'd0321':'\u4E16\u754C\u68EE\u6797\u65E5 \u6D88\u9664\u79CD\u65CF\u6B67\u89C6\u56FD\u9645\u65E5 \u4E16\u754C\u513F\u6B4C\u65E5',\r
		'd0322':'\u4E16\u754C\u6C34\u65E5',\r
		'd0323':'\u4E16\u754C\u6C14\u8C61\u65E5',\r
		'd0324':'\u4E16\u754C\u9632\u6CBB\u7ED3\u6838\u75C5\u65E5',\r
		'd0325':'\u5168\u56FD\u4E2D\u5C0F\u5B66\u751F\u5B89\u5168\u6559\u80B2\u65E5',\r
		'd0330':'\u5DF4\u52D2\u65AF\u5766\u56FD\u571F\u65E5',\r
		'd0401':'\u611A\u4EBA\u8282 \u5168\u56FD\u7231\u56FD\u536B\u751F\u8FD0\u52A8\u6708(\u56DB\u6708) \u7A0E\u6536\u5BA3\u4F20\u6708(\u56DB\u6708)',\r
		'd0407':'\u4E16\u754C\u536B\u751F\u65E5',\r
		'd0422':'\u4E16\u754C\u5730\u7403\u65E5',\r
		'd0423':'\u4E16\u754C\u56FE\u4E66\u548C\u7248\u6743\u65E5',\r
		'd0424':'\u4E9A\u975E\u65B0\u95FB\u5DE5\u4F5C\u8005\u65E5',\r
		'd0501':'\u52B3\u52A8\u8282',\r
		'd0504':'\u9752\u5E74\u8282',\r
		'd0505':'\u7898\u7F3A\u4E4F\u75C5\u9632\u6CBB\u65E5',\r
		'd0508':'\u4E16\u754C\u7EA2\u5341\u5B57\u65E5',\r
		'd0512':'\u56FD\u9645\u62A4\u58EB\u8282',\r
		'd0515':'\u56FD\u9645\u5BB6\u5EAD\u65E5',\r
		'd0517':'\u4E16\u754C\u7535\u4FE1\u65E5',\r
		'd0518':'\u56FD\u9645\u535A\u7269\u9986\u65E5',\r
		'd0520':'\u5168\u56FD\u5B66\u751F\u8425\u517B\u65E5',\r
		'd0522':'\u56FD\u9645\u751F\u7269\u591A\u6837\u6027\u65E5',\r
		'd0523':'\u56FD\u9645\u725B\u5976\u65E5',\r
		'd0531':'\u4E16\u754C\u65E0\u70DF\u65E5', \r
		'd0601':'\u56FD\u9645\u513F\u7AE5\u8282',\r
		'd0605':'\u4E16\u754C\u73AF\u5883\u65E5',\r
		'd0606':'\u5168\u56FD\u7231\u773C\u65E5',\r
		'd0617':'\u9632\u6CBB\u8352\u6F20\u5316\u548C\u5E72\u65F1\u65E5',\r
		'd0623':'\u56FD\u9645\u5965\u6797\u5339\u514B\u65E5',\r
		'd0625':'\u5168\u56FD\u571F\u5730\u65E5',\r
		'd0626':'\u56FD\u9645\u7981\u6BD2\u65E5',\r
		'd0701':'\u9999\u6E2F\u56DE\u5F52\u7EAA\u5FF5\u65E5 \u4E2D\u5171\u8BDE\u8FB0 \u4E16\u754C\u5EFA\u7B51\u65E5',\r
		'd0702':'\u56FD\u9645\u4F53\u80B2\u8BB0\u8005\u65E5',\r
		'd0707':'\u6297\u65E5\u6218\u4E89\u7EAA\u5FF5\u65E5',\r
		'd0711':'\u4E16\u754C\u4EBA\u53E3\u65E5',\r
		'd0730':'\u975E\u6D32\u5987\u5973\u65E5',\r
		'd0801':'\u5EFA\u519B\u8282',\r
		'd0808':'\u4E2D\u56FD\u7537\u5B50\u8282(\u7238\u7238\u8282)',\r
		'd0815':'\u6297\u65E5\u6218\u4E89\u80DC\u5229\u7EAA\u5FF5',\r
		'd0908':'\u56FD\u9645\u626B\u76F2\u65E5 \u56FD\u9645\u65B0\u95FB\u5DE5\u4F5C\u8005\u65E5',\r
		'd0909':'\u6BDB\u6CFD\u4E1C\u901D\u4E16\u7EAA\u5FF5',\r
		'd0910':'\u4E2D\u56FD\u6559\u5E08\u8282', \r
		'd0914':'\u4E16\u754C\u6E05\u6D01\u5730\u7403\u65E5',\r
		'd0916':'\u56FD\u9645\u81ED\u6C27\u5C42\u4FDD\u62A4\u65E5',\r
		'd0918':'\u4E5D\u4E00\u516B\u4E8B\u53D8\u7EAA\u5FF5\u65E5',\r
		'd0920':'\u56FD\u9645\u7231\u7259\u65E5',\r
		'd0927':'\u4E16\u754C\u65C5\u6E38\u65E5',\r
		'd0928':'\u5B54\u5B50\u8BDE\u8FB0',\r
		'd1001':'\u56FD\u5E86\u8282 \u4E16\u754C\u97F3\u4E50\u65E5 \u56FD\u9645\u8001\u4EBA\u8282',\r
		'd1002':'\u56FD\u9645\u548C\u5E73\u4E0E\u6C11\u4E3B\u81EA\u7531\u6597\u4E89\u65E5',\r
		'd1004':'\u4E16\u754C\u52A8\u7269\u65E5',\r
		'd1006':'\u8001\u4EBA\u8282',\r
		'd1008':'\u5168\u56FD\u9AD8\u8840\u538B\u65E5 \u4E16\u754C\u89C6\u89C9\u65E5',\r
		'd1009':'\u4E16\u754C\u90AE\u653F\u65E5 \u4E07\u56FD\u90AE\u8054\u65E5',\r
		'd1010':'\u8F9B\u4EA5\u9769\u547D\u7EAA\u5FF5\u65E5 \u4E16\u754C\u7CBE\u795E\u536B\u751F\u65E5',\r
		'd1013':'\u4E16\u754C\u4FDD\u5065\u65E5 \u56FD\u9645\u6559\u5E08\u8282',\r
		'd1014':'\u4E16\u754C\u6807\u51C6\u65E5',\r
		'd1015':'\u56FD\u9645\u76F2\u4EBA\u8282(\u767D\u624B\u6756\u8282)',\r
		'd1016':'\u4E16\u754C\u7CAE\u98DF\u65E5',\r
		'd1017':'\u4E16\u754C\u6D88\u9664\u8D2B\u56F0\u65E5',\r
		'd1022':'\u4E16\u754C\u4F20\u7EDF\u533B\u836F\u65E5',\r
		'd1024':'\u8054\u5408\u56FD\u65E5 \u4E16\u754C\u53D1\u5C55\u4FE1\u606F\u65E5',\r
		'd1031':'\u4E16\u754C\u52E4\u4FED\u65E5',\r
		'd1107':'\u5341\u6708\u793E\u4F1A\u4E3B\u4E49\u9769\u547D\u7EAA\u5FF5\u65E5',\r
		'd1108':'\u4E2D\u56FD\u8BB0\u8005\u65E5',\r
		'd1109':'\u5168\u56FD\u6D88\u9632\u5B89\u5168\u5BA3\u4F20\u6559\u80B2\u65E5',\r
		'd1110':'\u4E16\u754C\u9752\u5E74\u8282',\r
		'd1111':'\u56FD\u9645\u79D1\u5B66\u4E0E\u548C\u5E73\u5468(\u672C\u65E5\u6240\u5C5E\u7684\u4E00\u5468)',\r
		'd1112':'\u5B59\u4E2D\u5C71\u8BDE\u8FB0\u7EAA\u5FF5\u65E5',\r
		'd1114':'\u4E16\u754C\u7CD6\u5C3F\u75C5\u65E5',\r
		'd1117':'\u56FD\u9645\u5927\u5B66\u751F\u8282 \u4E16\u754C\u5B66\u751F\u8282',\r
		'd1121':'\u4E16\u754C\u95EE\u5019\u65E5 \u4E16\u754C\u7535\u89C6\u65E5',\r
		'd1129':'\u56FD\u9645\u58F0\u63F4\u5DF4\u52D2\u65AF\u5766\u4EBA\u6C11\u56FD\u9645\u65E5',\r
		'd1201':'\u4E16\u754C\u827E\u6ECB\u75C5\u65E5',\r
		'd1203':'\u4E16\u754C\u6B8B\u75BE\u4EBA\u65E5',\r
		'd1205':'\u56FD\u9645\u7ECF\u6D4E\u548C\u793E\u4F1A\u53D1\u5C55\u5FD7\u613F\u4EBA\u5458\u65E5',\r
		'd1208':'\u56FD\u9645\u513F\u7AE5\u7535\u89C6\u65E5',\r
		'd1209':'\u4E16\u754C\u8DB3\u7403\u65E5',\r
		'd1210':'\u4E16\u754C\u4EBA\u6743\u65E5',\r
		'd1212':'\u897F\u5B89\u4E8B\u53D8\u7EAA\u5FF5\u65E5',\r
		'd1213':'\u5357\u4EAC\u5927\u5C60\u6740(1937\u5E74)\u7EAA\u5FF5\u65E5\uFF01\u7D27\u8BB0\u8840\u6CEA\u53F2\uFF01',\r
		'd1220':'\u6FB3\u95E8\u56DE\u5F52\u7EAA\u5FF5',\r
		'd1221':'\u56FD\u9645\u7BEE\u7403\u65E5',\r
		'd1224':'\u5E73\u5B89\u591C',\r
		'd1225':'\u5723\u8BDE\u8282',\r
		'd1226':'\u6BDB\u6CFD\u4E1C\u8BDE\u8FB0\u7EAA\u5FF5'\r
	};\r
	\r
	//\u519C\u5386\u8282\u65E5\r
	var lunarFestival = {\r
		'd0101':'\u6625\u8282',\r
		'd0115':'\u5143\u5BB5\u8282',\r
		'd0202':'\u9F99\u62AC\u5934\u8282',\r
		'd0323':'\u5988\u7956\u751F\u8FB0',\r
		'd0505':'\u7AEF\u5348\u8282',\r
		'd0707':'\u4E03\u5915\u60C5\u4EBA\u8282',\r
		'd0715':'\u4E2D\u5143\u8282',\r
		'd0815':'\u4E2D\u79CB\u8282',\r
		'd0909':'\u91CD\u9633\u8282',\r
		'd1015':'\u4E0B\u5143\u8282',\r
		'd1208':'\u814A\u516B\u8282',\r
		'd1223':'\u5C0F\u5E74',\r
		'd0100':'\u9664\u5915'\r
	}\r
\r
	/**\r
	 * 1890 - 2100 \u5E74\u7684\u519C\u5386\u6570\u636E\r
	 * \u6570\u636E\u683C\u5F0F\uFF1A[0,2,9,21936]\r
	 * [\u95F0\u6708\u6240\u5728\u6708\uFF0C0\u4E3A\u6CA1\u6709\u95F0\u6708; *\u6B63\u6708\u521D\u4E00\u5BF9\u5E94\u516C\u5386\u6708; *\u6B63\u6708\u521D\u4E00\u5BF9\u5E94\u516C\u5386\u65E5; *\u519C\u5386\u6BCF\u6708\u7684\u5929\u6570\u7684\u6570\u7EC4\uFF08\u9700\u8F6C\u6362\u4E3A\u4E8C\u8FDB\u5236,\u5F97\u5230\u6BCF\u6708\u5927\u5C0F\uFF0C0=\u5C0F\u6708(29\u65E5),1=\u5927\u6708(30\u65E5)\uFF09;]\r
	*/\r
	var lunarInfo = [[2,1,21,22184],[0,2,9,21936],[6,1,30,9656],[0,2,17,9584],[0,2,6,21168],[5,1,26,43344],[0,2,13,59728],[0,2,2,27296],[3,1,22,44368],[0,2,10,43856],[8,1,30,19304],[0,2,19,19168],[0,2,8,42352],[5,1,29,21096],[0,2,16,53856],[0,2,4,55632],[4,1,25,27304],[0,2,13,22176],[0,2,2,39632],[2,1,22,19176],[0,2,10,19168],[6,1,30,42200],[0,2,18,42192],[0,2,6,53840],[5,1,26,54568],[0,2,14,46400],[0,2,3,54944],[2,1,23,38608],[0,2,11,38320],[7,2,1,18872],[0,2,20,18800],[0,2,8,42160],[5,1,28,45656],[0,2,16,27216],[0,2,5,27968],[4,1,24,44456],[0,2,13,11104],[0,2,2,38256],[2,1,23,18808],[0,2,10,18800],[6,1,30,25776],[0,2,17,54432],[0,2,6,59984],[5,1,26,27976],[0,2,14,23248],[0,2,4,11104],[3,1,24,37744],[0,2,11,37600],[7,1,31,51560],[0,2,19,51536],[0,2,8,54432],[6,1,27,55888],[0,2,15,46416],[0,2,5,22176],[4,1,25,43736],[0,2,13,9680],[0,2,2,37584],[2,1,22,51544],[0,2,10,43344],[7,1,29,46248],[0,2,17,27808],[0,2,6,46416],[5,1,27,21928],[0,2,14,19872],[0,2,3,42416],[3,1,24,21176],[0,2,12,21168],[8,1,31,43344],[0,2,18,59728],[0,2,8,27296],[6,1,28,44368],[0,2,15,43856],[0,2,5,19296],[4,1,25,42352],[0,2,13,42352],[0,2,2,21088],[3,1,21,59696],[0,2,9,55632],[7,1,30,23208],[0,2,17,22176],[0,2,6,38608],[5,1,27,19176],[0,2,15,19152],[0,2,3,42192],[4,1,23,53864],[0,2,11,53840],[8,1,31,54568],[0,2,18,46400],[0,2,7,46752],[6,1,28,38608],[0,2,16,38320],[0,2,5,18864],[4,1,25,42168],[0,2,13,42160],[10,2,2,45656],[0,2,20,27216],[0,2,9,27968],[6,1,29,44448],[0,2,17,43872],[0,2,6,38256],[5,1,27,18808],[0,2,15,18800],[0,2,4,25776],[3,1,23,27216],[0,2,10,59984],[8,1,31,27432],[0,2,19,23232],[0,2,7,43872],[5,1,28,37736],[0,2,16,37600],[0,2,5,51552],[4,1,24,54440],[0,2,12,54432],[0,2,1,55888],[2,1,22,23208],[0,2,9,22176],[7,1,29,43736],[0,2,18,9680],[0,2,7,37584],[5,1,26,51544],[0,2,14,43344],[0,2,3,46240],[4,1,23,46416],[0,2,10,44368],[9,1,31,21928],[0,2,19,19360],[0,2,8,42416],[6,1,28,21176],[0,2,16,21168],[0,2,5,43312],[4,1,25,29864],[0,2,12,27296],[0,2,1,44368],[2,1,22,19880],[0,2,10,19296],[6,1,29,42352],[0,2,17,42208],[0,2,6,53856],[5,1,26,59696],[0,2,13,54576],[0,2,3,23200],[3,1,23,27472],[0,2,11,38608],[11,1,31,19176],[0,2,19,19152],[0,2,8,42192],[6,1,28,53848],[0,2,15,53840],[0,2,4,54560],[5,1,24,55968],[0,2,12,46496],[0,2,1,22224],[2,1,22,19160],[0,2,10,18864],[7,1,30,42168],[0,2,17,42160],[0,2,6,43600],[5,1,26,46376],[0,2,14,27936],[0,2,2,44448],[3,1,23,21936],[0,2,11,37744],[8,2,1,18808],[0,2,19,18800],[0,2,8,25776],[6,1,28,27216],[0,2,15,59984],[0,2,4,27424],[4,1,24,43872],[0,2,12,43744],[0,2,2,37600],[3,1,21,51568],[0,2,9,51552],[7,1,29,54440],[0,2,17,54432],[0,2,5,55888],[5,1,26,23208],[0,2,14,22176],[0,2,3,42704],[4,1,23,21224],[0,2,11,21200],[8,1,31,43352],[0,2,19,43344],[0,2,7,46240],[6,1,27,46416],[0,2,15,44368],[0,2,5,21920],[4,1,24,42448],[0,2,12,42416],[0,2,2,21168],[3,1,22,43320],[0,2,9,26928],[7,1,29,29336],[0,2,17,27296],[0,2,6,44368],[5,1,26,19880],[0,2,14,19296],[0,2,3,42352],[4,1,24,21104],[0,2,10,53856],[8,1,30,59696],[0,2,18,54560],[0,2,7,55968],[6,1,27,27472],[0,2,15,22224],[0,2,5,19168],[4,1,25,42216],[0,2,12,42192],[0,2,1,53584],[2,1,21,55592],[0,2,9,54560]];\r
	\r
	/**\r
	 * \u4E8C\u5341\u56DB\u8282\u6C14\u6570\u636E\uFF0C\u8282\u6C14\u70B9\u65F6\u95F4\uFF08\u5355\u4F4D\u662F\u5206\u949F\uFF09\r
	 * \u4ECE0\u5C0F\u5BD2\u8D77\u7B97\r
	 */\r
	var termInfo = [0,21208,42467,63836,85337,107014,128867,150921,173149,195551,218072,240693,263343,285989,308563,331033,353350,375494,397447,419210,440795,462224,483532,504758];\r
	\r
	/**\r
	 * \u5224\u65AD\u519C\u5386\u5E74\u95F0\u6708\u6570\r
	 * @param {Number} year \u519C\u5386\u5E74\r
	 * return \u95F0\u6708\u6570 \uFF08\u6708\u4EFD\u4ECE1\u5F00\u59CB\uFF09\r
	 */\r
	function getLunarLeapYear(year){\r
		var yearData = lunarInfo[year-minYear];\r
		return yearData[0];\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u519C\u5386\u5E74\u4EFD\u4E00\u5E74\u7684\u6BCF\u6708\u7684\u5929\u6570\u53CA\u4E00\u5E74\u7684\u603B\u5929\u6570\r
	 * @param {Number} year \u519C\u5386\u5E74\r
	 */\r
	function getLunarYearDays(year){\r
		var yearData = lunarInfo[year-minYear];\r
		var leapMonth = yearData[0]; //\u95F0\u6708\r
		var monthData = yearData[3].toString(2);\r
		var monthDataArr = monthData.split('');\r
		\r
		//\u8FD8\u539F\u6570\u636E\u81F316\u4F4D,\u5C11\u4E8E16\u4F4D\u7684\u5728\u524D\u9762\u63D2\u51650\uFF08\u4E8C\u8FDB\u5236\u5B58\u50A8\u65F6\u524D\u9762\u76840\u88AB\u5FFD\u7565\uFF09\r
		for(var i=0;i<16-monthDataArr.length;i++){\r
			monthDataArr.unshift(0);\r
		}\r
		\r
		var len = leapMonth ? 13 : 12; //\u8BE5\u5E74\u6709\u51E0\u4E2A\u6708\r
		var yearDays = 0;\r
		var monthDays = [];\r
		for(var i=0;i<len;i++){\r
			if(monthDataArr[i]==0){\r
				yearDays += 29;\r
				monthDays.push(29);\r
			}else{\r
				yearDays += 30;\r
				monthDays.push(30);\r
			}\r
		}\r
		\r
		return {\r
			yearDays : yearDays,\r
			monthDays : monthDays\r
		};\r
	};\r
	\r
	/**\r
	 * \u901A\u8FC7\u95F4\u9694\u5929\u6570\u67E5\u627E\u519C\u5386\u65E5\u671F\r
	 * @param {Number} year,between \u519C\u5386\u5E74\uFF0C\u95F4\u9694\u5929\u6570\r
	 */\r
	function getLunarDateByBetween(year,between){\r
		var lunarYearDays = getLunarYearDays(year);\r
		var end = between>0 ? between : lunarYearDays.yearDays - Math.abs(between);\r
		var monthDays = lunarYearDays.monthDays;\r
		var tempDays = 0;\r
		var month = 0;\r
		for(var i=0;i<monthDays.length;i++){\r
			tempDays += monthDays[i];\r
			if(tempDays > end){\r
				month = i;\r
				tempDays = tempDays-monthDays[i];\r
				break;\r
			}\r
		}\r
		\r
		return [year,month,end - tempDays + 1];\r
	};\r
\r
	/**\r
	 * \u6839\u636E\u8DDD\u79BB\u6B63\u6708\u521D\u4E00\u7684\u5929\u6570\u8BA1\u7B97\u519C\u5386\u65E5\u671F\r
	 * @param {Number} year \u516C\u5386\u5E74\uFF0C\u6708\uFF0C\u65E5\r
	 */\r
	function getLunarByBetween(year,month,day){\r
		var yearData = lunarInfo[year-minYear];\r
		var zenMonth = yearData[1];\r
		var zenDay = yearData[2];\r
		var between = getDaysBetweenSolar(year,zenMonth-1,zenDay,year,month,day);\r
		if(between==0){ //\u6B63\u6708\u521D\u4E00\r
			return [year,0,1];\r
		}else{\r
			var lunarYear = between>0 ? year : year-1;\r
			return getLunarDateByBetween(lunarYear,between);\r
		}\r
	};\r
	\r
	/**\r
	 * \u4E24\u4E2A\u516C\u5386\u65E5\u671F\u4E4B\u95F4\u7684\u5929\u6570\r
	 */\r
	function getDaysBetweenSolar(year,month,day,year1,month1,day1){\r
		var date = new Date(year,month,day).getTime();\r
		var date1 = new Date(year1,month1,day1).getTime();\r
		return (date1-date) / 86400000;\r
	};\r
	\r
	/**\r
	 * \u8BA1\u7B97\u519C\u5386\u65E5\u671F\u79BB\u6B63\u6708\u521D\u4E00\u6709\u591A\u5C11\u5929\r
	 * @param {Number} year,month,day \u519C\u5E74\uFF0C\u6708(0-12\uFF0C\u6709\u95F0\u6708)\uFF0C\u65E5\r
	 */\r
	function getDaysBetweenZheng(year,month,day){\r
		var lunarYearDays = getLunarYearDays(year);\r
		var monthDays = lunarYearDays.monthDays;\r
		var days = 0;\r
		for(var i=0;i<monthDays.length;i++){\r
			if(i<month){\r
				days += monthDays[i];\r
			}else{\r
				break;\r
			}\r
		};\r
		return days+day-1;\r
	};\r
	\r
	/**\r
	 * \u67D0\u5E74\u7684\u7B2Cn\u4E2A\u8282\u6C14\u4E3A\u51E0\u65E5\r
	 * 31556925974.7\u4E3A\u5730\u7403\u516C\u8F6C\u5468\u671F\uFF0C\u662F\u6BEB\u79D2\r
	 * 1890\u5E74\u7684\u6B63\u5C0F\u5BD2\u70B9\uFF1A01-05 16:02:31\uFF0C1890\u5E74\u4E3A\u57FA\u51C6\u70B9\r
	 * @param {Number} y \u516C\u5386\u5E74\r
	 * @param {Number} n \u7B2C\u51E0\u4E2A\u8282\u6C14\uFF0C\u4ECE0\u5C0F\u5BD2\u8D77\u7B97\r
	 * \u7531\u4E8E\u519C\u538624\u8282\u6C14\u4EA4\u8282\u65F6\u523B\u91C7\u7528\u8FD1\u4F3C\u7B97\u6CD5\uFF0C\u53EF\u80FD\u5B58\u5728\u5C11\u91CF\u8BEF\u5DEE(30\u5206\u949F\u5185)\r
	 */\r
	function getTerm(y,n) {\r
		var offDate = new Date( ( 31556925974.7*(y-1890) + termInfo[n]*60000  ) + Date.UTC(1890,0,5,16,2,31) );\r
		return(offDate.getUTCDate());\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u516C\u5386\u5E74\u4E00\u5E74\u7684\u4E8C\u5341\u56DB\u8282\u6C14\r
	 * \u8FD4\u56DEkey:\u65E5\u671F\uFF0Cvalue:\u8282\u6C14\u4E2D\u6587\u540D\r
	 */\r
	function getYearTerm(year){\r
		var res = {};\r
		var month = 0;\r
		for(var i=0;i<24;i++){\r
			var day = getTerm(year,i);\r
			if(i%2==0)month++\r
			res[formateDayD4(month-1,day)] = DATA.solarTerm[i];\r
		}\r
		return res;\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u751F\u8096\r
	 * @param {Number} year \u5E72\u652F\u6240\u5728\u5E74\uFF08\u9ED8\u8BA4\u4EE5\u7ACB\u6625\u524D\u7684\u516C\u5386\u5E74\u4F5C\u4E3A\u57FA\u6570\uFF09\r
	 */\r
	function getYearZodiac(year){\r
		 var num = year-1890+25; //\u53C2\u8003\u5E72\u652F\u7EAA\u5E74\u7684\u8BA1\u7B97\uFF0C\u751F\u8096\u5BF9\u5E94\u5730\u652F\r
		 return DATA.zodiac[num%12];\r
	};\r
	\r
	/**\r
	 * \u8BA1\u7B97\u5929\u5E72\u5730\u652F\r
	 * @param {Number} num 60\u8FDB\u5236\u4E2D\u7684\u4F4D\u7F6E(\u628A60\u4E2A\u5929\u5E72\u5730\u652F\uFF0C\u5F53\u6210\u4E00\u4E2A60\u8FDB\u5236\u7684\u6570)\r
	 */\r
	function cyclical(num) {\r
		return(DATA.heavenlyStems[num%10]+DATA.earthlyBranches[num%12]);\r
	}\r
	\r
	/**\r
	 * \u83B7\u53D6\u5E72\u652F\u7EAA\u5E74\r
	 * @param {Number} year \u5E72\u652F\u6240\u5728\u5E74\r
	 * @param {Number} offset \u504F\u79FB\u91CF\uFF0C\u9ED8\u8BA4\u4E3A0\uFF0C\u4FBF\u4E8E\u67E5\u8BE2\u4E00\u4E2A\u5E74\u8DE8\u4E24\u4E2A\u5E72\u652F\u7EAA\u5E74\uFF08\u4EE5\u7ACB\u6625\u4E3A\u5206\u754C\u7EBF\uFF09\r
	 */\r
	function getLunarYearName(year,offset){\r
		offset = offset || 0;\r
		//1890\u5E741\u6708\u5C0F\u5BD2\uFF08\u5C0F\u5BD2\u4E00\u822C\u662F1\u67085\u62166\u65E5\uFF09\u4EE5\u524D\u4E3A\u5DF1\u4E11\u5E74\uFF0C\u572860\u8FDB\u5236\u4E2D\u639225\r
		return cyclical(year-1890+25+offset);\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u5E72\u652F\u7EAA\u6708\r
	 * @param {Number} year,month \u516C\u5386\u5E74\uFF0C\u5E72\u652F\u6240\u5728\u6708\r
	 * @param {Number} offset \u504F\u79FB\u91CF\uFF0C\u9ED8\u8BA4\u4E3A0\uFF0C\u4FBF\u4E8E\u67E5\u8BE2\u4E00\u4E2A\u6708\u8DE8\u4E24\u4E2A\u5E72\u652F\u7EAA\u6708\uFF08\u6709\u7ACB\u6625\u76842\u6708\uFF09\r
	 */\r
	function getLunarMonthName(year,month,offset){\r
		offset = offset || 0;\r
		//1890\u5E741\u6708\u5C0F\u5BD2\u4EE5\u524D\u4E3A\u4E19\u5B50\u6708\uFF0C\u572860\u8FDB\u5236\u4E2D\u639212\r
		return cyclical((year-1890)*12+month+12+offset);\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u5E72\u652F\u7EAA\u65E5\r
	 * @param {Number} year,month,day \u516C\u5386\u5E74\uFF0C\u6708\uFF0C\u65E5\r
	 */\r
	function getLunarDayName(year,month,day){\r
		//\u5F53\u65E5\u4E0E1890/1/1 \u76F8\u5DEE\u5929\u6570\r
		//1890/1/1\u4E0E 1970/1/1 \u76F8\u5DEE29219\u65E5, 1890/1/1 \u65E5\u67F1\u4E3A\u58EC\u5348\u65E5(60\u8FDB\u523618)\r
		var dayCyclical = Date.UTC(year,month,day)/86400000+29219+18;\r
		return cyclical(dayCyclical);\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u516C\u5386\u6708\u4EFD\u7684\u5929\u6570\r
	 * @param {Number} year \u516C\u5386\u5E74\r
	 * @param {Number} month \u516C\u5386\u6708\r
	 */\r
	function getSolarMonthDays(year,month){\r
		 var monthDays = [31,isLeapYear(year)?29:28,31,30,31,30,31,31,30,31,30,31];\r
		 return monthDays[month];\r
	};\r
	\r
	/**\r
	 * \u5224\u65AD\u516C\u5386\u5E74\u662F\u5426\u662F\u95F0\u5E74\r
	 * @param {Number} year \u516C\u5386\u5E74\r
	 */\r
	function isLeapYear(year){\r
		return ((year%4==0 && year%100 !=0) || (year%400==0));\r
	};\r
		\r
	/*\r
	 * \u7EDF\u4E00\u65E5\u671F\u8F93\u5165\u53C2\u6570\uFF08\u8F93\u5165\u6708\u4EFD\u4ECE1\u5F00\u59CB\uFF0C\u5185\u90E8\u6708\u4EFD\u7EDF\u4E00\u4ECE0\u5F00\u59CB\uFF09\r
	 */\r
	function formateDate(year,month,day,_minYear){\r
		var argsLen = arguments.length;\r
		var now = new Date();\r
		year = argsLen ? parseInt(year,10) : now.getFullYear();\r
		month = argsLen ? parseInt(month-1,10) : now.getMonth();\r
		day = argsLen ? parseInt(day,10) || now.getDate() : now.getDate();\r
		if(year < (_minYear ? _minYear : minYear+1) || year > maxYear)return {error:100, msg:errorCode[100]};\r
		return {\r
			year : year,\r
			month : month,\r
			day : day\r
		};\r
	};\r
	\r
	/**\r
	 * \u5C06\u519C\u5386\u8F6C\u6362\u4E3A\u516C\u5386\r
	 * @param {Number} year,month,day \u519C\u5386\u5E74\uFF0C\u6708(1-13\uFF0C\u6709\u95F0\u6708)\uFF0C\u65E5\r
	 */\r
	function lunarToSolar(_year,_month,_day){\r
		var inputDate = formateDate(_year,_month,_day);\r
		if(inputDate.error)return inputDate;\r
		var year = inputDate.year;\r
		var month = inputDate.month;\r
		var day = inputDate.day;\r
		\r
		var between = getDaysBetweenZheng(year,month,day); //\u79BB\u6B63\u6708\u521D\u4E00\u7684\u5929\u6570\r
		var yearData = lunarInfo[year-minYear];\r
		var zenMonth = yearData[1];\r
		var zenDay = yearData[2];\r
		\r
		var offDate = new Date(year,zenMonth-1,zenDay).getTime() + between * 86400000;\r
			offDate = new Date(offDate);\r
		return {\r
			year : offDate.getFullYear(),\r
			month : offDate.getMonth()+1,\r
			day : offDate.getDate()\r
		};\r
	};\r
	\r
	/**\r
	 * \u5C06\u516C\u5386\u8F6C\u6362\u4E3A\u519C\u5386\r
	 * @param {Number} year,month,day \u516C\u5386\u5E74\uFF0C\u6708\uFF0C\u65E5\r
	 */\r
	function solarToLunar(_year,_month,_day){\r
		var inputDate = formateDate(_year,_month,_day,minYear);\r
		if(inputDate.error)return inputDate;\r
		var year = inputDate.year;\r
		var month = inputDate.month;\r
		var day = inputDate.day;\r
		\r
		cacheUtil.setCurrent(year);\r
		//\u7ACB\u6625\u65E5\u671F\r
		var term2 = cacheUtil.get('term2') ? cacheUtil.get('term2') : cacheUtil.set('term2',getTerm(year,2));\r
		//\u4E8C\u5341\u56DB\u8282\u6C14\r
		var termList = cacheUtil.get('termList') ? cacheUtil.get('termList') : cacheUtil.set('termList',getYearTerm(year));\r
		\r
		var firstTerm = getTerm(year,month*2); //\u67D0\u6708\u7B2C\u4E00\u4E2A\u8282\u6C14\u5F00\u59CB\u65E5\u671F\r
		var GanZhiYear = (month>1 || month==1 && day>=term2) ? year+1 : year;//\u5E72\u652F\u6240\u5728\u5E74\u4EFD\r
		var GanZhiMonth = day>=firstTerm ? month+1 : month; //\u5E72\u652F\u6240\u5728\u6708\u4EFD\uFF08\u4EE5\u8282\u6C14\u4E3A\u754C\uFF09\r
		\r
		var lunarDate = getLunarByBetween(year,month,day);\r
		var lunarLeapMonth = getLunarLeapYear(lunarDate[0]);\r
		var lunarMonthName = '';\r
		if(lunarLeapMonth>0 && lunarLeapMonth==lunarDate[1]){\r
			lunarMonthName = '\u95F0'+DATA.monthCn[lunarDate[1]-1]+'\u6708';\r
		}else if(lunarLeapMonth>0 && lunarDate[1]>lunarLeapMonth){\r
			lunarMonthName = DATA.monthCn[lunarDate[1]-1]+'\u6708';\r
		}else{\r
			lunarMonthName = DATA.monthCn[lunarDate[1]]+'\u6708';\r
		}\r
		\r
		//\u519C\u5386\u8282\u65E5\u5224\u65AD\r
		var lunarFtv = '';\r
		var lunarMonthDays = getLunarYearDays(lunarDate[0]).monthDays;\r
		//\u9664\u5915\r
		if(lunarDate[1] == lunarMonthDays.length-1 && lunarDate[2]==lunarMonthDays[lunarMonthDays.length-1]){\r
			lunarFtv = lunarFestival['d0100'];\r
		}else if(lunarLeapMonth>0 && lunarDate[1]>lunarLeapMonth){\r
			lunarFtv = lunarFestival[formateDayD4(lunarDate[1]-1,lunarDate[2])];\r
		}else{\r
			lunarFtv = lunarFestival[formateDayD4(lunarDate[1],lunarDate[2])];\r
		}\r
		\r
		var res = {\r
			zodiac : getYearZodiac(GanZhiYear),\r
			GanZhiYear : getLunarYearName(GanZhiYear),\r
			GanZhiMonth : getLunarMonthName(year,GanZhiMonth),\r
			GanZhiDay : getLunarDayName(year,month,day),\r
			//\u653E\u5047\u5B89\u6392\uFF1A0\u65E0\u7279\u6B8A\u5B89\u6392\uFF0C1\u5DE5\u4F5C\uFF0C2\u653E\u5047\r
			worktime : worktime['y'+year] && worktime['y'+year][formateDayD4(month,day)] ? worktime['y'+year][formateDayD4(month,day)] : 0,\r
			term : termList[formateDayD4(month,day)],\r
			\r
			lunarYear : lunarDate[0],\r
			lunarMonth : lunarDate[1]+1,\r
			lunarDay : lunarDate[2],\r
			lunarMonthName : lunarMonthName,\r
			lunarDayName : DATA.dateCn[lunarDate[2]-1],\r
			lunarLeapMonth : lunarLeapMonth,\r
			\r
			solarFestival : solarFestival[formateDayD4(month,day)],\r
			lunarFestival : lunarFtv\r
		};\r
\r
		return res;\r
	};\r
	\r
	/**\r
	 * \u83B7\u53D6\u6307\u5B9A\u516C\u5386\u6708\u4EFD\u7684\u519C\u5386\u6570\u636E\r
	 * return res{Object}\r
	 * @param {Number} year,month \u516C\u5386\u5E74\uFF0C\u6708\r
	 * @param {Boolean} fill \u662F\u5426\u7528\u4E0A\u4E0B\u6708\u6570\u636E\u8865\u9F50\u9996\u5C3E\u7A7A\u7F3A\uFF0C\u9996\u4F8B\u6570\u636E\u4ECE\u5468\u65E5\u5F00\u59CB\r
	 */\r
	function calendar(_year,_month,fill){\r
		var inputDate = formateDate(_year,_month);\r
		if(inputDate.error)return inputDate;\r
		var year = inputDate.year;\r
		var month = inputDate.month;\r
		\r
		var calendarData = solarCalendar(year,month+1,fill);\r
		for(var i=0;i<calendarData.monthData.length;i++){\r
			var cData = calendarData.monthData[i];\r
			var lunarData = solarToLunar(cData.year,cData.month,cData.day);\r
			extend(calendarData.monthData[i],lunarData);\r
		}\r
		return calendarData;\r
	};\r
	\r
	/**\r
	 * \u516C\u5386\u67D0\u6708\u65E5\u5386\r
	 * return res{Object}\r
	 * @param {Number} year,month \u516C\u5386\u5E74\uFF0C\u6708\r
	 * @param {Boolean} fill \u662F\u5426\u7528\u4E0A\u4E0B\u6708\u6570\u636E\u8865\u9F50\u9996\u5C3E\u7A7A\u7F3A\uFF0C\u9996\u4F8B\u6570\u636E\u4ECE\u5468\u65E5\u5F00\u59CB (7*6\u9635\u5217)\r
	 */\r
	function solarCalendar(_year,_month,fill){\r
		var inputDate = formateDate(_year,_month);\r
		if(inputDate.error)return inputDate;\r
		var year = inputDate.year;\r
		var month = inputDate.month;\r
		\r
		var firstDate = new Date(year,month,1);\r
		var preMonthDays,preMonthData,nextMonthData;\r
		\r
		var res = {\r
			firstDay : firstDate.getDay(), //\u8BE5\u67081\u53F7\u661F\u671F\u51E0\r
			monthDays : getSolarMonthDays(year,month), //\u8BE5\u6708\u5929\u6570\r
			monthData : []\r
		};\r
		\r
		res.monthData = creatLenArr(year,month+1,res.monthDays,1);\r
\r
		if(fill){\r
			if(res.firstDay > 0){ //\u524D\u8865\r
				var preYear = month-1<0 ? year-1 : year;\r
				var preMonth = month-1<0 ? 11 : month-1;\r
				preMonthDays = getSolarMonthDays(preYear,preMonth);\r
				preMonthData = creatLenArr(preYear,preMonth+1,res.firstDay,preMonthDays-res.firstDay+1);\r
				res.monthData = preMonthData.concat(res.monthData);\r
			}\r
			\r
			if(7*6 - res.monthData.length!=0){ //\u540E\u8865\r
				var nextYear = month+1>11 ? year+1 : year;\r
				var nextMonth = month+1>11 ? 0 : month+1;\r
				var fillLen = 7*6 - res.monthData.length;\r
				nextMonthData = creatLenArr(nextYear,nextMonth+1,fillLen,1);\r
				res.monthData = res.monthData.concat(nextMonthData);\r
			}\r
		}\r
		\r
		return res;\r
	};\r
	\r
	/**\r
	 * \u8BBE\u7F6E\u653E\u5047\u5B89\u6392\u3010\u5BF9\u5916\u66B4\u9732\u63A5\u53E3\u3011\r
	 * @param {Object} workData\r
	 */\r
	function setWorktime(workData){\r
		extend(worktime,workData);\r
	};\r
\r
	var LunarCalendar = {\r
		solarToLunar : solarToLunar,\r
		lunarToSolar : lunarToSolar,\r
		calendar : calendar,\r
		solarCalendar : solarCalendar,\r
		setWorktime : setWorktime,\r
		getSolarMonthDays : getSolarMonthDays\r
	};\r
	\r
	if (true){\r
		!(__WEBPACK_AMD_DEFINE_RESULT__ = (function (){\r
			return LunarCalendar;\r
		}).call(exports, __webpack_require__, exports, module),
		__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r
	}else {};\r
})();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///34757
`)}}]);
