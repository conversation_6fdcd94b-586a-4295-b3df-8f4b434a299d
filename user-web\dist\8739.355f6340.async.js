"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[8739],{60325:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ ItemUsedTableCreateView; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/stores/TaskItemUsedCreateStore.tsx
var TaskItemUsedCreateStore = __webpack_require__(14682);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useDebounceEffect/index.js
var useDebounceEffect = __webpack_require__(8527);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./src/components/Task/TaskItemUsed/CategoryQuantitySelector.tsx + 1 modules
var CategoryQuantitySelector = __webpack_require__(11371);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/TaskItemUsed/CreateItemCreateView.tsx















var Item = es_form/* default */.Z.Item;

/**\r
 * GIAO DI\u1EC6N TH\xCAM S\u1ED0 L\u01AF\u1EE2NG D\u1EF0 KI\u1EBEN V\u1EACT T\u01AF TRONG GIAO DI\u1EC6N CREATE TASK\r
 */

var CreateItem = function CreateItem() {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    treeData = _useState6[0],
    setTreeData = _useState6[1];
  var _useTaskItemUsedCreat = (0,TaskItemUsedCreateStore/* useTaskItemUsedCreateStore */.W)(),
    setTaskItemUsed = _useTaskItemUsedCreat.setTaskItemUsed;
  var intl = (0,_umi_production_exports.useIntl)();
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        var dataGroup;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,product_item/* getItemByGroup */.Kd)({});
            case 2:
              dataGroup = _context.sent;
              if (dataGroup.data) {
                setTreeData(dataGroup.data.map(function (item) {
                  return {
                    title: item.item_group_label,
                    value: item.item_group,
                    key: item.item_group,
                    normalized_title: item.item_group_label.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''),
                    children: item.item.map(function (child) {
                      return {
                        title: child.label || '',
                        value: child.name || '',
                        key: child.name || '',
                        normalized_title: (child.label || '').toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''),
                        fullObject: child
                      };
                    })
                  };
                }));
              }
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchData();
  }, []);
  var showModal = function showModal() {
    return setOpen(true);
  };
  var hideModal = function hideModal() {
    return setOpen(false);
  };
  var findNodeByKey = function findNodeByKey(key, data) {
    var _iterator = createForOfIteratorHelper_default()(data),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var node = _step.value;
        if (node.key === key) return node;
        if (node.children) {
          var found = findNodeByKey(key, node.children);
          if (found) return found;
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  };
  var handleSelectChange = function handleSelectChange(selectedWithBOM, selectedWithoutBOM) {
    var combinedSelected = [].concat(toConsumableArray_default()(selectedWithBOM), toConsumableArray_default()(selectedWithoutBOM));
    var selectedItems = combinedSelected.map(function (key) {
      var item = findNodeByKey(key.toString(), treeData);
      if (item && !item.children) {
        return {
          iot_category_id: key.toString(),
          item_name: item.fullObject.item_name,
          label: item.title,
          uom: item.fullObject.uom,
          uom_label: item.fullObject.uom_label,
          conversion_factor: item.fullObject.conversion_factor,
          exp_quantity: 0,
          bom: item.fullObject.bom || [],
          uoms: item.fullObject.uoms
        };
      }
    }).filter(Boolean);
    setTaskItemUsed(toConsumableArray_default()(selectedItems));
  };
  var handleOk = function handleOk() {
    setOpen(false);
  };
  var handleCancel = function handleCancel() {
    hideModal();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
      type: "primary",
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      onClick: showModal,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), " ", intl.formatMessage({
        id: 'common.add_material'
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: intl.formatMessage({
        id: 'common.add_material'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      width: 800,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 5,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            labelCol: {
              span: 24
            },
            name: "categories",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(CategoryQuantitySelector/* default */.Z, {
              treeData: treeData || [],
              onCheck: handleSelectChange
            })
          })
        })
      })
    })]
  });
};
/* harmony default export */ var CreateItemCreateView = (CreateItem);
;// CONCATENATED MODULE: ./src/components/Task/TaskItemUsed/ItemUsedTableCreateView.tsx












/**\r
 * GIAO DI\u1EC6N T\u1EA0O DANH S\xC1CH V\u1EACT T\u01AF LI\xCAN QUAN TRONG GIAO DI\u1EC6N T\u1EA0O TASK\r
 */



var TaskItemTable = function TaskItemTable() {
  var _useTaskItemUsedCreat = (0,TaskItemUsedCreateStore/* useTaskItemUsedCreateStore */.W)(),
    taskItemUsed = _useTaskItemUsedCreat.taskItemUsed,
    setTaskItemUsed = _useTaskItemUsedCreat.setTaskItemUsed;
  var _useState = (0,react.useState)([]),
    _useState2 = slicedToArray_default()(_useState, 2),
    editableKeys = _useState2[0],
    setEditableRowKeys = _useState2[1];
  var _useState3 = (0,react.useState)(0),
    _useState4 = slicedToArray_default()(_useState3, 2),
    version = _useState4[0],
    setVersion = _useState4[1]; // State variable to force re-render
  var actionRef = (0,react.useRef)();
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material_management.category_code"
    }),
    dataIndex: 'iot_category_id',
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: entity === null || entity === void 0 ? void 0 : entity.item_name
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "category.material_management.category_name"
    }),
    dataIndex: 'label',
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: entity === null || entity === void 0 ? void 0 : entity.label
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "storage_management.category_management.expected_quantity"
    }),
    dataIndex: 'exp_quantity',
    valueType: 'digit'
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_label',
    editable: false
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.action"
    }),
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        danger: true,
        size: "small",
        style: {
          display: 'flex',
          alignItems: 'center'
        },
        onClick: function onClick() {
          return handlerRemove(entity.iot_category_id);
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {})
      });
    }
  }];
  (0,useDebounceEffect/* default */.Z)(function () {
    setEditableRowKeys(taskItemUsed.map(function (item) {
      return item.iot_category_id;
    }));
    setVersion(function (prev) {
      return prev + 1;
    }); // Force re-render by updating version
  }, [taskItemUsed]);
  var handlerRemove = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(id) {
      var updatedDataSource;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            try {
              updatedDataSource = taskItemUsed.filter(function (item) {
                return item.iot_category_id !== id;
              });
              setTaskItemUsed(updatedDataSource);
            } catch (error) {
              console.log(error);
            }
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlerRemove(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
      // Use version to force re-render
      actionRef: actionRef,
      headerTitle: intl.formatMessage({
        id: 'seasonalTab.suppliesList'
      }),
      columns: columns,
      rowKey: "iot_category_id",
      dataSource: taskItemUsed,
      value: taskItemUsed,
      editable: {
        type: 'multiple',
        editableKeys: editableKeys,
        actionRender: function actionRender(row, config, defaultDoms) {
          return [defaultDoms["delete"]];
        },
        onValuesChange: function onValuesChange(record, recordList) {
          setTaskItemUsed(recordList);
          setEditableRowKeys(recordList.map(function (item) {
            return item.iot_category_id;
          }));
        },
        onChange: function onChange(key) {
          setEditableRowKeys(key);
        }
      },
      pagination: {
        defaultPageSize: 100,
        pageSizeOptions: ['20', '50', '100']
      },
      options: {
        setting: {
          listsHeight: 400
        }
      },
      recordCreatorProps: false,
      search: false
    }, version)
  });
};
var TaskManagement = function TaskManagement() {
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("h2", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(CreateItemCreateView, {})]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(TaskItemTable, {})]
  });
};
/* harmony default export */ var ItemUsedTableCreateView = (TaskManagement);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///60325
`)},1316:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ ProductionTableCreateView; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/stores/TaskProductionCreateStore.tsx
var TaskProductionCreateStore = __webpack_require__(55059);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js + 1 modules
var EditableTable = __webpack_require__(88280);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/ahooks/es/useDebounceEffect/index.js
var useDebounceEffect = __webpack_require__(8527);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js
var createForOfIteratorHelper = __webpack_require__(64599);
var createForOfIteratorHelper_default = /*#__PURE__*/__webpack_require__.n(createForOfIteratorHelper);
// EXTERNAL MODULE: ./src/services/InventoryManagementV3/product-item.ts
var product_item = __webpack_require__(58642);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./src/components/Task/TaskProductionNew/ProductionQuantitySelector.tsx + 1 modules
var ProductionQuantitySelector = __webpack_require__(90956);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/TaskProductionNew/CreateProductionCreateView.tsx















var Item = es_form/* default */.Z.Item;
var CreateProductionCreateView = function CreateProductionCreateView() {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _useState5 = (0,react.useState)([]),
    _useState6 = slicedToArray_default()(_useState5, 2),
    treeData = _useState6[0],
    setTreeData = _useState6[1];
  var _useTaskProductionCre = (0,TaskProductionCreateStore/* useTaskProductionCreateStore */.N)(),
    setTaskProduction = _useTaskProductionCre.setTaskProduction;
  var intl = (0,_umi_production_exports.useIntl)();
  (0,react.useEffect)(function () {
    var fetchData = /*#__PURE__*/function () {
      var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
        var dataGroup;
        return regeneratorRuntime_default()().wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return (0,product_item/* getItemByGroup */.Kd)({});
            case 2:
              dataGroup = _context.sent;
              if (dataGroup.data) {
                setTreeData(dataGroup.data.map(function (item) {
                  return {
                    title: item.item_group_label,
                    value: item.item_group,
                    key: item.item_group,
                    normalized_title: item.item_group_label.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''),
                    children: item.item.map(function (child) {
                      return {
                        title: child.label || '',
                        value: child.name || '',
                        key: child.name || '',
                        normalized_title: (child.label || '').toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, ''),
                        fullObject: child
                      };
                    })
                  };
                }));
              }
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }));
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    fetchData();
  }, []);
  var showModal = function showModal() {
    return setOpen(true);
  };
  var hideModal = function hideModal() {
    return setOpen(false);
  };
  var handleOk = function handleOk() {
    return setOpen(false);
  };
  var findNodeByKey = function findNodeByKey(key, data) {
    var _iterator = createForOfIteratorHelper_default()(data),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var node = _step.value;
        if (node.key === key) return node;
        if (node.children) {
          var found = findNodeByKey(key, node.children);
          if (found) return found;
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  };
  var handleSelectChange = function handleSelectChange(selectedWithBOM, selectedWithoutBOM) {
    var combinedSelected = [].concat(toConsumableArray_default()(selectedWithBOM), toConsumableArray_default()(selectedWithoutBOM));
    var selectedItems = combinedSelected.map(function (key) {
      var item = findNodeByKey(key.toString(), treeData);
      if (item && !item.children) {
        return {
          product_id: key.toString(),
          item_name: item.fullObject.item_name,
          label: item.title,
          uom: item.fullObject.uom,
          uom_label: item.fullObject.uom_label,
          conversion_factor: item.fullObject.conversion_factor,
          exp_quantity: 0,
          bom: item.fullObject.bom || [],
          uoms: item.fullObject.uoms
        };
      }
    }).filter(Boolean);
    setTaskProduction(toConsumableArray_default()(selectedItems));
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
      type: "primary",
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      onClick: showModal,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), intl.formatMessage({
        id: 'common.add_production'
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      width: 800,
      title: intl.formatMessage({
        id: 'common.add_production'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: hideModal,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(row/* default */.Z, {
        gutter: 5,
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
          className: "gutter-row",
          md: 24,
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
            labelCol: {
              span: 24
            },
            name: "products",
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProductionQuantitySelector/* default */.Z, {
              treeData: treeData || [],
              onCheck: handleSelectChange
            })
          })
        })
      })
    })]
  });
};
/* harmony default export */ var TaskProductionNew_CreateProductionCreateView = (CreateProductionCreateView);
;// CONCATENATED MODULE: ./src/components/Task/TaskProductionNew/ProductionTableCreateView.tsx














var TaskProductionTable = function TaskProductionTable() {
  var _useTaskProductionCre = (0,TaskProductionCreateStore/* useTaskProductionCreateStore */.N)(),
    taskProduction = _useTaskProductionCre.taskProduction,
    setTaskProduction = _useTaskProductionCre.setTaskProduction;
  var _useState = (0,react.useState)(0),
    _useState2 = slicedToArray_default()(_useState, 2),
    version = _useState2[0],
    setVersion = _useState2[1]; // State variable to force re-render
  var _useState3 = (0,react.useState)([]),
    _useState4 = slicedToArray_default()(_useState3, 2),
    editableKeys = _useState4[0],
    setEditableRowKeys = _useState4[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "production.material_management.production_code"
    }),
    dataIndex: 'product_id',
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: entity === null || entity === void 0 ? void 0 : entity.item_name
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "production.material_management.production_name"
    }),
    dataIndex: 'label',
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: entity === null || entity === void 0 ? void 0 : entity.label
      });
    }
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "production_management.production_management.expected_quantity"
    }),
    dataIndex: 'exp_quantity',
    valueType: 'digit',
    editable: false
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.unit"
    }),
    dataIndex: 'uom_label',
    editable: false
  }, {
    title: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
      id: "common.action"
    }),
    editable: false,
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
        danger: true,
        size: "small",
        style: {
          display: 'flex',
          alignItems: 'center'
        },
        onClick: function onClick() {
          return handlerRemove(entity.product_id);
        },
        children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {})
      });
    }
  }];
  var handlerRemove = /*#__PURE__*/function () {
    var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(id) {
      var updatedTaskProduction;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            try {
              updatedTaskProduction = taskProduction.filter(function (item) {
                return item.product_id !== id;
              });
              setTaskProduction(updatedTaskProduction);
            } catch (error) {
              console.log(error);
            }
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function handlerRemove(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  (0,useDebounceEffect/* default */.Z)(function () {
    console.log('task production', taskProduction);
    setEditableRowKeys(taskProduction.map(function (item) {
      return item.product_id;
    }));
    setVersion(function (prev) {
      return prev + 1;
    }); // Force re-render by updating version
  }, [taskProduction]);
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditableTable/* default */.Z, {
      // Use version to force re-render
      headerTitle: intl.formatMessage({
        id: 'common.production'
      }),
      columns: columns,
      rowKey: "product_id",
      dataSource: taskProduction,
      value: taskProduction,
      editable: {
        type: 'multiple',
        editableKeys: editableKeys,
        actionRender: function actionRender(row, config, defaultDoms) {
          return [defaultDoms["delete"]];
        },
        onValuesChange: function onValuesChange(record, recordList) {
          setTaskProduction(recordList);
        },
        onChange: function onChange(key) {
          setEditableRowKeys(key);
        }
      },
      pagination: {
        defaultPageSize: 20,
        pageSizeOptions: ['20', '50', '100']
      },
      options: {
        setting: {
          listsHeight: 400
        }
      },
      recordCreatorProps: false,
      search: false
    }, version)
  });
};
var TaskProductionManagement = function TaskProductionManagement() {
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      },
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)("h2", {}), /*#__PURE__*/(0,jsx_runtime.jsx)(TaskProductionNew_CreateProductionCreateView, {})]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(TaskProductionTable, {})]
  });
};
/* harmony default export */ var ProductionTableCreateView = (TaskProductionManagement);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///1316
`)},84726:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`
// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  Z: function() { return /* binding */ CreateTodoTableEditer; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/toConsumableArray.js
var toConsumableArray = __webpack_require__(19632);
var toConsumableArray_default = /*#__PURE__*/__webpack_require__.n(toConsumableArray);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/customerUser.ts
var customerUser = __webpack_require__(40063);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js
var DeleteOutlined = __webpack_require__(82061);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-table/es/Table.js + 76 modules
var Table = __webpack_require__(4894);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/uuid/dist/esm-browser/v4.js + 3 modules
var v4 = __webpack_require__(57632);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/PlusOutlined.js
var PlusOutlined = __webpack_require__(51042);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/antd/es/form/index.js + 18 modules
var es_form = __webpack_require__(98138);
// EXTERNAL MODULE: ./node_modules/antd/es/modal/index.js + 1 modules
var modal = __webpack_require__(85576);
// EXTERNAL MODULE: ./node_modules/antd/es/row/index.js
var row = __webpack_require__(71230);
// EXTERNAL MODULE: ./node_modules/antd/es/col/index.js
var col = __webpack_require__(15746);
// EXTERNAL MODULE: ./node_modules/antd/es/input/index.js + 4 modules
var input = __webpack_require__(96365);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/CreateTodo.tsx












var Item = es_form/* default */.Z.Item;
var CreateTodo = function CreateTodo(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var intl = (0,_umi_production_exports.useIntl)();
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var showModal = function showModal() {
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsxs)(es_button/* default */.ZP, {
      type: "primary",
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      onClick: showModal,
      children: [/*#__PURE__*/(0,jsx_runtime.jsx)(PlusOutlined/* default */.Z, {}), " ", /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
        id: "common.add_sub_task"
      })]
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: intl.formatMessage({
        id: 'common.add_sub_task'
      }),
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return params === null || params === void 0 ? void 0 : params.onFinish(values);
                case 2:
                  setOpen(false);
                  form.resetFields();
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 5,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.task_name"
              }),
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: intl.formatMessage({
                  id: 'common.required'
                })
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.executor"
              }),
              showSearch: true,
              name: "customer_user_id",
              request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
                var result;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 2;
                      return (0,customerUser/* getCustomerUserList */.J9)();
                    case 2:
                      result = _context2.sent;
                      return _context2.abrupt("return", result.data.map(function (item) {
                        return {
                          label: item.last_name + ' ' + item.first_name,
                          value: item.name
                        };
                      }));
                    case 4:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }))
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: "common.description"
              }),
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {})
            })
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var TaskTodo_CreateTodo = (CreateTodo);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/EditOutlined.js
var EditOutlined = __webpack_require__(47389);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/UpdateTodo.tsx












var UpdateTodo_Item = es_form/* default */.Z.Item;
var UpdateTodo = function UpdateTodo(params) {
  var _useState = (0,react.useState)(false),
    _useState2 = slicedToArray_default()(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(false),
    _useState4 = slicedToArray_default()(_useState3, 2),
    isOpen = _useState4[0],
    setOpen = _useState4[1];
  var _Form$useForm = es_form/* default */.Z.useForm(),
    _Form$useForm2 = slicedToArray_default()(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var showModal = function showModal() {
    form.setFieldsValue(params.data);
    setOpen(true);
  };
  var hideModal = function hideModal() {
    setOpen(false);
  };
  var handleOk = function handleOk() {
    form.submit();
  };
  var handleCancel = function handleCancel() {
    hideModal();
    form.resetFields();
  };
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
      size: "small"
      //type="dashed"
      ,
      style: {
        display: 'flex',
        alignItems: 'center'
      },
      onClick: showModal,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(EditOutlined/* default */.Z, {})
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(modal/* default */.Z, {
      title: "Ch\\u1EC9nh s\\u1EEDa c\\xF4ng vi\\u1EC7c con",
      open: isOpen,
      onOk: handleOk,
      onCancel: handleCancel,
      confirmLoading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsx)(es_form/* default */.Z, {
        layout: "horizontal",
        labelCol: {
          span: 24
        },
        labelAlign: "left",
        form: form,
        onFinish: ( /*#__PURE__*/function () {
          var _ref = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
            var _params$data;
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  values.name = (_params$data = params.data) === null || _params$data === void 0 ? void 0 : _params$data.name;
                  _context.next = 3;
                  return params === null || params === void 0 ? void 0 : params.onFinish(values);
                case 3:
                  setOpen(false);
                  form.resetFields();
                case 5:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }()),
        children: /*#__PURE__*/(0,jsx_runtime.jsxs)(row/* default */.Z, {
          gutter: 5,
          children: [/*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateTodo_Item, {
              label: "T\\xEAn c\\xF4ng vi\\u1EC7c",
              labelCol: {
                span: 24
              },
              rules: [{
                required: true,
                message: 'B\u1EAFt bu\u1ED9c \u0111i\u1EC1n'
              }],
              name: "label",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z, {})
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
              label: 'Ng\u01B0\u1EDDi th\u1EF1c hi\u1EC7n',
              showSearch: true,
              name: "customer_user_id",
              request: /*#__PURE__*/asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2() {
                var result;
                return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      _context2.next = 2;
                      return (0,customerUser/* getCustomerUserList */.J9)();
                    case 2:
                      result = _context2.sent;
                      return _context2.abrupt("return", result.data.map(function (item) {
                        return {
                          label: item.last_name + ' ' + item.first_name,
                          value: item.name
                        };
                      }));
                    case 4:
                    case "end":
                      return _context2.stop();
                  }
                }, _callee2);
              }))
            })
          }), /*#__PURE__*/(0,jsx_runtime.jsx)(col/* default */.Z, {
            className: "gutter-row",
            md: 24,
            children: /*#__PURE__*/(0,jsx_runtime.jsx)(UpdateTodo_Item, {
              label: /*#__PURE__*/(0,jsx_runtime.jsx)(_umi_production_exports.FormattedMessage, {
                id: 'common.form.description'
              }),
              labelCol: {
                span: 24
              },
              name: "description",
              children: /*#__PURE__*/(0,jsx_runtime.jsx)(input/* default */.Z.TextArea, {})
            })
          })]
        })
      })
    })]
  });
};
/* harmony default export */ var TaskTodo_UpdateTodo = (UpdateTodo);
;// CONCATENATED MODULE: ./src/components/Task/TaskTodo/CreateTodoTableEditer.tsx















/* harmony default export */ var CreateTodoTableEditer = (function (_ref) {
  var dataSource = _ref.dataSource,
    setDataSource = _ref.setDataSource,
    customerUserOptions = _ref.customerUserOptions;
  var intl = (0,_umi_production_exports.useIntl)();
  var columns = [{
    title: intl.formatMessage({
      id: 'common.label'
    }),
    dataIndex: 'label'
  }, {
    title: intl.formatMessage({
      id: 'common.executor'
    }),
    // 'Ng\u01B0\u1EDDi th\u1EF1c hi\u1EC7n'
    dataIndex: 'customer_user_id',
    width: 250,
    render: function render(dom, entity) {
      var customerUserName = entity.customer_user_name || "".concat(entity.user_last_name, " ").concat(entity.user_first_name);
      return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
        children: customerUserName
      });
    }
  }, {
    title: intl.formatMessage({
      id: 'common.description'
    }),
    // 'M\xF4 t\u1EA3'
    dataIndex: 'description',
    formItemProps: {
      rules: [{
        required: true,
        whitespace: true,
        message: intl.formatMessage({
          id: 'common.description_required'
        }) // 'M\xF4 t\u1EA3 kh\xF4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng'
      }]
    }
  }, {
    title: intl.formatMessage({
      id: 'common.action'
    }),
    // 'H\xE0nh \u0111\u1ED9ng'
    dataIndex: 'name',
    render: function render(dom, entity) {
      return /*#__PURE__*/(0,jsx_runtime.jsxs)(jsx_runtime.Fragment, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodo_UpdateTodo, {
          data: entity,
          onFinish: handlerEdit
        }), " ", '  ', /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
          danger: true,
          size: "small",
          style: {
            display: 'flex',
            alignItems: 'center'
          },
          onClick: function onClick() {
            return handlerRemove(dom);
          },
          children: /*#__PURE__*/(0,jsx_runtime.jsx)(DeleteOutlined/* default */.Z, {})
        })]
      });
    }
  }];
  var handlerAddTodo = /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(values) {
      var userInfo, _newData;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.prev = 0;
            values.name = (0,v4/* default */.Z)();
            _context.next = 4;
            return (0,customerUser/* getCustomerUserList */.J9)({
              filters: [['iot_customer_user', 'name', 'like', values.customer_user_id]]
            });
          case 4:
            userInfo = _context.sent;
            values.user_first_name = userInfo.data[0].first_name;
            values.user_last_name = userInfo.data[0].last_name;
            values.customer_user_id = userInfo.data[0].name;
            _newData = dataSource;
            _newData.push(values);
            setDataSource(toConsumableArray_default()(_newData));
            _context.next = 16;
            break;
          case 13:
            _context.prev = 13;
            _context.t0 = _context["catch"](0);
            console.log(_context.t0);
          case 16:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[0, 13]]);
    }));
    return function handlerAddTodo(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var handlerEdit = /*#__PURE__*/function () {
    var _ref3 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee2(values) {
      var userInfo, _newData;
      return regeneratorRuntime_default()().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 3;
            return (0,customerUser/* getCustomerUserList */.J9)({
              filters: [['iot_customer_user', 'name', 'like', values.customer_user_id]]
            });
          case 3:
            userInfo = _context2.sent;
            values.user_first_name = userInfo.data[0].first_name;
            values.user_last_name = userInfo.data[0].last_name;
            values.customer_user_id = userInfo.data[0].name;
            _newData = dataSource.map(function (d) {
              return d.name === values.name ? values : d;
            });
            setDataSource(toConsumableArray_default()(_newData));
            _context2.next = 14;
            break;
          case 11:
            _context2.prev = 11;
            _context2.t0 = _context2["catch"](0);
            console.log(_context2.t0);
          case 14:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 11]]);
    }));
    return function handlerEdit(_x2) {
      return _ref3.apply(this, arguments);
    };
  }();
  var handlerRemove = /*#__PURE__*/function () {
    var _ref4 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee3(name) {
      var _newData;
      return regeneratorRuntime_default()().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            try {
              _newData = dataSource.filter(function (d) {
                return d.name !== name;
              });
              setDataSource(toConsumableArray_default()(_newData));
            } catch (error) {
              console.log(error);
            }
          case 1:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function handlerRemove(_x3) {
      return _ref4.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/(0,jsx_runtime.jsx)(jsx_runtime.Fragment, {
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(Table/* default */.Z, {
      headerTitle: intl.formatMessage({
        id: 'common.sub_task'
      }) // 'C\xF4ng vi\u1EC7c con'
      ,
      columns: columns,
      rowKey: "name",
      dataSource: toConsumableArray_default()(dataSource),
      toolBarRender: function toolBarRender() {
        return [/*#__PURE__*/(0,jsx_runtime.jsx)(TaskTodo_CreateTodo, {
          onFinish: handlerAddTodo
        })];
      },
      search: false
    })
  });
});//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///84726
`)},77890:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ak: function() { return /* binding */ updateCrop; },
/* harmony export */   Gz: function() { return /* binding */ getCropManagementInfoList; },
/* harmony export */   Ir: function() { return /* binding */ deleteCropNote; },
/* harmony export */   JB: function() { return /* binding */ addParticipantInCrop; },
/* harmony export */   LY: function() { return /* binding */ getTemplateCropList; },
/* harmony export */   No: function() { return /* binding */ getParticipantsInCrop; },
/* harmony export */   TQ: function() { return /* binding */ getCropList; },
/* harmony export */   Tq: function() { return /* binding */ deleteParticipantsInCrop; },
/* harmony export */   WP: function() { return /* binding */ getStatisticNoteList; },
/* harmony export */   bx: function() { return /* binding */ updateCropNote; },
/* harmony export */   mP: function() { return /* binding */ createCrop; },
/* harmony export */   rC: function() { return /* binding */ createCropNote; },
/* harmony export */   vW: function() { return /* binding */ getCurrentStateOfCrop; },
/* harmony export */   xu: function() { return /* binding */ getCropNoteList; }
/* harmony export */ });
/* unused harmony exports updateParticipantsInCrop, getStatisticPestList */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(467);




var getCropList = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getCropList(_x) {
    return _ref.apply(this, arguments);
  };
}();
var getTemplateCropList = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee2(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop/template'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function getTemplateCropList(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var getCropManagementInfoList = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee3(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-management-info'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function getCropManagementInfoList(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var getCurrentStateOfCrop = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee4(cropId) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop-current-state'), {
            method: 'GET',
            params: {
              crop_id: cropId
            }
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res.result);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function getCurrentStateOfCrop(_x4) {
    return _ref4.apply(this, arguments);
  };
}();
var createCrop = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee5(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _context5.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context5.sent;
          return _context5.abrupt("return", res.result);
        case 4:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function createCrop(_x5) {
    return _ref5.apply(this, arguments);
  };
}();
var updateCrop = /*#__PURE__*/function () {
  var _ref6 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res.result);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function updateCrop(_x6) {
    return _ref6.apply(this, arguments);
  };
}();
var getCropNoteList = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee7(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res.result);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function getCropNoteList(_x7) {
    return _ref7.apply(this, arguments);
  };
}();
var createCropNote = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee8(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res.result);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function createCropNote(_x8) {
    return _ref8.apply(this, arguments);
  };
}();
var updateCropNote = /*#__PURE__*/function () {
  var _ref9 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee9(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          _context9.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/note'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context9.sent;
          return _context9.abrupt("return", res.result);
        case 4:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function updateCropNote(_x9) {
    return _ref9.apply(this, arguments);
  };
}();
var deleteCropNote = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee10(_ref10) {
    var name, res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          name = _ref10.name;
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/note?name=".concat(name)), {
            method: 'DELETE'
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", res.result);
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function deleteCropNote(_x10) {
    return _ref11.apply(this, arguments);
  };
}();
// Participants
var getParticipantsInCrop = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee11(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res.result);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function getParticipantsInCrop(_x11) {
    return _ref12.apply(this, arguments);
  };
}();
var addParticipantInCrop = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function addParticipantInCrop(_x12) {
    return _ref13.apply(this, arguments);
  };
}();
var updateParticipantsInCrop = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref14 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee13(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return request(generateAPIPath('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function updateParticipantsInCrop(_x13) {
    return _ref14.apply(this, arguments);
  };
}()));
var deleteParticipantsInCrop = /*#__PURE__*/function () {
  var _ref15 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee14(name) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)('api/v2/cropManage/iot_employee_in_crop'), {
            method: 'DELETE',
            params: {
              name: name
            }
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function deleteParticipantsInCrop(_x14) {
    return _ref15.apply(this, arguments);
  };
}();
var getStatisticPestList = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref16 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee15(params) {
    var res;
    return _regeneratorRuntime().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          _context15.next = 2;
          return request(generateAPIPath("api/v2/cropManage/statisticPestList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: getParamsReqList(params)
          });
        case 2:
          res = _context15.sent;
          return _context15.abrupt("return", res.result);
        case 4:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getStatisticPestList(_x15) {
    return _ref16.apply(this, arguments);
  };
}()));
var getStatisticNoteList = /*#__PURE__*/function () {
  var _ref17 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee16(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          _context16.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_2__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .generateAPIPath */ .rH)("api/v2/cropManage/statisticNoteList?crop_id=".concat(params === null || params === void 0 ? void 0 : params.cropId)), {
            method: 'GET',
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_3__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context16.sent;
          return _context16.abrupt("return", res.result);
        case 4:
        case "end":
          return _context16.stop();
      }
    }, _callee16);
  }));
  return function getStatisticNoteList(_x16) {
    return _ref17.apply(this, arguments);
  };
}();//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///77890
`)}}]);
