"use strict";(self.webpackChunkant_design_pro=self.webpackChunkant_design_pro||[]).push([[1145],{29648:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* binding */ Details; }
});

// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/regeneratorRuntime.js
var regeneratorRuntime = __webpack_require__(15009);
var regeneratorRuntime_default = /*#__PURE__*/__webpack_require__.n(regeneratorRuntime);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(99289);
var asyncToGenerator_default = /*#__PURE__*/__webpack_require__.n(asyncToGenerator);
// EXTERNAL MODULE: ./src/services/approval.ts
var approval = __webpack_require__(34082);
// EXTERNAL MODULE: ./src/.umi-production/exports.ts + 34 modules
var _umi_production_exports = __webpack_require__(7837);
// EXTERNAL MODULE: ./node_modules/antd/es/app/index.js + 1 modules
var app = __webpack_require__(31418);
;// CONCATENATED MODULE: ./src/hook/timesheetV2/approval-management/useApprovalDetails.ts





function useApprovalDetails() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)( /*#__PURE__*/function () {
    var _ref2 = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee(id) {
      var _res$data;
      var res, data;
      return regeneratorRuntime_default()().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return (0,approval/* getTimesheetApproval */.eH)({
              page: 1,
              size: 1,
              name: id
              // filters: [[DOCTYPE_ERP.iotTimesheetApproval, 'name', '=', id]],
            });
          case 2:
            res = _context.sent;
            data = (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data[0];
            if (data) {
              _context.next = 6;
              break;
            }
            throw new Error();
          case 6:
            return _context.abrupt("return", {
              data: data
            });
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), {
    manual: true,
    onError: function onError(err) {
      message.error(err.message);
    },
    onSuccess: function onSuccess(data) {
      // message.success(
      //   formatMessage({
      //     id: 'common.success',
      //   }),
      // );
      _onSuccess === null || _onSuccess === void 0 || _onSuccess(data);
    }
  });
}
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js
var Descriptions = __webpack_require__(44688);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-layout/es/components/PageContainer/index.js + 18 modules
var PageContainer = __webpack_require__(6110);
// EXTERNAL MODULE: ./node_modules/antd/es/result/index.js + 6 modules
var result = __webpack_require__(29905);
// EXTERNAL MODULE: ./node_modules/antd/es/divider/index.js + 1 modules
var divider = __webpack_require__(96074);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(67294);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectSpread2.js
var objectSpread2 = __webpack_require__(97857);
var objectSpread2_default = /*#__PURE__*/__webpack_require__.n(objectSpread2);
// EXTERNAL MODULE: ./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/slicedToArray.js
var slicedToArray = __webpack_require__(5574);
var slicedToArray_default = /*#__PURE__*/__webpack_require__.n(slicedToArray);
// EXTERNAL MODULE: ./src/common/contanst/constanst.ts
var constanst = __webpack_require__(86604);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/ProForm/index.js + 3 modules
var ProForm = __webpack_require__(34994);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/layouts/index.js
var layouts = __webpack_require__(24739);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Text/index.js
var Text = __webpack_require__(5966);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/DateTimePicker/index.js
var DateTimePicker = __webpack_require__(22452);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/Select/index.js
var Select = __webpack_require__(64317);
// EXTERNAL MODULE: ./node_modules/@ant-design/pro-form/es/components/TextArea/index.js
var TextArea = __webpack_require__(90672);
// EXTERNAL MODULE: ./node_modules/antd/es/alert/index.js + 3 modules
var es_alert = __webpack_require__(38925);
// EXTERNAL MODULE: ./node_modules/antd/es/card/index.js + 3 modules
var card = __webpack_require__(76216);
// EXTERNAL MODULE: ./node_modules/antd/es/space/index.js + 2 modules
var space = __webpack_require__(42075);
// EXTERNAL MODULE: ./src/services/timesheetsV2.ts
var timesheetsV2 = __webpack_require__(62872);
;// CONCATENATED MODULE: ./src/hook/timesheetV2/approval-management/useUpdateApproval.ts



function useUpdateApproval() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
    _onSuccess = _ref.onSuccess;
  var _App$useApp = app/* default */.Z.useApp(),
    message = _App$useApp.message;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  return (0,_umi_production_exports.useRequest)(timesheetsV2/* editTimeSheetApprove */.hs, {
    manual: true,
    onError: function onError(err) {
      message.error(err.message);
    },
    onSuccess: function onSuccess() {
      message.success(formatMessage({
        id: 'common.success'
      }));
      _onSuccess === null || _onSuccess === void 0 || _onSuccess();
    }
  });
}
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/CheckOutlined.js
var CheckOutlined = __webpack_require__(88284);
// EXTERNAL MODULE: ./node_modules/antd/es/button/index.js + 9 modules
var es_button = __webpack_require__(14726);
// EXTERNAL MODULE: ./node_modules/dayjs/dayjs.min.js
var dayjs_min = __webpack_require__(27484);
var dayjs_min_default = /*#__PURE__*/__webpack_require__.n(dayjs_min);
// EXTERNAL MODULE: ./node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(85893);
;// CONCATENATED MODULE: ./src/pages/MyUser/Approval/Details/Accept.tsx









var Accept = function Accept(_ref) {
  var id = _ref.id,
    onSuccess = _ref.onSuccess,
    comment = _ref.comment;
  (0,react.useEffect)(function () {
    console.log('comment: ', comment);
  }, [comment]);
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useUpdateApproval = useUpdateApproval(),
    run = _useUpdateApproval.run;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    type: "primary",
    onClick: function onClick() {
      modal.confirm({
        okButtonProps: {
          // danger: true,
        },
        title: "".concat(formatMessage({
          id: 'common.confirm'
        }), " ").concat(formatMessage({
          id: 'common.approval'
        })),
        onOk: function () {
          var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return run({
                    name: id,
                    approval_date: dayjs_min_default()().toISOString(),
                    approval_status: 'Approved',
                    comment: comment
                  });
                case 2:
                  onSuccess === null || onSuccess === void 0 || onSuccess();
                  return _context.abrupt("return", true);
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          function onOk() {
            return _onOk.apply(this, arguments);
          }
          return onOk;
        }()
      });
    },
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(CheckOutlined/* default */.Z, {}),
    children: formatMessage({
      id: 'common.accept'
    })
  });
};
/* harmony default export */ var Details_Accept = (Accept);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/StopOutlined.js + 1 modules
var StopOutlined = __webpack_require__(87784);
;// CONCATENATED MODULE: ./src/pages/MyUser/Approval/Details/Refuse.tsx








var Refuse = function Refuse(_ref) {
  var id = _ref.id,
    onSuccess = _ref.onSuccess,
    comment = _ref.comment;
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _useUpdateApproval = useUpdateApproval(),
    run = _useUpdateApproval.run;
  var _App$useApp = app/* default */.Z.useApp(),
    modal = _App$useApp.modal;
  return /*#__PURE__*/(0,jsx_runtime.jsx)(es_button/* default */.ZP, {
    danger: true,
    type: "primary",
    onClick: function onClick() {
      modal.confirm({
        okButtonProps: {
          danger: true
        },
        title: "".concat(formatMessage({
          id: 'common.confirm'
        }), " ").concat(formatMessage({
          id: 'common.refuse'
        })),
        onOk: function () {
          var _onOk = asyncToGenerator_default()( /*#__PURE__*/regeneratorRuntime_default()().mark(function _callee() {
            return regeneratorRuntime_default()().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return run({
                    name: id,
                    approval_date: dayjs_min_default()().toISOString(),
                    approval_status: 'Deny',
                    comment: comment
                  });
                case 2:
                  onSuccess === null || onSuccess === void 0 || onSuccess();
                  return _context.abrupt("return", true);
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          function onOk() {
            return _onOk.apply(this, arguments);
          }
          return onOk;
        }()
      });
    },
    icon: /*#__PURE__*/(0,jsx_runtime.jsx)(StopOutlined/* default */.Z, {}),
    children: formatMessage({
      id: 'common.refuse'
    })
  });
};
/* harmony default export */ var Details_Refuse = (Refuse);
;// CONCATENATED MODULE: ./src/pages/MyUser/Approval/Details/Info.tsx












var Info = function Info(_ref) {
  var id = _ref.id,
    reload = _ref.reload;
  console.log('id: ', id);
  var _useState = (0,react.useState)(''),
    _useState2 = slicedToArray_default()(_useState, 2),
    comment = _useState2[0],
    setComment = _useState2[1];
  var _useIntl = (0,_umi_production_exports.useIntl)(),
    formatMessage = _useIntl.formatMessage;
  var _ProForm$useForm = ProForm/* ProForm */.A.useForm(),
    _ProForm$useForm2 = slicedToArray_default()(_ProForm$useForm, 1),
    form = _ProForm$useForm2[0];
  var _useApprovalDetails = useApprovalDetails({
      onSuccess: function onSuccess(data) {
        form.setFieldsValue(objectSpread2_default()(objectSpread2_default()({}, data), {}, {
          request_user: "".concat(data.first_name, " ").concat(data.last_name, " - ").concat(data.email)
        }));
      }
    }),
    run = _useApprovalDetails.run,
    loading = _useApprovalDetails.loading,
    data = _useApprovalDetails.data;
  (0,react.useEffect)(function () {
    if (id) {
      run(id);
    }
  }, [id]);
  var isDisabled = true;
  var extra = [];
  if ((data === null || data === void 0 ? void 0 : data.approval_status) === 'Processing') {
    extra.push.apply(extra, [/*#__PURE__*/(0,jsx_runtime.jsx)(Details_Accept, {
      onSuccess: reload,
      id: id,
      comment: comment
    }, "accept"), /*#__PURE__*/(0,jsx_runtime.jsx)(Details_Refuse, {
      onSuccess: reload,
      id: id,
      comment: comment
    }, "Refuse")]);
  }
  if ((data === null || data === void 0 ? void 0 : data.approval_status) === 'Approved') {
    extra.push( /*#__PURE__*/(0,jsx_runtime.jsx)(es_alert/* default */.Z, {
      message: formatMessage({
        id: 'common.approved'
      }),
      type: "success",
      showIcon: true
    }, 'success'));
  }
  if ((data === null || data === void 0 ? void 0 : data.approval_status) === 'Deny') {
    extra.push( /*#__PURE__*/(0,jsx_runtime.jsx)(es_alert/* default */.Z, {
      message: formatMessage({
        id: 'common.refused'
      }),
      type: "error",
      showIcon: true
    }, "Refused"));
  }
  var isCommentDisabled = (data === null || data === void 0 ? void 0 : data.approval_status) !== 'Processing';
  return /*#__PURE__*/(0,jsx_runtime.jsx)(card/* default */.Z, {
    title: formatMessage({
      id: 'common.info'
    }),
    extra: /*#__PURE__*/(0,jsx_runtime.jsx)(space/* default */.Z, {
      children: extra
    }),
    children: /*#__PURE__*/(0,jsx_runtime.jsx)(ProForm/* ProForm */.A, {
      form: form,
      submitter: false,
      loading: loading,
      children: /*#__PURE__*/(0,jsx_runtime.jsxs)(layouts/* ProFormGroup */.UW, {
        children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          width: 'lg',
          disabled: isDisabled,
          name: "timesheet_label",
          label: formatMessage({
            id: 'common.name'
          })
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(Text/* default */.Z, {
          width: 'lg',
          disabled: isDisabled,
          label: formatMessage({
            id: 'common.request_user'
          }),
          name: "request_user"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
          width: 'lg',
          disabled: isDisabled,
          label: formatMessage({
            id: 'common.request_date'
          }),
          name: "request_date",
          fieldProps: {
            format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
          }
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(Select/* default */.Z, {
          width: 'lg',
          disabled: isDisabled,
          label: formatMessage({
            id: 'common.approval_status'
          }),
          name: "approval_status"
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(DateTimePicker/* default */.Z, {
          width: 'lg',
          disabled: isDisabled,
          label: formatMessage({
            id: 'common.approval_date'
          }),
          name: "approval_date",
          fieldProps: {
            format: constanst/* DEFAULT_DATE_FORMAT_WITHOUT_TIME */.ug
          }
        }), /*#__PURE__*/(0,jsx_runtime.jsx)(TextArea/* default */.Z, {
          disabled: isCommentDisabled,
          width: 'lg',
          fieldProps: {
            onChange: function onChange(v) {
              setComment(v.currentTarget.value);
            }
          },
          label: formatMessage({
            id: 'common.comment'
          }),
          name: "comment"
        })]
      })
    })
  });
};
/* harmony default export */ var Details_Info = (Info);
// EXTERNAL MODULE: ./src/pages/MyUser/TimekeepingV2/components/DetailTimesheet/TimeSheetTask/TimesheetTaskTable.tsx + 5 modules
var TimesheetTaskTable = __webpack_require__(47821);
;// CONCATENATED MODULE: ./src/pages/MyUser/Approval/Details/index.tsx









var Index = function Index() {
  var _useParams = (0,_umi_production_exports.useParams)(),
    id = _useParams.id;
  var _useApprovalDetails = useApprovalDetails(),
    run = _useApprovalDetails.run,
    data = _useApprovalDetails.data,
    loading = _useApprovalDetails.loading;
  // const extra = [];
  // if (data?.approval_status === 'Processing') {
  //   extra.push(...[<Accept key="accept" />, <Refuse key="Refuse" />]);
  // }
  // console.log('data: ', data);
  var reload = (0,react.useCallback)(function () {
    if (id) run(id);
  }, [id, run]);
  (0,react.useEffect)(function () {
    if (id) {
      run(id);
    }
  }, [id]);
  if (loading) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(Descriptions/* DescriptionsSkeleton */.Yk, {
      active: true
    });
  }
  if (!data) {
    return /*#__PURE__*/(0,jsx_runtime.jsx)(result/* default */.ZP, {
      title: "404",
      subTitle: "Not Found"
    });
  }
  return /*#__PURE__*/(0,jsx_runtime.jsxs)(PageContainer/* PageContainer */._z
  // fixedHeader
  // extra={extra}
  , {
    children: [/*#__PURE__*/(0,jsx_runtime.jsx)(Details_Info, {
      reload: reload,
      id: data.name
      // fullName={\`\${data.first_name} \${data.last_name} - \${data.email}\`}
      // timesheetLabel={data.timesheet_label}
      // requestDate={data.request_date}
    }), /*#__PURE__*/(0,jsx_runtime.jsx)(divider/* default */.Z, {}), /*#__PURE__*/(0,jsx_runtime.jsx)(TimesheetTaskTable/* default */.Z, {
      readonly: true,
      approvalId: id,
      timeSheetId: data.timesheet_id
    })]
  });
};
/* harmony default export */ var Details = (Index);//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///29648
`)},34082:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   eH: function() { return /* binding */ getTimesheetApproval; }
/* harmony export */ });
/* unused harmony exports IIotTimesheetApproval, updateTimesheetApproval, createTimesheetApproval */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(72004);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_createClass_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12444);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_classCallCheck_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9783);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_defineProperty_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(7837);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);








/**\r
 * Generate by D:\\WORK\\PYROJECT\\VIIS\\iot-backend-typescript\\tools\\gen_type.js\r
 */
var IIotTimesheetApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (_createClass(function IIotTimesheetApproval() {
  _classCallCheck(this, IIotTimesheetApproval);
  _defineProperty(this, "name", void 0);
  _defineProperty(this, "timesheet_id", void 0);
  // Link
  _defineProperty(this, "approver_id", void 0);
  // Link
  _defineProperty(this, "request_date", void 0);
  // Date
  _defineProperty(this, "approval_date", void 0);
  // Date
  _defineProperty(this, "approval_status", void 0);
  // Approved|Processing|Deny
  _defineProperty(this, "comment", void 0);
  // Text
  _defineProperty(this, "customer_id", void 0);
} // Link
)));
var getTimesheetApproval = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_0___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_5__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'GET',
            params: params
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTimesheetApproval(_x) {
    return _ref.apply(this, arguments);
  };
}();
var updateTimesheetApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res.result);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function updateTimesheetApproval(_x2) {
    return _ref2.apply(this, arguments);
  };
}()));
var createTimesheetApproval = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res.result);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function createTimesheetApproval(_x3) {
    return _ref3.apply(this, arguments);
  };
}()));//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///34082
`)},62872:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval(`/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Kv: function() { return /* binding */ createTimeSeedTask; },
/* harmony export */   Mr: function() { return /* binding */ deleteTimesheetTask; },
/* harmony export */   OR: function() { return /* binding */ createTimeSheet; },
/* harmony export */   Qf: function() { return /* binding */ getTimeSheetTasks; },
/* harmony export */   ZS: function() { return /* binding */ getTimeSheetTaskRecords; },
/* harmony export */   d7: function() { return /* binding */ getTimeSheetReport; },
/* harmony export */   eQ: function() { return /* binding */ editTimeSheet; },
/* harmony export */   fB: function() { return /* binding */ createTimeSheetApprove; },
/* harmony export */   gr: function() { return /* binding */ getTimeSheetApproves; },
/* harmony export */   hs: function() { return /* binding */ editTimeSheetApprove; },
/* harmony export */   id: function() { return /* binding */ deleteTimesheet; },
/* harmony export */   is: function() { return /* binding */ getTimeSheets; },
/* harmony export */   j1: function() { return /* binding */ getAttendanceV2Report; },
/* harmony export */   v6: function() { return /* binding */ editTimesheet; }
/* harmony export */ });
/* unused harmony exports editTimeSheetApproveForRequest, copyTimeSheetTask */
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(13769);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(97857);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(15009);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99289);
/* harmony import */ var D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _umijs_max__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(7837);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(27484);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(467);




var _excluded = ["start_date", "end_date", "employee_id"];



var getTimeSheets = /*#__PURE__*/function () {
  var _ref = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee(params) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            params: (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)
          });
        case 2:
          res = _context.sent;
          return _context.abrupt("return", res.result);
        case 4:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function getTimeSheets(_x) {
    return _ref.apply(this, arguments);
  };
}();
var createTimeSheet = /*#__PURE__*/function () {
  var _ref2 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee2(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context2.sent;
          return _context2.abrupt("return", res);
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return function createTimeSheet(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
var editTimeSheet = /*#__PURE__*/function () {
  var _ref3 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee3(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_employee_timesheet'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context3.sent;
          return _context3.abrupt("return", res);
        case 4:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  }));
  return function editTimeSheet(_x3) {
    return _ref3.apply(this, arguments);
  };
}();
var deleteTimesheet = /*#__PURE__*/function () {
  var _ref4 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee4(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)("api/v2/timesheet-table-v2/iot_employee_timesheet?name=".concat(id)), {
            method: 'DELETE'
          });
        case 2:
          res = _context4.sent;
          return _context4.abrupt("return", res);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  }));
  return function deleteTimesheet(_x4) {
    return _ref4.apply(this, arguments);
  };
}();

//===========================================

var getTimeSheetApproves = /*#__PURE__*/function () {
  var _ref5 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee5() {
    var _ref6,
      params,
      timesheet_id,
      name,
      res,
      _args5 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          _ref6 = _args5.length > 0 && _args5[0] !== undefined ? _args5[0] : {}, params = _ref6.params, timesheet_id = _ref6.timesheet_id, name = _ref6.name;
          _context5.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              timesheet_id: timesheet_id,
              name: name
            })
          });
        case 3:
          res = _context5.sent;
          console.log('res approval list', res);
          return _context5.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                approval_full_name: "".concat(item.approver_first_name, " ").concat(item.approver_last_name)
              });
            })
          }));
        case 6:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  }));
  return function getTimeSheetApproves() {
    return _ref5.apply(this, arguments);
  };
}();
var createTimeSheetApprove = /*#__PURE__*/function () {
  var _ref7 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee6(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          _context6.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'POST',
            data: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, data), {}, {
              request_date: dayjs__WEBPACK_IMPORTED_MODULE_5___default()().toISOString()
            })
          });
        case 2:
          res = _context6.sent;
          return _context6.abrupt("return", res);
        case 4:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  }));
  return function createTimeSheetApprove(_x5) {
    return _ref7.apply(this, arguments);
  };
}();
var editTimeSheetApprove = /*#__PURE__*/function () {
  var _ref8 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee7(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_approval'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context7.sent;
          return _context7.abrupt("return", res);
        case 4:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  }));
  return function editTimeSheetApprove(_x6) {
    return _ref8.apply(this, arguments);
  };
}();
var editTimeSheetApproveForRequest = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref9 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee8(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          _context8.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/iot_timesheet_approval/request'), {
            method: 'PUT',
            data: data
          });
        case 2:
          res = _context8.sent;
          return _context8.abrupt("return", res);
        case 4:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  }));
  return function editTimeSheetApproveForRequest(_x7) {
    return _ref9.apply(this, arguments);
  };
}()));
//================================================================

var getTimeSheetTasks = /*#__PURE__*/function () {
  var _ref10 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee9() {
    var params,
      res,
      _args9 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee9$(_context9) {
      while (1) switch (_context9.prev = _context9.next) {
        case 0:
          params = _args9.length > 0 && _args9[0] !== undefined ? _args9[0] : {};
          _context9.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context9.sent;
          return _context9.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.timesheet_task_label
              });
            })
          }));
        case 5:
        case "end":
          return _context9.stop();
      }
    }, _callee9);
  }));
  return function getTimeSheetTasks() {
    return _ref10.apply(this, arguments);
  };
}();
var getTimeSheetTaskRecords = /*#__PURE__*/function () {
  var _ref11 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee10() {
    var params,
      res,
      _args10 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee10$(_context10) {
      while (1) switch (_context10.prev = _context10.next) {
        case 0:
          params = _args10.length > 0 && _args10[0] !== undefined ? _args10[0] : {};
          _context10.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task_record'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context10.sent;
          return _context10.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item), {}, {
                label: item.task_record_label
              });
            })
          }));
        case 5:
        case "end":
          return _context10.stop();
      }
    }, _callee10);
  }));
  return function getTimeSheetTaskRecords() {
    return _ref11.apply(this, arguments);
  };
}();
var createTimeSeedTask = /*#__PURE__*/function () {
  var _ref12 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee11(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee11$(_context11) {
      while (1) switch (_context11.prev = _context11.next) {
        case 0:
          _context11.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context11.sent;
          return _context11.abrupt("return", res);
        case 4:
        case "end":
          return _context11.stop();
      }
    }, _callee11);
  }));
  return function createTimeSeedTask(_x8) {
    return _ref12.apply(this, arguments);
  };
}();
var editTimesheet = /*#__PURE__*/function () {
  var _ref13 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee12(data) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee12$(_context12) {
      while (1) switch (_context12.prev = _context12.next) {
        case 0:
          _context12.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context12.sent;
          return _context12.abrupt("return", res);
        case 4:
        case "end":
          return _context12.stop();
      }
    }, _callee12);
  }));
  return function editTimesheet(_x9) {
    return _ref13.apply(this, arguments);
  };
}();
var deleteTimesheetTask = /*#__PURE__*/function () {
  var _ref14 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee13(id) {
    var res;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee13$(_context13) {
      while (1) switch (_context13.prev = _context13.next) {
        case 0:
          _context13.next = 2;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/iot_timesheet_task'), {
            method: 'DELETE',
            params: {
              name: id
            }
          });
        case 2:
          res = _context13.sent;
          return _context13.abrupt("return", res);
        case 4:
        case "end":
          return _context13.stop();
      }
    }, _callee13);
  }));
  return function deleteTimesheetTask(_x10) {
    return _ref14.apply(this, arguments);
  };
}();
var copyTimeSheetTask = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref15 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee14(data) {
    var res;
    return _regeneratorRuntime().wrap(function _callee14$(_context14) {
      while (1) switch (_context14.prev = _context14.next) {
        case 0:
          _context14.next = 2;
          return request(generateAPIPath('api/v2/timesheet-table-v2/copy_iot_timesheet_task'), {
            method: 'POST',
            data: data
          });
        case 2:
          res = _context14.sent;
          return _context14.abrupt("return", res);
        case 4:
        case "end":
          return _context14.stop();
      }
    }, _callee14);
  }));
  return function copyTimeSheetTask(_x11) {
    return _ref15.apply(this, arguments);
  };
}()));
var getTimeSheetReport = /*#__PURE__*/function () {
  var _ref16 = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee15() {
    var params,
      res,
      _args15 = arguments;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee15$(_context15) {
      while (1) switch (_context15.prev = _context15.next) {
        case 0:
          params = _args15.length > 0 && _args15[0] !== undefined ? _args15[0] : {};
          _context15.next = 3;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, params)
          });
        case 3:
          res = _context15.sent;
          return _context15.abrupt("return", D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, res.result), {}, {
            data: res.result.data.map(function (item) {
              return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({}, item);
            })
          }));
        case 5:
        case "end":
          return _context15.stop();
      }
    }, _callee15);
  }));
  return function getTimeSheetReport() {
    return _ref16.apply(this, arguments);
  };
}();
function getAttendanceV2Report(_x12) {
  return _getAttendanceV2Report.apply(this, arguments);
}
function _getAttendanceV2Report() {
  _getAttendanceV2Report = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_3___default()( /*#__PURE__*/D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().mark(function _callee16(_ref17) {
    var start_date, end_date, employee_id, params, result;
    return D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_2___default()().wrap(function _callee16$(_context16) {
      while (1) switch (_context16.prev = _context16.next) {
        case 0:
          start_date = _ref17.start_date, end_date = _ref17.end_date, employee_id = _ref17.employee_id, params = D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_0___default()(_ref17, _excluded);
          _context16.prev = 1;
          _context16.next = 4;
          return (0,_umijs_max__WEBPACK_IMPORTED_MODULE_4__.request)((0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .generateAPIPath */ .rH)('api/v2/timesheet-table-v2/report-no-group'), {
            method: 'GET',
            params: D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()(D_WORK_PYROJECT_VIIS_viis_iot_web_v2_user_web_node_modules_umijs_babel_preset_umi_node_modules_babel_runtime_helpers_objectSpread2_js__WEBPACK_IMPORTED_MODULE_1___default()({
              page: 1,
              size: 10000
            }, (0,_utils__WEBPACK_IMPORTED_MODULE_6__/* .getParamsReqList */ .vj)(params)), {}, {
              start_date: start_date,
              end_date: end_date,
              employee_id: employee_id
            })
          });
        case 4:
          result = _context16.sent;
          return _context16.abrupt("return", {
            data: result.result.data || [],
            pagination: result.result.pagination
          });
        case 8:
          _context16.prev = 8;
          _context16.t0 = _context16["catch"](1);
          return _context16.abrupt("return", {
            data: []
          });
        case 11:
        case "end":
          return _context16.stop();
      }
    }, _callee16, null, [[1, 8]]);
  }));
  return _getAttendanceV2Report.apply(this, arguments);
}//# sourceURL=[module]
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
//# sourceURL=webpack-internal:///62872
`)}}]);
