// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/maintenance","parentId":"ant-design-pro-layout","id":"1"},"2":{"path":"/user","layout":false,"id":"2"},"3":{"name":"Login","path":"/user/login","parentId":"2","id":"3"},"4":{"name":"Forgot Password","path":"/user/forgot-password","parentId":"2","id":"4"},"5":{"name":"Reset New Password","path":"/user/update-password","parentId":"2","id":"5"},"6":{"name":"Reset New Password","path":"/user/update-password/:key","parentId":"2","id":"6"},"7":{"path":"/home","name":"home","icon":"/icons/menu/home-icon.svg","parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/farming-management","name":"farming-management","icon":"/icons/menu/tree-icon.svg","access":"canAccessFarmingManagement","parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/farming-management/dashboard","name":"dashboard","access":"canAccessPageSeasonalManagement","icon":"/icons/menu/sun.svg","parentId":"8","id":"9"},"10":{"path":"/farming-management/seasonal-management","name":"season-management","access":"canAccessPageSeasonalManagement","icon":"/icons/menu/sun.svg","parentId":"8","id":"10"},"11":{"path":"/farming-management/seasonal-management/create","name":"season-management.create","hideInMenu":true,"parentId":"8","id":"11"},"12":{"path":"/farming-management/seasonal-management/detail/:id","name":"season-management.detail","hideInMenu":true,"parentId":"8","id":"12"},"13":{"path":"/farming-management/seasonal-management/detail/:cropId/ticket/:id/history","name":"season-management.detail","hideInMenu":true,"parentId":"8","id":"13"},"14":{"path":"/farming-management/seasonal-management/detail/:cropId/workflow-management/detail/:id","name":"work-management.detail","hideInMenu":true,"parentId":"8","id":"14"},"15":{"path":"/farming-management/seasonal-management/edit/:id","name":"workflow-management.edit","hideInMenu":true,"parentId":"8","id":"15"},"16":{"path":"/farming-management","redirect":"/farming-management/workflow-management","hideInMenu":true,"access":"canAccessPageWorkFlowManagement","parentId":"8","id":"16"},"17":{"path":"/farming-management/workflow-management","name":"work-management","icon":"/icons/menu/bag.svg","parentId":"8","id":"17"},"18":{"path":"/farming-management/workflow-management/create","name":"work-management.create","hideInMenu":true,"parentId":"8","id":"18"},"19":{"path":"/farming-management/workflow-management/detail/:id","name":"work-management.detail","hideInMenu":true,"parentId":"8","id":"19"},"20":{"path":"/farming-management/workflow-management/edit/:id","name":"work-management.edit","hideInMenu":true,"parentId":"8","id":"20"},"21":{"path":"/farming-management/crop-management-plan","name":"Kế hoạch canh tác","hideInMenu":true,"icon":"/icons/menu/calendar.svg","parentId":"8","id":"21"},"22":{"path":"/farming-management/crop-management-plan/detail/:id","name":"Kế hoạch canh tác","hideInMenu":true,"parentId":"8","id":"22"},"23":{"path":"/farming-management/crop-log","name":"Nhật kí canh tác","icon":"/icons/menu/log.svg","hideInMenu":true,"parentId":"8","id":"23"},"24":{"path":"/farming-management/crop-log","name":"Nhật kí canh tác","redirect":"/farming-management/crop-log/overview","hideInMenu":true,"parentId":"23","id":"24"},"25":{"path":"/farming-management/crop-log/overview","name":"Tổng quan","hideInMenu":true,"parentId":"23","id":"25"},"26":{"path":"/farming-management/crop-log/crop-note-detail","name":"Chi tiết ghi chú mùa vụ","hideInMenu":true,"parentId":"23","id":"26"},"27":{"path":"/farming-management/crop-log/crop-pest-detail","name":"Chi tiết ghi chú dịch hại","hideInMenu":true,"parentId":"23","id":"27"},"28":{"path":"/farming-management/crop-log/task-info-detail","name":"Chi tiết ghi chú công việc","hideInMenu":true,"parentId":"23","id":"28"},"29":{"path":"/farming-management/crop-log/log","name":"Nhật kí","hideInMenu":true,"parentId":"23","id":"29"},"30":{"path":"/farming-management/crop-log/info","name":"Tổng quan","hideInMenu":true,"parentId":"23","id":"30"},"31":{"path":"/farming-management/crop-log/supplies","name":"Vật liệu","hideInMenu":true,"parentId":"23","id":"31"},"32":{"path":"/farming-management/crop-library","name":"plant-library","icon":"/icons/menu/one-tree.svg","parentId":"8","id":"32"},"33":{"path":"/farming-management/crop-library/:id/detail","name":"Chi tiết cây trồng","hideInMenu":true,"parentId":"8","id":"33"},"34":{"path":"/farming-management/crop-library/:id/detail","name":"Chi tiết cây trồng","redirect":"/farming-management/crop-library/:id/detail/general-info","parentId":"33","id":"34"},"35":{"path":"/farming-management/crop-library/:id/detail/general-info","name":"Thông tin chung","parentId":"33","id":"35"},"36":{"path":"/farming-management/crop-library/:id/detail/care-instructions","name":"Hướng dẫn chăm sóc","parentId":"33","id":"36"},"37":{"path":"/farming-management/crop-library/:id/edit","name":"Chi tiết cây trồng","hideInMenu":true,"parentId":"8","id":"37"},"38":{"path":"/farming-management/crop-library/:id/edit","name":"Chi tiết cây trồng","redirect":"/farming-management/crop-library/:id/edit/general-info","parentId":"37","id":"38"},"39":{"path":"/farming-management/crop-library/:id/edit/general-info","name":"Chỉnh sửa cây trồng","parentId":"37","id":"39"},"40":{"path":"/farming-management/crop-library/:id/edit/care-instructions","name":"Chỉnh sửa cây trồng","parentId":"37","id":"40"},"41":{"path":"/farming-management/crop-statistical","name":"crop-statistical","parentId":"8","id":"41"},"42":{"path":"/farming-diary-static","name":"farming-diary-static","icon":"/images/diary-static/diary.svg","parentId":"ant-design-pro-layout","id":"42"},"43":{"path":"/farming-diary-static/procedure","name":"procedure","parentId":"42","id":"43"},"44":{"path":"/farming-diary-static/procedure","index":true,"redirect":"/farming-diary-static/procedure/list","parentId":"43","id":"44"},"45":{"path":"/farming-diary-static/procedure/list","name":"list","hideInMenu":true,"parentId":"43","id":"45"},"46":{"path":"/farming-diary-static/procedure/create","name":"create","hideInMenu":true,"parentId":"43","id":"46"},"47":{"path":"/farming-diary-static/procedure/detail/:id","name":"detail","hideInMenu":true,"parentId":"43","id":"47"},"48":{"path":"/farming-diary-static/procedure/edit/:id","name":"detail","hideInMenu":true,"parentId":"43","id":"48"},"49":{"path":"/farming-diary-static/stage-of-crop","name":"stage-of-crop","parentId":"42","id":"49"},"50":{"path":"/farming-diary-static/stage-of-crop","index":true,"redirect":"/farming-diary-static/stage-of-crop/list","parentId":"49","id":"50"},"51":{"path":"/farming-diary-static/stage-of-crop/list","name":"list","hideInMenu":true,"parentId":"49","id":"51"},"52":{"path":"/farming-diary-static/stage-of-crop/create","name":"create","hideInMenu":true,"parentId":"49","id":"52"},"53":{"path":"/farming-diary-static/task","name":"task","parentId":"42","id":"53"},"54":{"path":"/farming-diary-static/task","index":true,"redirect":"/farming-diary-static/task/list","parentId":"53","id":"54"},"55":{"path":"/farming-diary-static/task/list","name":"list","hideInMenu":true,"parentId":"53","id":"55"},"56":{"path":"/farming-diary-static/task/create","name":"create","hideInMenu":true,"parentId":"53","id":"56"},"57":{"path":"/farming-diary-static/task/edit/:id","name":"detail","hideInMenu":true,"parentId":"53","id":"57"},"58":{"path":"/farming-diary-static/note","name":"note","parentId":"42","id":"58"},"59":{"path":"/farming-diary-static/note","index":true,"redirect":"/farming-diary-static/note/list","parentId":"58","id":"59"},"60":{"path":"/farming-diary-static/note/list","name":"list","hideInMenu":true,"parentId":"58","id":"60"},"61":{"path":"/farming-diary-static/note/create","name":"create","hideInMenu":true,"parentId":"58","id":"61"},"62":{"path":"/farming-diary-static/certification","name":"certification","parentId":"42","id":"62"},"63":{"path":"/farming-diary-static/certification","index":true,"redirect":"/farming-diary-static/certification/list","parentId":"62","id":"63"},"64":{"path":"/farming-diary-static/certification/list","name":"list","hideInMenu":true,"parentId":"62","id":"64"},"65":{"path":"/farming-diary-static/certification/create","name":"create","hideInMenu":true,"parentId":"62","id":"65"},"66":{"path":"/farming-diary-static/product-procedure","name":"product-procedure","parentId":"42","id":"66"},"67":{"path":"/farming-diary-static/product-procedure","index":true,"redirect":"/farming-diary-static/product-procedure/list","parentId":"66","id":"67"},"68":{"path":"/farming-diary-static/product-procedure/list","name":"list","hideInMenu":true,"parentId":"66","id":"68"},"69":{"path":"/farming-diary-static/product-procedure/create","name":"create","hideInMenu":true,"parentId":"66","id":"69"},"70":{"path":"/farming-diary-static/product-procedure/edit/:id","name":"detail","hideInMenu":true,"parentId":"66","id":"70"},"71":{"path":"/farming-diary-static/enterprise","name":"enterprise","parentId":"42","id":"71"},"72":{"path":"/farming-diary-static/enterprise","index":true,"redirect":"/farming-diary-static/enterprise/list","parentId":"71","id":"72"},"73":{"path":"/farming-diary-static/enterprise/list","name":"list","hideInMenu":true,"parentId":"71","id":"73"},"74":{"path":"/farming-diary-static/enterprise/create","name":"create","hideInMenu":true,"parentId":"71","id":"74"},"75":{"path":"/farming-diary-static/enterprise/edit/:id","name":"detail","hideInMenu":true,"parentId":"71","id":"75"},"76":{"path":"/farming-diary-static/tem","name":"stamp","hideInMenu":true,"parentId":"42","id":"76"},"77":{"path":"/farming-diary-static/tem","index":true,"redirect":"/farming-diary-static/tem/list","parentId":"76","id":"77"},"78":{"path":"/farming-diary-static/tem/list","name":"list","hideInMenu":true,"parentId":"76","id":"78"},"79":{"path":"/farming-diary-static/tem/create","name":"create","hideInMenu":true,"parentId":"76","id":"79"},"80":{"path":"/farming-diary-static/tem/edit/:id","name":"detail","hideInMenu":true,"parentId":"76","id":"80"},"81":{"path":"/farming-diary-static/trace","name":"trace","parentId":"42","id":"81"},"82":{"path":"/farming-diary-static/trace","index":true,"redirect":"/farming-diary-static/trace/list","parentId":"81","id":"82"},"83":{"path":"/farming-diary-static/trace/list","name":"list","hideInMenu":true,"parentId":"81","id":"83"},"84":{"path":"/farming-diary-static/trace/create","name":"create","hideInMenu":true,"parentId":"81","id":"84"},"85":{"path":"/farming-diary-static/trace/edit/:id","name":"detail","hideInMenu":true,"parentId":"81","id":"85"},"86":{"path":"/farming-diary","name":"farming-diary","icon":"/icons/menu/calendar.svg","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"86"},"87":{"path":"/farming-diary/detail/:id","name":"farming-diary.detail","icon":"/icons/menu/sun.svg","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"87"},"88":{"path":"/traceability","name":"seek-an-origin","icon":"SecurityScanOutlined","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"88"},"89":{"path":"/project-management","name":"project-zone","icon":"/icons/menu/three-tree.svg","access":"canAccessPageProjectNewManagement","parentId":"ant-design-pro-layout","id":"89"},"90":{"path":"/project-management","hideInMenu":true,"parentId":"89","id":"90"},"91":{"path":"/project-management/create","name":"create","hideInMenu":true,"parentId":"89","id":"91"},"92":{"path":"/project-management/detail/:id","name":"detail","hideInMenu":true,"parentId":"89","id":"92"},"93":{"path":"/project-management/update/:id","name":"edit","hideInMenu":true,"parentId":"89","id":"93"},"94":{"path":"/inventory-management-v3","name":"category-v3","icon":"/icons/menu/turtle-car.svg","access":"canAccessTabCategory","parentId":"ant-design-pro-layout","id":"94"},"95":{"path":"/inventory-management-v3/products","name":"product","access":"isSubscribedPrimary","parentId":"94","id":"95"},"96":{"path":"/inventory-management-v3/bom","name":"bom","access":"isSubscribedPrimary","parentId":"94","id":"96"},"97":{"path":"/inventory-management-v3/customers","name":"customer","access":"isSubscribedStock","parentId":"94","id":"97"},"98":{"path":"/inventory-management-v3/customers-sales-order","name":"customer.sales-order","access":"isSubscribedStock","parentId":"94","id":"98"},"99":{"path":"/inventory-management-v3/customers/to-pdf","name":"customer-to-pdf","headerRender":false,"footerRender":false,"menuRender":false,"menuHeaderRender":false,"hideInMenu":true,"parentId":"94","id":"99"},"100":{"path":"/inventory-management-v3/suppliers","name":"supplier","access":"isSubscribedStock","parentId":"94","id":"100"},"101":{"path":"/inventory-management-v3/suppliers/to-pdf","name":"supplier-to-pdf","headerRender":false,"footerRender":false,"menuRender":false,"menuHeaderRender":false,"hideInMenu":true,"parentId":"94","id":"101"},"102":{"hideInMenu":true,"path":"/inventory-management-v3/uom","locale":false,"name":"Đơn vị","parentId":"94","id":"102"},"103":{"path":"/warehouse-management-v3","name":"inventory-management-v3","access":"canAccessTabInventory","icon":"/icons/menu/home-icon.svg","parentId":"ant-design-pro-layout","id":"103"},"104":{"path":"warehouse-management-v3/dashboard","name":"dashboard","parentId":"103","id":"104"},"105":{"path":"/warehouse-management-v3/warehouse","name":"list","parentId":"103","id":"105"},"106":{"path":"/warehouse-management-v3/inventory","name":"inventory","parentId":"103","id":"106"},"107":{"path":"/warehouse-management-v3/inventory/dashboard","name":"Dashboard","hideInMenu":true,"parentId":"106","id":"107"},"108":{"path":"/warehouse-management-v3/inventory/inventory-list","name":"stock-ledger","hideInMenu":true,"parentId":"106","id":"108"},"109":{"path":"/warehouse-management-v3/inventory/import-history","name":"import-history","hideInMenu":true,"parentId":"106","id":"109"},"110":{"path":"/warehouse-management-v3/inventory/export-history","name":"export-history","hideInMenu":true,"parentId":"106","id":"110"},"111":{"path":"/warehouse-management-v3/inventory/reconciliation-history","name":"reconciliation-history","hideInMenu":true,"parentId":"106","id":"111"},"112":{"path":"/warehouse-management-v3/inventory/stock-entry","name":"stock-entry","hideInMenu":true,"parentId":"106","id":"112"},"113":{"path":"/warehouse-management-v3/inventory/report","name":"report","hideInMenu":true,"parentId":"106","id":"113"},"114":{"path":"/warehouse-management-v3/to-pdf","name":"to-pdf","headerRender":false,"footerRender":false,"menuRender":false,"menuHeaderRender":false,"hideInMenu":true,"parentId":"ant-design-pro-layout","id":"114"},"115":{"path":"/iot-device-management","name":"manage_iot_devices","icon":"/icons/menu/iot.svg","access":"canAccessPageIotDeviceManagement","parentId":"ant-design-pro-layout","id":"115"},"116":{"path":"/iot-device-management","redirect":"/iot-device-management","hideInMenu":true,"parentId":"115","id":"116"},"117":{"path":"/expense-management","name":"Quản lý duyệt chi","icon":"/icons/menu/bill.svg","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"117"},"118":{"path":"/expense-management","redirect":"/expense-management","hideInMenu":true,"parentId":"117","id":"118"},"119":{"path":"/employee-management","name":"employee_management","icon":"/icons/menu/staff.svg","access":"canAccessTabEmployeeManagement","parentId":"ant-design-pro-layout","id":"119"},"120":{"path":"/employee-management/employee-list","name":"list_of_employee","access":"isSubscribed","parentId":"119","id":"120"},"121":{"path":"/employee-management/dynamic-role","name":"list_of_permissions","access":"isSubscribed","parentId":"119","id":"121"},"122":{"path":"/employee-management/timekeeping","name":"attendance","access":"isSubscribedEnterpriseOrEmployee","parentId":"119","id":"122"},"123":{"path":"/employee-management/timekeeping/checkin-history","name":"history_in_and_out","hideInMenu":true,"parentId":"122","id":"123"},"124":{"path":"/employee-management/timekeeping/general","name":"synthetic","hideInMenu":true,"parentId":"122","id":"124"},"125":{"path":"/employee-management/timekeeping/report","name":"report","hideInMenu":true,"parentId":"122","id":"125"},"126":{"path":"/employee-management/timekeeping/work-shift","name":"work-shift","hideInMenu":true,"parentId":"122","id":"126"},"127":{"path":"/employee-management/timekeeping/approval","name":"approval","hideInMenu":true,"parentId":"122","id":"127"},"128":{"path":"/employee-management/timekeepingV2","name":"timekeeping","access":"isSubscribedEnterpriseOrEmployee","parentId":"119","id":"128"},"129":{"index":true,"hideInMenu":true,"parentId":"128","id":"129"},"130":{"path":"/employee-management/timekeepingV2/:id","name":"timesheets","hideInMenu":true,"parentId":"128","id":"130"},"131":{"path":"/employee-management/approval","name":"approval","hideInMenu":true,"parentId":"119","id":"131"},"132":{"path":"/employee-management/approval","index":true,"hideInMenu":true,"parentId":"131","id":"132"},"133":{"name":"details","path":"/employee-management/approval/details/:id","hideInMenu":true,"parentId":"131","id":"133"},"134":{"path":"/employee-management/timekeeping/to-pdf","name":"timekeeping/to-pdf","headerRender":false,"footerRender":false,"menuRender":false,"menuHeaderRender":false,"hideInMenu":true,"parentId":"ant-design-pro-layout","id":"134"},"135":{"path":"/employee-management/visitor-management","name":"in_and_out_management","access":"isSubscribedVisitor","icon":"/icons/menu/log.svg","parentId":"ant-design-pro-layout","id":"135"},"136":{"name":"statistical_tables","icon":"BarChartOutlined","path":"/employee-management/visitor-management/dashboard-visitor","parentId":"135","id":"136"},"137":{"name":"visit_history","icon":"HomeOutlined","path":"/employee-management/visitor-management/history","parentId":"135","id":"137"},"138":{"path":"/employee-management/visitor-management/history","redirect":"/employee-management/visitor-management/history/all","parentId":"137","id":"138"},"139":{"name":"all","path":"/employee-management/visitor-management/history/all","hideInMenu":true,"parentId":"137","id":"139"},"140":{"name":"detail","path":"/employee-management/visitor-management/history/detail","hideInMenu":true,"parentId":"137","id":"140"},"141":{"name":"user","icon":"UsergroupAddOutlined","path":"/employee-management/visitor-management/member","parentId":"135","id":"141"},"142":{"path":"/employee-management/visitor-management/member","redirect":"/employee-management/visitor-management/member/all","parentId":"141","id":"142"},"143":{"name":"all","path":"/employee-management/visitor-management/member/all","hideInMenu":true,"parentId":"141","id":"143"},"144":{"name":"detail","path":"/employee-management/visitor-management/member/detail","hideInMenu":true,"parentId":"141","id":"144"},"145":{"name":"card","icon":"CreditCardOutlined","path":"/employee-management/visitor-management/card","parentId":"135","id":"145"},"146":{"path":"/employee-management/visitor-management/card","redirect":"/employee-management/visitor-management/card/all","parentId":"145","id":"146"},"147":{"name":"all","path":"/employee-management/visitor-management/card/all","hideInMenu":true,"parentId":"145","id":"147"},"148":{"name":"detail","path":"/employee-management/visitor-management/card/detail","hideInMenu":true,"parentId":"145","id":"148"},"149":{"name":"location","icon":"EnvironmentOutlined","path":"/employee-management/visitor-management/location","parentId":"135","id":"149"},"150":{"path":"/employee-management/visitor-management/location","redirect":"/employee-management/visitor-management/location/all","parentId":"149","id":"150"},"151":{"name":"all","path":"/employee-management/visitor-management/location/all","hideInMenu":true,"parentId":"149","id":"151"},"152":{"name":"detail","path":"/employee-management/visitor-management/location/detail","hideInMenu":true,"parentId":"149","id":"152"},"153":{"path":"/time-attendance-management","name":"timekeeping_management","icon":"/icons/menu/timekeeping.svg","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"153"},"154":{"path":"/time-attendance-management","redirect":"/time-attendance-management","hideInMenu":true,"parentId":"153","id":"154"},"155":{"name":"notification","icon":"/icons/menu/notification.svg","path":"/user-notification","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"155"},"156":{"path":"/documents","name":"documents","icon":"QuestionCircleOutlined","parentId":"ant-design-pro-layout","id":"156"},"157":{"path":"/documents/:id/detail","name":"Chi tiết tài liệu","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"157"},"158":{"path":"/documents/:id/detail","name":"Chi tiết tài liệu","redirect":"/documents/:id/detail/general-info","parentId":"157","id":"158"},"159":{"path":"/documents/:id/detail/general-info","name":"Thông tin chung","parentId":"157","id":"159"},"160":{"path":"/documents/:id/detail/care-instructions","name":"Hướng dẫn chăm sóc","parentId":"157","id":"160"},"161":{"path":"/documents/:id/edit","name":"Chi tiết tài liệu","locale":"false","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"161"},"162":{"path":"/documents/:id/edit","name":"Chi tiết tài liệu","redirect":"/documents/:id/edit/general-info","parentId":"161","id":"162"},"163":{"path":"/documents/:id/edit/general-info","name":"Chỉnh sửa tài liệu","parentId":"161","id":"163"},"164":{"path":"/documents/:id/edit/care-instructions","name":"Chỉnh sửa tài liệu","parentId":"161","id":"164"},"165":{"path":"/account-information","name":"account-information","icon":"/icons/menu/farmer.svg","parentId":"ant-design-pro-layout","id":"165"},"166":{"path":"/log-out","name":"logout","icon":"/icons/menu/log-out.svg","parentId":"ant-design-pro-layout","id":"166"},"167":{"path":"/","redirect":"/home","parentId":"ant-design-pro-layout","id":"167"},"168":{"path":"*","parentId":"ant-design-pro-layout","id":"168"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__Maintenance__index" */'@/pages/Maintenance/index.tsx')),
'2': React.lazy(() => import( './EmptyRoute')),
'3': React.lazy(() => import(/* webpackChunkName: "p__User__Login__index" */'@/pages/User/Login/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__User__ForgotPassword__index" */'@/pages/User/ForgotPassword/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__User__ResetNewPassword__index" */'@/pages/User/ResetNewPassword/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__User__ResetNewPassword__index" */'@/pages/User/ResetNewPassword/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__DashboardNew__index" */'@/pages/DashboardNew/index.tsx')),
'8': React.lazy(() => import( './EmptyRoute')),
'9': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__Dashboard__index" */'@/pages/FarmingManagement/SeasonalManagement/Dashboard/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__index" */'@/pages/FarmingManagement/SeasonalManagement/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__Create__index" */'@/pages/FarmingManagement/SeasonalManagement/Create/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__Detail__index" */'@/pages/FarmingManagement/SeasonalManagement/Detail/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__Detail__Ticket__components__TicketMessages" */'@/pages/FarmingManagement/SeasonalManagement/Detail/Ticket/components/TicketMessages.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__WorkflowManagement__Detail__index" */'@/pages/FarmingManagement/WorkflowManagement/Detail/index.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__SeasonalManagement__Edit__index" */'@/pages/FarmingManagement/SeasonalManagement/Edit/index.tsx')),
'16': React.lazy(() => import( './EmptyRoute')),
'17': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__WorkflowManagement__index" */'@/pages/FarmingManagement/WorkflowManagement/index.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__WorkflowManagement__Create__index" */'@/pages/FarmingManagement/WorkflowManagement/Create/index.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__WorkflowManagement__Detail__index" */'@/pages/FarmingManagement/WorkflowManagement/Detail/index.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__WorkflowManagement__Edit__index" */'@/pages/FarmingManagement/WorkflowManagement/Edit/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropPlan__index" */'@/pages/FarmingManagement/CropPlan/index.tsx')),
'22': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropPlan__Detail__index" */'@/pages/FarmingManagement/CropPlan/Detail/index.tsx')),
'23': React.lazy(() => import( './EmptyRoute')),
'24': React.lazy(() => import( './EmptyRoute')),
'25': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__index" */'@/pages/FarmingManagement/CropLog/index.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__DetailCropNote__index" */'@/pages/FarmingManagement/CropLog/DetailCropNote/index.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__DetailCropPest__index" */'@/pages/FarmingManagement/CropLog/DetailCropPest/index.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__DetailTaskInfo__index" */'@/pages/FarmingManagement/CropLog/DetailTaskInfo/index.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__Log__index" */'@/pages/FarmingManagement/CropLog/Log/index.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__Info__index" */'@/pages/FarmingManagement/CropLog/Info/index.tsx')),
'31': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLog__Supplies__index" */'@/pages/FarmingManagement/CropLog/Supplies/index.tsx')),
'32': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLibrary__index" */'@/pages/FarmingManagement/CropLibrary/index.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLibrary__Detail__index" */'@/pages/FarmingManagement/CropLibrary/Detail/index.tsx')),
'34': React.lazy(() => import( './EmptyRoute')),
'35': React.lazy(() => import( './EmptyRoute')),
'36': React.lazy(() => import( './EmptyRoute')),
'37': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropLibrary__Edit__index" */'@/pages/FarmingManagement/CropLibrary/Edit/index.tsx')),
'38': React.lazy(() => import( './EmptyRoute')),
'39': React.lazy(() => import( './EmptyRoute')),
'40': React.lazy(() => import( './EmptyRoute')),
'41': React.lazy(() => import(/* webpackChunkName: "p__FarmingManagement__CropStatisticV2__index" */'@/pages/FarmingManagement/CropStatisticV2/index.tsx')),
'42': React.lazy(() => import( './EmptyRoute')),
'43': React.lazy(() => import( './EmptyRoute')),
'44': React.lazy(() => import( './EmptyRoute')),
'45': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__FarmingProcedure__index" */'@/pages/FarmingDiaryStatic/FarmingProcedure/index.tsx')),
'46': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__FarmingProcedure__CreateProcedurePage" */'@/pages/FarmingDiaryStatic/FarmingProcedure/CreateProcedurePage.tsx')),
'47': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__FarmingProcedure__DetailProcedurePage" */'@/pages/FarmingDiaryStatic/FarmingProcedure/DetailProcedurePage.tsx')),
'48': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__FarmingProcedure__EditProcedurePage" */'@/pages/FarmingDiaryStatic/FarmingProcedure/EditProcedurePage.tsx')),
'49': React.lazy(() => import( './EmptyRoute')),
'50': React.lazy(() => import( './EmptyRoute')),
'51': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__StageOfCrop__index" */'@/pages/FarmingDiaryStatic/StageOfCrop/index.tsx')),
'52': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__StageOfCrop__CreateStageOfCropPage" */'@/pages/FarmingDiaryStatic/StageOfCrop/CreateStageOfCropPage.tsx')),
'53': React.lazy(() => import( './EmptyRoute')),
'54': React.lazy(() => import( './EmptyRoute')),
'55': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Task__index" */'@/pages/FarmingDiaryStatic/Task/index.tsx')),
'56': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Task__CreateTaskPage" */'@/pages/FarmingDiaryStatic/Task/CreateTaskPage.tsx')),
'57': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Task__EditTaskPage" */'@/pages/FarmingDiaryStatic/Task/EditTaskPage.tsx')),
'58': React.lazy(() => import( './EmptyRoute')),
'59': React.lazy(() => import( './EmptyRoute')),
'60': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Note__index" */'@/pages/FarmingDiaryStatic/Note/index.tsx')),
'61': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Note__CreateNotePage" */'@/pages/FarmingDiaryStatic/Note/CreateNotePage.tsx')),
'62': React.lazy(() => import( './EmptyRoute')),
'63': React.lazy(() => import( './EmptyRoute')),
'64': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Certification__index" */'@/pages/FarmingDiaryStatic/Certification/index.tsx')),
'65': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Certification__CreateCertificationPage" */'@/pages/FarmingDiaryStatic/Certification/CreateCertificationPage.tsx')),
'66': React.lazy(() => import( './EmptyRoute')),
'67': React.lazy(() => import( './EmptyRoute')),
'68': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__ProductProcedure__index" */'@/pages/FarmingDiaryStatic/ProductProcedure/index.tsx')),
'69': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__ProductProcedure__CreateProdProcedurePage" */'@/pages/FarmingDiaryStatic/ProductProcedure/CreateProdProcedurePage.tsx')),
'70': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__ProductProcedure__EditProdProcedurePage" */'@/pages/FarmingDiaryStatic/ProductProcedure/EditProdProcedurePage.tsx')),
'71': React.lazy(() => import( './EmptyRoute')),
'72': React.lazy(() => import( './EmptyRoute')),
'73': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Enterprise__index" */'@/pages/FarmingDiaryStatic/Enterprise/index.tsx')),
'74': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Enterprise__CreatePage" */'@/pages/FarmingDiaryStatic/Enterprise/CreatePage.tsx')),
'75': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Enterprise__EditPage" */'@/pages/FarmingDiaryStatic/Enterprise/EditPage.tsx')),
'76': React.lazy(() => import( './EmptyRoute')),
'77': React.lazy(() => import( './EmptyRoute')),
'78': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Tem__index" */'@/pages/FarmingDiaryStatic/Tem/index.tsx')),
'79': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Tem__CreateTemPage" */'@/pages/FarmingDiaryStatic/Tem/CreateTemPage.tsx')),
'80': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Tem__EditTemPage" */'@/pages/FarmingDiaryStatic/Tem/EditTemPage.tsx')),
'81': React.lazy(() => import( './EmptyRoute')),
'82': React.lazy(() => import( './EmptyRoute')),
'83': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Trace__index" */'@/pages/FarmingDiaryStatic/Trace/index.tsx')),
'84': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Trace__CreateTracePage" */'@/pages/FarmingDiaryStatic/Trace/CreateTracePage.tsx')),
'85': React.lazy(() => import(/* webpackChunkName: "p__FarmingDiaryStatic__Trace__EditTracePage" */'@/pages/FarmingDiaryStatic/Trace/EditTracePage.tsx')),
'86': React.lazy(() => import(/* webpackChunkName: "p__DiaryManagement__index" */'@/pages/DiaryManagement/index.tsx')),
'87': React.lazy(() => import(/* webpackChunkName: "p__DiaryManagement__Detail__index" */'@/pages/DiaryManagement/Detail/index.tsx')),
'88': React.lazy(() => import(/* webpackChunkName: "p__DiaryManagement__TracingTable__page" */'@/pages/DiaryManagement/TracingTable/page.tsx')),
'89': React.lazy(() => import( './EmptyRoute')),
'90': React.lazy(() => import(/* webpackChunkName: "p__Project__index" */'@/pages/Project/index.tsx')),
'91': React.lazy(() => import(/* webpackChunkName: "p__Project__Create__index" */'@/pages/Project/Create/index.tsx')),
'92': React.lazy(() => import(/* webpackChunkName: "p__Project__Detail__index" */'@/pages/Project/Detail/index.tsx')),
'93': React.lazy(() => import(/* webpackChunkName: "p__Project__Update__index" */'@/pages/Project/Update/index.tsx')),
'94': React.lazy(() => import( './EmptyRoute')),
'95': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__index" */'@/pages/InventoryManagementV3/index.tsx')),
'96': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__BOM__index" */'@/pages/InventoryManagementV3/ProductManagement/BOM/index.tsx')),
'97': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__Customer__index" */'@/pages/InventoryManagementV3/ProductManagement/Customer/index.tsx')),
'98': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__Customer__SalesOrderList__index" */'@/pages/InventoryManagementV3/ProductManagement/Customer/SalesOrderList/index.tsx')),
'99': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__Customer__Report__Print__index" */'@/pages/InventoryManagementV3/ProductManagement/Customer/Report/Print/index.tsx')),
'100': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__Supplier__index" */'@/pages/InventoryManagementV3/ProductManagement/Supplier/index.tsx')),
'101': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__Supplier__Report__Print__index" */'@/pages/InventoryManagementV3/ProductManagement/Supplier/Report/Print/index.tsx')),
'102': React.lazy(() => import(/* webpackChunkName: "p__InventoryManagementV3__ProductManagement__UOM__UOMList__index" */'@/pages/InventoryManagementV3/ProductManagement/UOM/UOMList/index.tsx')),
'103': React.lazy(() => import( './EmptyRoute')),
'104': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Dashboard__index" */'@/pages/WarehouseManagementV3/Dashboard/index.tsx')),
'105': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Warehouse__index" */'@/pages/WarehouseManagementV3/Warehouse/index.tsx')),
'106': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__index" */'@/pages/WarehouseManagementV3/Inventory/index.tsx')),
'107': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__Dashboard__index" */'@/pages/WarehouseManagementV3/Inventory/Dashboard/index.tsx')),
'108': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__InventoryListTable__index" */'@/pages/WarehouseManagementV3/Inventory/InventoryListTable/index.tsx')),
'109': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__ImportHistory__index" */'@/pages/WarehouseManagementV3/Inventory/ImportHistory/index.tsx')),
'110': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__ExportHistory__index" */'@/pages/WarehouseManagementV3/Inventory/ExportHistory/index.tsx')),
'111': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__ReconciliationHistory__index" */'@/pages/WarehouseManagementV3/Inventory/ReconciliationHistory/index.tsx')),
'112': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__StockEntryHistory__index" */'@/pages/WarehouseManagementV3/Inventory/StockEntryHistory/index.tsx')),
'113': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__Report__index" */'@/pages/WarehouseManagementV3/Inventory/Report/index.tsx')),
'114': React.lazy(() => import(/* webpackChunkName: "p__WarehouseManagementV3__Inventory__ExportPage__index" */'@/pages/WarehouseManagementV3/Inventory/ExportPage/index.tsx')),
'115': React.lazy(() => import(/* webpackChunkName: "p__IoTDeviceMangement__index" */'@/pages/IoTDeviceMangement/index.tsx')),
'116': React.lazy(() => import( './EmptyRoute')),
'117': React.lazy(() => import( './EmptyRoute')),
'118': React.lazy(() => import( './EmptyRoute')),
'119': React.lazy(() => import( './EmptyRoute')),
'120': React.lazy(() => import(/* webpackChunkName: "p__MyUser__UserList__index" */'@/pages/MyUser/UserList/index.tsx')),
'121': React.lazy(() => import(/* webpackChunkName: "p__MyUser__DynamicRole__index" */'@/pages/MyUser/DynamicRole/index.tsx')),
'122': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__index" */'@/pages/MyUser/Timekeeping/index.tsx')),
'123': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__Checkin__index" */'@/pages/MyUser/Timekeeping/Checkin/index.tsx')),
'124': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__General__index" */'@/pages/MyUser/Timekeeping/General/index.tsx')),
'125': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__Report__index" */'@/pages/MyUser/Timekeeping/Report/index.tsx')),
'126': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__WorkShift__index" */'@/pages/MyUser/Timekeeping/WorkShift/index.tsx')),
'127': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Timekeeping__Approval__index" */'@/pages/MyUser/Timekeeping/Approval/index.tsx')),
'128': React.lazy(() => import( './EmptyRoute')),
'129': React.lazy(() => import(/* webpackChunkName: "p__MyUser__TimekeepingV2__index" */'@/pages/MyUser/TimekeepingV2/index.tsx')),
'130': React.lazy(() => import(/* webpackChunkName: "p__MyUser__TimekeepingV2__components__DetailTimesheet__index" */'@/pages/MyUser/TimekeepingV2/components/DetailTimesheet/index.tsx')),
'131': React.lazy(() => import( './EmptyRoute')),
'132': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Approval__index" */'@/pages/MyUser/Approval/index.tsx')),
'133': React.lazy(() => import(/* webpackChunkName: "p__MyUser__Approval__Details__index" */'@/pages/MyUser/Approval/Details/index.tsx')),
'134': React.lazy(() => import(/* webpackChunkName: "p__MyUser__ExportPage__index" */'@/pages/MyUser/ExportPage/index.tsx')),
'135': React.lazy(() => import( './EmptyRoute')),
'136': React.lazy(() => import(/* webpackChunkName: "p__Visitor__Dashboard__index" */'@/pages/Visitor/Dashboard/index.tsx')),
'137': React.lazy(() => import( './EmptyRoute')),
'138': React.lazy(() => import( './EmptyRoute')),
'139': React.lazy(() => import(/* webpackChunkName: "p__Visitor__History__index" */'@/pages/Visitor/History/index.tsx')),
'140': React.lazy(() => import(/* webpackChunkName: "p__Visitor__History__Detail__index" */'@/pages/Visitor/History/Detail/index.tsx')),
'141': React.lazy(() => import( './EmptyRoute')),
'142': React.lazy(() => import( './EmptyRoute')),
'143': React.lazy(() => import(/* webpackChunkName: "p__Visitor__PakingCustomer__index" */'@/pages/Visitor/PakingCustomer/index.tsx')),
'144': React.lazy(() => import(/* webpackChunkName: "p__Visitor__PakingCustomer__Detail__index" */'@/pages/Visitor/PakingCustomer/Detail/index.tsx')),
'145': React.lazy(() => import( './EmptyRoute')),
'146': React.lazy(() => import( './EmptyRoute')),
'147': React.lazy(() => import(/* webpackChunkName: "p__Visitor__MemberCard__index" */'@/pages/Visitor/MemberCard/index.tsx')),
'148': React.lazy(() => import(/* webpackChunkName: "p__Visitor__MemberCard__Detail__index" */'@/pages/Visitor/MemberCard/Detail/index.tsx')),
'149': React.lazy(() => import( './EmptyRoute')),
'150': React.lazy(() => import( './EmptyRoute')),
'151': React.lazy(() => import(/* webpackChunkName: "p__Visitor__Location__index" */'@/pages/Visitor/Location/index.tsx')),
'152': React.lazy(() => import(/* webpackChunkName: "p__Visitor__Location__Detail__index" */'@/pages/Visitor/Location/Detail/index.tsx')),
'153': React.lazy(() => import( './EmptyRoute')),
'154': React.lazy(() => import( './EmptyRoute')),
'155': React.lazy(() => import(/* webpackChunkName: "p__Notification__index" */'@/pages/Notification/index.tsx')),
'156': React.lazy(() => import(/* webpackChunkName: "p__Docs__CropLibrary__index" */'@/pages/Docs/CropLibrary/index.tsx')),
'157': React.lazy(() => import(/* webpackChunkName: "p__Docs__CropLibrary__Detail__index" */'@/pages/Docs/CropLibrary/Detail/index.tsx')),
'158': React.lazy(() => import( './EmptyRoute')),
'159': React.lazy(() => import( './EmptyRoute')),
'160': React.lazy(() => import( './EmptyRoute')),
'161': React.lazy(() => import(/* webpackChunkName: "p__Docs__CropLibrary__Edit__index" */'@/pages/Docs/CropLibrary/Edit/index.tsx')),
'162': React.lazy(() => import( './EmptyRoute')),
'163': React.lazy(() => import( './EmptyRoute')),
'164': React.lazy(() => import( './EmptyRoute')),
'165': React.lazy(() => import(/* webpackChunkName: "p__User__MyAccount__index" */'@/pages/User/MyAccount/index.tsx')),
'166': React.lazy(() => import(/* webpackChunkName: "p__User__Logout" */'@/pages/User/Logout.tsx')),
'167': React.lazy(() => import( './EmptyRoute')),
'168': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "t__plugin-layout__Layout" */'D:/WORK/PYROJECT/VIIS/viis-iot-web-v2/user-web/src/.umi-production/plugin-layout/Layout.tsx')),
},
  };
}
