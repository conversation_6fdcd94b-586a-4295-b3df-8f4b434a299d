import { DOCTYPE_ERP } from '@/common/contanst/constanst';
import ItemUsedTableCreateView from '@/components/Task/TaskItemUsed/ItemUsedTableCreateView';
import ProductionTableCreateView from '@/components/Task/TaskProductionNew/ProductionTableCreateView';
import CreateTodoTableEditer from '@/components/Task/TaskTodo/CreateTodoTableEditer';
import { customerUserListAll } from '@/services/customerUser';
import {
  createFarmingPlanTask,
  getFarmingPlan,
  getFarmingPlanState,
} from '@/services/farming-plan';
import { TaskItemUsed, useTaskItemUsedCreateStore } from '@/stores/TaskItemUsedCreateStore';
import { TaskProduction, useTaskProductionCreateStore } from '@/stores/TaskProductionCreateStore';
import { PageContainer, ProForm, useDeepCompareEffect } from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { App, Button, Modal, Space, UploadFile } from 'antd';
import dayjs from 'dayjs';
import moment from 'moment';
import { FC, ReactNode, useEffect, useState } from 'react';
import DetailedInfo from './DetailedInfo';

interface CreateWorkflowProps {
  children?: ReactNode;
  mode?: 'normal' | 'modal';
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onCreateSuccess?: () => void;
  farmingPlanStateId?: string;
  cropId?: string;
  planId?: string;
  defaultValue?: {
    start_date?: moment.Moment | null;
    end_date?: moment.Moment | null;
  };
  isTemplateTask?: boolean;
}
type IFormData = {
  label: string;
  farming_plan_state: string;
  dateRange: [string, string];
  intervalRange: [string, string];
  assigned_to: string;
  involved_in_users?: string[];
  description?: string;
  img?: UploadFile[];
  is_interval?: boolean;
  interval_value?: number;
  interval_type?: 'w' | 'd' | 'M';
  isTemplateTask?: boolean;
};

const CreateWorkflow: FC<CreateWorkflowProps> = ({
  mode = 'normal',
  onCreateSuccess,
  open,
  onOpenChange,
  farmingPlanStateId,
  planId,
  defaultValue,
  cropId,
  isTemplateTask,
}) => {
  const [todoList, setTodoList] = useState<any>([]);
  // const [taskItems, setTaskItems] = useState<any>([]);
  // const [productions, setProductions] = useState<any>([]);

  const { taskItemUsed: taskItems, setTaskItemUsed: setTaskItems } = useTaskItemUsedCreateStore();
  const { taskProduction: productions, setTaskProduction: setProductions } =
    useTaskProductionCreateStore();

  const [workTimes, setWorkTimes] = useState([]);

  const [loading, setLoading] = useState(false);
  const [customerUserOptions, setCustomerUserOptions] = useState([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [currentPlan, setCurrentPlan] = useState<any>({});

  //set taskItems and productions when taskItemUsed and taskProduction change
  // useEffect(() => {
  //   setTaskItems(taskItemUsed);
  //   setProductions(taskProduction);
  // }, [taskItemUsed, taskProduction]);

  const onFileListChange = (fileList: any[]) => {
    setFileList(fileList);
  };

  const getCustomerUser = async () => {
    try {
      setLoading(true);
      //call api
      const result = await customerUserListAll();
      console.log('result', result);
      setCustomerUserOptions(
        result?.data?.map((d: any) => {
          return {
            value: d.name,
            label: `${d.full_name} ${d.email}`,
          };
        }),
      );
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getCurrentFarmingPlan = async () => {
    //get default farming plan
    if (planId) {
      const farmingPlan = await getFarmingPlan(planId);
      setCurrentPlan(farmingPlan.data);
      return;
    }

    //get farming plan only if farmingPlanStateId is not undefined
    if (!farmingPlanStateId) return;
    const filters = [[DOCTYPE_ERP.iotFarmingPlanState, 'name', 'like', farmingPlanStateId]];
    console.log('filters', filters);
    const farmingPlanState = await getFarmingPlanState({ filters });
    console.log('farming plan state is', farmingPlanState);

    const farmingPlanId = farmingPlanState.data[0].farming_plan;
    const farmingPlan = await getFarmingPlan(farmingPlanId);
    setCurrentPlan(farmingPlan.data);
  };

  useEffect(() => {
    getCustomerUser();
    getCurrentFarmingPlan();
  }, []);

  const { message } = App.useApp();
  const [submitting, setSubmitting] = useState(false);
  const [form] = ProForm.useForm();
  // const formRef = useRef<ProFormInstance<any>>();
  const onFinish = async (values: any) => {
    console.log('start on finish');
    setSubmitting(true);

    let imagePath = values?.['upload-image'] || '';

    try {
      const {
        is_interval,
        interval_type,
        interval_value,
        intervalRange,
        start_date,
        end_date,
        enable_origin_tracing,
        involved_in_users,
      } = values;

      let requestArr: any = [];

      // Ensure intervalRange is defined and valid before mapping
      if (intervalRange && Array.isArray(intervalRange)) {
        values.intervalRange = intervalRange.map((d: string) => {
          return moment(d, 'DD-MM-YYYY').format('YYYY-MM-DD');
        });
      } else {
        values.intervalRange = [];
      }

      if (
        is_interval &&
        interval_type &&
        interval_value &&
        values.intervalRange.length === 2 &&
        moment(values.intervalRange[0]).isValid() &&
        moment(values.intervalRange[1]).isValid()
      ) {
        let start_check = moment(values.intervalRange[0]);
        let counter = 1;

        while (start_check.isBefore(values.intervalRange[1])) {
          const task = {
            label: values.label,
            farming_plan_state: values.farming_plan_state,
            start_date: moment(start_date)
              .add(interval_value * counter, interval_type)
              .format('YYYY-MM-DD HH:mm:ss'),
            end_date: moment(end_date)
              .add(interval_value * counter, interval_type)
              .format('YYYY-MM-DD HH:mm:ss'),
            description: values.description,
            assigned_to: values.assigned_to,
            image: imagePath,
            status: values.status,
            enable_origin_tracing: enable_origin_tracing ? 1 : 0,
            involve_in_users:
              involved_in_users?.map((d: any) => {
                const id = typeof d === 'string' ? d : d.customer_user;
                return { customer_user: id };
              }) || [],
            worksheet_list: workTimes.map((d: any) => {
              const {
                work_type_id = null,
                exp_quantity = 0,
                quantity = 0,
                type = null,
                description = null,
                cost = 0,
              } = d;
              return { cost, work_type_id, exp_quantity, quantity, type, description };
            }),
            item_list: taskItems.map((d: TaskItemUsed) => {
              const {
                quantity = 0,
                description = null,
                iot_category_id = null,
                exp_quantity,
                conversion_factor = 1,
                loss_quantity = 0,
                active_uom,
                active_conversion_factor,
              } = d;

              // Tính toán lại exp_quantity nếu cần thiết
              const calculatedExpQuantity =
                exp_quantity !== undefined && conversion_factor !== undefined
                  ? exp_quantity * conversion_factor
                  : exp_quantity;

              return {
                quantity,
                description,
                iot_category_id,
                exp_quantity: calculatedExpQuantity,
                loss_quantity,
                active_uom,
                active_conversion_factor,
              };
            }),
            todo_list: todoList.map((d: any) => {
              delete d['name'];
              const { label, description = null, customer_user_id } = d;
              return { label, description, customer_user_id };
            }),
            prod_quantity_list: productions.map((d: TaskProduction) => {
              const {
                quantity = 0,
                description = null,
                product_id = null,
                exp_quantity,
                conversion_factor = 1,
                lost_quantity = 0,
                active_uom,
                active_conversion_factor,
              } = d;

              // Tính toán lại exp_quantity nếu cần thiết
              const calculatedExpQuantity =
                exp_quantity !== undefined && conversion_factor !== undefined
                  ? exp_quantity * conversion_factor
                  : exp_quantity;

              return {
                quantity,
                description,
                product_id,
                exp_quantity: calculatedExpQuantity,
                lost_quantity,
                active_uom,
                active_conversion_factor,
              };
            }),
          };
          requestArr.push(task);
          start_check = start_check.add(interval_value, interval_type);
          counter++;
          console.log('counter is ', counter);
        }

        requestArr = requestArr.filter((d: any) => {
          return d.start_date !== moment(values.start_date[0]).format('YYYY-MM-DD HH:mm:ss');
        });

        requestArr.push({
          label: values.label,
          farming_plan_state: values.farming_plan_state,
          start_date: moment(start_date).format('YYYY-MM-DD HH:mm:ss'),
          end_date: moment(end_date).format('YYYY-MM-DD HH:mm:ss'),
          description: values.description,
          assigned_to: values.assigned_to,
          status: values.status,
          image: imagePath,
          enable_origin_tracing: enable_origin_tracing ? 1 : 0,
          involve_in_users:
            involved_in_users?.map((d: any) => {
              const id = typeof d === 'string' ? d : d.customer_user;
              return { customer_user: id };
            }) || [],
          worksheet_list: workTimes.map((d: any) => {
            const {
              cost = 0,
              work_type_id = null,
              exp_quantity = 0,
              quantity = 0,
              type = null,
              description = null,
            } = d;
            return { cost, work_type_id, exp_quantity, quantity, type, description };
          }),
          item_list: taskItems.map((d: TaskItemUsed) => {
            const {
              quantity = 0,
              description = null,
              iot_category_id = null,
              exp_quantity,
              conversion_factor = 1,
              loss_quantity = 0,
              active_uom,
              active_conversion_factor,
            } = d;

            // Tính toán lại exp_quantity nếu cần thiết
            const calculatedExpQuantity =
              exp_quantity !== undefined && conversion_factor !== undefined
                ? exp_quantity * conversion_factor
                : exp_quantity;

            return {
              quantity,
              description,
              iot_category_id,
              exp_quantity: calculatedExpQuantity,
              loss_quantity,
              active_uom,
              active_conversion_factor,
            };
          }),
          todo_list: todoList.map((d: any) => {
            delete d['name'];
            const { label, description = null, customer_user_id } = d;
            return { label, description, customer_user_id };
          }),
          prod_quantity_list: productions.map((d: TaskProduction) => {
            const {
              quantity = 0,
              description = null,
              product_id = null,
              exp_quantity,
              conversion_factor = 1,
              lost_quantity = 0,
              active_uom,
              active_conversion_factor,
            } = d;

            // Tính toán lại exp_quantity nếu cần thiết
            const calculatedExpQuantity =
              exp_quantity !== undefined && conversion_factor !== undefined
                ? exp_quantity * conversion_factor
                : exp_quantity;

            return {
              quantity,
              description,
              product_id,
              exp_quantity: calculatedExpQuantity,
              lost_quantity,
              active_uom,
              active_conversion_factor,
            };
          }),
        });
      } else {
        requestArr.push({
          label: values.label,
          farming_plan_state: values.farming_plan_state,
          start_date: moment(start_date).format('YYYY-MM-DD HH:mm:ss'),
          end_date: moment(end_date).format('YYYY-MM-DD HH:mm:ss'),
          description: values.description,
          assigned_to: values.assigned_to,
          status: values.status,
          image: imagePath,
          enable_origin_tracing: enable_origin_tracing ? 1 : 0,
          involve_in_users:
            involved_in_users?.map((d: any) => {
              const id = typeof d === 'string' ? d : d.customer_user;
              return { customer_user: id };
            }) || [],
          worksheet_list: workTimes.map((d: any) => {
            const {
              cost = 0,
              work_type_id = null,
              exp_quantity = 0,
              quantity = 0,
              type = null,
              description = null,
            } = d;
            return { cost, work_type_id, exp_quantity, quantity, type, description };
          }),
          item_list: taskItems.map((d: TaskItemUsed) => {
            const {
              quantity = 0,
              description = null,
              iot_category_id = null,
              exp_quantity,
              conversion_factor = 1,
              loss_quantity = 0,
              active_uom,
              active_conversion_factor,
            } = d;

            // Tính toán lại exp_quantity nếu cần thiết
            const calculatedExpQuantity =
              exp_quantity !== undefined && conversion_factor !== undefined
                ? exp_quantity * conversion_factor
                : exp_quantity;

            return {
              quantity,
              description,
              iot_category_id,
              exp_quantity: calculatedExpQuantity,
              loss_quantity,
              active_uom,
              active_conversion_factor,
            };
          }),
          todo_list: todoList.map((d: any) => {
            delete d['name'];
            const { label, description = null, customer_user_id } = d;
            return { label, description, customer_user_id };
          }),
          prod_quantity_list: productions.map((d: TaskProduction) => {
            const {
              quantity = 0,
              description = null,
              product_id = null,
              exp_quantity,
              conversion_factor = 1,
              lost_quantity = 0,
              active_uom,
              active_conversion_factor,
            } = d;

            // Tính toán lại exp_quantity nếu cần thiết
            const calculatedExpQuantity =
              exp_quantity !== undefined && conversion_factor !== undefined
                ? exp_quantity * conversion_factor
                : exp_quantity;

            return {
              quantity,
              description,
              product_id,
              exp_quantity: calculatedExpQuantity,
              lost_quantity,
              active_uom,
              active_conversion_factor,
            };
          }),
        });
      }

      requestArr = requestArr.map((d: any) => ({
        ...d,
        task_progress: 0,
        tag: values.tag,
      }));

      await createFarmingPlanTask(requestArr);

      message.success({
        content: 'Created successfully',
      });
      onOpenChange?.(false);
      if (onCreateSuccess) {
        onCreateSuccess?.();
      } else {
        history.push('/farming-management/workflow-management');
      }

      return true;
    } catch (error) {
      console.log('error', error);
      return false;
    } finally {
      setSubmitting(false);
    }
  };

  const [searchParams, setSearchParams] = useSearchParams();
  const farmingPlanState = searchParams.get('farming_plan_state');

  const intl = useIntl();
  /// default value for form
  useDeepCompareEffect(() => {
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
    }
  }, [defaultValue]);

  const content = (
    <ProForm<IFormData>
      onFinish={onFinish}
      submitter={false}
      initialValues={{
        farming_plan_state: farmingPlanStateId || farmingPlanState,
        start_date: dayjs(),
        crop: cropId,
      }}
      form={form}
      // formRef={formRef}
    >
      <Space
        size={'large'}
        direction="vertical"
        style={{
          width: '100%',
        }}
      >
        <DetailedInfo
          openFromModal={mode === 'modal'}
          isTemplateTask={isTemplateTask}
          currentPlanParam={currentPlan}
          onEditTagSuccess={onCreateSuccess}
          onFileListChange={onFileListChange}
          setTaskItems={setTaskItems}
          setTodoList={setTodoList}
          setWorkTimes={setWorkTimes}
          setProductions={setProductions}
        />
        <CreateTodoTableEditer
          dataSource={todoList}
          setDataSource={setTodoList}
          customerUserOptions={customerUserOptions}
        />
        <ItemUsedTableCreateView />
        {/* <CreateWorkTimeTableEditer dataSource={workTimes} setDataSource={setWorkTimes} /> */}
        {/* <ProductionTableCreateView dataSource={productions} setDataSource={setProductions} /> */}
        <ProductionTableCreateView />
        {/* <RelatedMaterials /> */}
        {/* <EstimateLaborAndCost /> */}
        {/* <CreateEstimateLaborAndCostTable getFormRef={() => formRef.current} /> */}
      </Space>
    </ProForm>
  );

  const footer = [
    <Space key="footer">
      <Button
        key={'cancel'}
        onClick={() => {
          if (mode === 'modal') {
            onOpenChange?.(false);
            return;
          }
          history.back();
        }}
      >
        {intl.formatMessage({ id: 'common.cancel' })}
      </Button>
      <Button
        onClick={async (event) => {
          console.log('submitting', event);
          try {
            const valid = await form.validateFields(); // Thêm validateFields để kiểm tra lỗi
            console.log('valid', valid);
            onFinish(form.getFieldsValue());
            // form.submit(); // Call form.submit() to trigger onFinish
          } catch (err) {
            console.error('Validation failed:', err);
          }
          // form.submit();
        }}
        loading={submitting}
        key="save"
        type="primary"
      >
        {intl.formatMessage({ id: 'common.save' })}
      </Button>
    </Space>,
  ];

  if (mode === 'modal')
    return (
      <Modal
        open={open}
        onCancel={() => {
          onOpenChange?.(false);
        }}
        confirmLoading={loading}
        width={800}
        title={intl.formatMessage({ id: 'common.create' })}
        footer={footer}
      >
        {content}
      </Modal>
    );
  return (
    <PageContainer
      fixedHeader
      // extra={[
      //   <Button
      //     key={'cancel'}
      //     onClick={() => {
      //       history.back();
      //     }}
      //   >
      //     Hủy
      //   </Button>,
      //   <Button key="save" type="primary">
      //     Lưu
      //   </Button>,
      // ]}
      footer={footer}
    >
      {content}
    </PageContainer>
  );
};

export default CreateWorkflow;
