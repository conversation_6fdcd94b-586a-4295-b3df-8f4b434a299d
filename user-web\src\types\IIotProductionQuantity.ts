/**
 * Generate by /Users/<USER>/Documents/viis/iot-backend-typescript/tools/gen_type.js
 */
export class IIotProductionQuantity {
  name?: string | number;
  quantity?: number; // Float
  original_quantity?: number; // Float
  exp_quantity?: number; // Float
  original_exp_quantity?: number; // Float
  lost_quantity?: number; // Float
  original_lost_quantity?: number; // Float
  uom_label?: string;
  uom_id?: string;
  packing_unit_label?: string; // Similar change for packing_unit
  packing_unit_id?: string;
  description?: string; // Small Text (or Data for more flexibility)
  task_id?: string; // Link
  product_id?: string; // Link
  finished_quantity?: number;
  original_finished_quantity?: number;
  issued_quantity?: number;
  original_issued_quantity?: number;
  total_qty_in_crop?: number; // Float
  original_total_qty_in_crop?: number; // Float
  draft_quantity?: number;
  original_draft_quantity?: number;
  conversion_factor?: number; // Float (to handle unit conversions)
  active_uom?: string; // Active UOM ID
  active_conversion_factor?: number; // Active conversion factor
  uoms?: UOM[];
  packing_uoms?: UOM[]; // To store packing unit conversion data
}

export type UOM = {
  uom_id: string;
  uom_label: string;
  conversion_factor: number;
};
